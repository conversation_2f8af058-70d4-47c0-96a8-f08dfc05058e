<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="color-scheme" content="light only" />
  <title>Boom Webview</title>
  <base href="/">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, viewport-fit=cover"
  />
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script type="application/javascript">
    window.onmessage=(m)=>{(window.postMessageQueue=window.postMessageQueue || []).push(m.data);}; window.parent.postMessage(JSON.stringify({function: 'moduleStarted', data: null}), '*');
    !function(){var n="/lgnp/api";function e(n){"function"==typeof window.onmessage?window.postMessage(n,'*'):(window.postMessageQueue=window.postMessageQueue||[]).push(n)}var t=window.location.search;if(t&&-1!==t.indexOf("ott")){var[o,s]=function(n,e){for(var t=[],o=null,s=("?"===n[0]?n.substr(1):n).split("&"),i=0;i<s.length;i++){var a=s[i].split("=");decodeURIComponent(a[0])===e?o=decodeURIComponent(a[1]):t.push(s[i])}return[o,t.join("&")]}(t,"ott");if(!o)return;window.history.replaceState(null,"",window.location.pathname+(s.length?"?"+s:"")+window.location.hash),e(JSON.stringify({type:"loginStarted",data:(new Date).getTime()})),function(e,t,o){var s=new XMLHttpRequest;s.open("GET",n+"/user/xott?ott="+e),s.responseType="json",s.onload=function(){if(200!==s.status)return o(s.response);var n=s.response;return t(n)},s.send()}(o,function(n){e(JSON.stringify({type:"loginResponse",data:{success:!0,loginResponse:n.data}}))},function(n){e(JSON.stringify({type:"loginResponse",data:{success:!1,loginResponse:n.data}}))})}}();
  </script>
  <script>
    void 0!==window.Borusan&& void 0!==window.Borusan.postMessage&&(console.log("MESSAGE SENDED - Get Mobile Info"),window.Borusan.postMessage(JSON.stringify({function:"getMobileInfo",data:{}})))
  </script>
  <style>#front-loader{z-index: 10000; position: absolute; left: 50%; top: 50%; margin-left:-50px; margin-top:-50px; border: 16px solid #7f7e7e; border-radius: 50%; border-top: 16px solid #FFA300; border-bottom: 16px solid #FFA300; width: 100px; height: 100px; animation: spin 1s linear infinite; box-sizing: border-box;}@-webkit-keyframes spin{0%{-webkit-transform: rotate(0deg);}100%{-webkit-transform: rotate(360deg);}}@keyframes spin{0%{transform: rotate(0deg);}100%{transform: rotate(360deg);}}</style> <script type="application/javascript"> document.write('<div id="front-loader"></div>'); </script>
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-DBY5T7HBT7"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-DBY5T7HBT7');
  </script>
</head>
<body class="mat-typography">
<cat-root></cat-root>
</body>
</html>
