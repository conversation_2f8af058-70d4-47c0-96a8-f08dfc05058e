<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="0821e206-deae-4536-b1b3-038b8bcbe200"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="7d8831e5-3b8a-46eb-999e-5071afd2a0bc"  >
</g>
<g transform="matrix(2.87 0 0 1.22 540 310.56)" id="24ac037a-4abd-4a55-a1cc-e096b6eef757"  >
<path style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(236,242,244); fill-opacity: 0.97; fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-187, -243.12)" d="M -1 6.00001 C -1 -3.94112 7.05888 -12 17 -12 L 357 -12 C 366.941 -12 375 -3.94113 375 6 L 375 384.373 C 375 392.433 369.642 399.511 361.884 401.698 L 21.884 497.549 C 10.3992 500.786 -1 492.156 -1 480.224 L -1 6.00001 Z" stroke-linecap="round" />
</g>
</svg>