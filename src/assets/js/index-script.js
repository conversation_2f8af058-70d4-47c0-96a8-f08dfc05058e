(function() {
  var api = '/api';

  function n(encoded) {
    if (typeof window.onmessage === 'function') {
      window.postMessage(encoded);
    } else {
      (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
    }
  }

  function shiftParam(queryString, key) {
    var remaining = [];
    var value = null;
    var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
    for (var i = 0; i < pairs.length; i++) {
      var pair = pairs[i].split('=');
      if (decodeURIComponent(pair[0]) === key) {
        value = decodeURIComponent(pair[1]);
      } else {
        remaining.push(pairs[i]);
      }
    }
    return [value, remaining.join('&')];
  }

  function xott(ott, onDone, onError) {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', api + '/user/xott?ott=' + ott);
    xhr.responseType = 'json';
    xhr.onload = function() {
      if (xhr.status !== 200) {
        return onError(xhr.response);
      }
      var responseObj = xhr.response;

      return onDone(responseObj);
    };
    xhr.send();
  }

  var s = window.location.search;
  if (s && s.indexOf('ott') !== -1) {
    var [ott, query] = shiftParam(s, 'ott');

    if (!ott) {
      return;
    }
    window.history.replaceState(null, '', window.location.pathname + (query.length ? '?' + query : ''));

    n(JSON.stringify({
      type: 'loginStarted',
      data: new Date().getTime(),
    }));

    xott(ott, function(response) {
      n(JSON.stringify({
        type: 'loginResponse',
        data: {
          success      : true,
          loginResponse: response.data,
        },
      }));
    }, function(response) {
      n(JSON.stringify({
        type: 'loginResponse',
        data: {
          success      : false,
          loginResponse: response.data,
        },
      }));
    });
  }
})();
