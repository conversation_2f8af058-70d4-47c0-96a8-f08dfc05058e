import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule, } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { ngxLoadingAnimationTypes, NgxLoadingModule } from 'ngx-loading';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './container/app/app.component';
import { ErrorInterceptor } from './interceptor/error.interceptor';
import { TokenInterceptor } from './interceptor/token.interceptor';
import { CustomTranslateLoader } from './service/custom-translate-loader';
import { StoreModule } from './store.module';
import { SharedModule } from './module/shared/shared.module';
import { ClientErrorInterceptorService } from './interceptor/client-error-interceptor.service';
import { NonBoomComponent } from './container/non-boom/non-boom.component';
import { ExternalErrorComponent } from './container/external-error/external-error.component';

@NgModule({
  declarations: [
    AppComponent,
    NonBoomComponent,
    ExternalErrorComponent
  ],
  imports: [
    AppRoutingModule,
    CommonModule,
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    StoreModule,
    NgxLoadingModule.forRoot({
      animationType: ngxLoadingAnimationTypes.circle,
      backdropBorderRadius: '2px',
      primaryColour: '#7b7b7b',
      fullScreenBackdrop: true,
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: ServerTranslateFactory,
        deps: [HttpClient],
      },
    }),
    SharedModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ClientErrorInterceptorService,
      multi: true,
    }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

export function ServerTranslateFactory(http: HttpClient) {
  return new CustomTranslateLoader(http);
}
