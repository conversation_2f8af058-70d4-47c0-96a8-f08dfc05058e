import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {Observable} from 'rxjs';
import { environment } from '../../environments/environment';
import {TranslateService} from '@ngx-translate/core';
import { LanguageModel } from '../model/language.model';
import { CacheService } from './cache.service';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  path = `${environment.assets}/language`;

  constructor(
    private readonly http: HttpClient,
    private readonly translate: TranslateService,
    private readonly cacheService: CacheService,
    private readonly route: ActivatedRoute,
  ) {
  }

  supportedLanguages(): Observable<LanguageModel[]> {
    return this.cacheService.get(CacheService.LANGUAGE_KEY, `${this.path}/supported-languages.json`);
  }

  public getCurrentLang(languages: Array<string>): string {
    let currentLanguageCode = CacheService.getLocalCache(CacheService.CURRENT_LANG_KEY);
    if (!currentLanguageCode) {
      const { language } = this.route.snapshot.queryParams;
      const browserLang = language || this.translate.getBrowserLang();
      if (languages && languages.length > 0) {
        languages.forEach((l) => {
          if (browserLang === l) {
            currentLanguageCode = browserLang;
          }
        });
      }
    }
    if (!currentLanguageCode) {
      currentLanguageCode = 'en';
    }
    return currentLanguageCode;
  }


}
