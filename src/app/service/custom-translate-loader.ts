import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateLoader } from '@ngx-translate/core';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';
import { CacheService } from './cache.service';

@Injectable({ providedIn: 'root' })
export class CustomTranslateLoader implements TranslateLoader {
  constructor(private readonly http: HttpClient) { }

  getTranslation(lang: string): Observable<any> {
    return new Observable((observer) => {
      let prefix = '';
      let key = '';
      if (lang != null) {
        prefix = lang + CacheService.CACHE_DELIMITER;
      }
      key = prefix + CacheService.TRANSLATE_KEY;
      this.getApiTranslate().subscribe(
        (translates) => {
          const translateObjects = JSON.parse(translates.data);
          CacheService.set(key, translates);
          observer.next(translateObjects);
          observer.complete();
        },
        () => {
          console.log('Translate Loading Error');
          this.getLocalTranslate(key, lang).subscribe(
            (translates) => {
                observer.next(translates.data);
                observer.complete();
            }
          );
        }
      );
    });
  }

  getApiTranslate(): Observable<any> {
    return this.http.get<any>(`${environment.api}/localization/get724localizations?t=` + new Date().getTime());
  }

  getLocalTranslate(key, lang): Observable<any> {
    return new Observable((observer) => {
      this.http.get(`${environment.language}/i18n/${lang}.json?t=` + new Date().getTime()).subscribe(
        (data) => {
          console.log('Translate Loaded Local...');
          const translateData = JSON.stringify(data);
          CacheService.set(key, { data: translateData });
          observer.next({ data });
          observer.complete();
        }
      );
    });
  }
}
