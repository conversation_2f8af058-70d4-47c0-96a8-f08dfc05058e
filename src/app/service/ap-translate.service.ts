import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { I18n } from '../model/i18n.model';
import { CacheService } from './cache.service';

@Injectable({
  providedIn: 'root',
})
export class ApTranslateService {
  path = `${environment.assets}/i18n`;

  constructor(
    protected readonly http: HttpClient,
    private cacheService: CacheService,
  ) {
  }

  translations(langCode?: string): Observable<I18n[]> {
    let prefix = '';
    if (langCode != null) {
      prefix = langCode + CacheService.CACHE_DELIMITER;
    }
    return this.cacheService.get(
      prefix + CacheService.TRANSLATE_KEY,
      `${this.path}/${langCode}.json`,
    );
  }
}
