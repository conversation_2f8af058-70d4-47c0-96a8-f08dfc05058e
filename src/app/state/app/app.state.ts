import { Action, Selector, State, StateContext } from '@ngxs/store';
import { ConnectionStatusAction, SetHistoryStateAction } from '../../action/app/app.actions';
import { Injectable } from '@angular/core';

export interface AppStateModel {
  connectionModalStatus: boolean;
  history: any;
}

@State<AppStateModel>({
  name: 'app',
  defaults: {
    connectionModalStatus: false,
    history: null,
  },
})
@Injectable()
export class AppState {

  @Selector()
  public static connectionModalStatus({ connectionModalStatus }: AppStateModel): boolean {
    return connectionModalStatus;
  }

  @Selector()
  public static history({ history }: AppStateModel): boolean {
    return history;
  }

  @Action(ConnectionStatusAction)
  connectionModalStatusAction({ patchState }: StateContext<AppStateModel>, { status }: ConnectionStatusAction) {
    patchState({
      connectionModalStatus: status,
    });
  }

  @Action(SetHistoryStateAction)
  setHistoryAction({ patchState }: StateContext<AppStateModel>, { history }: SetHistoryStateAction) {
    patchState({
      history,
    });
  }


}
