export const disableBack = (onBack = null) => {
  history.pushState(null, document.title);

  const popFunction = () => {
    history.pushState(null, document.title);
    if (onBack) {
      onBack();
    }
  };
  window.addEventListener('popstate', popFunction);
  return {
    remove: () => {
      window.removeEventListener('popstate', popFunction);
    },
    removeAndPop: () => {
      window.removeEventListener('popstate', popFunction);
      history.back();
    },
  };
};


