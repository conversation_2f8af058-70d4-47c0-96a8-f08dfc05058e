/**
 * Converts markdown text to HTML
 * Handles basic markdown syntax:
 * - Bold: **text** or __text__
 * - Italic: *text* or _text_
 * - Links: [text](url)
 * - Lists: * item or - item
 * - Headers: # Header or ## Header
 * - Line breaks: Two spaces at the end of a line
 * - Paragraphs: Blank line between text blocks
 */
export function convertMarkdownToHtml(markdown: string): string {
  if (!markdown) {
    return '';
  }

  let html = markdown;

  // Convert headers
  html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
  html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
  html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');
  html = html.replace(/^#### (.+)$/gm, '<h4>$1</h4>');

  // Convert bold
  html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\_\_(.+?)\_\_/g, '<strong>$1</strong>');

  // Convert italic
  html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');
  html = html.replace(/\_(.+?)\_/g, '<em>$1</em>');

  // Convert links
  html = html.replace(/\[(.+?)\]\((.+?)\)/g, '<a href="$2">$1</a>');

  // Convert lists
  html = html.replace(/^\* (.+)$/gm, '<li>$1</li>');
  html = html.replace(/^- (.+)$/gm, '<li>$1</li>');

  // Wrap lists in <ul> tags
  html = html.replace(/(<li>.+<\/li>\n)+/g, '<ul>$&</ul>');

  // Convert paragraphs
  html = html.replace(/^([^<].+)$/gm, '<p>$1</p>');

  // Clean up any empty paragraphs
  html = html.replace(/<p><\/p>/g, '');

  return html;
}
