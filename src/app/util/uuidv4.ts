const uuidv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    // tslint:disable-next-line:one-variable-per-declaration no-bitwise triple-equals
    const r = (Math.random() * 16) | 0,
      v =
        c == 'x'
          ? r
          : // tslint:disable-next-line:no-bitwise
            (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

export default uuidv4;
