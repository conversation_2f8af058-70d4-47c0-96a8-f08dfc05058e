export const clearUrlParamsOnHistory = (removeParams = [], state = null) => {
  replaceHistoryState(state || history.state, clearUrlSearchParams(removeParams));
};

export const replaceHistoryState = (state, href = null) => {
  window.history.replaceState(state, '', href || window.location.href);
};

export const clearUrlSearchParams = (removeParams, href = null) => {
  const url = new URL(href || window.location.href);
  removeParams.map(param => {
    if (url.searchParams.has(param)) {
      url.searchParams.delete(param);
    }
  });
  console.log(':::NEW URL', url.toString());
  return url.toString();
};
