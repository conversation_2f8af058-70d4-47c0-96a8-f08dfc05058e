import { AbstractControl } from '@angular/forms';

export const defaultFormErrorMessages = {
  pattern: '_pattern',
  required: '_required',
  email: '_email_pattern',
  notMatched: '_not_matched',
  minlength: '_min_length',
  maxlength: '_max_length',
  max: '_max_number_exceed',
  min: '_min_number_exceed',
};

export const getFormErrorMessage = (control: AbstractControl, messages: any = []) => {
  messages = {
    ...defaultFormErrorMessages,
    ...messages,
  };

  for (const messagesKey in messages) {
    if (messages.hasOwnProperty(messagesKey) && control.hasError(messagesKey)) {
      return messages[messagesKey];
    }
  }

  return '';
};
