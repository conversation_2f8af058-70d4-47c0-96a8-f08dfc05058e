const attachmentTypeMap = {
  23: '_dispatch_report',
  18: '_reception_report',
  2: '_technical_report',
  5: '_test_report',
  9: '_sos_report',
  0: '_attach_unknown_attachment',
  1: '_attach_defect_list',
  // 2: '_technical_report',
  3: '_attach_miscellaneous_list',
  4: '_attach_redo_form',
  // 5: '_test_report',
  6: '_attach_additional_work',
  7: '_attach_photo',
  8: '_attach_product_status_report',
  // 9: '_sos_report',
  10: '_attach_required_product_update_record',
  11: '_attach_engine_recond_checklists',
  12: '_attach_powertrain_recond_checklists',
  13: '_attach_act',
  14: '_attach_contract',
  15: '_attach_open_wo_request',
  16: '_attach_parts_return',
  17: '_attach_quotation',
  // 18: '_reception_report',
  20: '_attach_customer_communication',
  21: '_attach_reman_return',
  22: '_attach_suppliers_quotation',
  // 23: '_dispatch_report',
  24: '_attach_signed_quotation',
  25: '_attach_invoices',
  26: '_attach_mailbox',
  27: '_attach_inspection',
  28: '_attach_cyl_head_manif_turb',
  30: '_attach_housings',
  31: '_attach_oil_and_water_pumps',
  32: '_attach_fuel_pump',
  33: '_attach_electric_system',
  35: '_attach_oil_results',
  1035: '_attach_wo_sheet',
  1036: '_attach_files',
  1037: '_attach_stock_transfer',
  1039: '_attach_used_parts_return',
  1040: '_attach_design_documentation',
  1042: '_attach_technical_assignment',
  1043: '_attach_technological_documentation',
  1045: '_attach_waiting_for_tech_support_start',
  1046: '_attach_waiting_for_tech_support_stop',
  1047: '_attach_waiting_for_warranty_start',
  1048: '_attach_waiting_for_warranty_stop',
  1051: '_attach_waiting_for_parts_price_opening_start',
  1052: '_attach_waiting_for_parts_price_opening_stop',
  1056: '_attach_request_for_aw',
};

export const getServiceAttachmentKey = (uploadType) => {
  return attachmentTypeMap[uploadType] || '_document';
};
