const StringToBytesFaster = (str) => {
  // http://stackoverflow.com/questions/1240408/reading-bytes-from-a-javascript-string
  let ch;
  let st;
  const re = [];
  let j = 0;
  for (let i = 0; i < str.length; i++) {
    ch = str.charCodeAt(i);
    if (ch < 127) {
      // tslint:disable-next-line:no-bitwise
      re[j++] = ch & 0xff;
    } else {
      st = [];
      do {
        // tslint:disable-next-line:no-bitwise
        st.push(ch & 0xff);
        // tslint:disable-next-line:no-bitwise
        ch = ch >> 8;
      } while (ch);
      st = st.reverse();
      // tslint:disable-next-line:prefer-for-of
      for (let k = 0; k < st.length; ++k) {
        re[j++] = st[k];
      }
    }
  }
  return re;
};

export default StringToBytesFaster;
