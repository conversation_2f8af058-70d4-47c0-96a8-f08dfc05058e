import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxLoadingModule } from 'ngx-loading';
import { GetOttComponent } from './container/get-ott/get-ott.component';
import { RefreshOttComponent } from './container/refresh-ott/refresh-ott.component';
import { OttRoutingModule } from './ott-routing.module';

@NgModule({
  declarations: [
    GetOttComponent,
    RefreshOttComponent,
  ],
  imports: [
    CommonModule,
    OttRoutingModule,
    NgxLoadingModule,
  ],
})
export class OttModule {
}
