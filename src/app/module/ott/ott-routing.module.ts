import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { GetOttComponent } from './container/get-ott/get-ott.component';
import { RefreshOttComponent } from './container/refresh-ott/refresh-ott.component';

const routes: Routes = [
  {
    path: 'get',
    component: GetOttComponent,
  },
  {
    path: 'refresh',
    component: RefreshOttComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OttRoutingModule {
}
