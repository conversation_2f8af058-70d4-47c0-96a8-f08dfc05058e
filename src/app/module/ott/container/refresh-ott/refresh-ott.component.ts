import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngxs/store';

@Component({
  selector: 'cat-refresh-ott',
  templateUrl: './refresh-ott.component.html',
  styleUrls: ['./refresh-ott.component.scss'],
})
export class RefreshOttComponent implements OnInit {

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
  ) {
  }

  ngOnInit(): void {
    const { ott, url } = this.route.snapshot.queryParams;
    if (ott && url) {
      const parsedUrl = this.router.parseUrl(url);
      parsedUrl.queryParams.ott = ott;
      this.router.navigateByUrl(parsedUrl.toString()).then();
    }
  }
}
