import { Component, Input, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { FormGroup, Validators } from '@angular/forms';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { FieldModel } from '../../model/field.model';
import { InspectionQuestion } from '../../model/inspection-response.model';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { Store } from '@ngxs/store';

@Component({
  selector: 'cat-field-text',
  templateUrl: './field-text.component.html',
  styleUrls: ['./field-text.component.scss']
})
export class FieldTextComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Input()
  form: FormGroup;

  @Input()
  field: InspectionQuestion;

  constructor(
    readonly store: Store
  ) { }

  ngOnInit(): void {
    // this.store.dispatch(new GetBasisSettingsAction());
  }
}
