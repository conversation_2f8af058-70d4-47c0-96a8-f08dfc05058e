import { Component, OnInit } from '@angular/core';
import uuidv4 from 'src/app/util/uuidv4';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { Select, Store } from '@ngxs/store';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import snq from 'snq';
import { SoundDiagnosticService } from '../../service/sound-diagnostic.service';
import { FaultCodesModel } from '../../model/sound-diagnostic.model';
import { FaultComponentEnum } from '../../enum/fault-components.enum';
import { EquipmentModel } from 'src/app/module/customer/model/equipment.model';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { IncomingMessageService } from 'src/app/module/shared/service/incoming-message.service';
import { IncomingMessageEnum } from 'src/app/module/shared/enum/incoming-message.enum';
import { InspectionService } from '../../service/inspection.service';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { LogService } from 'src/app/module/customer/service/log.service';
import { catchError, takeUntil } from 'rxjs/operators';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { Observable, Subject } from 'rxjs';
import { GetEquipmentDiagnosticDataPagingAction } from 'src/app/module/customer/action/equipment.action';

export enum HealthCodeEnum {
  _healty = '0',
  _unhealty = '1',
  _error = '2'
}

@Component({
  selector: 'cat-diagnostic-record-upload',
  templateUrl: './diagnostic-record-upload.component.html',
  styleUrls: ['./diagnostic-record-upload.component.scss']
})


export class DiagnosticRecordUploadComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  loading: boolean;
  form: FormGroup = new FormGroup({
    soundDiagFaultCodeKeys: new FormControl(null, [Validators.required]),
    recordCollectingAttentionTypes: new FormControl(null, [Validators.required]),
    soundHealthCode: new FormControl(null, [Validators.required]),
    rpm: new FormControl(null, [Validators.required, Validators.max(30000), Validators.min(1)]),
  });

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  soundDiagnosticSuccesModal: boolean;
  faults: any[] = [];
  FaultComponentEnum = FaultComponentEnum;
  attachId = uuidv4();
  equipment: any;
  private recorded: any;
  private equipmentNumber: string;
  private serialNumber: string;
  me: any;
  HealthCodeEnum = HealthCodeEnum
  constructor(
    private readonly frameService: MessageFrameService,
    private readonly store: Store,
    private readonly soundDiagnosticService: SoundDiagnosticService,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly inspectionService: InspectionService,
    private readonly logger: LogService
    ) { }

    protected subscriptions$: Subject<boolean> = new Subject();


  ngOnInit() {
    this.frameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: false});
    this.equipment = this.store.selectSnapshot(EquipmentState.equipmentDetail);
    this.me = this.store.selectSnapshot(LoginState.loginResponse);
    this.soundDiagnosticService.faultCodes().subscribe((res:any) => {
      res.data.forEach((element, index )=> {
        this.faults.push({name: element.name, value: element.value, id: index.toString() });
      });
    });
    this.equipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((equipment) => {
        if (equipment) {
          this.serialNumber = equipment?.serialNumber;
          this.equipmentNumber = equipment.equipmentNumber;
        }
      });

  }

  listenSoundRecorded() {
    this.recorded = this.incomingMessageService
      .subscribe(IncomingMessageEnum.soundRecordCollected, data => {
        this.loading = true;
        this.logger.log('SOUND_RECORD', 'SOUND_RECORD_COLLECTED', {
          soundRecordLength: data?.soundRecord?.length,
          equipmentNumber: this.equipment.equipmentNumber,
          equipmentSerialNumber: this.equipment.serialNumber,
        }).subscribe();
        const body = {
          equipmentNumber: this.equipment.equipmentNumber,
          equipmentSerialNumber: this.equipment.serialNumber,
          deviceId: this.me.appSessionId,
          customerNumber: this.me.customer.customerNumber,
          soundDiagFaultCodeKeys: this.form.value.soundDiagFaultCodeKeys,
          recordCollectingAttentionTypes: this.form.value.recordCollectingAttentionTypes,
          rpm: this.form.value.rpm,
          soundHealthCode: this.form.value.soundHealthCode
        }
        if(data.soundRecord){
          this.inspectionService.postSoundDiagnosticRecord(data.soundRecord,
            body.equipmentNumber,
            body.equipmentSerialNumber,
            body.deviceId,
            body.customerNumber,
            body.soundDiagFaultCodeKeys,
            body.recordCollectingAttentionTypes,
            body.rpm,
            body.soundHealthCode).subscribe(
            () => {
              this.loading = false
              this.soundDiagnosticSuccesModal = true
            }
          ),catchError((err): any => {
            this.loading = false
          })
        }
      });
  }
  startRecord() {
    const transactionId = uuidv4();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'START_RECORD_UPLOAD_SENDING_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.frameService.sendMessage(FrameMessageEnum.voice_record, {
      EquipmentNumber: this.equipment?.equipmentNumber,
      SerialNumber: this.equipment.serialNumber,
      CustomerNumber: this.equipment?.customerNumber,
      Source: 'recordCollecting',
      TransactionId: transactionId,
    });
    this.listenSoundRecorded();
  }

  scrollTop(){
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]){
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }


  navigateToBack() {
    this.soundDiagnosticSuccesModal = false;
    this.form.setValue({soundDiagFaultCodeKeys: null, recordCollectingAttentionTypes: null, soundHealthCode: null, rpm: null});
  }

  ToBack() {
    window.history.back();
  }

  navigateToHome() {
    this.frameService.sendMessage(FrameMessageEnum.back_home);
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

}
