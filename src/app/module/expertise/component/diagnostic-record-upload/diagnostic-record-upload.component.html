
  <div class="sound-diagnostic-component" *ngIf="equipment">
      <div class="h4 nav-back mb-4 ml-4 pt-4 d-flex">
        <i (click)="ToBack()" class="icon icon-back mr-2"></i>
        <div class="sound-diagnostic-component-title">{{'_upload_voice_fault_record' | translate}}</div>
      </div>
      <div class="fault-list" style="min-height: max-content;">
        <cat-info-box [title]="'_info' | translate">
          <div>{{'_sound_record_might_start' | translate}}</div>
         </cat-info-box>
        <form [formGroup]="form">
          <div class="form-group">
            <ng-select
            class="service-drp"
            [searchable]="false"
            [placeholder]="'_faults' | translate"
            [dropdownPosition]="'bottom'"
            [clearable]="false"
            formControlName="soundDiagFaultCodeKeys"
            (keypress)="scrollTop()"
            #soundDiagFaultCodeKeys
          >
          <div *ngFor="let item of faults; let i = index" >
            <ng-option
              [value]="item.id">
                <span>{{item.value}}</span>
              </ng-option
            >
          </div>
          </ng-select>
          <div class="mt-4">
            <ng-select
            class="service-drp"
            [searchable]="false"
            [placeholder]="'_component' | translate"
            [dropdownPosition]="'bottom'"
            [clearable]="false"
            formControlName="recordCollectingAttentionTypes"
            (keypress)="scrollTop()"
            #recordCollectingAttentionTypes
          >
          <div *ngFor="let item of FaultComponentEnum | enumToArray" >
            <ng-option
              [value]="item.value">
                <span>{{item.key | translate}}</span>
              </ng-option
            >
          </div>
          </ng-select>
          </div>
          <div class="mt-4">
            <ng-select
            class="service-drp"
            [searchable]="false"
            [placeholder]="'_health_status' | translate"
            [dropdownPosition]="'bottom'"
            [clearable]="false"
            formControlName="soundHealthCode"
            (keypress)="scrollTop()"
            #soundHealthCode
          >
          <div *ngFor="let item of HealthCodeEnum | enumToArray" >
            <ng-option
              [value]="item.value">
                <span>{{item.key | translate}}</span>
              </ng-option
            >
          </div>
          </ng-select>
          </div>

          <div class=" mt-4">
            <input
            [name]="'Rpm'"
            [placeholder]="'Rpm' | translate "
            class="form-control w-100"
            formControlName="rpm"
            type="tel"
            (input)="onInputPhone($event)"
          />
          <div
          [ngClass]="{ 'd-block': isShowError(form.controls.rpm) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.rpm) | translate }}
        </div>
          </div>
           <div>
          <button
          type="button"
          (click)="startRecord()"
          class="action-btn btn btn-warning btn-sm px-4 mb-5 mb-2 btn-block text-white my-4"
          [disabled]="form.valid === false"
        >
              {{ "_upload_sound" | translate }}
            </button>
           </div>
          </div>
        </form>
      </div>

    <div *ngIf="soundDiagnosticSuccesModal" [@preview] class="after-form-send">
      <div class="after-form-send-content text-center px-4">
        <i class="icon icon-message-success d-inline-block mb-4"></i>
        <div class="success-message mb-5">{{ '_voice_record_uploaded_successfully' | translate }}</div>

        <div
        class="btn btn-info btn-gradient btn-block text-white shadow btn-sm"
        (click)="navigateToBack()"
        >
          {{ '_add_new_record' | translate }}
        </div>
        <div
        class="btn btn-info btn-gradient btn-block text-white shadow btn-sm"
        (click)="navigateToHome()"
        >
          {{ '_back_to_home' | translate }}
        </div>
    </div>
  </div>

</div>



<cat-loader [show]="loading"></cat-loader>
