import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { FieldModel } from '../../model/field.model';

@Component({
  selector: 'cat-field-builder',
  templateUrl: './field-builder.component.html',
  styleUrls: ['./field-builder.component.scss']
})
export class FieldBuilderComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Input()
  form: FormGroup;

  @Input()
  field: FieldModel;

  @Input()
  id: string;

  constructor() { }

  ngOnInit(): void {
    if (!this.field.placeholder) {
      this.field.placeholder = this.field.label;
    }

    if (this.field.type !== 'checkbox') {
      this.form.addControl(this.field.name, new FormControl(this.field.default, this.field.required ? Validators.required : []));
    } else {
      const opts = {};
      for (const opt of this.field.options) {
        opts[opt.key] = new FormControl(this.field.default);
      }
      this.form.addControl(this.field.name, new FormGroup(opts));
    }
  }
}
