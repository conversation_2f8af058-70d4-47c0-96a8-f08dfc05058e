<div class="form-group mb-5" [formGroup]="form" *ngIf="field">
  <div class="d-flex mb-2"
  >
    <div class="pr-2" style="font-size: 22px; line-height: 40px"
         [ngClass]="{
         red: field.color == 'red',
         green: field.color == 'green',
         grey: field.color == 'grey',
         orange: field.color == 'orange'
         }"
    >
      {{id}}
    </div>
    <div>
      <label *ngIf="field.label" class="mt-2 form-control-label question-label font-size-14px"
             [attr.for]="field.label">
        {{field.label}}
        <strong class="text-danger" *ngIf="field.required">*</strong>
      </label>
    </div>
  </div>

  <div class="" [ngSwitch]="field.type">
    <cat-field-text *ngSwitchCase="'text'" [field]="field" [form]="form"></cat-field-text>
    <cat-field-textarea *ngSwitchCase="'textarea'" [field]="field" [form]="form"></cat-field-textarea>
    <cat-field-select *ngSwitchCase="'dropdown'" [field]="field" [form]="form"></cat-field-select>
    <cat-field-radio *ngSwitchCase="'radio'" [field]="field" [form]="form"></cat-field-radio>
    <cat-field-file *ngSwitchCase="'file'" [field]="field" [form]="form"></cat-field-file>
    <cat-field-checkbox *ngSwitchCase="'checkbox'" [field]="field" [form]="form"></cat-field-checkbox>
    <cat-field-sound-diagnostic *ngSwitchCase="'sound-diagnostic'" [field]="field"
                                [form]="form"></cat-field-sound-diagnostic>

    <!--    <div class="alert alert-danger my-1 p-2 fadeInDown animated" *ngIf="!isValid && isDirty">{{field.label}} is required</div>-->
    <div
      [ngClass]="{ 'd-block': isShowError(form.controls[field.name]) }"
      class="invalid-feedback pl-3"
    >
      {{ getFormErrorMessage(form.controls[field.name]) | translate }}
    </div>
  </div>
</div>
