import { Component, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { FieldTextComponent } from '../field-text/field-text.component';

@Component({
  selector: 'cat-field-textarea',
  templateUrl: './field-textarea.component.html',
  styleUrls: ['./field-textarea.component.scss']
})
export class FieldTextareaComponent extends FieldTextComponent implements OnInit {
  ngOnInit(): void {
    // this.store.dispatch(new GetBasisSettingsAction());
  }
}
