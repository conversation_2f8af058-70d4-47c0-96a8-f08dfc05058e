@import "variable/bootstrap-variable";

label {
  margin-bottom: 0;
}

.btn-sm {
  padding: 0.27rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 8px;
}

.btn-group > .btn{
  position: unset;
}

.form-check{
  position: unset;
  overflow-wrap: anywhere;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.btn-group .btn {
  border-radius: 6px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:nth-child(n+3), .btn-group > :not(.btn-check) + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-outline-secondary.selected {
  color: #fff;
  background-color: $secondary;
  border-color: $secondary;
}
.btn-outline-danger.selected {
  color: #fff;
  background-color: #DA3A3C;
  border-color: #DA3A3C;
}

.btn-outline-success.selected {
  color: #fff;
  background-color: #5E9731;
  border-color: #5E9731;
}

.btn-outline-warning.selected {
  color: #212529;
  background-color: #FFA300;
  border-color: #FFA300;
}
