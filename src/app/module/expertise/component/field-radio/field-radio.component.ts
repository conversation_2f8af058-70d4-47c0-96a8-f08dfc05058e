import { Component } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { InspectionQuestionOption } from '../../model/inspection-response.model';
import { Validators } from '@angular/forms';

@Component({
  selector: 'cat-field-radio',
  templateUrl: './field-radio.component.html',
  styleUrls: ['./field-radio.component.scss']
})
export class FieldRadioComponent extends FieldTextComponent {

  onChange(opt: InspectionQuestionOption) {
    this.field.selected = opt;

    // this.form.get(this.field.questionId).get('Comment').setErrors(null);
    this.form.get(this.field.questionId).get('Comment').setValidators(
      opt.isCommentBoxRequired ? [Validators.required] : []);
    this.form.get(this.field.questionId).get('Comment').updateValueAndValidity();

  }
}
