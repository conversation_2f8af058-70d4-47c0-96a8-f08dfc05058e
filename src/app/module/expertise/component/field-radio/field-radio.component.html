<div [formGroup]="form">
  <div [formGroupName]="field.questionId">
    <div *ngIf="field.options?.length" class="form-check btn-group w-100 row">
      <ng-container *ngFor="let opt of field.options">
        <!--      <label class="form-check-label">-->
        <!--        <input class="form-check-input"-->
        <!--               type="radio"-->
        <!--               [formControlName]="'Answer'"-->
        <!--               [value]="opt.value"-->
        <!--               (change)="onChange(opt)"-->
        <!--        >-->

        <input type="radio"
               class="btn-check d-none"
               [id]="opt.optionId"
               [formControlName]="'Answer'"
               [value]="opt.value"
               (change)="onChange(opt)"
        >
        <label class="col-3 btn btn-sm"
               [class.selected]="field?.selected?.optionId === opt.optionId"
               [ngClass]="{
               'btn-outline-danger' : opt.color === 'red',
               'btn-outline-success' : opt.color === 'green',
               'btn-outline-warning' : opt.color === 'orange',
               'btn-outline-secondary' : opt.color === 'grey' || !(opt.color === 'red' ||
                  opt.color === 'green' ||
                  opt.color === 'orange')
               }"
               [for]="opt.optionId">{{opt.title}}</label>
      </ng-container>
      <!--      </label>-->
    </div>


    <!--    <div class="btn-group" role="group">-->
    <!--      <input type="radio" name="radio-Btn-Group" class="btn-check" id="radioBtn1">-->
    <!--      <label class="btn btn-outline-danger" for="radioBtn1">Click Me</label>-->

    <!--      <input type="radio" name="radio-Btn-Group" class="btn-check" id="radioBtn2">-->
    <!--      <label class="btn btn-outline-danger" for="radioBtn2">Click Me</label>-->

    <!--      <input type="radio" name="radio-Btn-Group" class="btn-check" id="radioBtn3">-->
    <!--      <label class="btn btn-outline-danger" for="radioBtn3">Click Me</label>-->
    <!--    </div>-->
  </div>

</div>
