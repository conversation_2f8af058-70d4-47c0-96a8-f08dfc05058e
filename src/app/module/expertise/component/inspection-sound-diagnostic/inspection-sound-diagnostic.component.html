<div *ngIf="!soundDone" class="d-flex flex-column content">
  <div class="flex-grow-1 ">
      <cat-field-sound-diagnostic
        [data]="{
          serialNumber : activeForm?.serialNumber,
          equipmentNumber : equipmentNumber,
          customerNumber: customerNumber
        }"
        [intro]="true"
      >
      </cat-field-sound-diagnostic>
  </div>

  <div class="d-flex justify-content-between">
    <a
      *ngIf="this.step !== 0"
      class="btn btn-secondary btn-gradient text-white shadow rounded-lg min-width-100"
      (click)="navigateToBack()"
    >
      {{'_prev' | translate}}
    </a>
    <button catUserClick [section]="'INSPECTION'"
      [subsection]="'INSPECTION_COMPLETE_WITHOUT_SOUND_DIAGNOSTIC_BUTTON_CLICK'"
      *ngIf="!activeGroup?.isSoundDiagRequired"
      (click)="completeWithoutSound()"
      type="button"
      class="btn btn-warning btn-gradient  text-white shadow rounded-lg min-width-100"
    >
      <!--      [disabled]="!soundId"-->
      {{'_complete_inspection' | translate}}
    </button>

  </div>
</div>

<div *ngIf="soundDone" class="after-form-send ">
  <div class="success-screen mt-4">
    <div class="h-100 d-flex flex-column   text-center px-4">
      <div class="flex-grow-1 d-flex flex-column justify-content-center">
        <i class="icon icon-message-success d-inline-block mb-4"></i>
        <div class="success-message mb-3">{{ "_successfully_send_form" | translate }}</div>


        <div *ngIf="soundId && soundCheckLoading"
             class="d-flex justify-content-between mt-3 font-size-14px text-center">
          <div class="sound-loading" *ngIf="soundCheckLoading">
            <cat-mini-loader [show]="true"></cat-mini-loader>
          </div>
          <div>
            {{'_sound_received_message' | translate}}
          </div>


        </div>


        <div *ngIf="soundId && soundResult" class="notification-item overflow-hidden mt-3 mb-4 text-left">
          <div
            [ngClass]="{
        'notification-item-color-green': healthy(),
        'notification-item-color-orange': unHealthy(),
        'notification-item-color-red': healthCheckFail()
      }"
            class="notification-item-color notification-item-color-green"
          ></div>

          <div *ngIf="soundId" class="status-pop pb-3 pr-3">
            <div *ngIf="soundResult"
                 class="d-flex justify-content-between mb-2 overflow-hidden align-items-end"
            >
              <div class="font-weight-semi-bold h6 m-0">
                {{ soundResult?.serialNumber }}

              </div>
              <div
                [ngClass]="{ 'text-success': healthy(), 'text-warning': unHealthy() }"
                class="font-weight-semi-bold"
                style="top: 0"
              >
                <i *ngIf="healthy()" class="icon icon-success font-size-22px"></i>
              </div>

            </div>
            <div *ngIf="soundResult" class="label-text">
              {{ soundResult.resultCode === HealthCodeEnum.Unhealthy ? ('_sound_result_error' | translate): soundResult?.message }}
            </div>

            <div *ngIf="healthCheckFail()" class="mt-2">
              <cat-field-sound-diagnostic
                [data]="{
                  serialNumber : activeForm?.serialNumber,
                  equipmentNumber : equipmentNumber
                }"
                [intro]="false"
                [button]="'_sound_diagnostic_retry'"
              >
              </cat-field-sound-diagnostic>
            </div>

          </div>

        </div>
      </div>

      <div >
        <button
        type="button"
        class="btn btn-info btn-block text-white shadow mb-5 mx-auto d-flex mailsender"
        (click)="sendMail()"
        [disabled]="disabled"
        >
          {{ (mailSenderText ? '_mail_sended' : '_share_form_email_button') | translate }}
          <div
            class="download-file border text-lg-right border-info rounded-circle d-flex justify-content-center align-items-center ml-2"
            [class.spinner]="mailSenderLoading">
            <i class="icon icon-spinner8 border-light" *ngIf="mailSenderLoading"></i>
          </div>
        </button>
      </div>
      <div >
        <div
          class="btn btn-warning btn-gradient btn-block text-white shadow"
          (click)="close()"
        >
          {{ "_close" | translate }}
        </div>
      </div>
    </div>

  </div>
</div>

<cat-loader [show]="loading"></cat-loader>
