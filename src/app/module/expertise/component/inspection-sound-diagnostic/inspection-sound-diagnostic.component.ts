import { ChangeDetectorRef, Component, Input, NgZone, OnInit } from '@angular/core';
import { InspectionGroup, InspectionResponseModel } from '../../model/inspection-response.model';
import { IncomingMessageService } from '../../../shared/service/incoming-message.service';
import { IncomingMessageEnum } from '../../../shared/enum/incoming-message.enum';
import { SoundDiagnosticService } from '../../service/sound-diagnostic.service';
import { InspectionService } from '../../service/inspection.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HealthCodeEnum } from '../../enum/health-code.enum';
import { disableBack } from '../../../../util/disable-back.util';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { SoundDiagnosticModel } from '../../model/sound-diagnostic.model';
import { Select, Store } from '@ngxs/store';
import { CommonState } from '../../../shared/state/common/common.state';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import { Observable } from 'rxjs';
import { LogService } from '../../../customer/service/log.service';

@Component({
  selector: 'cat-inspection-sound-diagnostic',
  templateUrl: './inspection-sound-diagnostic.component.html',
  styleUrls: ['./inspection-sound-diagnostic.component.scss']
})
export class InspectionSoundDiagnosticComponent implements OnInit {
  disableBack = disableBack;
  HealthCodeEnum = HealthCodeEnum;
  @Input()
  activeForm: InspectionResponseModel;

  @Input()
  activeGroup: InspectionGroup;

  @Input()
  step: number;

  @Input()
  equipmentNumber: string;

  customerNumber: string;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  soundId: string;
  soundCheckLoading: boolean;
  soundResult: SoundDiagnosticModel;
  checkCount: number;
  loading: boolean;
  formSendStatus: boolean;
  timeOutId: any;
  soundDone: number;
  private disabledBack: any;
  mailSenderText = false;
  mailSenderLoading = false;
  disabled = false;

  constructor(
    protected readonly incomingMessageService: IncomingMessageService,
    protected readonly diagnosticService: SoundDiagnosticService,
    private inspectionService: InspectionService,
    private router: Router,
    private route: ActivatedRoute,
    private ref: ChangeDetectorRef,
    private messageFrameService: MessageFrameService,
    private logService: LogService,
    private readonly zone: NgZone,
    private readonly store: Store,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const { soundDone } = params;
      this.soundDone = parseInt(soundDone);
    });

    this.currentCustomer$.subscribe(customer => this.customerNumber = customer?.customer?.customerNumber);

    console.log('subscript');
    this.incomingMessageService
      .subscribe(IncomingMessageEnum.uploadedSoundID, (soundId) => {
        this.soundId = soundId;
        this.soundResult = null;
        setTimeout(() => {
          this.zone.run(() => {
            this.checkSound(soundId);
            this.ref.detectChanges();
            this.router
              .navigate([], {
                relativeTo: this.route,
                queryParams: { soundDone: 1 },
                queryParamsHandling: 'merge',
              });
            this.complete();

          });
        });
      });
  }

  checkSound(soundId) {
    this.soundCheckLoading = true;
    this.diagnosticService.soundResult(soundId)
      .subscribe(result => {
        if (result.data === null) {
          this.checkCount++;
          this.timeOutId && clearTimeout(this.timeOutId);
          this.timeOutId = setTimeout(() => {
            this.checkSound(soundId);
          }, 3000);
        } else {
          this.soundCheckLoading = false;
          this.soundResult = result?.data;
        }

      }, error => {
        this.soundCheckLoading = false;
      });
  }

  complete() {
    console.log('activeForm', this.activeForm);
    this.loading = true;
    this.inspectionService.saveSound({
      SoundId: this.soundId,
      InspectionFormId: this.activeForm.formId,
      PortalUserInspectionFormId: this.activeForm.portalUserInspectionFormId,
    }).subscribe(data => {
      this.completeWithoutSound();
    }, error => {
      this.loading = false;

    });

  }

  healthy() {
    return this.soundResult?.resultCode === HealthCodeEnum.Healthy;
  }

  unHealthy() {
    return this.soundResult?.resultCode === HealthCodeEnum.Unhealthy;
  }

  healthCheckFail() {
    return this.soundResult?.resultCode === HealthCodeEnum.Error;
  }

  navigateToBack() {
    window.history.back();
  }


  close() {
    if (this.store.selectSnapshot(CommonState.currentCustomer)) {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }

    this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
  }

  completeWithoutSound() {
    this.loading = true;

    this.logService
      .action('EXPERTISE', 'EXPERTISE_COMPLATE', {
        formId: this.activeForm.formId
      })
      .subscribe();

    this.inspectionService
      .complete({
        InspectionFormId: this.activeForm.formId,
        PortalUserInspectionFormId: this.activeForm.portalUserInspectionFormId,
      })
      .subscribe(res => {
        this.loading = false;
        // this.timeOutId && clearTimeout(this.timeOutId);
        // this.formSendStatus = true;
        this.soundDone = 1;

        if (this.disabledBack) {
          this.disabledBack?.remove();
        }
        this.disabledBack = this.disableBack(() => {
          this.close();
        });

      }, error => {
        this.loading = false;
      });

  }
  sendMail(){
    this.mailSenderLoading = true;
    this.disabled = true;
    this.inspectionService.sendExpertiseResultsMail(
      this.activeForm.formId,
      this.activeForm.portalUserInspectionFormId,
      ).subscribe(res => {
        this.mailSenderLoading = false;
        this.mailSenderText = true;
      }, error => {
      this.disabled = false;
      this.mailSenderText = false;
      this.mailSenderLoading = false;
    });
  }
}
