@import "variable/bootstrap-variable";

.content {
  min-height: calc(100vh - 69px - 1rem);
}


.notification {

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }

      &-red {
        background: #FF0000;
      }
    }
  }
}


.status-pop {
  padding: 16px 30px;
  min-height: 100px;
}
.label-text {
  font-size: 13px;
}

.success-screen{
  height: 90vh;
}

.sound-loading {
  position: relative;
  width: 50px;
  height: 50px;
  //left: 0;
  //top: 0;
}

.after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
}

.app-loader {
  z-index: 10000;
  position: relative;

  border: 8px solid #7f7e7e;
  border-radius: 50%;
  border-top: 8px solid #FFA300;
  border-bottom: 8px solid #FFA300;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.download-file {
  width: max-content;
  height: max-content;
  text-align: center;
  display: flex;
  justify-content: center;
  margin-top: 2px;
  border: 2px;
}

.mailsender{
  min-width: 75%;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
}
