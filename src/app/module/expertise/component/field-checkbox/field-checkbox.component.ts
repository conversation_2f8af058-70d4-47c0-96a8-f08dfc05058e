import { Component, OnInit } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'cat-field-checkbox',
  templateUrl: './field-checkbox.component.html',
  styleUrls: ['./field-checkbox.component.scss']
})
export class FieldCheckboxComponent extends FieldTextComponent implements OnInit {

  inlineForm: FormGroup = new FormGroup({});

  ngOnInit() {
    super.ngOnInit();
    let answers = [];
    if (this.field.answer?.answer) {
      answers = this.field.answer.answer.split('||');
    }
    const opts = {};
    for (const opt of this.field.options) {
      opts[opt.value] = new FormControl(answers.indexOf(opt.value) !== -1);
    }
    this.inlineForm = new FormGroup(opts);
  }

  onChange($event, opt) {
    const answer = Object.entries(this.inlineForm.value).filter(item => item[1]).map(item => item[0]).join('||');
    this.form.get(this.field.questionId).patchValue({
      Answer: answer
    });

    this.field.selected = opt;
    if (opt.isCommentBoxRequired && $event.target.checked) {
      this.form.get(this.field.questionId).get('Comment').setValidators([Validators.required]);
    }
  }
}
