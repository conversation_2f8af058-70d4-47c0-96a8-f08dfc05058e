import { Component, Input } from '@angular/core';
import { FileUploadPreviewComponent } from '../../../../export/file-upload/component/file-upload-preview/file-upload-preview.component';
import { FileUploadService } from '../../../../export/file-upload/service/file-upload.service';
import { InspectionFileService } from '../../service/inspection-file.service';
import { FileModel } from '../../../../export/file-upload/model/file.model';
import { InspectionAttachment } from '../../model/inspection-response.model';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'cat-inspection-file-upload-preview',
  templateUrl: './inspection-file-upload-preview.component.html',
  // templateUrl: '../../../../export/file-upload/component/file-upload-preview/file-upload-preview.component.html',
  styleUrls: ['./inspection-file-upload-preview.component.scss']
})
export class InspectionFileUploadPreviewComponent extends FileUploadPreviewComponent {

  constructor(
    protected api: FileUploadService,
    protected inspectionFileService: InspectionFileService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService
  ) {
    super(api);
  }
  loadingList: any[] = [];

  @Input()
  loadingCounter: any[];

  deleteLoading: boolean;

  fileDeleteService(id) {
    return this.inspectionFileService.delete(id);
  }
  onClickDeleteFile(file: FileModel | InspectionAttachment | any) {
    if (!this.isDeleting(file.AssignedId || file.attachmentId)) {
      this.loadingList.push(file.AssignedId || file.attachmentId);
    }
    this.deleteLoading = true;
    this.fileDeleteService(file.id || file.attachmentId).subscribe(
      (res) => {
        if (res) {
          this.deleteLoading = false;
          this.loadingList = this.loadingList.filter(data => data !== file.AssignedId || file.attachmentId);
          this.deleteFile.emit(file);
        }
      },
      () => {
        this.deleteLoading = false;
        this.modalService.errorModal({
          message: this.translateService.instant('_error')
        });
      }
    );
  }

  isImage(FileType: string) {
    return [
      'jpg',
      'jpeg',
      'png',
    ].indexOf(FileType) !== -1;
  }

  isDeleting(id: string) {
    return !!this.loadingList.filter(data => data === id)?.length;
  }
}
