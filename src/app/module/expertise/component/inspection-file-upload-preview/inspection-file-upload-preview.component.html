<div class="d-flex flex-wrap">
  <div
    *ngFor="let file of files"
    class="image-container d-flex align-items-center border border-warning rounded-sm mr-2 overflow-hidden mb-2"

  >
    <div
      *ngIf="deleteButtonStatus"
      class="bg-warning h-100 d-flex align-items-center px-2 cursor-pointer" (click)="onClickDeleteFile(file)">
      <i class="icon icon-x-bold font-size-12px"></i>
    </div>
    <div [id]="file.id" class="flex-row" style="max-width: calc(100% - 28px)"
         [ngStyle]="{'height': isDeleting(file.AssignedId || file.attachmentId) ? '78px' : ''}"
         [ngClass]="{'d-flex justify-content-center align-items-center image-width w-100': isDeleting(file.AssignedId || file.attachmentId) }">
      <div *ngIf="!isDeleting(file.AssignedId || file.attachmentId)">
        <div *ngIf="isImage(file.FileType || file.fileType)">
          <img class="image-preview" [src]="
          file.ImageContent ?
          'data:image/png;base64,'+file.ImageContent : file.path">
        </div>
        <div class="font-size-10px py-1 px-2 text-break">
          <ng-container>
            {{file.Name || file.name }}.{{file.FileType || file.fileType}} <br>
          </ng-container>
          <span *ngIf="deleteButtonStatus" class="text-muted">
              {{humanFileSize(file?.Size || file?.size)}}
            </span>
        </div>
      </div>
      <div class="inspection-mini-loader" *ngIf="deleteLoading && isDeleting(file.AssignedId || file.attachmentId)">
        <cat-dot-loader></cat-dot-loader>
      </div>
    </div>
  </div>
  <div
  *ngFor="let file of loadingCounter"
  class="image-container d-flex align-items-center border border-warning rounded-sm mr-2 overflow-hidden mb-2">
  <div
    *ngIf="deleteButtonStatus"
    class="bg-warning h-100 d-flex align-items-center px-2 cursor-pointer">
    <i class="icon icon-x-bold font-size-12px"></i>
  </div>
  <div class="flex-row w-100 d-flex justify-content-center align-items-center image-width" style="height: 78px;">
    <div class="inspection-mini-loader">
      <cat-dot-loader></cat-dot-loader>
    </div>
  </div>
</div>
  </div>
<!-- <cat-loader [show]="loading"></cat-loader> -->
