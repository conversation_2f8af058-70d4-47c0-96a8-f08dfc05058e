import { Component, Input, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { InspectionQuestion } from '../../model/inspection-response.model';

@Component({
  selector: 'cat-question-builder',
  templateUrl: './question-builder.component.html',
  styleUrls: ['./question-builder.component.scss']
})
export class QuestionBuilderComponent implements OnInit {

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Input()
  form: FormGroup;

  @Input()
  question: InspectionQuestion;

  @Input()
  id: string;

  constructor() { }

  ngOnInit(): void {

    this.question = { ...this.question };
    const commentRequired = (this.question.selected?.isCommentBoxRequired || (
        this.question.options.length === 0
        && this.question.isRequired
        && !this.question.attachmentEnabled
        && this.question.isCommentBoxEnabled)
    );
    if (this.question.answer) {
      this.question.selected = this.question.options.find(item => item.value === this.question.answer.answer);
    }

    this.form.addControl(this.question.questionId, new FormGroup({
      Answer: new FormControl(this.question.answer?.answer, this.question.isRequired && this.question.options?.length ? Validators.required : []),
      Comment: new FormControl(this.question.answer?.comment, commentRequired ? [Validators.required] : []),
      Attachments: new FormControl(this.question.attachments || [],
        this.question.minAttachmentCount > 0 ?
          [Validators.required, Validators.minLength(this.question.minAttachmentCount)] : []),
    }));

  }

}
