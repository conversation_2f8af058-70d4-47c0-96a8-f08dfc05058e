<div class="form-group mb-4" [formGroup]="form" *ngIf="question">
  <div class="d-flex mb-2"
  >
    <div class="pr-2" style="font-size: 22px; line-height: 24px">
      <label class="grey"
             [ngClass]="{
              red: question.selected?.color == 'red',
              green: question.selected?.color == 'green',
              orange: question.selected?.color == 'orange'
              }"
      >{{question.header}}</label>
    </div>
    <div>
      <label *ngIf="question.question" class="form-control-label question-label font-size-14px"
             [attr.for]="question.question">
        {{question.question}}
        <strong class="text-danger" *ngIf="question.isRequired">*</strong>
      </label>
    </div>
  </div>

  <div class="" [ngSwitch]="question.optionType">
    <!--    <cat-field-text *ngSwitchCase="'text'" [field]="question" [form]="form"></cat-field-text>-->
    <!--    <cat-field-textarea *ngSwitchCase="'textarea'" [field]="question" [form]="form"></cat-field-textarea>-->
    <!--    <cat-field-select *ngSwitchCase="'dropdown'" [field]="question" [form]="form"></cat-field-select>-->
    <ng-container *ngSwitchCase="'SingleSelect'">

      <cat-field-radio [field]="question" [form]="form"></cat-field-radio>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Answer']) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Answer'], {
        required: '_required_answer'
      }) | translate }}
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="'MultiSelect'">
      <cat-field-checkbox [field]="question" [form]="form"></cat-field-checkbox>
      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Answer']) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Answer']) | translate }}-->
      <!--      </div>-->
    </ng-container>

    <div class="mt-2" *ngIf="question.isCommentBoxEnabled || question.selected?.isCommentBoxRequired">
      <!--      <label class="col-form-label-sm">Yorumunuz:</label>-->
      <cat-field-textarea *ngIf="question.isCommentBoxMultiLine"
                          [field]="question" [form]="form"></cat-field-textarea>
      <cat-field-text *ngIf="!question.isCommentBoxMultiLine"
                      [field]="question" [form]="form"></cat-field-text>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Comment']) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Comment']) | translate }}
      </div>
    </div>

    <ng-container *ngIf="question.attachmentEnabled || question.selected?.isAttachmentEnabled">
      <cat-field-file [field]="question" [form]="form"></cat-field-file>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Attachments']) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Attachments'], {
        required : '_required_file',
        minlength: '_min_length_file'
      }) | translate }}
      </div>
    </ng-container>

    <!--    <cat-field-file *ngSwitchCase="'file'" [field]="question" [form]="form"></cat-field-file>-->
    <!--    <cat-field-checkbox *ngSwitchCase="'checkbox'" [field]="question" [form]="form"></cat-field-checkbox>-->
    <!--    <cat-field-sound-diagnostic *ngSwitchCase="'sound-diagnostic'" [field]="question"-->
    <!--                                [form]="form"></cat-field-sound-diagnostic>-->

    <!--    <div class="alert alert-danger my-1 p-2 fadeInDown animated" *ngIf="!isValid && isDirty">{{field.label}} is required</div>-->
    <!--    {{isShowError(form.controls[question.questionId])}}-->
    <!--    {{getFormErrorMessage(form.controls[question.questionId])}}-->
    <!--    {{getFormErrorMessage(form.controls[question.questionId]['controls']['Comment'])}}-->
    <!--    {{getFormErrorMessage(form.controls[question.questionId]['controls']['Answer'])}}-->


    <!--    <div-->
    <!--      [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]) }"-->
    <!--      class="invalid-feedback pl-3"-->
    <!--    >-->
    <!--      {{ getFormErrorMessage(form.controls[question.questionId]) | translate }}-->
    <!--    </div>-->

  </div>
</div>
