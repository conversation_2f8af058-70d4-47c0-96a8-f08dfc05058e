import { Component, OnInit } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { FileModel } from '../../../../export/file-upload/model/file.model';
import snq from 'snq';
import { Select } from '@ngxs/store';
import { InspectionState } from '../../state/inspection.state';
import { Observable } from 'rxjs';
import { InspectionAttachment, InspectionResponseModel } from '../../model/inspection-response.model';

@Component({
  selector: 'cat-field-file',
  templateUrl: './field-file.component.html',
  styleUrls: ['./field-file.component.scss']
})
export class FieldFileComponent extends FieldTextComponent implements OnInit {

  @Select(InspectionState.activeForm)
  activeForm$: Observable<InspectionResponseModel>;

  loadingCounter: any[] = [];
  onDeleteAttachmentFile(file: FileModel | InspectionAttachment | any) {
    this.form.get(this.field.questionId).patchValue({
      Attachments: snq(() => this.form.get(this.field.questionId).value.Attachments, [])
        .filter((f: FileModel | InspectionAttachment | any) => (f.id || f.attachmentId) !== (file.id || file.attachmentId)
      ),
    });
  }
  onUploadlaodingCounter(counter: any) {
    this.loadingCounter = counter;
  }
}
