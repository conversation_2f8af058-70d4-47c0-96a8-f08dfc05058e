<div [formGroup]="form">
  <!--    [attr.type]="field.type"-->
  <!--    [id]="field.name"-->
  <!--  [formType]="field.formType"-->
  <div [formGroupName]="field.questionId">
    <div class="mb-3 mt-3 d-flex justify-content-end">
      <label *ngIf="field.attachmentTitle || 1" class="mr-3 float-right">
        {{ field.attachmentTitle }}
      </label>
      <div class="d-flex">
        <cat-inspection-file-uploader
          [PortalUserInspectionFormId]="(activeForm$ |async)?.portalUserInspectionFormId || '8d18936f-962e-4ec5-ab37-f5d00c880bf3'"
          [question]="field"
          [formControlName]="'Attachments'"
          [formType]="6"
          (onloadingCounter)="onUploadlaodingCounter($event)"
        ></cat-inspection-file-uploader>
        <strong class="ml-1 text-danger" *ngIf="field.minAttachmentCount">*</strong>
      </div>
    </div>
    <div class="mb-3">
      <cat-inspection-file-upload-preview
        (deleteFile)="onDeleteAttachmentFile($event)"
        [deleteButtonStatus]="true"
        [files]="form.get(field.questionId).get('Attachments').value"
        [loadingCounter]="loadingCounter"
      ></cat-inspection-file-upload-preview>
    </div>
  </div>
</div>

