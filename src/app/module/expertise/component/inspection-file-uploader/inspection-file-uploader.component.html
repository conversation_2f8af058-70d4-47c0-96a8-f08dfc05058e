<div *ngIf="question?.guruCompatible">
  <span class="font-size-12px text-muted">
    {{  '_boom_guru_inspection_text' | translate }}
  </span>
</div>
<div class="position-relative">

  <div
    (click)="chooseFile()"
    class="btn btn-info btn-sm font-weight-semi-bold  py-1 px-4 rounded-lg"
  >
    {{ "_choose_file" | translate }}
  </div>
  <input
    class="file-upload-input"
    #fileInput
    (change)="fileChangeListener($event)"
    type="file"
    multiple
    [accept]="accept"
  />
</div>
<!-- <cat-loader [show]="loading || loadingCount > 0"></cat-loader> -->
