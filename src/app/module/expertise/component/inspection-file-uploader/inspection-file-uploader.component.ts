import { ChangeDetectorRef, Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import {
  FileUploadComponent
} from '../../../../export/file-upload/component/file-upload/file-upload.component';
import { FileUploadService } from '../../../../export/file-upload/service/file-upload.service';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { ModalService } from '../../../shared/service/modal.service';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { IncomingMessageService } from '../../../shared/service/incoming-message.service';
import { Store } from '@ngxs/store';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FileModel } from '../../../../export/file-upload/model/file.model';
import { InspectionQuestion } from '../../model/inspection-response.model';
import { InspectionFileService } from '../../service/inspection-file.service';

@Component({
  selector: 'cat-inspection-file-uploader',
  templateUrl: './inspection-file-uploader.component.html',
  styleUrls: ['./inspection-file-uploader.component.scss'],
  providers: [
    TranslatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InspectionFileUploaderComponent),
      multi: true,
    },
  ],
})
export class InspectionFileUploaderComponent extends FileUploadComponent {

  @Input()
  question: InspectionQuestion;

  @Input()
  PortalUserInspectionFormId: string;

  @Output()
  onloadingCounter: EventEmitter<any[]> = new EventEmitter<any[]>();

  constructor(
    protected readonly fileUploadService: FileUploadService,
    protected readonly translatePipe: TranslatePipe,
    protected readonly modalService: ModalService,
    protected readonly messageFrameService: MessageFrameService,
    protected readonly incomingMessageService: IncomingMessageService,
    protected readonly translateService: TranslateService,
    protected readonly cdr: ChangeDetectorRef,
    protected readonly store: Store,
    protected readonly inspectionFileService: InspectionFileService,
  ) {
    super(
      fileUploadService,
      translatePipe,
      modalService,
      messageFrameService,
      incomingMessageService,
      translateService,
      cdr,
      store
    );
    this.loadingCounter.subscribe((loadingCounter: any) => {
      this.onloadingCounter.emit(loadingCounter);
    });
  }

  protected getSettings() {
    this.fileUploadService.settings().subscribe((res) => {
      if (res.code === 0) {
        this.settings = res.data;
        this.settings.availableFileTypes = this.question.attachmentMimeTypes || this.settings.availableFileTypes;
        this.settings.maxAttachmentCount = this.question.maxAttachmentCount;
      }
    });
  }


  uploadService(file: FileModel) {

    return this.inspectionFileService.fileUpload({
      Name: file.Name,
      FileType: file.FileType,
      QuestionId: this.question.questionId,
      PortalUserInspectionFormId: this.PortalUserInspectionFormId,
      Content: file.ImageContent,
    });
  }
}
