<div style="display: flex; flex-direction: column; justify-content: space-between; height: calc(100vh - 30px);">
  <div class="px-4 pb-4">
  <div class="h4 py-4 mb-0 text-center nav-back">
    <i
      *ngIf="!introInfo || backButton"
      class="icon icon-back mr-2 float-left"
      (click)="backClick()"
    ></i>
    {{ !introInfo ? selectedForm?.formName : ("_expertise_form" | translate) }}
  </div>

  <div class="" *ngIf="introInfo">
    <!-- <ng-select
      class="mb-3"
      [placeholder]="this.selectedForm?.formName"
      [dropdownPosition]="'bottom'"
      [searchable]="false"
      (change)="onChangeForm($event)"
    >
      <ng-option *ngFor="let form of formList" [value]="form">{{
        form.formName
      }}</ng-option>
    </ng-select> -->

    <div
      class="mx-2 my-4 font-size-12px greetings"
      [innerHTML]="selectedForm?.greetings"
    ></div>
    <div class="d-flex justify-content-center flex-column w-100">
      <div class="my-2"
      >
        <div class="d-flex mb-1">
          <div class="flex-grow-1 general-button" *ngIf="isButtonActive('8c988a66-5e02-49f4-9736-53801dc3aae7')" (click)="start('8c988a66-5e02-49f4-9736-53801dc3aae7')" [ngClass]="{
            'mr-1': isButtonActive('cc7b8b9c-727e-48f2-954f-b8a363e379c5')
          }">
            <div class="mx-1 d-flex align-items-center">
              <img [src]="equipmentUnfilled" style="width: 35px; height: 35px; margin: 0 16px 0 10px;" />
              {{'_inspection_equipments' | translate }}
            </div>
          </div>
          <div class="flex-fill general-button" *ngIf="isButtonActive('cc7b8b9c-727e-48f2-954f-b8a363e379c5')" (click)="start('cc7b8b9c-727e-48f2-954f-b8a363e379c5')" [ngClass]="{
            'ml-1': isButtonActive('8c988a66-5e02-49f4-9736-53801dc3aae7')
          }">
          <div class="mx-1 d-flex align-items-center">
            <img [src]="equipmentUnfilled" style="width: 35px; height: 35px;margin: 0 16px 0 10px;" />
            {{'_inspection_equipments_short_form' | translate }}
          </div>
          </div>
        </div>
        <div style="background-color: #BB4724;" class="general-button my-2" *ngIf="isButtonActive('77594d82-6147-4be6-a440-a981f70b59a5')" (click)="start('77594d82-6147-4be6-a440-a981f70b59a5')">
          <i class="icon icon-generator"></i>
          {{'_inspection_generators' | translate }}
        </div>
        <div style="background-color: #4A96C0;" class="general-button my-2" *ngIf="isButtonActive('fe042b95-d47c-4e4c-a042-d41fcc3d1763')" (click)="start('fe042b95-d47c-4e4c-a042-d41fcc3d1763')">
          <i class="icon icon-marina"></i>
          {{'_inspection_marine' | translate }}
        </div>
      </div>
      <!-- <button
        catUserClick
        [section]="'EXPERTISE'"
        [subsection]="'EXPERTISE_START'"
        [data]="{ formId: this.selectedForm?.formId }"
        [disabled]="!selectedForm"
        class="min-width-200 btn mt-4 font-weight-semi-bold btn-warning btn-gradient text-white shadow rounded-lg mx-auto"
        (click)="start()"
      >
        {{ "_start" | translate }}
      </button> -->
    </div>
  </div>

  <form *ngIf="!introInfo" (submit)="onSubmitForm()" [formGroup]="form">
    <div class="form-group">
      <input
        catInputLength
        [name]="'Name'"
        [placeholder]="'_name' | translate"
        class="form-control form-control"
        formControlName="Name"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Surname'"
        [placeholder]="'_surname' | translate"
        class="form-control form-control"
        formControlName="Surname"
        type="text"
        minlength="2"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Email'"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="email"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Email) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Phone'"
        [placeholder]="'_phone_number' | translate"
        class="form-control form-control"
        formControlName="Phone"
        type="tel"
        (input)="onInputPhone($event)"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Phone) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Phone) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        catInputLength
        [name]="'CompanyName'"
        [placeholder]="'_company_name' | translate"
        class="form-control form-control"
        formControlName="CompanyName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>

    <div *ngIf="!equipmentSerialNumber" class="form-group">
      <!-- <ng-select
        #serialSelect
        *ngIf="equipmentList?.length > 0"
        class="service-drp"
        [placeholder]="'_machine_serial_number' | translate"
        [dropdownPosition]="'bottom'"
        formControlName="SerialNumber"
        [searchable]="true"
        [clearable]="false"
        (close)="serialSelect.blur()"
        (change)="equipmentSelect($event)"
      >
        <ng-option
          *ngFor="let equipmentType of equipmentList"
          [value]="equipmentType.serialNumber"
          >{{ equipmentType.model }} /
          {{ equipmentType.serialNumber | serialFormat }}</ng-option
        >
      </ng-select> -->
      <cat-machine-serial
        [form]="form"
        [fieldName]="'SerialNumber'"
        (selectEquipment)="equipmentSelect($event)"
      ></cat-machine-serial>
    </div>
    <!--      <div style="">-->
    <!--        <input-->
    <!--          class=""-->
    <!--          type="checkbox"-->
    <!--          id="not_in_list"-->
    <!--        />-->
    <!--        <label-->
    <!--          class="form-check-label text-secondary font-size-13px"-->
    <!--          for="not_in_list"-->
    <!--        >{{'_equipment_not_in_list' | translate}}</label>-->
    <!--      </div>-->

    <div class="form-group">
      <input
        catInputLength
        [name]="'SerialNumber'"
        [placeholder]="'_machine_serial_number' | translate"
        *ngIf="!!equipmentSerialNumber"
        class="form-control"
        formControlName="SerialNumber"
        type="text"
        [readOnly]="!!equipmentSerialNumber"
        minlength="3"
      />
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.SerialNumber)
        }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.SerialNumber) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        catInputLength
        [name]="'Make'"
        [placeholder]="'_make' | translate"
        class="form-control"
        formControlName="Make"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.Make)
        }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Make) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        catInputLength
        [name]="'Model'"
        [placeholder]="'_equipment_model' | translate"
        class="form-control"
        formControlName="Model"
        type="text"
        minlength="2"
      />
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.Model)
        }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Model) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        [placeholder]="'_working_hours' | translate"
        class="form-control"
        formControlName="WorkingHours"
        type="text"
        [mask]="'9*'"
        inputmode="numeric"
      />
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.WorkingHours)
        }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.WorkingHours) | translate }}
      </div>
    </div>

    <div class="form-group">
      <!--      <cat-form-label [label]="'_equipment_location' | translate"></cat-form-label>-->
      <ng-select
        catScrollTopWhenTyping
        [searchable]="true"
        (change)="onChangeCountry($event)"
        *ngIf="!(countryListLoading$ | async)"
        [placeholder]="'_country' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="false"
        formControlName="Country"
        #country
      >
        <ng-option
          *ngFor="let country of searchCity(countryList$ | async, country.searchTerm)"
          [value]="country.code"
          >{{ country.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Country) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Country) | translate }}
      </div>
    </div>
    <div
      *ngIf="
        (!!form.controls['Country'].value || (cityList$ | async)?.length > 0) &&
        isActiveCountry(form.controls['Country'].value)
      "
      class="form-group"
    >
      <ng-select
        catScrollTopWhenTyping
        class="city-dropdown"
        [searchable]="true"
        *ngIf="!(cityListLoading$ | async)"
        [placeholder]="'_city' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="false"
        formControlName="City"
        (open)="cityDropdownIsOpen = true"
        (close)="cityDropdownIsOpen = false"
        (keypress)="scrollTop()"
        #city
      >
        <ng-option
          id="city"
          *ngFor="let city of searchCity(cityList, city.searchTerm)"
          [value]="city.name"
          >{{ city.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.City) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.City) | translate }}
      </div>
    </div>
    <div class="form-group">
      <textarea
        catInputLength
        [name]="'Address'"
        [placeholder]="'_address' | translate"
        [rows]="2"
        class="form-control"
        formControlName="Address"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Address) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Address) | translate }}
      </div>
    </div>

    <cat-agreement-list
      *ngIf="!user"
      [form]="agreementForm"
      [formType]="agreementTypeEnum.InspectionForm"
    ></cat-agreement-list>

    <input
      [value]="'_continue' | translate"
      class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
      type="submit"
    />
    <div *ngIf="user && cityDropdownIsOpen" class="margin-bottom-90"></div>
  </form>
  <cat-loader
    [show]="
      (countryListLoading$ | async) ||
      (cityListLoading$ | async) ||
      (equipmentListLoading$ | async)
    "
  ></cat-loader>
  <!-- <cat-loader
    [show]="(countryListLoading$ | async) || (cityListLoading$ | async) || (equipmentListLoading$ | async)"
  ></cat-loader> -->
  </div>
  <div class="d-flex justify-content-center">
    <div catUserClick
    [section]="'OFFLINE'"
    [subsection]="'OFFLINE_MODE_INSPECTION'" class="openOffline" *ngIf="!loading && systemFeatureOfflineModeButton && user && offlineAndQrVersionController" (click)="openOffline()"
    [innerHTML]="'_inspection_open_offline_form' | translate | safeHtml"
    >
    </div>
  </div>
</div>
<cat-loader [show]="loading"></cat-loader>

<cat-basic-modal [(status)]="existentForm">
  <div class="p-2">
    {{ "_form_already_exists_continue" | translate }}
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button catUserClick [section]="'EXPERTISE'"
    [subsection]="'EXPERTISE_START_NEW_BUTTON_CLICK'"
      (click)="startNew()"
      class="modal-btn btn btn-secondary btn-gradient text-white shadow"
    >
      {{ "_start_new" | translate }}
    </button>

    <button catUserClick [section]="'EXPERTISE'"
    [subsection]="'EXPERTISE_CONTINUE_BUTTON_CLICK'"
      (click)="continueExistent()"
      class="modal-btn btn btn-warning btn-gradient text-white shadow"
    >
      {{ "_continue" | translate }}
    </button>
  </div>
</cat-basic-modal>
