@import "variable/bootstrap-variable";

.ng-select ::ng-deep .ng-select-container {
  border-radius: 6px;
  border: 1px solid #d7e5ea;
}

.success-message {
  font-size: 26px;
  font-weight: 700;
}

.nav-item a {
  color: $body-color;
}

.nav-item {
  max-width: 50%;
}

.nav-item .active {
  font-weight: bold;
}

// /* Safari 10.1 */
// @media not all and (min-resolution: 0.001dpcm) {
//   @supports (-webkit-appearance: none) and
//     (not (stroke-color: transparent)) {
//     .margin-bottom-only-ios {
//       margin-bottom: 40px;
//     }
//   }
// }
::ng-deep .ng-dropdown-panel .scroll-host.ng-dropdown-panel-items {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}
.margin-bottom-90 {
  margin-bottom: 90px;
}

.general-button {
  border-radius: 8px;
  border: 1px solid #E8E8E8;
  background: #FFA553;
  box-shadow: 0px 6px 4px 0px rgba(0, 0, 0, 0.03);
  height: 79px;
  color: #FFF;
  font-size: 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  word-break: break-word;
  flex-shrink: 0;
  .icon {
    font-size: 35px;
    margin: 0px 16px  0px 5px;
  }

}

.greetings {
  border-radius: 5px;
  border: 1px solid #E6E6E6;
  padding: 16px;
}


.openOffline {
  height: 50px;
  color: #3C3C3C;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 135%; /* 18.9px */
  width: 90%;
  margin: 0 auto;
}
