import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject, Subscription } from 'rxjs';
import { EquipmentModel } from '../../../customer/model/equipment.model';
import { DefinitionState } from '../../../definition/state/definition.state';
import { Country } from '../../../definition/model/country.model';
import { City } from '../../../definition/model/city.model';
import { CommonState } from '../../../shared/state/common/common.state';
import { CustomerRelationModel } from '../../../shared/model/company.model';
import { UserModel } from '../../../authentication/model/user.model';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { PermissionEnum } from '../../../definition/enum/permission.enum';
import { GetAllCountryListAction, GetCityListAction } from '../../../definition/action/definition.actions';
import { LoginState } from '../../../authentication/state/login/login.state';
import snq from 'snq';
import { FormType } from '../../../../export/file-upload/model/file.model';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { InspectionState } from '../../state/inspection.state';
import { InspectionFormModel } from '../../model/inspection-form.model';
import { GetInspectionFormListAction, SetActiveInspectionFormAction } from '../../state/inspection.actions';
import { CustomValidator } from '../../../../util/custom-validator';
import { InspectionService } from '../../service/inspection.service';
import { validateAllFormFields } from '../../../../util/validate-all-form-fields.util';
import { SerialFormatPipe } from '../../../shared/pipe/serial-format.pipe';
import { LogService } from 'src/app/module/customer/service/log.service';
import { environment } from '../../../../../environments/environment';
import { map, takeUntil } from 'rxjs/operators';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { GetEquipmentDetailAction } from 'src/app/module/customer/action/equipment.action';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { appVersionController } from 'src/app/util/app-version-controller.util';

@Component({
  selector: 'cat-expertise-intro',
  templateUrl: './expertise-intro.component.html',
  styleUrls: ['./expertise-intro.component.scss'],
  providers: [SerialFormatPipe]
})
export class ExpertiseIntroComponent implements OnInit, OnDestroy {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  systemFeatureOfflineModeButton: boolean

  equipmentUnfilled = `${environment.assets}/equipment-unfilled.svg`;


  user: UserModel;
  FormType = FormType;
  cityList: City[];
  equipmentSerialNumber: string;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [Validators.required, CustomValidator.mailFormat]),
    Phone: new FormControl(null, [Validators.required]),
    CompanyName: new FormControl(null, [Validators.required]),

    Country: new FormControl(null, [Validators.required]),
    City: new FormControl(null /*, [Validators.required]*/),
    Address: new FormControl(null),
    // "EquipmentFamily":"FAMILY_ALL",
    SerialNumber: new FormControl(null, [Validators.required]),
    Make: new FormControl(null, [Validators.required]),
    Model: new FormControl(null, [Validators.required]),
    WorkingHours: new FormControl(null, [Validators.required, Validators.max(10000000)]),
    CompanyId: new FormControl(),
  });
  agreementForm = new FormGroup({});

  formSendStatus: boolean;
  subscription: Subscription = new Subscription();
  introInfo: any = 1;
  backButton: any = 0;
  agreementTypeEnum = AgreementTypeEnum;

  selectedForm: Partial<InspectionFormModel>;

  @Select(InspectionState.formList)
  formList$: Observable<InspectionFormModel[]>;
  formList: InspectionFormModel[];
  loading = false;
  existentForm: any;
  equipmentList: EquipmentModel[];
  equipmentNumber: string;
  cityDropdownIsOpen = false;
  customerNumber: string;
  protected subscriptions$: Subject<boolean> = new Subject();
  id: any;
  offlineAndQrVersionController: boolean;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly inspectionService: InspectionService,
    private readonly messageFrameService: MessageFrameService,
    private readonly serialFormatPipe: SerialFormatPipe,
    private readonly loggerService: LogService,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: true});
    this.cityList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.cityList = data.slice();
      });
    this.store.dispatch(new GetInspectionFormListAction(this?.equipment?.productHierarchy));

    this.loadForm();

    this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);

    this.route.queryParams.subscribe(q => {
      // this.introInfo = 'introInfo' in q ? parseInt(q.introInfo) : 1;
      // if (!this.selectedForm && 'formId' in q) {
      //   this.selectedForm = {
      //     formId: q.formId
      //   };
      //
      //   this.formList$.subscribe(forms => {
      //     if (forms) {
      //       this.selectedForm = forms.find(i => i.formId === q.formId);
      //     }
      //   });
      // }

      if ('countryCode' in q) {
        let { countryCode } = q;
        if (countryCode && countryCode.length !== 2) {
          countryCode = null;
        }
        if (countryCode) {
          this.countryList$.subscribe(() => {
            this.form.patchValue({ Country: countryCode?.toUpperCase() });
            this.onChangeCountry(q.countryCode?.toUpperCase());
          });
        }
      }

      this.backButton = 'backButton' in q ? parseInt(q.backButton, 10) : 0;
    });

    this.formList$.subscribe(item => {
      if (item) {
        this.formList = item;
        if (!this.selectedForm) {
          this.selectedForm = item?.[0];
        }
      }
    });

    // this.form.get('SerialNumber').valueChanges.subscribe(value => {
    //   this.equipmentSelect(value);
    // });

    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.systemFeatureOfflineModeButton = systemFeature('offline_mode', features, true);
      }
    });
    this.offlineAndQrVersionController = appVersionController(this.store.selectSnapshot(CommonState.version), 'offlineAndQr');
  }

  loadForm() {
    this.store.dispatch(new GetAllCountryListAction());
    this.user = this.store.selectSnapshot(LoginState.user);
    const company = this.store.selectSnapshot(LoginState.company);
    const customer = this.store.selectSnapshot(LoginState.customer);
    // const currentCustomer = this.store.selectSnapshot(
    //   CommonState.currentCustomer
    // );

    const {
      ...queryParams
    } = this.route.snapshot.queryParams;

    const patchValues: any = {
      ...queryParams,
    };

    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
      patchValues.Email = this.user.email.toLowerCase();
      patchValues.Phone = this.user.mobile;

      this.form.patchValue(patchValues);
    }

    if (company) {
      patchValues.CompanyId = company.id;
      // patchValues.CompanyName = company.name;
    }
    if (customer) {
      // console.log('customer in mda', customer);
      patchValues.CompanyName = customer.name;
    }
    const { id } = this.route.snapshot.params;
    this.id = id;
    if (id) {
      this.equipmentDetail$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(
          (equipmentDetail: EquipmentModel) => {
            if (equipmentDetail) {
              this.equipmentSerialNumber = this.serialFormatPipe.transform(equipmentDetail.serialNumber);
              this.equipmentNumber = equipmentDetail.equipmentNumber;

              this.form.patchValue({
                SerialNumber: this.equipmentSerialNumber,
                Make: equipmentDetail.brand,
                Model: equipmentDetail.model,
                WorkingHours: equipmentDetail?.workingHours,
              });
              console.log(equipmentDetail);
            }
          }
        );
      this.store.dispatch(new GetEquipmentDetailAction(snq(() => id)));
    }
    this.form.patchValue(patchValues);
  }

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  onChangeCountry(countryCode: string) {
    console.log('on change', countryCode);
    const selectedCountry = this.getCountryByCode(countryCode);
    this.form.patchValue({ EquipmentCity: null });

    this.form.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.form.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.City.clearValidators();
      this.form.controls.City.updateValueAndValidity();
      return of([]);
    }
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  onSubmitForm() {
    if (this.form.valid) {
      this.loggerService
        .action('EXPERTISE', 'EXPERTISE_CONTINUE', {
          // companyId,
          // customerId: this.customer.id,
          equipmentNumber: this.form.controls?.SerialNumber?.value,
          formId: this.selectedForm?.formId
        })
        .subscribe();

      this.loading = true;
      this.inspectionService.getStatus({
        formId: this.selectedForm.formId,
        equipmentSerialNumber: this.form.value.SerialNumber,
        equipmentModel: this.form.value.Model
      }).subscribe(res => {
        this.loading = false;

        if (!res.portalUserInspectionFormId) {
          return this.save();
        }
        this.existentForm = res.portalUserInspectionFormId;
      }, () => {
        this.loading = false;
      });

    } else {
      validateAllFormFields(this.form);
      console.log(this.form.value);
      console.log(this.form.errors);
    }
  }

  sendForm() {
    const { value } = this.form;
  }


  navigateToBack() {
    this.back();
  }

  back() {
    window.history.back();
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }


  onChangeForm(event: any) {
    console.log(event);
    this.selectedForm = event;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  private save(update = {}) {
    const params: any = {
      InspectionFormId: this.selectedForm.formId,
      ...this.form.value,
      ...update
    };

    // agreements
    let headers = {};
    if (!this.user) {
      const entries = Object.entries(this.agreementForm.get('agreements')?.value)
        .filter(([key, value]) => value)
        .map((a) => a[0]);
      headers = entries.length
        ? { ApprovedAgreementNames: entries.join(';') } : {};
    }


    this.loading = true;
    this.inspectionService.save(params, headers)
      .subscribe(res => {
        this.loading = false;
        this.store.dispatch(new SetActiveInspectionFormAction(res));
        this.routeToSteps(res.formId, res.portalUserInspectionFormId);
      }, () => {
        this.loading = false;
      });
  }

  private routeToSteps(formId, portalUserInspectionFormId) {
    // if (this.equipmentNumber){
    //   this.router
    //   .navigate([
    //     ...environment.rootUrl.split('/'),
    //     'expertise',
    //     formId, portalUserInspectionFormId, this.equipmentNumber, 'step', 1 ], {
    //     // relativeTo: this.route,
    //     replaceUrl: true
    //   });
    // }
    // else {
    this.router
      .navigate([
        ...environment.rootUrl.split('/'),
        'expertise',
        formId, portalUserInspectionFormId, 'step', 1], {
        // relativeTo: this.route,
        queryParams: {
          equipmentNumber: this.equipmentNumber,
        },
        replaceUrl: true
      });
    // }
  }

  startNew() {
    this.loading = true;
    this.inspectionService.delete(this.existentForm)
      .subscribe(() => {
        this.save();
      }, () => {
        this.loading = true;
      });
  }

  continueExistent() {
    this.save({
      portalUserInspectionFormId: this.existentForm
    });
    // this.routeToSteps(this.selectedForm.formId, this.existentForm);
  }

  equipmentSelect(detail) {
    if (detail) {
      this.equipmentNumber = detail.equipmentNumber;
      this.form.patchValue({
        EquipmentSerialNumber: detail.serialNumber,
        Make: detail.brand,
        Model: detail.model,
        WorkingHours: detail?.workingHours,
      });
    }
  }

  backClick() {
    if (this.introInfo === 1) {
      this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: false});
      return this.navigateToBack();
    }
    this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: true});

    this.introInfo = 1;
  }

  protected autoSelectCountry() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        if (!currentCustomer) {
          return;
        }
        this.countryList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe((countries) => {
            if (!countries?.length) {
              return;
            }
            let countryCode = currentCustomer?.groupKey;
            this.equipmentDetail$
              .pipe(takeUntil(this.subscriptions$))
              .subscribe((equipment) => {
                if (!equipment) {
                  if (this.id) {
                    return;
                  }
                }
                let found;
                if (equipment?.location?.locationName) {
                  found = countries.find(item => item.name === equipment?.location?.locationName);
                  countryCode = found?.code || countryCode;
                }

                // this.form.patchValue({ CountryCode: countryCode });
                this.form.patchValue({ Country: countryCode });
                this.onChangeCountry(countryCode)
                  .pipe(takeUntil(this.subscriptions$))
                  .pipe(map(() => this.store.selectSnapshot(DefinitionState.cityList)))
                  .subscribe((cities) => {
                    const city = found ? equipment?.city : currentCustomer?.cityName;
                    if (city) {
                      const foundCity = cities?.find(item => item.name.toLowerCase() === city.toLowerCase())
                      if (foundCity) {
                        this.form.patchValue({ City: foundCity.name });
                      }
                    }
                  });
              });
          });
      });
  }


  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: false});
  }

  openOffline() {
    this.messageFrameService.sendMessage(FrameMessageEnum.openOfflineMenu, {
      selectedApp: 'Inspection',
      equipment: this.store.selectSnapshot(EquipmentState.equipmentDetail),
    });
  }

  start(id) {
    this.form.reset();
    this.loadForm();

    if(id) {
      this.selectedForm = this.formList.find(item => {
        return item.formId === id
      })
    }
    if(this.selectedForm){
      this.loggerService.log('EXPERTISE', 'EXPERTISE_START', {
        formId: this.selectedForm?.formId
      }).subscribe();
      this.introInfo = 0;
      this.autoSelectCountry();
    }
  }

  isButtonActive(id) {
    return this.formList?.some(item => {
      return item.formId === id
    })
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter(d => d.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }
}
