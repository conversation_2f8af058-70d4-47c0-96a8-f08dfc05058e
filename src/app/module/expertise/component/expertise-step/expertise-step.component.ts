import { AfterViewInit, Component, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { InspectionGroup, InspectionResponseModel } from '../../model/inspection-response.model';
import { InspectionState } from '../../state/inspection.state';
import { InspectionDetailAction, SetActiveInspectionFormAction } from '../../state/inspection.actions';
import { InspectionService } from '../../service/inspection.service';
import { InspectionAnswerModel } from '../../model/inspection-answer.model';
import { validateAllFormFieldsRecursive } from '../../../../util/validate-all-form-fields-recursive.util';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonState } from '../../../shared/state/common/common.state';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { disableBack } from '../../../../util/disable-back.util';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-expertise-step',
  templateUrl: './expertise-step.component.html',
  styleUrls: ['./expertise-step.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class ExpertiseStepComponent implements OnInit, AfterViewInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  disableBack = disableBack;

  form: FormGroup = new FormGroup({});
  step: number;
  disableSubmitButton: boolean;
  @Select(InspectionState.activeForm)
  activeForm$: Observable<InspectionResponseModel>;

  @Select(InspectionState.formLoading)
  formLoading$: Observable<boolean>;

  activeForm: InspectionResponseModel;
  activeGroup: InspectionGroup;
  loading: boolean;
  successfullyDone: number;
  equipmentNumber: string;
  private disabledBack: any;
  hasPermissionToSoundDiagnostic: boolean;
  PermissionEnum = PermissionEnum;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store,
    private inspectionService: InspectionService,
    private messageFrameService: MessageFrameService,
    private permissionService: HasPermissionsService
  ) { }

  ngOnInit(): void {
    this.hasPermissionToSoundDiagnostic = this.permissionService.checkPermission(PermissionEnum.SoundDiagnosticsCheck);
    this.route.queryParams.subscribe(params => {
      const { successfullyDone } = params;
      // tslint:disable-next-line: radix
      this.successfullyDone = parseInt(successfullyDone);
    });

    this.route.params.subscribe(params => {
      window.scroll({ top: 0 });

      this.form = new FormGroup({});
      const { id, formId, userFormId } = params;

      // tslint:disable-next-line: radix
      this.step = parseInt(id);

      this.activeForm = this.store.selectSnapshot(InspectionState.activeForm);
      if (!this.activeForm) {
        this.store.dispatch(new InspectionDetailAction({
          formId, portalUserInspectionFormId: userFormId
        }));
      } else {
        this.activeGroup = this.activeForm.groups.find(i => i.groupOrder === this.step);
      }
    });

    if (this.route.snapshot.queryParamMap.get('equipmentNumber')) {
      this.equipmentNumber = this.route.snapshot.queryParamMap.get('equipmentNumber');
    }

    this.activeForm$.subscribe(form => {
      if (form) {
        this.activeForm = form;
        this.activeGroup = form.groups.find(i => i.groupOrder === this.step);
      }
    });


  }

  ngAfterViewInit() {

  }

  navigateToBack() {
    this.back();
  }

  back() {
    window.history.back();
  }

  onSubmitForm() {
    console.log('submit', this.form.value);

    const body: InspectionAnswerModel = {
      InspectionFormId: this.activeForm.formId,
      PortalUserInspectionFormId: this.activeForm.portalUserInspectionFormId || '8d18936f-962e-4ec5-ab37-f5d00c880bf3',
      Answers: []
    };

    Object.entries(this.form.value).map((item: [string, any]) => {
      const [key, value] = item;
      body.Answers.push({
        InspectionFormQuestionId: key,
        Answer: value.Answer,
        AnswerDescription: value.Answer,
        Comment: value.Comment
      });
    });

    console.log(body);

    if (this.form.valid) {
      this.loading = true;

      this.inspectionService.answer(body)
        .subscribe((data: any) => {
          this.disableSubmitButton = true;
          this.loading = false;
          console.log('this.activeForm.groups.length', this.activeForm.groups.length);
          console.log('this.id', this.step);
          let sound = data?.groups?.find(i => i.groupType === 'Sound');
          if(!this.hasPermissionToSoundDiagnostic && sound) {
            data.groups.splice(sound.groupOrder - 1, 1);
          }
          this.store.dispatch(new SetActiveInspectionFormAction(data));
          // if (data.data === 'ok') {
          if (this.activeForm.groups.length > this.step) {
            this.router
              .navigate(['..', this.step + 1], { relativeTo: this.route });
          } else {
            if (this.activeForm?.groups?.length === this.step && !this.activeGroup.isSoundDiagRequired) {
              this.completeWithoutSound();
            }
            // this.router
            //   .navigate([], {
            //     relativeTo: this.route,
            //     queryParams: { successfullyDone: 1 },
            //     queryParamsHandling: 'merge',
            //   });
          }
        }, error => {
          this.loading = false;
          this.disableSubmitButton = false;
        }, () => {
          this.disableSubmitButton = false;
        });

    } else {
      validateAllFormFieldsRecursive(this.form);
      console.log('not valid');
    }

  }

  close() {
    if (this.store.selectSnapshot(CommonState.currentCustomer)) {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }

    this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
  }


  completeWithoutSound() {
    this.loading = true;

    this.inspectionService
      .complete({
        InspectionFormId: this.activeForm.formId,
        PortalUserInspectionFormId: this.activeForm.portalUserInspectionFormId,
      })
      .subscribe(res => {
        this.loading = false;
        // this.timeOutId && clearTimeout(this.timeOutId);

        this.successfullyDone = 1;

        if (this.disabledBack) {
          this.disabledBack?.remove();
        }
        this.disabledBack = this.disableBack(() => {
          this.close();
        });

      }, error => {
        this.loading = false;
      });

  }
}
