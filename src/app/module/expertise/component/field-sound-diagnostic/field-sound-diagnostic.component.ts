import { Component, Input, OnInit } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { LogService } from '../../../customer/service/log.service';
import { v4 as uuid } from 'uuid';
import { Select, Store } from '@ngxs/store';
import { takeUntil } from 'rxjs/operators';
import { CommonState } from '../../../shared/state/common/common.state';
import { Observable, Subject } from 'rxjs';
import { PermissionEnum } from '../../../definition/enum/permission.enum';
import { ModalService } from '../../../shared/service/modal.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'cat-field-sound-diagnostic',
  templateUrl: './field-sound-diagnostic.component.html',
  styleUrls: ['./field-sound-diagnostic.component.scss']
})
export class FieldSoundDiagnosticComponent extends FieldTextComponent implements OnInit{

  @Input()
  intro: boolean;

  @Input()
  button: string;

  @Input()
  data: any = {};

  status: any;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;
  errorModal: any;
  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly frameService: MessageFrameService,
    private readonly loggerService: LogService,
    protected readonly modalService: ModalService,
    protected readonly translateService: TranslateService,
    protected readonly messageFrameService: MessageFrameService,
    readonly store: Store,
  ) {
    super(store);
    this.listenPermissionError();
  }

  listenPermissionError() {
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.permissionRequest();

          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.goToSettings(data.action)
          });
        });
  }

  permissionRequest() {
    this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);
  }

  goToSettings(action) {
    this.messageFrameService.sendMessage(action);
  }

  onClick() {
    const transactionId = uuid();

    this.loggerService
      .log('INSPECTION', 'SOUND_DIAGNOSTIC_BUTTON', {
        // companyId,
        // customerId: this.customer.id,
        equipmentNumber: this.data.equipmentNumber,
        source: 'Inspection',
        transactionId,
        serialNumber: this.data.serialNumber,
      })
      .subscribe();

    this.frameService.sendMessage(FrameMessageEnum.voice_record, {
      SerialNumber: this.data.serialNumber,
      Source: 'Inspection',
      TransactionId: transactionId,
      EquipmentNumber: this.data?.equipmentNumber,
      CustomerNumber: this.data?.customerNumber,
    });
  }


}
