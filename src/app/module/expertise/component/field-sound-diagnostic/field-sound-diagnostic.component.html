<div class="text-center">
  <div class="text-left mb-3" *ngIf="intro">
    {{'_sound_diagnostic_intro' | translate}}
  </div>

  <div
    (click)="onClick()"
    class="cursor-pointer d-inline-flex sound-btn px-2 py-1"
  >
    <div class="sound-button-text">
      <p><i class="text-white icon icon-sound cursor-pointer"></i></p>
    </div>
    <div class="ml-2 sound-button-text text-wrap text-left">
      <p>
        {{ (button ? button : "_sound_diagnostic") | translate }}
      </p>
    </div>
  </div>


  <div class="mt-4 font-size-12px" *ngIf="intro">
    {{'_sound_diag_pass_warning' | translate}}
  </div>

</div>
