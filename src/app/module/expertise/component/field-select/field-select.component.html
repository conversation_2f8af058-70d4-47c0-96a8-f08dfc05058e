<!--<div [formGroup]="form">-->
<!--  <ng-select-->
<!--    [id]="field.name"-->
<!--    [placeholder]="field.placeholder"-->
<!--    [formControlName]="field.name"-->

<!--    [searchable]="false"-->
<!--    (change)="onChange($event)"-->
<!--    [clearable]="false"-->
<!--    formControlName="CountryCode"-->
<!--  >-->
<!--    <ng-option *ngFor="let opt of field.options" [value]="opt.key">{{opt.label}}</ng-option>-->
<!--  </ng-select>-->

<!--&lt;!&ndash;  <div class="mb-3">&ndash;&gt;-->
<!--&lt;!&ndash;    <cat-file-upload-preview&ndash;&gt;-->
<!--&lt;!&ndash;      [deleteButtonStatus]="true"&ndash;&gt;-->
<!--&lt;!&ndash;      [files]="form.get(field.name).value"&ndash;&gt;-->
<!--&lt;!&ndash;      (deleteFile)="onDeleteAttachmentFile($event)"&ndash;&gt;-->
<!--&lt;!&ndash;    ></cat-file-upload-preview>&ndash;&gt;-->
<!--&lt;!&ndash;  </div>&ndash;&gt;-->
<!--</div>-->
