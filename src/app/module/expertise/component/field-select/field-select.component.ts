import { Component } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { FileModel } from '../../../../export/file-upload/model/file.model';

@Component({
  selector: 'cat-field-select',
  templateUrl: './field-select.component.html',
  styleUrls: ['./field-select.component.scss']
})
export class FieldSelectComponent extends FieldTextComponent {

  onChange($event: any) {
    console.log('testdt',this.form.get(this.field.questionId).value);
  }

  onDeleteAttachmentFile($event: FileModel) {

  }
}
