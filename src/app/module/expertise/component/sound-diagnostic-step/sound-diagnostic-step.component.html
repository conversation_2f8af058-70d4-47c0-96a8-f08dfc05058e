<head>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
</head>

<div *ngIf="equipmentDetail$ | async as equipmentDetail" class="equipment">
  <div class="px-4 pb-4">
    <div class="h4 py-4 mb-0 text-center nav-back">
      <i
        class="icon icon-back mr-2 float-left"
        (click)="backClick()"
      ></i>
      <div class="title">
        {{ "_sound_diagnostic" | translate }}
      </div>
    </div>
    <div class="choose-text">
      {{ "_choose_action" | translate }}
    </div>
    <div class="body">
      <div class="choose-area">
        <div class="option-field" [hasPermission]="PermissionEnum.SoundDiagnosticsCheck" (click)="soundDiagnostic(equipmentDetail)">
          <img class="sound-image" [src]="soundDiagnosticImg"/>
            <div class="make-sound-diagnostic">
              {{ "_make_sound_diagnostic" | translate }}
            </div>
        </div>
        <div class="line"></div>
        <div class="option-field" [hasPermission]="PermissionEnum.SoundDiagnosticsFeedback" (click)="startRecordUpload()">
          <img class="file-image" [src]="startRecordImg"/>
          <div class="sound-diagnostic-send-file">
            {{ "_sound_diagnostic_send_file" | translate }}
          </div>
        </div>
        <div class="bottom-text">
          {{ "_sound_diagnostic_information_text" | translate }}
        </div>
      </div>
    </div>
  </div>
</div>
