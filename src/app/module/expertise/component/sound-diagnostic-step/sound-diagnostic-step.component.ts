import { Component, OnInit } from '@angular/core';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { InspectionFormModel } from '../../model/inspection-form.model';
import { Select, Store } from '@ngxs/store';
import { InspectionState } from '../../state/inspection.state';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { Router } from '@angular/router';
import { EquipmentModel } from 'src/app/module/customer/model/equipment.model';
import { v4 as uuid } from 'uuid';
import { LogService } from 'src/app/module/customer/service/log.service';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { CustomerModel } from 'src/app/module/customer/model/customer.model';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { SystemFeatureAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { takeUntil } from 'rxjs/operators';
import { GetEquipmentDiagnosticDataPagingAction } from '../../../customer/action/equipment.action'
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
@Component({
  selector: 'cat-sound-diagnostic-step',
  templateUrl: './sound-diagnostic-step.component.html',
  styleUrls: ['./sound-diagnostic-step.component.scss']
})
export class SoundDiagnosticStepComponent implements OnInit {
  @Select(InspectionState.formList)
  formList$: Observable<InspectionFormModel[]>;

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentDetailLoading)
  equipmentDetailLoading$: Observable<boolean>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  introInfo: any = 1;
  selectedForm: Partial<InspectionFormModel>;
  user: UserModel;
  featureSoundDiagnostics: boolean = true;
  HasPSECampaign: boolean;
  pseRequestId: string;
  warrantiesStatus = false;

  private soundSending: boolean;
  private equipmentNumber: string;
  private serialNumber: string;
  private customer: CustomerModel;

  PermissionEnum = PermissionEnum;

  formList: InspectionFormModel[];
  loading = false;
  startRecordImg = environment.assets + '/sound_file_icon.png';
  soundDiagnosticImg = environment.assets + '/sound_diagnostic_icon.svg';
  constructor(
    private readonly messageFrameService: MessageFrameService,
    private readonly router: Router,
    private readonly logger: LogService,
    private readonly frameService: MessageFrameService,
    private readonly store: Store,
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  ngOnInit() {
    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.featureSoundDiagnostics = systemFeature('sound_diagnostics', features, true);
      }
    });

    this.equipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((equipment) => {
        if (equipment) {
          this.HasPSECampaign = equipment?.hasPseCampaign;
          this.pseRequestId = equipment.pseRequestId;
          this.serialNumber = equipment?.serialNumber;
          this.store.dispatch(new GetEquipmentDiagnosticDataPagingAction(equipment.equipmentNumber, equipment.serialNumber,  13, 1));

          if (equipment?.warranties?.length) {
            const nowDate = new Date();
            this.warrantiesStatus = !!(equipment.warranties.filter(x => (new Date(x?.endDate)) > nowDate)?.length);
          }
        }
      });
  }

  onChangeForm(event: any) {
    console.log(event);
    this.selectedForm = event;
  }

  back() {
    window.history.back();
  }

  navigateToBack() {
    this.back();
  }

  backClick() {
    if (this.introInfo === 1) {
      this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: false});
      return this.navigateToBack();
    }
    this.messageFrameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: true});
    this.introInfo = 1;
  }

  soundDiagnostic(equipmentDetail: EquipmentModel) {
    if (this.soundSending) {
      return;
    }
    this.soundSending = true;
    setTimeout(() => this.soundSending = false, 1000);

    const transactionId = uuid();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'SOUND_DIAGNOSTIC_STEP_PAGE_ENTER_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.frameService.sendMessage(FrameMessageEnum.voice_record, {
      EquipmentNumber: equipmentDetail?.equipmentNumber,
      SerialNumber: equipmentDetail.serialNumber,
      CustomerNumber: this.customer?.customerNumber,
      Source: 'Equipment',
      TransactionId: transactionId,
    });

    history.back();
  }

  startRecordUpload() {

    const transactionId = uuid();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'START_RECORD_UPLOAD_STEP_PAGE_ENTER_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'expertise',
        'diagnostic-record',
      ], {
        queryParams: {
          backButton: 1
        }
      })
      .then();
  }
}
