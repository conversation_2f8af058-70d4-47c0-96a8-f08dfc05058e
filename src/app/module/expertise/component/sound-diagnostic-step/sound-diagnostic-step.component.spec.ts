/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { SoundDiagnosticStepComponent } from './sound-diagnostic-step.component';

describe('SoundDiagnosticStepComponent', () => {
  let component: SoundDiagnosticStepComponent;
  let fixture: ComponentFixture<SoundDiagnosticStepComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SoundDiagnosticStepComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SoundDiagnosticStepComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
