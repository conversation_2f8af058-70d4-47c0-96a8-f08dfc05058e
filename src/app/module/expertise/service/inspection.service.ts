import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { InspectionFormModel } from '../model/inspection-form.model';
import { InspectionStatusModel } from '../model/inspection-status.model';
import { CompleteModel, InspectionResponseModel, SoundModel } from '../model/inspection-response.model';
import { InspectionAnswerModel } from '../model/inspection-answer.model';

@Injectable({
  providedIn: 'root'
})
export class InspectionService {

  constructor(
    private readonly http: HttpClient
  ) { }


  public getList(productHierarchy) {
    return this.http.post<HttpResponse<InspectionFormModel[]>>(`${environment.api}/inspectionForm/getList`, { productHierarchy: productHierarchy ?? 'FAMILY-ALL'}).pipe(
      map(val => {
        // const data = {
        //   'data': [{
        //     'formId': '8c988a66-5e02-49f4-9736-53801dc3aae7',
        //     'formName': 'PSSR Ekspertiz Formu',
        //     'formDescription': 'PSSR Ekspertiz Formu',
        //     'greetings': '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean placerat, ex vel aliquet tristique, tellus purus consectetur turpis, at condimentum nisl neque id nisi.</p> <p>Aenean euismod risus eget accumsan dictum. Cras nec fermentum magna. Suspendisse non gravida ipsum. Phasellus sit amet lacus ullamcorper, tincidunt dolor egestas, pellentesque felis. Suspendisse ut suscipit lorem, a rutrum quam. Quisque blandit tortor et varius porttitor. Vivamus vitae mauris pharetra, ultrices justo eu, pulvinar arcu. Ut tincidunt lacus sit amet justo bibendum, quis feugiat ex pretium</p>',
        //     'isWorkingHourRequired': true,
        //     'targetEquipmentCategories': ['FAMILY-ALL'],
        //     'formSetup': null,
        //     'equipmentFamily': null
        //   },
        //     {
        //       'formId': '8c988a66-5e02-49f4-9736',
        //       'formName': 'Ekspertiz Formu',
        //       'formDescription': 'PSSR Ekspertiz Formu',
        //       'greetings': 'Hosgeldiniz metni',
        //       'isWorkingHourRequired': true,
        //       'targetEquipmentCategories': ['FAMILY-ALL'],
        //       'formSetup': null,
        //       'equipmentFamily': null
        //     },
        //
        //   ],
        //   'paging': null, 'code': 0, 'message': null
        // };
        if (val.code === 0) {
          // return data.data;
          return val.data;
        }
        return null;
      }),
    );
  }

  public getStatus(params: { formId, equipmentSerialNumber, equipmentModel }) {
    return this.http.post<HttpResponse<InspectionStatusModel>>(`${environment.api}/inspectionForm/getStatus`, params).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public getDetail(params: { formId: string, portalUserInspectionFormId: string }) {
    return this.http.post<HttpResponse<InspectionResponseModel>>(`${environment.api}/inspectionForm/detail`, params)
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public save(body, headers = {}) {
    return this.http.post<HttpResponse<InspectionResponseModel>>(`${environment.api}/inspectionform/save`,
      body,
      {
        headers
      }
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public answer(body: InspectionAnswerModel) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/inspectionform/answer`,
      body
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public delete(portalUserInspectionFormId) {
    return this.http.delete<HttpResponse<any>>(`${environment.api}/inspectionform/delete`, {
      params: {
        portalUserInspectionFormId
      }
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }



  public saveSound(body: SoundModel) {
    return this.http.post<HttpResponse<InspectionResponseModel>>(`${environment.api}/inspectionform/saveSound`,
      body
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  public complete(body: CompleteModel) {
    return this.http.post<HttpResponse<InspectionResponseModel>>(`${environment.api}/inspectionform/complete`,
      body
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public sendExpertiseResultsMail(formId: string , portalUserInspectionFormId: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/inspectionForm/sendmail`, { formId, portalUserInspectionFormId })
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  public postSoundDiagnosticRecord(
    fileContent,
    equipmentNumber,
    equipmentSerialNumber,
    deviceId,
    customerNumber,
    soundDiagFaultCodeKeys,
    recordCollectingAttentionTypes,
    rpm,
    soundHealthCode
) {
    return this.http.post<HttpResponse<InspectionResponseModel>>(`${environment.api}/SoundDiagRecordCollecting/sendRecord`,
     {
        fileContent,
        equipmentNumber,
        equipmentSerialNumber,
        deviceId,
        customerNumber,
        soundDiagFaultCodeKeys,
        recordCollectingAttentionTypes,
        rpm,
        soundHealthCode
      },
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

}
