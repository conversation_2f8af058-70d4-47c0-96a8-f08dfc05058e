import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { environment } from '../../../../environments/environment';
import { InspectionFileModel } from '../model/inspection-form.model';
import { FileModelResponse } from '../../../export/file-upload/model/file.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class InspectionFileService {

  constructor(
    private readonly http: HttpClient
  ) { }


  fileUpload(file: InspectionFileModel): Observable<HttpResponse<FileModelResponse>> {
    return this.http.post<HttpResponse<FileModelResponse>>(
      `${environment.api}/inspectionform/attachments/add`,
      file
    );
  }

  delete(attachmentId: string): Observable<any> {
    return this.http.delete<HttpResponse<boolean>>(`${environment.api}/inspectionform/attachments/delete`,
      {
        params: { attachmentId },
      }
    );
  }

  // settings(): Observable<HttpResponse<FileSettingsModel>> {
  //   return this.http.get<HttpResponse<FileSettingsModel>>(
  //     `${environment.api}/settings/file`
  //   );
  // }

}
