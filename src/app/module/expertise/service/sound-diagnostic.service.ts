import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { InspectionResponseModel } from '../model/inspection-response.model';
import { environment } from '../../../../environments/environment';
import { SoundDiagnosticModel, FaultCodesModel } from '../model/sound-diagnostic.model';

@Injectable({
  providedIn: 'root'
})
export class SoundDiagnosticService {

  constructor(
    private readonly http: HttpClient
  ) { }

  faultCodes() {
    return this.http.get<HttpResponse<FaultCodesModel>>(`${environment.api}/equipment/sounddiag/faultcodes`);
  }

  soundResult(soundId) {
    return this.http.get<HttpResponse<SoundDiagnosticModel>>(`${environment.api}/equipment/soundResult`, {
      params: { soundId }
    });
  }
}
