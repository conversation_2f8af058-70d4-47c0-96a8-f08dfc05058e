import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExpertiseLayoutComponent } from './component/expertise-layout/expertise-layout.component';
import { ExpertiseIntroComponent } from './component/expertise-intro/expertise-intro.component';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { ExpertiseStepComponent } from './component/expertise-step/expertise-step.component';
import { DiagnosticRecordUploadComponent } from './component/diagnostic-record-upload/diagnostic-record-upload.component';
import { MessageFrameService } from '../shared/service/message-frame.service';
import { FrameMessageEnum } from '../shared/enum/frame-message.enum';
import { SoundDiagnosticStepComponent } from './component/sound-diagnostic-step/sound-diagnostic-step.component';

const routes: Routes = [
  {
    path: '',
    component: ExpertiseLayoutComponent,
    children: [
      { path: 'init', component: ExpertiseIntroComponent },
      { path: 'init/:id', component: ExpertiseIntroComponent },
      { path: ':formId/:userFormId/step/:id', component: ExpertiseStepComponent },
      { path: 'diagnostic-record', component: DiagnosticRecordUploadComponent },
      { path: 'sound-diagnostic-step', component: SoundDiagnosticStepComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ExpertiseRoutingModule {
  constructor(
    private readonly frameService: MessageFrameService,
  ) {
    this.frameService.sendMessage(FrameMessageEnum.headerStatus, {closeModal: true});
    stopLoadingAnimation();
  }
}
