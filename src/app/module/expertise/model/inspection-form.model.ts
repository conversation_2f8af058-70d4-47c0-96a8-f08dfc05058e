export interface InspectionFormModel {
  formId: string;
  formName: string;
  formDescription: string;
  greetings: string;
  isWorkingHourRequired: boolean;
  targetEquipmentCategories: string[];
  formSetup: any;
  equipmentFamily: any;
}

export interface InspectionFileModel {
  // AssignedId: string;
  Name: string;
  FileType: string;
  QuestionId: string;
  PortalUserInspectionFormId: string;
  Content: string;
}
