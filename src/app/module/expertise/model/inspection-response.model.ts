export interface InspectionResponseModel {
  address: string;
  appSessionId: string;
  city: string;
  comments: string;
  coordinates: string;
  country: string;
  customerId: string;
  email: string;
  equipmentFamily: string;
  formDescription: string;
  formId: string;
  formName: string;
  formSetup: string;
  greetings: string;
  // groups: InspectionGroupModel[];
  groups: any[];
  inspectionNumber: string;
  inspector: string;
  isWorkingHourRequired: boolean;
  make: string;
  model: string;
  name: string;
  phone: string;
  portalUserInspectionFormId: string;
  serialNumber: string;
  status: string;
  surname: string;
}

export interface InspectionGroup {
  groupId: string;
  groupOrder: string;
  groupTitle: string;
  questions: InspectionQuestion[];
  groupType: string;
  isSoundDiagRequired: boolean;
  soundResult: any;
}

export interface InspectionQuestion {
  attachmentEnabled: boolean;
  attachmentMimeTypes: string[];
  attachmentTitle: string;
  attachments: InspectionAttachment[];
  commentBoxMaxLength: number;
  header: string;
  isCommentBoxEnabled: boolean;
  isCommentBoxMultiLine: boolean;
  commentTitle: string;
  isRequired: boolean;
  minAttachmentCount: number;
  maxAttachmentCount: number;
  guruCompatible: boolean;
  optionType: string;
  options: InspectionQuestionOption[];
  order: number;
  question: string;
  questionId: string;
  tag: string;
  answer?: InspectionAnswer;
  selected?: InspectionQuestionOption;
}

export interface InspectionQuestionOption {
  optionId: string;
  title: string;
  value: string;
  color: string;
  leadScore: number;
  order: number;
  isCommentBoxRequired: boolean;
  isAttachmentEnabled: boolean;
}

export interface InspectionAnswer {
  answerId: string;
  answer: string;
  answerDescription: string;
  comment: string;
}

export interface InspectionAttachment {
  appSessionId: string;
  attachmentId: string;
  fileType: number;
  name: string;
  path: string;
  portalUserId: string;
  portalUserInspectionFormId: string;
  questionId: string;
  size: number;
}


export interface SoundModel {
  InspectionFormId: string;
  PortalUserInspectionFormId: string;
  SoundId: string;
}
export interface CompleteModel {
  InspectionFormId: string;
  PortalUserInspectionFormId: string;
}
