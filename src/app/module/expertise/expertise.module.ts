import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExpertiseIntroComponent } from './component/expertise-intro/expertise-intro.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { SharedModule } from '../shared/shared.module';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';
import { FileUploadModule } from '../../export/file-upload/file-upload.module';
import { ExpertiseLayoutComponent } from './component/expertise-layout/expertise-layout.component';
import { ExpertiseRoutingModule } from './expertise-routing.module';
import { ExpertiseStepComponent } from './component/expertise-step/expertise-step.component';
import { FieldSelectComponent } from './component/field-select/field-select.component';
import { FieldTextComponent } from './component/field-text/field-text.component';
import { FieldBuilderComponent } from './component/field-builder/field-builder.component';
import { FieldTextareaComponent } from './component/field-textarea/field-textarea.component';
import { FieldRadioComponent } from './component/field-radio/field-radio.component';
import { FieldFileComponent } from './component/field-file/field-file.component';
import { FieldCheckboxComponent } from './component/field-checkbox/field-checkbox.component';
import { FieldSoundDiagnosticComponent } from './component/field-sound-diagnostic/field-sound-diagnostic.component';
import { QuestionBuilderComponent } from './component/question-builder/question-builder.component';
import { InspectionFileUploaderComponent } from './component/inspection-file-uploader/inspection-file-uploader.component';
import { InspectionFileUploadPreviewComponent } from './component/inspection-file-upload-preview/inspection-file-upload-preview.component';
import { InspectionSoundDiagnosticComponent } from './component/inspection-sound-diagnostic/inspection-sound-diagnostic.component';
import { NgxLoadingModule } from 'ngx-loading';
import { NgxMaskModule } from 'ngx-mask';
import { MachineSerial } from '../form/component/machine-serial.module';
import { DiagnosticRecordUploadComponent } from './component/diagnostic-record-upload/diagnostic-record-upload.component';
import { EnumToArrayModule } from 'src/app/export/enum-to-array/enum-to-array.module';
import { SoundDiagnosticStepComponent } from './component/sound-diagnostic-step/sound-diagnostic-step.component';
import { HasPermissionsModule } from 'src/app/export/permissions/has-permissions.module';


@NgModule({
  declarations: [
    ExpertiseIntroComponent,
    ExpertiseLayoutComponent,
    ExpertiseStepComponent,
    FieldSelectComponent,
    FieldTextComponent,
    FieldBuilderComponent,
    FieldTextareaComponent,
    FieldRadioComponent,
    FieldFileComponent,
    FieldCheckboxComponent,
    FieldSoundDiagnosticComponent,
    QuestionBuilderComponent,
    InspectionFileUploaderComponent,
    InspectionFileUploadPreviewComponent,
    InspectionSoundDiagnosticComponent,
    DiagnosticRecordUploadComponent,
    SoundDiagnosticStepComponent
  ],
  exports: [
    QuestionBuilderComponent,
    FieldRadioComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    NgSelectModule,
    SharedModule,
    UserEventLogModule,
    FileUploadModule,
    ExpertiseRoutingModule,
    FormsModule,
    NgxLoadingModule,
    NgxMaskModule.forRoot(),
    MachineSerial,
    EnumToArrayModule,
    HasPermissionsModule
  ]
})
export class ExpertiseModule {}
