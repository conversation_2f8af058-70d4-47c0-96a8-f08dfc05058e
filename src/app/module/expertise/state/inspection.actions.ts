import { InspectionResponseModel } from '../model/inspection-response.model';

export class GetInspectionFormListAction {
  public static readonly type = '[Inspection] Get form List';

  constructor(public productHierarchy: string) {
  }
}


export class InspectionSaveAction {
  public static readonly type = '[Inspection] save';

  constructor(public body) {
  }
}

export class InspectionDetailAction {
  public static readonly type = '[Inspection] detail';

  constructor(public params: { formId: string, portalUserInspectionFormId: string }) {
  }
}


export class SetActiveInspectionFormAction {
  public static readonly type = '[Inspection] set active form';

  constructor(public form: InspectionResponseModel) {
  }
}
