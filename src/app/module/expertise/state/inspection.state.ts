import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { InspectionFormModel } from '../model/inspection-form.model';
import {
  GetInspectionFormListAction,
  InspectionDetailAction,
  InspectionSaveAction,
  SetActiveInspectionFormAction
} from './inspection.actions';
import { InspectionService } from '../service/inspection.service';
import { InspectionResponseModel } from '../model/inspection-response.model';

export interface InspectionStateModel {
  formList: InspectionFormModel[];
  formLoading: boolean;
  activeForm: InspectionResponseModel;
}

@State<InspectionStateModel>({
  name: 'inspection',
  defaults: {
    formList: [],
    formLoading: false,
    activeForm: null,
  }
})
@Injectable()
export class InspectionState {

  constructor(
    private readonly inspectionService: InspectionService
  ) {}

  @Selector()
  public static getState(state: InspectionStateModel) {
    return state;
  }

  @Selector()
  public static formList({ formList }: InspectionStateModel): InspectionFormModel[] {
    return formList;
  }

  @Selector()
  public static formLoading({ formLoading }: InspectionStateModel): boolean {
    return formLoading;
  }

  @Selector()
  public static activeForm({ activeForm }: InspectionStateModel): InspectionResponseModel {
    return activeForm;
  }


  @Action(GetInspectionFormListAction)
  getInspectionFormListAction(
    { patchState }: StateContext<InspectionStateModel>,
    inspectionFormListAction: GetInspectionFormListAction,
  ) {
    patchState({ formLoading: true });
    this.inspectionService.getList(inspectionFormListAction.productHierarchy)
      .subscribe(list => {
        patchState({
          formList: list,
          formLoading: false
        });
      }, err => {
        patchState({ formLoading: true });
      });
  }

  @Action(InspectionSaveAction)
  inspectionSaveAction(
    { patchState }: StateContext<InspectionStateModel>,
    { body }: InspectionSaveAction,
  ) {
    patchState({ formLoading: true });
    this.inspectionService.save(body)
      .subscribe(form => {
        patchState({
          activeForm: form,
          formLoading: false
        });
      }, err => {
        patchState({ formLoading: true });
      });
  }

  @Action(SetActiveInspectionFormAction)
  setActiveInspectionFormAction(
    { patchState }: StateContext<InspectionStateModel>,
    { form }: SetActiveInspectionFormAction,
  ) {
    patchState({ activeForm: form });
  }

  @Action(InspectionDetailAction)
  InspectionDetailAction(
    { patchState }: StateContext<InspectionStateModel>,
    { params }: InspectionDetailAction,
  ) {
    patchState({ formLoading: true });
    this.inspectionService.getDetail(params)
      .subscribe(form => {
        patchState({
          activeForm: form,
          formLoading: false
        });
      }, err => {
        patchState({ formLoading: true });
      });
  }

}
