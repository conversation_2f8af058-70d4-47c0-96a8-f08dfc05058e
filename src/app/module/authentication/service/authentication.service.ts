import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import {
  LoginResponse,
  RefreshTokenResponseModel,
} from '../response/login.response';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  constructor(private readonly http: HttpClient) {}

  login(ott: string): Observable<LoginResponse> {
    return this.http
      .post<HttpResponse<LoginResponse>>(`${environment.api}/user/xott`, { ott }, {
        headers: { TOKEN_FREE: 'true' },
      })
      .pipe(
        map((val) => {
          console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  refreshToken(
    token: string,
    refreshToken: string
  ): Observable<RefreshTokenResponseModel> {
    return this.http
      .post<HttpResponse<RefreshTokenResponseModel>>(
        `${environment.api}/user/refreshtoken`,
        { token, refreshToken }, {
          headers: { TOKEN_FREE: 'true' },
        }
      )
      .pipe(
        map((val) => {
          console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
