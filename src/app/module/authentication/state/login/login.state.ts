import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import snq from 'snq';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { CompanyModel } from '../../../company/model/company.model';
import { CustomerModel } from '../../../customer/model/customer.model';
import {
  ChangeTokenAction,
  LoginWithOttAction,
  RefreshTokenAction,
  ResetLoginStateAction,
  SetLoginResponseAction,
  UpdateLanguageCodeAction,
  UpdateLoadingAction,
  UpdateLoginStateAction,
} from '../../action/login/login.actions';
import { UserModel } from '../../model/user.model';
import { LoginResponse } from '../../response/login.response';
import { AuthenticationService } from '../../service/authentication.service';
import { CommonStoreAction } from '../../../shared/state/common/common.actions';
import { BorusanBlockedActionsAction } from '../../../shared/state/settings/settings.actions';
import { LoginType } from '../../enum/login-type.enum';

export interface LoginStateModel {
  loginResponse: LoginResponse;
  customer: CustomerModel;
  company: CompanyModel;
  user: UserModel;
  token: string;
  refreshToken: string;
  headerCompanies: string;
  language: string;
  loginLoading: boolean;
  isLdapLogin: boolean;
  loginType: LoginType;
}

@State<LoginStateModel>({
  name: 'login',
  defaults: {
    loginResponse: null,
    customer: null,
    company: null,
    user: null,
    token: null,
    refreshToken: null,
    headerCompanies: null,
    language: null,
    loginLoading: false,
    isLdapLogin: null,
    loginType: null
  },
})
@Injectable()
export class LoginState {
  constructor(
    private readonly authenticationService: AuthenticationService,
    private readonly frameMessage: MessageFrameService,
    private readonly store: Store,
  ) {
  }

  @Selector()
  public static getState(state: LoginStateModel) {
    return state;
  }

  @Selector()
  public static loginResponse({ loginResponse }: LoginStateModel): LoginResponse {
    return loginResponse;
  }

  @Selector()
  public static customer({ customer }: LoginStateModel): CustomerModel {
    return customer;
  }

  @Selector()
  public static customerNumber({ customer }: LoginStateModel): string {
    return customer.customerNumber;
  }

  @Selector()
  public static company({ company }: LoginStateModel): CompanyModel {
    return company;
  }

  @Selector()
  public static headerCompanies({ headerCompanies }: LoginStateModel): string {
    return headerCompanies;
  }

  @Selector()
  public static user({ user }: LoginStateModel): UserModel {
    return user;
  }

  @Selector()
  public static token({ token }: LoginStateModel): string {
    return token;
  }

  @Selector()
  public static refreshToken({ refreshToken }: LoginStateModel): string {
    return refreshToken;
  }

  @Selector()
  public static language({ language }: LoginStateModel): string {
    return language;
  }

  @Selector()
  public static loginLoading({ loginLoading }: LoginStateModel): boolean {
    return loginLoading;
  }

  @Selector()
  public static isBorusanUser({
    isLdapLogin,
  }: LoginStateModel): boolean {
    return isLdapLogin;
  }

  @Selector()
  public static isSAMLLogin({
    loginType,
  }: LoginStateModel): boolean {
    return loginType === LoginType.Saml;
  }

  @Action(LoginWithOttAction)
  loginWithOttAction({ patchState, getState }: StateContext<LoginStateModel>, { ott }: LoginWithOttAction) {
    // if (getState().user) {
    //   return of('');
    // }
    patchState({
      loginLoading: true,
    });
    return this.authenticationService.login(ott).pipe(
      tap((value) => {
        patchState({
          loginResponse: value,
          loginLoading: false,
          customer: snq(() => value.customer),
          // company: snq(() => value.company),
          company: snq(() => value.companies?.[0]),
          user: snq(() => value.user),
          token: snq(() => value.token),
          refreshToken: snq(() => value.refreshToken),
          headerCompanies: snq(() => value.headerCompanies),
          language: snq(() => value.language),
          isLdapLogin: value.user.isLdapLogin,
        });
        if (getState().isLdapLogin) {
          this.store.dispatch(new BorusanBlockedActionsAction());
        }
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(UpdateLanguageCodeAction)
  updateLanguageCodeAction({ patchState }: StateContext<LoginStateModel>, { languageCode }: UpdateLanguageCodeAction) {
    patchState({
      language: languageCode,
    });
  }

  @Action(ResetLoginStateAction)
  resetLoginStateAction({ patchState }: StateContext<LoginStateModel>) {
    patchState({
      token: null,
      refreshToken: null,
    });
  }


  @Action(RefreshTokenAction)
  refreshToken({ patchState, getState }: StateContext<LoginStateModel>) {
    patchState({
      loginLoading: true,
    });
    const { token, refreshToken, loginResponse } = getState();
    return this.authenticationService.refreshToken(token, refreshToken).pipe(
      tap((value) => {
        patchState({
          loginResponse: { ...loginResponse, ...value },
          loginLoading: false,
          user: value?.user,
          token: value?.token,
          refreshToken: value?.refreshToken,
          language: value?.language,
        });
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        this.frameMessage.sendMessage(FrameMessageEnum.unauthorised);
        return throwError(err);
      })
    );
  }

  @Action(ChangeTokenAction)
  changeToken(
    { patchState, getState }: StateContext<LoginStateModel>,
    { value }: ChangeTokenAction
  ) {
    const { loginResponse } = getState();
    patchState({
      loginResponse: { ...loginResponse, ...value },
      loginLoading: false,
      user: value?.user,
      token: value?.token,
      refreshToken: value?.refreshToken,
      language: value?.language,
    });
  }


  @Action(SetLoginResponseAction)
  setLoginResponseAction(
    { getState, patchState }: StateContext<LoginStateModel>,
    { loginResponse }: SetLoginResponseAction
  ): any {
    // App Session ID null check & set
    if (loginResponse?.appSessionId) {
      this.store.dispatch(new CommonStoreAction({
        deviceToken: loginResponse.appSessionId
      }));
    }
    patchState({
      loginResponse,
      loginLoading: false,
      customer: loginResponse?.customer,
      company: loginResponse?.company || loginResponse.companies?.[0],
      user: loginResponse?.user,
      token: loginResponse?.token,
      refreshToken: loginResponse?.refreshToken,
      headerCompanies: loginResponse?.headerCompanies,
      language: loginResponse?.language,
    });
  }


  @Action(UpdateLoadingAction)
  updateLoading(
    { getState, patchState }: StateContext<LoginStateModel>,
    { loading }: UpdateLoadingAction
  ): void {
    patchState({
      loginLoading: loading,
    });
  }


  @Action(UpdateLoginStateAction)
  updateLoginState(
    { patchState }: StateContext<LoginStateModel>,
    { payload }: UpdateLoginStateAction
  ): void {
    patchState(payload);
  }
}
