import { CompanyModel } from '../../company/model/company.model';
import { CustomerModel } from '../../customer/model/customer.model';
import { UserModel } from '../model/user.model';



export interface RefreshTokenResponseModel {
  token: string;
  tokenExpiresIn: number;
  tokenExpiresAt: Date;
  refreshToken: string;
  refreshTokenExpiresIn: number;
  refreshTokenExpiresAt: Date;
  language: string;
  user: UserModel;
  appSessionId?: string;
}
export interface RelatedPersonModel{
  relatedPersonCrmId: string;
}
export interface LoginResponse extends RefreshTokenResponseModel {
  headerCompanies: string;
  customer: CustomerModel;
  company: CompanyModel;
  companies: CompanyModel[];
  relatedPerson: RelatedPersonModel;
}
