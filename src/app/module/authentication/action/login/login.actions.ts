import { LoginResponse, RefreshTokenResponseModel } from '../../response/login.response';
import { LoginStateModel } from '../../state/login/login.state';

export class LoginWithOttAction {
  public static readonly type = '[Login] With Ott';

  constructor(public ott: string) {
  }
}

export class UpdateLanguageCodeAction {
  public static readonly type = '[Login] Update Language Code';

  constructor(public languageCode: string) {
  }
}

export class ResetLoginStateAction {
  public static readonly type = '[Login] Reset State';

  constructor() {
  }
}

export class ChangeTokenAction {
  public static readonly type = '[Login] change token';

  constructor(public value: RefreshTokenResponseModel) {
  }
}

export class RefreshTokenAction {
  public static readonly type = '[Login] refresh token';

  constructor() {
  }
}

export class SetLoginResponseAction {
  public static readonly type = '[Login] set login response';

  constructor(public loginResponse: LoginResponse) {
  }
}

export class UpdateLoadingAction {
  public static readonly type = '[Login] loading';

  constructor(public loading: boolean) {
  }
}

export class UpdateLoginStateAction {
  public static readonly type = '[Login] update login';

  constructor(public payload: Partial<LoginStateModel>) {
  }
}

