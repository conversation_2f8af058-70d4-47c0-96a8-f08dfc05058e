import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { InstantSurveyLayoutComponent } from './container/instant-survey-layout/instant-survey-layout.component';
import { InstantSurveyInitComponent } from './container/instant-survey-init/instant-survey-init.component';
import { InstantSurveyQuestionComponent } from './container/instant-survey-question/instant-survey-question.component';
import { InstantSurveyResultComponent } from './container/instant-survey-result/instant-survey-result.component';
import { InstantSurveyAdminComponent } from './container/instant-survey-admin/instant-survey-admin.component';

const routes: Routes = [
  {
    path: '',
    component: InstantSurveyLayoutComponent,
    children: [
      { path: '', component: InstantSurveyInitComponent },
      { path: 'question/:id', component: InstantSurveyQuestionComponent },
      { path: 'result/:id', component: InstantSurveyResultComponent },
      { path: 'admin', component: InstantSurveyAdminComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  declarations: [],
})
export class InstantSurveyRoutingModule {
  constructor() {
    stopLoadingAnimation();
  }
}
