import { InstantSurveyAnswerRequest, InstantSurveyModel, InstantSurveyAdminRequest } from '../model/instant-survey.model';

export class GetInstantSurveysAction {
  public static readonly type = '[InstantSurvey] Get Surveys';
  constructor() {}
}

export class SetActiveInstantSurveyAction {
  public static readonly type = '[InstantSurvey] Set Active Survey';
  constructor(public survey: InstantSurveyModel) {}
}

export class SubmitInstantSurveyAnswerAction {
  public static readonly type = '[InstantSurvey] Submit Answer';
  constructor(public answerRequest: InstantSurveyAnswerRequest) {}
}

export class ClearInstantSurveysAction {
  public static readonly type = '[InstantSurvey] Clear Surveys';
  constructor() {}
}

export class GetUserStatsAction {
  public static readonly type = '[InstantSurvey] Get User Stats';
  constructor() {}
}

// Admin actions
export class GetAdminSurveysAction {
  public static readonly type = '[InstantSurvey] Get Admin Surveys';
  constructor() {}
}

export class GetAdminSurveyAction {
  public static readonly type = '[InstantSurvey] Get Admin Survey';
  constructor(public surveyId: string) {}
}

export class CreateAdminSurveyAction {
  public static readonly type = '[InstantSurvey] Create Admin Survey';
  constructor(public survey: InstantSurveyAdminRequest) {}
}

export class UpdateAdminSurveyAction {
  public static readonly type = '[InstantSurvey] Update Admin Survey';
  constructor(public surveyId: string, public survey: InstantSurveyAdminRequest) {}
}

export class DeleteAdminSurveyAction {
  public static readonly type = '[InstantSurvey] Delete Admin Survey';
  constructor(public surveyId: string) {}
}

export class GetUserTargetingOptionsAction {
  public static readonly type = '[InstantSurvey] Get User Targeting Options';
  constructor() {}
}

export class AssignSurveyToUsersAction {
  public static readonly type = '[InstantSurvey] Assign Survey To Users';
  constructor(public surveyId: string, public userIds: string[]) {}
}
