export interface InstantSurveyRequest {
  Position: string;
  GetDetails: boolean;
}

export interface InstantSurveyResponse {
  surveys: InstantSurveyModel[];
  totalCount: number;
}

export interface InstantSurveyModel {
  surveyId: string;
  questionId: string;
  question: string;
  questionType: string;
  pointValue: number;
  isActive: boolean;
  targetAudience: string[];
  deeplink?: string;
  options: InstantSurveyOption[];
  correctAnswerId?: string;
  userStats?: InstantSurveyUserStats;
  timingConfig?: InstantSurveyTimingConfig;
  conditionalLogic?: InstantSurveyConditionalLogic;
}

export interface InstantSurveyOption {
  optionId: string;
  title: string;
  value: string;
  order: number;
}

export interface InstantSurveyUserStats {
  hasAnsweredBefore: boolean;
  previousAnswerCorrect?: boolean;
  totalPointsEarned: number;
  totalQuestionsAnswered: number;
}

export interface InstantSurveyTimingConfig {
  delayDays: number;
  showToCorrectAnswerers: boolean;
  showToIncorrectAnswerers: boolean;
  showToNewUsers: boolean;
}

export interface InstantSurveyConditionalLogic {
  assignToAnsweredUsers: boolean;
  assignToNewUsers: boolean;
  requireCorrectPreviousAnswer: boolean;
  requireIncorrectPreviousAnswer: boolean;
}

export interface InstantSurveyAnswerRequest {
  surveyId: string;
  questionId: string;
  selectedOptionId: string;
  answerTime: string;
}

export interface InstantSurveyAnswerResponse {
  isCorrect: boolean;
  pointsEarned: number;
  totalPoints: number;
  message: string;
  nextQuestionId?: string;
  nextQuestionDelay?: number;
}

export interface InstantSurveyAdminModel {
  surveyId: string;
  questionId: string;
  question: string;
  questionType: string;
  pointValue: number;
  isActive: boolean;
  targetAudience: string[];
  deeplink: string;
  options: InstantSurveyOption[];
  correctAnswerId: string;
  timingConfig: InstantSurveyTimingConfig;
  conditionalLogic: InstantSurveyConditionalLogic;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
}

export interface InstantSurveyAdminRequest {
  surveyId?: string;
  questionId?: string;
  question: string;
  questionType: string;
  pointValue: number;
  isActive: boolean;
  targetAudience: string[];
  deeplink?: string;
  options: InstantSurveyOption[];
  correctAnswerId: string;
  timingConfig: InstantSurveyTimingConfig;
  conditionalLogic: InstantSurveyConditionalLogic;
}

export interface InstantSurveyUserTargeting {
  userSegments: string[];
  companies: string[];
  countries: string[];
  specificUsers: string[];
}

export enum InstantSurveyQuestionType {
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  TRUE_FALSE = 'TRUE_FALSE',
  SINGLE_SELECT = 'SINGLE_SELECT'
}

export enum InstantSurveyPosition {
  INSTANT_SURVEY_MAIN = 'INSTANT_SURVEY_MAIN'
}
