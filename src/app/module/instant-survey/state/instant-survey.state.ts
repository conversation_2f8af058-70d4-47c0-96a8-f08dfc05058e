import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  GetInstantSurveysAction,
  SetActiveInstantSurveyAction,
  SubmitInstantSurveyAnswerAction,
  ClearInstantSurveysAction,
  GetUserStatsAction,
  GetAdminSurveysAction,
  GetAdminSurveyAction,
  CreateAdminSurveyAction,
  UpdateAdminSurveyAction,
  DeleteAdminSurveyAction,
  GetUserTargetingOptionsAction,
  AssignSurveyToUsersAction
} from '../action/instant-survey.action';
import { InstantSurveyModel, InstantSurveyAdminModel, InstantSurveyAnswerResponse } from '../model/instant-survey.model';
import { InstantSurveyService } from '../service/instant-survey.service';

export interface InstantSurveyStateModel {
  surveys: InstantSurveyModel[];
  activeSurvey: InstantSurveyModel | null;
  loading: boolean;
  totalCount: number;
  userStats: any;
  lastAnswerResponse: InstantSurveyAnswerResponse | null;
  
  // Admin state
  adminSurveys: InstantSurveyAdminModel[];
  activeAdminSurvey: InstantSurveyAdminModel | null;
  adminLoading: boolean;
  userTargetingOptions: any;
}

@State<InstantSurveyStateModel>({
  name: 'instantSurvey',
  defaults: {
    surveys: [],
    activeSurvey: null,
    loading: false,
    totalCount: 0,
    userStats: null,
    lastAnswerResponse: null,
    adminSurveys: [],
    activeAdminSurvey: null,
    adminLoading: false,
    userTargetingOptions: null
  }
})
@Injectable()
export class InstantSurveyState {

  constructor(private instantSurveyService: InstantSurveyService) {}

  @Selector()
  static getSurveys(state: InstantSurveyStateModel): InstantSurveyModel[] {
    return state.surveys;
  }

  @Selector()
  static getActiveSurvey(state: InstantSurveyStateModel): InstantSurveyModel | null {
    return state.activeSurvey;
  }

  @Selector()
  static getLoading(state: InstantSurveyStateModel): boolean {
    return state.loading;
  }

  @Selector()
  static getTotalCount(state: InstantSurveyStateModel): number {
    return state.totalCount;
  }

  @Selector()
  static getUserStats(state: InstantSurveyStateModel): any {
    return state.userStats;
  }

  @Selector()
  static getLastAnswerResponse(state: InstantSurveyStateModel): InstantSurveyAnswerResponse | null {
    return state.lastAnswerResponse;
  }

  @Selector()
  static getAdminSurveys(state: InstantSurveyStateModel): InstantSurveyAdminModel[] {
    return state.adminSurveys;
  }

  @Selector()
  static getActiveAdminSurvey(state: InstantSurveyStateModel): InstantSurveyAdminModel | null {
    return state.activeAdminSurvey;
  }

  @Selector()
  static getAdminLoading(state: InstantSurveyStateModel): boolean {
    return state.adminLoading;
  }

  @Selector()
  static getUserTargetingOptions(state: InstantSurveyStateModel): any {
    return state.userTargetingOptions;
  }

  @Action(GetInstantSurveysAction)
  getInstantSurveys({ patchState }: StateContext<InstantSurveyStateModel>) {
    patchState({ loading: true });
    return this.instantSurveyService.getInstantSurveys().pipe(
      tap((response) => {
        patchState({
          loading: false,
          surveys: response.surveys,
          totalCount: response.totalCount
        });
      }),
      catchError((err) => {
        patchState({ loading: false });
        return throwError(err);
      }),
    );
  }

  @Action(SetActiveInstantSurveyAction)
  setActiveInstantSurvey(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { survey }: SetActiveInstantSurveyAction
  ) {
    patchState({ activeSurvey: survey });
  }

  @Action(SubmitInstantSurveyAnswerAction)
  submitInstantSurveyAnswer(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { answerRequest }: SubmitInstantSurveyAnswerAction
  ) {
    patchState({ loading: true });
    return this.instantSurveyService.submitAnswer(answerRequest).pipe(
      tap((response) => {
        patchState({
          loading: false,
          lastAnswerResponse: response
        });
      }),
      catchError((err) => {
        patchState({ loading: false });
        return throwError(err);
      }),
    );
  }

  @Action(ClearInstantSurveysAction)
  clearInstantSurveys({ patchState }: StateContext<InstantSurveyStateModel>) {
    patchState({
      surveys: [],
      activeSurvey: null,
      totalCount: 0,
      lastAnswerResponse: null
    });
  }

  @Action(GetUserStatsAction)
  getUserStats({ patchState }: StateContext<InstantSurveyStateModel>) {
    return this.instantSurveyService.getUserStats().pipe(
      tap((userStats) => {
        patchState({ userStats });
      }),
      catchError((err) => {
        return throwError(err);
      }),
    );
  }

  @Action(GetAdminSurveysAction)
  getAdminSurveys({ patchState }: StateContext<InstantSurveyStateModel>) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.getAdminSurveys().pipe(
      tap((adminSurveys) => {
        patchState({
          adminLoading: false,
          adminSurveys
        });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(GetAdminSurveyAction)
  getAdminSurvey(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { surveyId }: GetAdminSurveyAction
  ) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.getAdminSurvey(surveyId).pipe(
      tap((activeAdminSurvey) => {
        patchState({
          adminLoading: false,
          activeAdminSurvey
        });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(CreateAdminSurveyAction)
  createAdminSurvey(
    { patchState, getState }: StateContext<InstantSurveyStateModel>,
    { survey }: CreateAdminSurveyAction
  ) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.createSurvey(survey).pipe(
      tap((newSurvey) => {
        const state = getState();
        patchState({
          adminLoading: false,
          adminSurveys: [...state.adminSurveys, newSurvey],
          activeAdminSurvey: newSurvey
        });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(UpdateAdminSurveyAction)
  updateAdminSurvey(
    { patchState, getState }: StateContext<InstantSurveyStateModel>,
    { surveyId, survey }: UpdateAdminSurveyAction
  ) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.updateSurvey(surveyId, survey).pipe(
      tap((updatedSurvey) => {
        const state = getState();
        const updatedSurveys = state.adminSurveys.map(s => 
          s.surveyId === surveyId ? updatedSurvey : s
        );
        patchState({
          adminLoading: false,
          adminSurveys: updatedSurveys,
          activeAdminSurvey: updatedSurvey
        });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(DeleteAdminSurveyAction)
  deleteAdminSurvey(
    { patchState, getState }: StateContext<InstantSurveyStateModel>,
    { surveyId }: DeleteAdminSurveyAction
  ) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.deleteSurvey(surveyId).pipe(
      tap(() => {
        const state = getState();
        const filteredSurveys = state.adminSurveys.filter(s => s.surveyId !== surveyId);
        patchState({
          adminLoading: false,
          adminSurveys: filteredSurveys,
          activeAdminSurvey: state.activeAdminSurvey?.surveyId === surveyId ? null : state.activeAdminSurvey
        });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(GetUserTargetingOptionsAction)
  getUserTargetingOptions({ patchState }: StateContext<InstantSurveyStateModel>) {
    return this.instantSurveyService.getUserTargetingOptions().pipe(
      tap((userTargetingOptions) => {
        patchState({ userTargetingOptions });
      }),
      catchError((err) => {
        return throwError(err);
      }),
    );
  }

  @Action(AssignSurveyToUsersAction)
  assignSurveyToUsers(
    { patchState }: StateContext<InstantSurveyStateModel>,
    { surveyId, userIds }: AssignSurveyToUsersAction
  ) {
    patchState({ adminLoading: true });
    return this.instantSurveyService.assignSurveyToUsers(surveyId, userIds).pipe(
      tap(() => {
        patchState({ adminLoading: false });
      }),
      catchError((err) => {
        patchState({ adminLoading: false });
        return throwError(err);
      }),
    );
  }
}
