import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import {
  InstantSurveyRequest,
  InstantSurveyResponse,
  InstantSurveyModel,
  InstantSurveyAnswerRequest,
  InstantSurveyAnswerResponse,
  InstantSurveyAdminModel,
  InstantSurveyAdminRequest,
  InstantSurveyPosition
} from '../model/instant-survey.model';

@Injectable({
  providedIn: 'root'
})
export class InstantSurveyService {

  constructor(
    private readonly http: HttpClient
  ) { }

  /**
   * Get instant surveys with the specified position and details
   */
  public getInstantSurveys(): Observable<InstantSurveyResponse> {
    const request: InstantSurveyRequest = {
      Position: InstantSurveyPosition.INSTANT_SURVEY_MAIN,
      GetDetails: true
    };

    return this.http.post<HttpResponse<InstantSurveyResponse>>(
      `${environment.api}/survey/instantsurvey`,
      request
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return { surveys: [], totalCount: 0 };
      }),
    );
  }

  /**
   * Submit answer for an instant survey question
   */
  public submitAnswer(answerRequest: InstantSurveyAnswerRequest): Observable<InstantSurveyAnswerResponse> {
    return this.http.post<HttpResponse<InstantSurveyAnswerResponse>>(
      `${environment.api}/survey/instantsurvey/answer`,
      answerRequest
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        throw new Error(val.message || 'Failed to submit answer');
      }),
    );
  }

  /**
   * Get user's survey statistics
   */
  public getUserStats(): Observable<any> {
    return this.http.get<HttpResponse<any>>(
      `${environment.api}/survey/instantsurvey/userstats`
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  // Admin methods for backoffice management

  /**
   * Get all surveys for admin management
   */
  public getAdminSurveys(): Observable<InstantSurveyAdminModel[]> {
    return this.http.get<HttpResponse<InstantSurveyAdminModel[]>>(
      `${environment.api}/survey/instantsurvey/admin/list`
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return [];
      }),
    );
  }

  /**
   * Get specific survey for admin editing
   */
  public getAdminSurvey(surveyId: string): Observable<InstantSurveyAdminModel> {
    return this.http.get<HttpResponse<InstantSurveyAdminModel>>(
      `${environment.api}/survey/instantsurvey/admin/${surveyId}`
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        throw new Error(val.message || 'Survey not found');
      }),
    );
  }

  /**
   * Create new survey
   */
  public createSurvey(survey: InstantSurveyAdminRequest): Observable<InstantSurveyAdminModel> {
    return this.http.post<HttpResponse<InstantSurveyAdminModel>>(
      `${environment.api}/survey/instantsurvey/admin/create`,
      survey
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        throw new Error(val.message || 'Failed to create survey');
      }),
    );
  }

  /**
   * Update existing survey
   */
  public updateSurvey(surveyId: string, survey: InstantSurveyAdminRequest): Observable<InstantSurveyAdminModel> {
    return this.http.put<HttpResponse<InstantSurveyAdminModel>>(
      `${environment.api}/survey/instantsurvey/admin/${surveyId}`,
      survey
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        throw new Error(val.message || 'Failed to update survey');
      }),
    );
  }

  /**
   * Delete survey
   */
  public deleteSurvey(surveyId: string): Observable<boolean> {
    return this.http.delete<HttpResponse<any>>(
      `${environment.api}/survey/instantsurvey/admin/${surveyId}`
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return true;
        }
        throw new Error(val.message || 'Failed to delete survey');
      }),
    );
  }

  /**
   * Get user targeting options
   */
  public getUserTargetingOptions(): Observable<any> {
    return this.http.get<HttpResponse<any>>(
      `${environment.api}/survey/instantsurvey/admin/targeting-options`
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  /**
   * Assign survey to users
   */
  public assignSurveyToUsers(surveyId: string, userIds: string[]): Observable<boolean> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/survey/instantsurvey/admin/${surveyId}/assign`,
      { userIds }
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return true;
        }
        throw new Error(val.message || 'Failed to assign survey');
      }),
    );
  }
}
