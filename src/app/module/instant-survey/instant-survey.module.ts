import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';

import { InstantSurveyRoutingModule } from './instant-survey-routing.module';
import { InstantSurveyLayoutComponent } from './container/instant-survey-layout/instant-survey-layout.component';
import { InstantSurveyInitComponent } from './container/instant-survey-init/instant-survey-init.component';
import { InstantSurveyQuestionComponent } from './container/instant-survey-question/instant-survey-question.component';
import { InstantSurveyResultComponent } from './container/instant-survey-result/instant-survey-result.component';
import { InstantSurveyAdminComponent } from './container/instant-survey-admin/instant-survey-admin.component';
import { InstantSurveyQuestionBuilderComponent } from './component/instant-survey-question-builder/instant-survey-question-builder.component';
import { InstantSurveyPointMessageComponent } from './component/instant-survey-point-message/instant-survey-point-message.component';
import { InstantSurveyVisionLinkComponent } from './component/instant-survey-vision-link/instant-survey-vision-link.component';
import { InstantSurveyService } from './service/instant-survey.service';

@NgModule({
  declarations: [
    InstantSurveyLayoutComponent,
    InstantSurveyInitComponent,
    InstantSurveyQuestionComponent,
    InstantSurveyResultComponent,
    InstantSurveyAdminComponent,
    InstantSurveyQuestionBuilderComponent,
    InstantSurveyPointMessageComponent,
    InstantSurveyVisionLinkComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    UserEventLogModule,
    InstantSurveyRoutingModule,
  ],
  providers: [InstantSurveyService]
})
export class InstantSurveyModule {
  constructor() {
    stopLoadingAnimation();
  }
}
