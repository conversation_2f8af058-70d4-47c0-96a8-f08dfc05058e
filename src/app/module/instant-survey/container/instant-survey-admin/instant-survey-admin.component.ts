import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { 
  InstantSurveyAdminModel, 
  InstantSurveyAdminRequest, 
  InstantSurveyQuestionType,
  InstantSurveyOption 
} from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { 
  GetAdminSurveysAction, 
  CreateAdminSurveyAction, 
  UpdateAdminSurveyAction, 
  DeleteAdminSurveyAction,
  GetUserTargetingOptionsAction
} from '../../action/instant-survey.action';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-instant-survey-admin',
  templateUrl: './instant-survey-admin.component.html',
  styleUrls: ['./instant-survey-admin.component.scss']
})
export class InstantSurveyAdminComponent implements OnInit, OnDestroy {
  @Select(InstantSurveyState.getAdminSurveys)
  adminSurveys$: Observable<InstantSurveyAdminModel[]>;

  @Select(InstantSurveyState.getAdminLoading)
  adminLoading$: Observable<boolean>;

  @Select(InstantSurveyState.getUserTargetingOptions)
  userTargetingOptions$: Observable<any>;

  surveyForm: FormGroup;
  adminSurveys: InstantSurveyAdminModel[] = [];
  userTargetingOptions: any = null;
  editingSurvey: InstantSurveyAdminModel | null = null;
  showForm = false;
  questionTypes = Object.values(InstantSurveyQuestionType);

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private router: Router
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.store.dispatch(new GetAdminSurveysAction());
    this.store.dispatch(new GetUserTargetingOptionsAction());

    this.adminSurveys$.pipe(takeUntil(this.subscriptions$)).subscribe(surveys => {
      this.adminSurveys = surveys || [];
    });

    this.userTargetingOptions$.pipe(takeUntil(this.subscriptions$)).subscribe(options => {
      this.userTargetingOptions = options;
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  initializeForm(): void {
    this.surveyForm = this.fb.group({
      question: ['', Validators.required],
      questionType: [InstantSurveyQuestionType.MULTIPLE_CHOICE, Validators.required],
      pointValue: [10, [Validators.required, Validators.min(1)]],
      isActive: [true],
      deeplink: [''],
      correctAnswerId: ['', Validators.required],
      options: this.fb.array([]),
      targetAudience: this.fb.array([]),
      timingConfig: this.fb.group({
        delayDays: [0, [Validators.min(0)]],
        showToCorrectAnswerers: [true],
        showToIncorrectAnswerers: [true],
        showToNewUsers: [true]
      }),
      conditionalLogic: this.fb.group({
        assignToAnsweredUsers: [false],
        assignToNewUsers: [true],
        requireCorrectPreviousAnswer: [false],
        requireIncorrectPreviousAnswer: [false]
      })
    });

    // Initialize with default options
    this.addOption();
    this.addOption();
  }

  get optionsArray(): FormArray {
    return this.surveyForm.get('options') as FormArray;
  }

  get targetAudienceArray(): FormArray {
    return this.surveyForm.get('targetAudience') as FormArray;
  }

  addOption(): void {
    const optionGroup = this.fb.group({
      optionId: [''],
      title: ['', Validators.required],
      value: [''],
      order: [this.optionsArray.length]
    });
    this.optionsArray.push(optionGroup);
  }

  removeOption(index: number): void {
    if (this.optionsArray.length > 2) {
      this.optionsArray.removeAt(index);
      // Update order values
      this.optionsArray.controls.forEach((control, i) => {
        control.get('order')?.setValue(i);
      });
    }
  }

  createNewSurvey(): void {
    this.editingSurvey = null;
    this.showForm = true;
    this.initializeForm();
  }

  editSurvey(survey: InstantSurveyAdminModel): void {
    this.editingSurvey = survey;
    this.showForm = true;
    this.populateForm(survey);
  }

  populateForm(survey: InstantSurveyAdminModel): void {
    // Clear existing options
    while (this.optionsArray.length !== 0) {
      this.optionsArray.removeAt(0);
    }

    // Add survey options
    survey.options.forEach(option => {
      const optionGroup = this.fb.group({
        optionId: [option.optionId],
        title: [option.title, Validators.required],
        value: [option.value],
        order: [option.order]
      });
      this.optionsArray.push(optionGroup);
    });

    this.surveyForm.patchValue({
      question: survey.question,
      questionType: survey.questionType,
      pointValue: survey.pointValue,
      isActive: survey.isActive,
      deeplink: survey.deeplink,
      correctAnswerId: survey.correctAnswerId,
      timingConfig: survey.timingConfig,
      conditionalLogic: survey.conditionalLogic
    });
  }

  onSubmit(): void {
    if (this.surveyForm.valid) {
      const formValue = this.surveyForm.value;
      
      // Generate option IDs if not present
      formValue.options.forEach((option: any, index: number) => {
        if (!option.optionId) {
          option.optionId = `option_${Date.now()}_${index}`;
        }
        if (!option.value) {
          option.value = option.title;
        }
      });

      const surveyRequest: InstantSurveyAdminRequest = {
        ...formValue,
        targetAudience: formValue.targetAudience || []
      };

      if (this.editingSurvey) {
        this.store.dispatch(new UpdateAdminSurveyAction(this.editingSurvey.surveyId, surveyRequest));
      } else {
        this.store.dispatch(new CreateAdminSurveyAction(surveyRequest));
      }

      this.cancelEdit();
    }
  }

  deleteSurvey(survey: InstantSurveyAdminModel): void {
    if (confirm('Are you sure you want to delete this survey?')) {
      this.store.dispatch(new DeleteAdminSurveyAction(survey.surveyId));
    }
  }

  cancelEdit(): void {
    this.showForm = false;
    this.editingSurvey = null;
    this.initializeForm();
  }

  goBack(): void {
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }
}
