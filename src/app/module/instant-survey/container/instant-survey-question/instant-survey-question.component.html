<div class="instant-survey-question px-3 py-4" *ngIf="activeSurvey">
  <div class="question-container">
    <!-- Header -->
    <div class="d-flex align-items-center mb-4">
      <button 
        class="btn btn-link p-0 me-3"
        (click)="goBack()"
      >
        <i class="icon icon-arrow-left"></i>
      </button>
      <h3 class="mb-0">{{ "_instant_survey" | translate }}</h3>
    </div>

    <!-- Point Message -->
    <cat-instant-survey-point-message 
      [message]="getPointMessage()"
      [pointValue]="activeSurvey.pointValue"
    ></cat-instant-survey-point-message>

    <!-- Question Card -->
    <div class="card question-card mb-4">
      <div class="card-body">
        <h4 class="question-title mb-4">{{ activeSurvey.question }}</h4>

        <form [formGroup]="questionForm" (ngSubmit)="onSubmit()">
          <!-- Question Options -->
          <div class="options-container mb-4">
            <div 
              *ngFor="let option of activeSurvey.options; let i = index"
              class="option-item mb-3"
            >
              <label class="option-label">
                <input 
                  type="radio" 
                  [value]="option.optionId"
                  formControlName="selectedOption"
                  class="option-radio"
                >
                <div class="option-content">
                  <span class="option-letter">{{ String.fromCharCode(65 + i) }}</span>
                  <span class="option-text">{{ option.title }}</span>
                </div>
              </label>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="text-center">
            <button 
              type="submit"
              class="btn btn-primary btn-lg px-5"
              [disabled]="!questionForm.valid || isSubmitting || (loading$ | async)"
            >
              <span *ngIf="isSubmitting || (loading$ | async)">
                <i class="spinner-border spinner-border-sm me-2"></i>
                {{ "_submitting" | translate }}
              </span>
              <span *ngIf="!(isSubmitting || (loading$ | async))">
                {{ "_submit_answer" | translate }}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- VisionLink Integration -->
    <cat-instant-survey-vision-link 
      [deeplink]="activeSurvey.deeplink"
      [visionLinkUrl]="getVisionLinkUrl()"
    ></cat-instant-survey-vision-link>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="!activeSurvey" class="text-center py-5">
  <cat-mini-loader></cat-mini-loader>
  <p class="mt-3 text-muted">{{ "_loading_question" | translate }}</p>
</div>
