import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InstantSurveyModel, InstantSurveyAnswerRequest } from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { SubmitInstantSurveyAnswerAction } from '../../action/instant-survey.action';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-instant-survey-question',
  templateUrl: './instant-survey-question.component.html',
  styleUrls: ['./instant-survey-question.component.scss']
})
export class InstantSurveyQuestionComponent implements OnInit, OnDestroy {
  @Select(InstantSurveyState.getActiveSurvey)
  activeSurvey$: Observable<InstantSurveyModel>;

  @Select(InstantSurveyState.getLoading)
  loading$: Observable<boolean>;

  @Select(InstantSurveyState.getLastAnswerResponse)
  lastAnswerResponse$: Observable<any>;

  questionForm: FormGroup;
  activeSurvey: InstantSurveyModel | null = null;
  questionId: string;
  isSubmitting = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private store: Store
  ) {
    this.questionForm = this.fb.group({
      selectedOption: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.questionId = this.route.snapshot.paramMap.get('id') || '';

    this.activeSurvey$.pipe(takeUntil(this.subscriptions$)).subscribe(survey => {
      this.activeSurvey = survey;
      if (!survey) {
        // If no active survey, redirect back to init
        this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
      }
    });

    this.lastAnswerResponse$.pipe(takeUntil(this.subscriptions$)).subscribe(response => {
      if (response) {
        // Navigate to result page
        this.router.navigate([
          ...environment.rootUrl.split('/'),
          'instant-survey',
          'result',
          this.questionId
        ]);
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onSubmit(): void {
    if (this.questionForm.valid && this.activeSurvey) {
      this.isSubmitting = true;
      
      const answerRequest: InstantSurveyAnswerRequest = {
        surveyId: this.activeSurvey.surveyId,
        questionId: this.activeSurvey.questionId,
        selectedOptionId: this.questionForm.get('selectedOption')?.value,
        answerTime: new Date().toISOString()
      };

      this.store.dispatch(new SubmitInstantSurveyAnswerAction(answerRequest));
    }
  }

  getPointMessage(): string {
    if (!this.activeSurvey?.userStats) {
      return `Bu soruya doğru cevap vermeniz halinde ${this.activeSurvey?.pointValue} puan kazanacaksınız`;
    }

    const stats = this.activeSurvey.userStats;
    if (!stats.hasAnsweredBefore) {
      return `Bu soruya doğru cevap vermeniz halinde ${this.activeSurvey.pointValue} puan kazanacaksınız`;
    } else {
      return `Daha önceden ${stats.totalQuestionsAnswered} soruya cevap verip toplam ${stats.totalPointsEarned} puan kazandınız. Bu soruya doğru cevap vermeniz halinde ${this.activeSurvey.pointValue} puan kazanacaksınız`;
    }
  }

  getVisionLinkUrl(): string {
    // This should be configured based on your VisionLink setup
    return 'https://visionlink.example.com/login';
  }

  goBack(): void {
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }
}
