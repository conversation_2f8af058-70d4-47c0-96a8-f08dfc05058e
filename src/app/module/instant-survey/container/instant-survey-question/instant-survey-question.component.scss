.instant-survey-question {
  max-width: 800px;
  margin: 0 auto;
}

.question-container {
  min-height: 60vh;
}

.question-card {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.question-title {
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.4;
}

.options-container {
  .option-item {
    .option-label {
      display: block;
      cursor: pointer;
      margin: 0;
      padding: 0;
      
      .option-radio {
        display: none;
      }
      
      .option-content {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        transition: all 0.2s ease;
        background-color: #fff;
        
        .option-letter {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          background-color: #f8f9fa;
          border-radius: 50%;
          font-weight: 600;
          color: #6c757d;
          margin-right: 16px;
          flex-shrink: 0;
        }
        
        .option-text {
          flex: 1;
          font-size: 16px;
          color: #2c3e50;
        }
      }
      
      &:hover .option-content {
        border-color: #007bff;
        background-color: #f8f9ff;
      }
    }
    
    .option-radio:checked + .option-content {
      border-color: #007bff;
      background-color: #e7f3ff;
      
      .option-letter {
        background-color: #007bff;
        color: white;
      }
    }
  }
}

.btn-primary {
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.icon-arrow-left {
  font-size: 1.5rem;
  color: #6c757d;
}
