<div class="instant-survey-init px-3 py-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">{{ "_instant_survey_title" | translate }}</h2>
    <button 
      class="btn btn-outline-primary btn-sm"
      (click)="navigateToAdmin()"
    >
      {{ "_admin_panel" | translate }}
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading$ | async" class="text-center py-5">
    <cat-mini-loader></cat-mini-loader>
    <p class="mt-3 text-muted">{{ "_loading_surveys" | translate }}</p>
  </div>

  <!-- User Stats -->
  <div *ngIf="userStats" class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">{{ "_your_stats" | translate }}</h5>
      <div class="row">
        <div class="col-6">
          <div class="text-center">
            <h3 class="text-primary mb-1">{{ userStats.totalPointsEarned || 0 }}</h3>
            <small class="text-muted">{{ "_total_points" | translate }}</small>
          </div>
        </div>
        <div class="col-6">
          <div class="text-center">
            <h3 class="text-success mb-1">{{ userStats.totalQuestionsAnswered || 0 }}</h3>
            <small class="text-muted">{{ "_questions_answered" | translate }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Available Surveys -->
  <div *ngIf="!(loading$ | async) && surveys.length > 0">
    <h4 class="mb-3">{{ "_available_surveys" | translate }}</h4>
    <div class="survey-list">
      <div 
        *ngFor="let survey of surveys" 
        class="survey-item card mb-3"
        (click)="startSurvey(survey)"
      >
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <h5 class="card-title mb-2">{{ survey.question }}</h5>
              <div class="d-flex align-items-center">
                <span class="badge badge-primary me-2">
                  {{ survey.pointValue }} {{ "_points" | translate }}
                </span>
                <span class="badge badge-secondary">
                  {{ survey.questionType }}
                </span>
              </div>
            </div>
            <i class="icon icon-chevron-right text-muted"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!(loading$ | async) && surveys.length === 0" class="text-center py-5">
    <cat-empty-content
      [title]="'_no_surveys_available' | translate"
      [description]="'_no_surveys_description' | translate"
    ></cat-empty-content>
  </div>
</div>
