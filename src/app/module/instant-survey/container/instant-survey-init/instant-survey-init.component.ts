import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InstantSurveyModel } from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { GetInstantSurveysAction, SetActiveInstantSurveyAction, GetUserStatsAction } from '../../action/instant-survey.action';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-instant-survey-init',
  templateUrl: './instant-survey-init.component.html',
  styleUrls: ['./instant-survey-init.component.scss']
})
export class InstantSurveyInitComponent implements OnInit, OnDestroy {
  @Select(InstantSurveyState.getSurveys)
  surveys$: Observable<InstantSurveyModel[]>;

  @Select(InstantSurveyState.getLoading)
  loading$: Observable<boolean>;

  @Select(InstantSurveyState.getUserStats)
  userStats$: Observable<any>;

  surveys: InstantSurveyModel[] = [];
  userStats: any = null;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private store: Store,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Load surveys and user stats
    this.store.dispatch(new GetInstantSurveysAction());
    this.store.dispatch(new GetUserStatsAction());

    this.surveys$.pipe(takeUntil(this.subscriptions$)).subscribe(surveys => {
      if (surveys) {
        this.surveys = surveys;
      }
    });

    this.userStats$.pipe(takeUntil(this.subscriptions$)).subscribe(stats => {
      this.userStats = stats;
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  startSurvey(survey: InstantSurveyModel): void {
    this.store.dispatch(new SetActiveInstantSurveyAction(survey));
    this.router.navigate([
      ...environment.rootUrl.split('/'),
      'instant-survey',
      'question',
      survey.questionId
    ]);
  }

  navigateToAdmin(): void {
    this.router.navigate([
      ...environment.rootUrl.split('/'),
      'instant-survey',
      'admin'
    ]);
  }
}
