import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InstantSurveyAnswerResponse } from '../../model/instant-survey.model';
import { InstantSurveyState } from '../../state/instant-survey.state';
import { ClearInstantSurveysAction } from '../../action/instant-survey.action';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-instant-survey-result',
  templateUrl: './instant-survey-result.component.html',
  styleUrls: ['./instant-survey-result.component.scss']
})
export class InstantSurveyResultComponent implements OnInit, OnDestroy {
  @Select(InstantSurveyState.getLastAnswerResponse)
  lastAnswerResponse$: Observable<InstantSurveyAnswerResponse>;

  answerResponse: InstantSurveyAnswerResponse | null = null;
  questionId: string;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store
  ) { }

  ngOnInit(): void {
    this.questionId = this.route.snapshot.paramMap.get('id') || '';

    this.lastAnswerResponse$.pipe(takeUntil(this.subscriptions$)).subscribe(response => {
      this.answerResponse = response;
      if (!response) {
        // If no response, redirect back to init
        this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  getResultMessage(): string {
    if (!this.answerResponse) return '';

    if (this.answerResponse.isCorrect) {
      return `Tebrikler, soruyu doğru bilerek bizden ${this.answerResponse.pointsEarned} puan kazandınız. Bir sonraki anketimizde görüşmek üzere`;
    } else {
      return 'Üzgünüz, sorumuza yanlış cevap verdiniz. Bir sonraki anketimizdeki soruya doğru cevap vererek Boomcoin kazanabilirsiniz';
    }
  }

  getResultIcon(): string {
    return this.answerResponse?.isCorrect ? 'icon-check-circle' : 'icon-x-circle';
  }

  getResultClass(): string {
    return this.answerResponse?.isCorrect ? 'success' : 'danger';
  }

  goToNextSurvey(): void {
    // Clear current state and go back to init
    this.store.dispatch(new ClearInstantSurveysAction());
    this.router.navigate([...environment.rootUrl.split('/'), 'instant-survey']);
  }

  goHome(): void {
    this.router.navigate([...environment.rootUrl.split('/')]);
  }
}
