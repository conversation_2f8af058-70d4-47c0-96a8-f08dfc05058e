.instant-survey-result {
  max-width: 600px;
  margin: 0 auto;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.result-container {
  width: 100%;
}

.result-icon {
  i {
    font-size: 4rem;
    
    &.text-success {
      color: #28a745 !important;
    }
    
    &.text-danger {
      color: #dc3545 !important;
    }
  }
}

.result-message {
  h3 {
    font-weight: 600;
  }
  
  .lead {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.points-display {
  .card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .stat-item {
    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0;
    }
    
    small {
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.next-question-info {
  .alert {
    border-radius: 8px;
    border: none;
    background-color: #e3f2fd;
    color: #1565c0;
  }
}

.action-buttons {
  .btn {
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 30px;
    
    &.btn-lg {
      font-size: 1rem;
    }
  }
}

@media (max-width: 576px) {
  .action-buttons {
    .btn {
      display: block;
      width: 100%;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
