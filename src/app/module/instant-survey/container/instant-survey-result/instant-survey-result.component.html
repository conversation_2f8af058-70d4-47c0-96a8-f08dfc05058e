<div class="instant-survey-result px-3 py-4" *ngIf="answerResponse">
  <div class="result-container text-center">
    <!-- Result Icon -->
    <div class="result-icon mb-4">
      <i [class]="getResultIcon()" [ngClass]="'text-' + getResultClass()"></i>
    </div>

    <!-- Result Message -->
    <div class="result-message mb-4">
      <h3 class="mb-3" [ngClass]="'text-' + getResultClass()">
        {{ answerResponse.isCorrect ? '_correct_answer' : '_incorrect_answer' | translate }}
      </h3>
      <p class="lead text-muted">
        {{ getResultMessage() }}
      </p>
    </div>

    <!-- Points Display -->
    <div class="points-display mb-5" *ngIf="answerResponse.isCorrect">
      <div class="card border-0 bg-light">
        <div class="card-body py-4">
          <div class="row">
            <div class="col-6">
              <div class="stat-item">
                <h2 class="text-success mb-1">+{{ answerResponse.pointsEarned }}</h2>
                <small class="text-muted">{{ "_points_earned" | translate }}</small>
              </div>
            </div>
            <div class="col-6">
              <div class="stat-item">
                <h2 class="text-primary mb-1">{{ answerResponse.totalPoints }}</h2>
                <small class="text-muted">{{ "_total_points" | translate }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Next Question Info -->
    <div class="next-question-info mb-4" *ngIf="answerResponse.nextQuestionId">
      <div class="alert alert-info">
        <i class="icon icon-clock me-2"></i>
        <span *ngIf="answerResponse.nextQuestionDelay">
          {{ "_next_question_available_in" | translate }} {{ answerResponse.nextQuestionDelay }} {{ "_days" | translate }}
        </span>
        <span *ngIf="!answerResponse.nextQuestionDelay">
          {{ "_next_question_available_now" | translate }}
        </span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button 
        class="btn btn-primary btn-lg me-3"
        (click)="goToNextSurvey()"
      >
        {{ "_continue_surveys" | translate }}
      </button>
      <button 
        class="btn btn-outline-secondary btn-lg"
        (click)="goHome()"
      >
        {{ "_go_home" | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="!answerResponse" class="text-center py-5">
  <cat-mini-loader></cat-mini-loader>
  <p class="mt-3 text-muted">{{ "_loading_result" | translate }}</p>
</div>
