import { AfterContentChecked, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { AgreementLink } from '../../../definition/model/agreement.model';
import { DefinitionService } from '../../../definition/service/definition.service';
import { MessageFrameService } from '../../service/message-frame.service';
import { FrameMessageEnum } from '../../enum/frame-message.enum';

@Component({
  selector: 'cat-agreement-modal',
  templateUrl: './agreement-modal.component.html',
  styleUrls: ['./agreement-modal.component.scss'],
})
export class AgreementModalComponent implements OnInit, AfterContentChecked {
  @ViewChild('target')
  target: ElementRef<HTMLDivElement>;

  @Input()
  content: AgreementLink;

  modalContent: any;
  loading = false;
  text: string;
  agreements: any;

  constructor(
    private readonly definitionService: DefinitionService,
    private readonly messageFrameService: MessageFrameService,
  ) { }

  ngAfterContentChecked() {
    const elements: any = this.target?.nativeElement?.querySelectorAll('a');
    // console.log('element found', elements);
    if (elements?.length > 0) {
      elements.forEach(el => {
        el.onclick = () => {
          this.openFrame(el?.href, el?.innerText);
          return false;
        };
      });
    }
  }

  ngOnInit(): void {
    // this.content.url = 'https://prod.borusancat.com/lgnd/api/agreement/detail/clarificationtext?language=tr';

    this.loading = true;
    const method = !this.content?.method ? this.definitionService.getAgreementContent(this.content.url)
      : this.definitionService[this.content.method](this.content.params);

    method.subscribe(response => {
      this.text = response;
      this.loading = false;
    }, () => {
      this.loading = false;
    });
  }


  openFrame(url, title) {
    if (url.slice(0, 4) === 'kvk:') {

      this.definitionService.agreementDetails(url.slice(4))
        .subscribe(data => {
          if (!data.length) {
            return;
          }
          this.modalContent = {
            text: '',
            linkText: '',
            url: data[0]?.url,
          };
        });
    } else {
      this.messageFrameService.sendMessage(FrameMessageEnum.openStore, {
        url,
      });
    }

  }
}
