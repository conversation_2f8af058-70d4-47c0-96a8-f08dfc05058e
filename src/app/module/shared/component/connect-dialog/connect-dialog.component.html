<div
  (click)="onClickBackdrop()"
  *ngIf="status"
  class="modal-backdrop"></div>

<div *ngIf="status" class="modal d-block" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-body py-5 px-4">
        <i class="icon icon-x cursor-pointer" (click)="onCloseModal()"></i>
        <div class="d-flex flex-column align-items-center justify-content-center">
          <i class="icon icon-connect-fail mb-3"></i>
          <p class="mb-4" [innerHTML]="('_internet_connection_error_message' | translate) | safeHtml"></p>
        </div>
        <button *ngIf="button" class="action-button btn btn-info btn-sm" (click)="onButtonClick()">
          {{button}}
        </button>
      </div>
    </div>
  </div>
</div>
