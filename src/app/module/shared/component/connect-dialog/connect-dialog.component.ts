import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Modal } from '../../model/modal.model';
import { AppState } from '../../../../state/app/app.state';
import { Observable } from 'rxjs';
import { Select } from '@ngxs/store';

@Component({
  selector: 'cat-connect-dialog',
  templateUrl: './connect-dialog.component.html',
  styleUrls: ['./connect-dialog.component.scss'],
})
export class ConnectDialogComponent extends Modal implements OnInit, OnChanges {
  @Select(AppState.connectionModalStatus)
  connectionModalStatus: Observable<boolean>;

  @Input() status: boolean;

  @Input() backdropClose = true;

  @Input() button = null;
  @Input() buttonClick = null;

  @Output() showChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  onInjectInputs(inputs: any): void {
    this.status = inputs.status;
    this.button = inputs.button;
    this.backdropClose = inputs.backdropClose;
    this.buttonClick = inputs.buttonClick;
  }

  ngOnInit(): void {
    this.connectionModalStatus.subscribe((x) => {
      if (x) {
        this.close();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {}

  onCloseModal() {
    this.status = false;
    this.showChange.emit(false);
    this.close();
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }
  onButtonClick() {
    if (typeof this.buttonClick === 'function') {
      this.buttonClick();
    }
  }
}
