import { animate, state, style, transition, trigger, } from '@angular/animations';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Modal } from '../../model/modal.model';
import { disableBack } from '../../../../util/disable-back.util';

@Component({
  selector: 'cat-error-modal',
  templateUrl: './error-modal.component.html',
  styleUrls: ['./error-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '20%', opacity: 0 })),
      state('*', style({ top: '35%', opacity: 1 })),
      transition('* => *', [animate('.05s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 0.1 })),
      transition('* => *', [animate('.05s')]),
    ]),
  ],
})
export class ErrorModalComponent extends Modal implements OnInit {
  @Input() message = '';
  @Input() backdropClose = true;
  @Input() translate: boolean;

  @Input() button = null;
  @Input() buttonClick = null;

  @Input() status: boolean;
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  warningIcon = `${environment.assets}/warning.svg`;
  private disabledBack: any;

  onInjectInputs(inputs: any): void {
    this.message = inputs.message;
    this.translate = inputs.translate;
    this.status = inputs.status;
    this.backdropClose = inputs.backdropClose;
    this.button = inputs.button;
    this.buttonClick = inputs.buttonClick;
  }

  ngOnInit(): void {
  }

  disableBack() {
    this.disabledBack = disableBack(() => {
      this.close();
    });
  }

  onCloseModal() {
    this.status = false;
    this.statusChange.emit();
    this.close();
  }

  close(output?: any) {
    super.close(output);
    if (this.disabledBack) {
      this.disabledBack?.removeAndPop();
      this.disabledBack = null;
    }
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }

  onButtonClick() {
    if (typeof this.buttonClick === 'function') {
      this.buttonClick(this);
    }
  }
}
