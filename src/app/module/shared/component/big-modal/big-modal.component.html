<div (click)="onClickBackdrop()" *ngIf="status" [@backdrop] class="modal-backdrop"></div>
<div *ngIf="status" [@modal] class="modal d-block" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        {{headerText | translate}}
        <i (click)="onCloseModal()" class="icon icon-x cursor-pointer"></i>
      </div>
      <div class="modal-body ">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
