<div (click)="onClickBackdrop()" *ngIf="status" [@backdrop] class="modal-backdrop"></div>
<div *ngIf="status" [@modal] class="modal d-block" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title w-100 mx-5" *ngIf="headerText">
          <img *ngIf="classItems" [src]="classItems" class="icon" style="height: 40px; margin-right: 6px; margin-bottom: 4px;"/>
          {{ headerText | translate }}
        </div>
        <i *ngIf="showCloseButton" (click)="onCloseModal()" class="icon icon-x cursor-pointer" [ngClass]="{
          'icon-x-pse': classItems
        }"></i>
      </div>
      <div class="modal-body ">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
