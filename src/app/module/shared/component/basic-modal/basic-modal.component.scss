:host {
  p {
    font-size: 16px;
    line-height: 1.4;
    text-align: center;
    color: #505050;
  }

  .modal-backdrop {
    opacity: 0.4;
    background-color: black;
  }

  .modal {
    left: 50%;
    top: 7%;
    transform: translate(-50%);
    width: 95vw;
    max-width: 400px;
    height: auto;
    overflow: visible;

    &-content {
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
      border-radius: 6px;
      border: none;
      max-height: 85vh;
      overflow-y: scroll;
    }

    &-header {
      padding: 1rem;
      font-size: 20px;
      font-weight: 600;
      border: 0;
      min-height: 40px;
    }

    &-title {
      font-size: 20px;
      font-weight: 800;
      text-align: center;
      overflow-wrap: anywhere;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-body {
      padding: 0.2rem 1rem;
      overflow-x: hidden;
    }

    &-btn {
      border-radius: 6px;
    }

    .icon-x {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 19px;
      color: #2c2c2c;
    }
    .icon-x-pse {
      position: absolute;
      right: 20px;
      top: 26px;
      font-size: 19px;
      color: #2c2c2c;
    }
  }
}
