import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { modalAnimations } from '../../util/modal-animations.util';

@Component({
  selector: 'cat-basic-modal',
  templateUrl: './basic-modal.component.html',
  styleUrls: ['./basic-modal.component.scss'],
  animations: modalAnimations,
})
export class BasicModalComponent implements OnInit {

  @Output()
  statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input()
  status = false;

  @Input()
  headerText: string;

  @Input()
  backdropClose = false;

  @Input()
  showCloseButton = true;

  @Input()
  classItems: string;

  constructor() {}

  ngOnInit(): void {
  }

  onCloseModal() {
    this.status = false;
    this.statusChange.emit();
    document.documentElement.style.overflow = 'auto';
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }

}
