<!--<div class="agreement" *ngIf="!(isGdprApproved$ | async)" [formGroup]="form">-->
<div class="agreement" *ngIf="true" [formGroup]="form">
  <div *ngFor="let agree of agreements" formGroupName="agreements" class="mb-3 agreement d-flex flex-row flex-nowrap justify-content-between">
    <input [formControlName]="agree.name" type="checkbox" class="form-check-input"
           [id]="agree.name">
    <label class="flex-grow-1 form-check-label text-secondary font-size-13px" [for]="agree.name">
<!--      <a (click)="showContent(agree)">{{agree.description }}</a> {{agree.description}}-->

      <ng-container *ngFor="let item of agree.descParts">
        <a (click)="showContent(agree)">{{item.linkText }}</a> {{item.text}}
      </ng-container>

    </label>
  </div>
</div>

<cat-fullscreen-modal *ngIf="modalContent && !user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-fullscreen-modal>
<cat-big-modal *ngIf="modalContent && user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-big-modal>
