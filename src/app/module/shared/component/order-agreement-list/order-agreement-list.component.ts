import { Component, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import {
  AgreementLink,
  OrderAgreementModel,
  QuotationAgreementRequest
} from '../../../definition/model/agreement.model';
import { Store } from '@ngxs/store';
import { LoginState } from '../../../authentication/state/login/login.state';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-order-agreement-list',
  templateUrl: './order-agreement-list.component.html',
  styleUrls: ['./order-agreement-list.component.scss'],
})
export class OrderAgreementListComponent implements OnInit {
  @Input()
  form: FormGroup;

  @Input()
  params: QuotationAgreementRequest;

  modalContent: AgreementLink;
  @Input()
  agreements: OrderAgreementModel[];

  @Output()
  user: boolean;

  constructor(
    private readonly fb: FormBuilder,
    private readonly store: Store
  ) {
    this.user = !!this.store.selectSnapshot(LoginState.user);
  }

  ngOnInit(): void {
    const group = this.fb.group({});
    this.agreements.map(item => {
      group.addControl(item.name, new FormControl());
    });
    this.form.addControl('agreements', group);

    this.agreements.map((item) => {
      item.descParts = item.description?.split('{').map((str) => {
        const [text, linkText] = str.split('}').reverse();

        let url = item.method;
        if (url && !environment.production) {
          url = url.replace(
            'https://prod.borusancat.com/lgnd/api',
            window.location.origin + '/api'
          );
        }
        return { linkText, text, url };
      });
    });
  }

  showContent(item: any) {
    this.modalContent = {
      ...item,
      params: this.params
    };
  }
}
