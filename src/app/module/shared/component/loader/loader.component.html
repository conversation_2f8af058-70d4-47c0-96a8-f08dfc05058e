<ng-template #customLoadingTemplate>
  <div [ngClass]="{
    'app-loader': bgColor !== 'transparent',
    'minimal-app-loader': bgColor === 'transparent'
  }"></div>
</ng-template>

<ngx-loading [show]="show"
             [config]="{animationType: 'none',backdropBackgroundColour: bgColor === 'transparent' ? bgColor :'rgba(255,255,255,0.7)', fullScreenBackdrop: overlay }"
             [template]="customLoadingTemplate"></ngx-loading>
