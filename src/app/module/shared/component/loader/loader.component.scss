.app-loader {
  z-index: 10000;
  position: fixed;
  left: 50%;
  top:50%;
  margin-left:-50px;
  margin-top:-50px;
  border: 16px solid #7f7e7e;
  border-radius: 50%;
  border-top: 16px solid #FFA300;
  border-bottom: 16px solid #FFA300;
  width: 100px;
  height: 100px;
  animation: spin 1s linear infinite;
}

.minimal-app-loader {
  z-index: 10000;
  position: relative;
  left: 48%;
  // bottom: 10px;
  margin-top: 10px;
  border: 6px solid #7f7e7e;
  border-radius: 50%;
  border-top: 6px solid #FFA300 !important;
  border-bottom: 6px solid #FFA300 !important;
  width: 26px;
  height: 26px;
  animation: spin 1s linear infinite;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
