.pull-to-refresh {
  height: calc(100vh - 73px);
  overflow: visible;
  position: relative;
  margin-bottom: -60px;

  &.active {
    height: 60px;
    margin-bottom: 0;
  }

  .icon-container {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    width: 100%;
  }

  .content {
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    overflow-x: hidden;
    //height: max-content;
    //height: inherit;
    padding-bottom: 40px;
    &.refreshing {
      margin-top: 60px;
    }
  }
}
