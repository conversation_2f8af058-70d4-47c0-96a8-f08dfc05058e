import { Component, EventEmitter, HostListener, Input, Output } from '@angular/core';

@Component({
  selector: 'cat-pull-to-refresh',
  templateUrl: './pull-to-refresh.component.html',
  styleUrls: ['./pull-to-refresh.component.scss']
})
export class PullToRefreshComponent {
  @Output() refresh: EventEmitter<any> = new EventEmitter();
  @Input() turnBack: boolean;


  private startY: number;
  private threshold: number = 90;
  private isDragging: boolean = false;
  isLoading = false;
  showIcon = false;

  @HostListener('touchstart', ['$event']) onTouchStart(event) {
    if (this.isAtTop()) {
      this.startY = event.touches[0].clientY;
      this.isDragging = true;
    }
  }

  @HostListener('touchmove', ['$event']) onTouchMove(event) {
    if (this.isDragging) {
      const currentY = event.touches[0].clientY;
      const distance = currentY - this.startY;

      if (distance > this.threshold) {
        this.showIcon = true;
      } else {
        this.showIcon = false;
      }
    }
  }

  @HostListener('touchend') onTouchEnd() {
    if (this.showIcon) {
      this.isLoading = true;
      this.refresh.emit();
    }
    this.isDragging = false;
    this.startY = 0;
    this.showIcon = false;
  }

  private isAtTop(): boolean {
    return window.scrollY === 0;
  }

}
