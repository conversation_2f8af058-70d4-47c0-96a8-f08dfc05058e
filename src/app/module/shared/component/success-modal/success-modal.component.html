<cat-basic-modal [status]="status" (statusChange)="onCloseModal($event)">
  <div class="d-flex flex-column align-items-center justify-content-center my-3">
    <i class="icon icon-message-success d-inline-block mb-3"></i>
    <div class="success-message">

      <p *ngIf="translate" class="modal-body-message text-center">
        {{ message | translate }}
      </p>
      <p *ngIf="!translate" class="modal-body-message text-center">
        {{ message }}
      </p>

    </div>
    <button class="px-5 btn btn-warning btn-sm text-white shadow" *ngIf="button"
            (click)="onButtonClick()">
      {{ translate ? (button | translate) : button }}
    </button>


  </div>
</cat-basic-modal>
