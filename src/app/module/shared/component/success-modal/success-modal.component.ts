import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Modal } from '../../model/modal.model';

@Component({
  selector: 'cat-success-modal',
  templateUrl: './success-modal.component.html',
  styleUrls: ['./success-modal.component.scss']
})
export class SuccessModalComponent extends Modal implements OnInit {

  @Input() message = '';
  @Input() backdropClose = true;
  @Input() translate = true;

  @Input() button = null;
  @Input() buttonClick = null;

  @Input() status: boolean;
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();


  onInjectInputs(inputs: any): void {
    this.message = inputs.message;
    this.translate = inputs.translate;
    this.status = inputs.status;
    this.backdropClose = inputs.backdropClose;
    this.button = inputs.button;
    this.buttonClick = inputs.buttonClick;
  }

  ngOnInit(): void {}

  onCloseModal($event = false) {
    this.status = $event;
    this.statusChange.emit($event);
    this.close();
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }

  onButtonClick() {
    if (typeof this.buttonClick === 'function') {
      this.buttonClick(this);
    } else {
      this.dismiss();
    }
  }
}
