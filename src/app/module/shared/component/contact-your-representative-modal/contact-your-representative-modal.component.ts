import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerModel, PssrList } from 'src/app/module/customer/model/customer.model';
import { CustomerState } from 'src/app/module/customer/state/customer.state';
import { MessageFrameService } from '../../service/message-frame.service';
import { LogService } from 'src/app/module/customer/service/log.service';
import { FrameMessageEnum } from '../../enum/frame-message.enum';

@Component({
  selector: 'cat-contact-your-representative-modal',
  templateUrl: './contact-your-representative-modal.component.html',
  styleUrls: ['./contact-your-representative-modal.component.scss']
})
export class ContactYourRepresentativeModalComponent implements OnInit {
  @Input()
  contactModal = false;

  @Output() handleClose = new EventEmitter<boolean>()

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  pssrList: PssrList[] | any = [];

  constructor(
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly logger: LogService
  ) { }

  private subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.customer$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(customer => {
      if (customer) {
        console.log(customer, 'customer')
        this.pssrList = customer.details?.pssrList.filter(pssr => {
          if (pssr.titles.find(data => data === 'PSSR')) {
            return pssr.mailList.length || pssr.telephoneList.length;
          }
          return false;
        });
      }
    });
  }

  onCall(phone: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }

  handleContactModal() {
    this.handleClose.emit();
  }


  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
