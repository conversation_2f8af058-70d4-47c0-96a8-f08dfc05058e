.service-person {
  margin: 0;
  padding: 0;
  list-style: none;
  border-bottom: 1px solid #dbdbdb;

  &-item {
    border-top: 1px solid #dbdbdb;
    padding: 1.25rem;

    &-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #505050;
    }

    &-detail {
      margin: 0;
      padding: 0;
      list-style: none;

      &-item {
        overflow-wrap: anywhere;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: #505050;
        margin-top: 1rem;

        .icon-area {
          margin-right: 1rem;
          border-radius: 50%;
          background-color: #ebebeb;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0.625rem;
        }
      }
    }
  }
}
