<!--<div class="agreement" *ngIf="!(isGdprApproved$ | async)" [formGroup]="form">-->
<div class="agreement" *ngIf="true" [formGroup]="form">
  <div *ngFor="let agree of agreements" formGroupName="agreements" class="mb-3 agreement d-flex flex-row flex-nowrap justify-content-between">
    <input *ngIf="agree.selectable" [formControlName]="agree.name" type="checkbox" class="form-check-input"
           [id]="agree.name">
    <label class="flex-grow-1 form-check-label text-secondary font-size-13px" [for]="agree.name">
      <ng-container *ngFor="let item of agree.descParts">
        <a (click)="showContent(item)">{{item.linkText }}</a> {{item.text}}
      </ng-container>

    </label>
  </div>
</div>

<cat-fullscreen-modal *ngIf="modalContent && !user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-fullscreen-modal>
<cat-big-modal *ngIf="modalContent && user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-big-modal>



<!--  <div formGroupName="agreements" class="form-check mb-3">-->
<!--    <input [formControlName]="'sd'" type="checkbox" class="form-check-input"-->
<!--           id="222">-->
<!--    <label class="form-check-label text-secondary font-size-13px" [for]="'222'">-->
<!--      asd-->
<!--    </label>-->
<!--  </div>-->

