import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { AgreementModel } from '../../../definition/model/agreement.model';
import { DefinitionService } from '../../../definition/service/definition.service';
import { Store } from '@ngxs/store';
import { LoginState } from '../../../authentication/state/login/login.state';

@Component({
  selector: 'cat-agreement-list',
  templateUrl: './agreement-list.component.html',
  styleUrls: ['./agreement-list.component.scss'],
})
export class AgreementListComponent implements OnInit {
  // @Select(UserState.isGdprApproved)
  // isGdprApproved$: Observable<boolean>;

  @Input()
  formType: AgreementTypeEnum;

  @Input()
  form: FormGroup;

  modalContent: any;
  agreements: AgreementModel[];

  @Output()
  agreementsData: EventEmitter<AgreementModel[]> = new EventEmitter();
  user: boolean;

  constructor(
    private readonly definitionService: DefinitionService,
    private readonly fb: FormBuilder,
    private readonly store: Store
  ) {
    this.user = !!this.store.selectSnapshot(LoginState.user);
  }

  ngOnInit(): void {
    console.log('AGREEMENT ', [this.formType, this.form]);
    this.definitionService.agreement(this.formType)
      .subscribe(data => {
        if (!data) {
          return;
        }
        this.agreements = data;
        this.agreementsData.emit(data);
        const group = this.fb.group({});
        this.agreements.map(item => {
          group.addControl(item.name, new FormControl(!item.selectable));
        });
        this.form.addControl('agreements', group);

      });
  }

  showContent(item: any) {
    this.modalContent = item;
  }

}
