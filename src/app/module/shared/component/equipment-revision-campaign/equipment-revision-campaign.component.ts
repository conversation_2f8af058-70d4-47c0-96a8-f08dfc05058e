import { Component, Input, OnInit } from '@angular/core';
import { EquipmentModel } from '../../../customer/model/equipment.model';
import { FrameMessageEnum } from '../../enum/frame-message.enum';
import { MessageFrameService } from '../../service/message-frame.service';
import { TranslateService } from '@ngx-translate/core';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'cat-equipment-revision-campaign',
  templateUrl: './equipment-revision-campaign.component.html',
  styleUrls: ['./equipment-revision-campaign.component.scss']
})
export class EquipmentRevisionCampaignComponent implements OnInit {

  @Input()
  equipmentDetail: EquipmentModel;

  descriptionLink: any;
  descriptionText: string;
  PermissionEnum = PermissionEnum;
  EquipmentDetailCampaignPermission: Observable<boolean>;

  constructor(
    private readonly frameService: MessageFrameService,
    private readonly translateService: TranslateService,
    private readonly permissionService: HasPermissionsService
  ) { }

  ngOnInit(): void {
    if (this.equipmentDetail?.equipmentRevisionCampaign?.description) {
      this.setDescription();
    }
    this.EquipmentDetailCampaignPermission = this.permissionService.hasPermission(PermissionEnum.EquipmentDetailCampaign);
  }

  setDescription() {
    const desc = this.equipmentDetail?.equipmentRevisionCampaign?.description;
    if (desc.split('<u>').length > 0 && desc.search('<u>') !== -1) {
      this.descriptionText = this.equipmentDetail?.equipmentRevisionCampaign?.description;

      this.descriptionText = this.descriptionText
        .slice(this.descriptionText.indexOf('<u>') + 3)
        .split('</u>')[0]
        ?.replace('<u>', '')
        ?.replace('</u>', '');

      this.descriptionLink = {
        text: this.getTag2Text(this.descriptionText, '<txt>'),
        url: this.getTag2Text(this.descriptionText, '<a>'),
        open: this.getTag2Text(this.descriptionText, '<oe>') === 'true',
        title: this.getTag2Text(this.descriptionText, '<t>'),
        before: desc.substring(0, desc.indexOf('<u>')),
        after: desc.slice(desc.indexOf('</u>') + 4),
        isTextBefore: desc.indexOf(this.descriptionText) < desc.indexOf('<txt>'),
      };
    } else {
      this.descriptionText = desc;
    }
  }

  getTag2Text(text, tag) {
    if (text.indexOf(tag) !== -1) {
      const tagStart = tag;
      const tagEnd = text.indexOf(tag.replace('<', '</')) > 0
        ? tag.replace('<', '</')
        : tag.replace('<', '</ ');
      this.descriptionText = text.replace(
        tagStart +
        text.slice(text.indexOf(tagStart) + tagStart.length, text.indexOf(tagEnd)) +
        tagEnd, '');
      return text.slice(text.indexOf(tagStart) + tagStart.length, text.indexOf(tagEnd));
    }
    return null;
  }

  descriptionGoLink() {
    const url = this.descriptionLink?.url;
    if (!url) {
      return;
    }
    if (this.descriptionLink.open) {
      this.frameService.sendMessage(FrameMessageEnum.openStore, {
        url
      });
    } else {
      const title = this.descriptionLink?.title || this.translateService.instant('_campaign');
      this.frameService.sendMessage(FrameMessageEnum.openModule, {
        url,
        title,
        closeButton: true,
      });
    }
  }
}
