<div *ngIf="equipmentDetail?.equipmentRevisionCampaign && (EquipmentDetailCampaignPermission | async)" class="my-3 custom-card-disount pt-2 pb-4 px-4">
  <div class="d-flex align-self-center justify-content-center h4 text-center">
    {{ equipmentDetail.equipmentRevisionCampaign?.title }}
  </div>
  <div *ngIf="equipmentDetail?.equipmentRevisionCampaign?.type === '_discount'" class="text-center text-success mb-2"
       style="font-size: 25px; font-weight: bolder; line-height: 1;">
    {{ equipmentDetail?.equipmentRevisionCampaign?.amount }} {{ equipmentDetail?.equipmentRevisionCampaign?.currency }}<br>
    <div class="font-size-16px text-center">{{ "_discount" | translate }}</div>
  </div>
  <div class="d-flex justify-content-between">
    <div class="d-flex flex-column justify-content-center flex-grow-1">
      <div class="font-size-14px text-center pr-2 text-break">
        {{ descriptionLink?.before }}
        <ng-container *ngIf="descriptionLink?.isTextBefore else isTextAfter">
          {{descriptionText}}
          <a *ngIf="descriptionLink?.url" (click)="descriptionGoLink()">{{descriptionLink.text}}</a>
        </ng-container>
        <ng-template #isTextAfter>
          <a *ngIf="descriptionLink?.url" (click)="descriptionGoLink()">{{descriptionLink.text}}</a>
          {{descriptionText}}
        </ng-template>
        {{ descriptionLink?.after }}
      </div>
    </div>
    <div [ngSwitch]="equipmentDetail?.equipmentRevisionCampaign?.type" class="d-flex justify-content-center w-40px text-success">
      <i *ngSwitchCase="'_campaign'" class="icon icon-campaign fs-40px"></i>
      <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon fs-40px"></i>
      <div *ngSwitchCase="'_percent_discount'"
           [style.font-size]="equipmentDetail?.equipmentRevisionCampaign?.amount === 100 ? '14px': 'inherit'"
           class="circle-discount-40 align-self-center text-center" style="line-height: 0.7;">
        %{{ equipmentDetail?.equipmentRevisionCampaign?.amount }}
      </div>
      <i *ngSwitchCase="'_discount'" class="icon {{ equipmentDetail?.equipmentRevisionCampaign?.currency | currenyIcon }} fs-40px"></i>
      <i *ngSwitchDefault class="icon icon-campaign fs-40px"></i>
    </div>
  </div>
</div>
