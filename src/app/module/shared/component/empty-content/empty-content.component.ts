import { Component, Input, OnInit } from '@angular/core';
import { FrameMessageEnum } from '../../enum/frame-message.enum';
import { MessageFrameService } from '../../service/message-frame.service';

@Component({
  selector: 'cat-empty-content',
  templateUrl: './empty-content.component.html',
  styleUrls: ['./empty-content.component.scss'],
})
export class EmptyContentComponent implements OnInit {
  @Input() iconName: string = '';
  @Input() message: string = '';
  @Input() hasBackButton: boolean = true;
  @Input() extraMessage = '';

  constructor(private readonly messageFrameService: MessageFrameService) {}

  ngOnInit(): void {}

  backToHome() {
    this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
  }
}
