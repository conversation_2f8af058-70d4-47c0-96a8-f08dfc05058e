<div
  class="empty-content d-flex align-items-center flex-column justify-content-center"
>
  <div class="empty-content-logo mb-2">
    <i class="icon icon-{{ iconName }} text-warning"></i>
  </div>
  <div class="empty-content-message text-center " [class.mb-5]="!extraMessage">
    {{ message | translate }}
  </div>
  <div *ngIf="extraMessage" class="empty-content-extra text-center mb-5">
    {{ extraMessage | translate }}
  </div>
  <ng-content></ng-content>
  <button
    class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm"
    *ngIf="hasBackButton"
    (click)="backToHome()"
  >
    {{ "_back_to_home" | translate }}
  </button>
</div>
