.modal {
  left: 50%;
  top: 0;
  transform: translate(-50%);
  width: 100vw;
  height: auto;
  overflow: visible;

  &-content {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
    //border-radius: 6px;
    border: none;
    max-height: 100vh;
    overflow-y: scroll;
  }

  &-header {
    padding: 1rem;
    font-size: 20px;
    font-weight: 600;
    border: 0;
    min-height: 40px;
  }

  &-body {
    //padding: 0.2rem 1rem;
    overflow-x: hidden;
  }

  &-backdrop {
    opacity: 0.4;
    background-color: black;
  }

  &-btn {
    border-radius: 6px;
  }

  .icon-x {
    position: absolute;
    right: 20px;
    top: 14px;
    font-size: 19px;
    color: #2c2c2c;
  }
}

::ng-deep.modal .modal {
  top: 0;

  &-backdrop {
    background-color: transparent;
  }
}

@media (min-width: 600px) and (max-width: 1024px) {
  .modal {
    max-width: 80%;
    &-dialog {
      max-width: 100%;
    }
  }
  ::ng-deep.modal .modal {
    max-width: 100%;

    &-dialog {
      max-width: 100%;
    }
  }
}

@media (min-width: 1024px){
  .modal {
    max-width: 1000px;
    &-dialog {
      max-width: 100%;
    }
  }
  ::ng-deep.modal .modal {
    max-width: 100%;

    &-dialog {
      max-width: 100%;
    }
  }
}
