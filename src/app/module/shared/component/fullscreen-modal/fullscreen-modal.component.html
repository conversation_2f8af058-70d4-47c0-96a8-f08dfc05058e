<div (click)="onClickBackdrop()" *ngIf="status" [@backdrop] class="modal-backdrop"></div>
<div *ngIf="status" class="modal d-block" role="dialog" tabindex="-1">
  <div class="modal-dialog" style="margin: 0" role="document">
    <div class="modal-content">
      <div class="modal-header">
        {{headerText | translate}}
        <i (click)="onCloseModal()" class="icon icon-x cursor-pointer"></i>
      </div>
      <div class="modal-body" style="padding: 0.5rem;">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
