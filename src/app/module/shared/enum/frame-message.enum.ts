import { PermissionEnum } from '../../definition/enum/permission.enum';
import { AppStorageModel } from '../state/common/common.state';
import { Survey } from '../../customer/model/service-request.model';
import { SSORedirectKeys } from './sso-pats.enum';

export enum FrameMessageEnum {
  unauthorised = 'unauthorised',
  openModule = 'openModule',
  openStore = 'openStore',
  // tokenChanged = 'tokenChanged',
  redirectMainPage = 'redirectMainPage',
  logout = 'logout',
  call_phone = 'call_phone',
  open_mail = 'open_mail',
  open_map = 'open_map',
  open_whatsapp = 'open_whatsapp',
  voice_record = 'voice_record',
  openTeklif = 'openTeklif',
  openCatalog = 'openCatalog',
  log = 'log',
  permissions = 'permissions',
  checkPermissions = 'checkPermissions',
  back_home = 'back_home',
  changeToken = 'changeToken',
  appStorage = 'appStorage',
  formBack = 'formBack',
  openSurvey = 'openSurvey',
  getLocation = 'getLocation',
  downloadFile = 'downloadFile',
  cancelDownloadFile = 'cancelDownloadFile',
  openCatApp = 'openCatApp',
  getMobileInfo = 'getMobileInfo',
  openPaymentPage = 'openPaymentPage',
  openAssistBox = 'openAssistBox',
  openSparePart = 'openSparePart',
  pageOpened = 'pageOpened',
  headerStatus = 'headerStatus',
  openOfflineMenu = 'openOfflineMenu',
  offlineEquipmentData = 'offlineEquipmentData',
  openInPCC = 'openInPCC',
  openVlSurvey = 'openVlSurvey'
}

export type FrameMessageDataType<T extends FrameMessageEnum> = T extends keyof FrameMessageData ? FrameMessageData[T] : any;

export interface FrameMessageData {
  [FrameMessageEnum.unauthorised]?: null;
  [FrameMessageEnum.logout]?: null;
  [FrameMessageEnum.call_phone]?: {
    phone: string
  };
  [FrameMessageEnum.openModule]?: {
    url: string
    title: string
    user?: object
    isPayment?: boolean
    quotationNumber?: string
    isSparePart?: boolean
    inspectionForm?: boolean
    listenEvent?: boolean
    disableHeader?: boolean
    backAction?: boolean
    onCloseWarning?: boolean
    closeButton?: boolean
  };
  [FrameMessageEnum.downloadFile]?: {
    url: string | ArrayBuffer
    id: string,
    isBase64: boolean,
    extension: string
  };
  [FrameMessageEnum.openStore]?: {
    url: string
  };
  [FrameMessageEnum.open_map]?: {
    lng: string | number,
    lat: string | number,
  };
  [FrameMessageEnum.open_whatsapp]?: {
    phone: string
  };

  [FrameMessageEnum.voice_record]?: {
    EquipmentNumber?: string;
    SerialNumber: string;
    CustomerNumber?: string;
    Source: string;
    TransactionId: string;
  };

  [FrameMessageEnum.openTeklif]?: {
    filename: string,
    contentType: string,
    file: any,
  };
  [FrameMessageEnum.log]?: any;
  [FrameMessageEnum.permissions]?: PermissionEnum[];
  [FrameMessageEnum.appStorage]?: AppStorageModel;
  [FrameMessageEnum.openSurvey]?: Survey;
  [FrameMessageEnum.headerStatus]?: {
    company?: boolean,
    backButton?: boolean,
    closeButton?: boolean,
    title?: null,
    notificationIcon?: boolean,
    hamburgerMenu?: boolean,
    closeModal?: boolean,
    closeModalMessage?: null,
    bgcolor?: null,
    img?: {
      src?: null,
      alt?: null,
    },
  };
  [FrameMessageEnum.redirectMainPage]?: {
    urlPath: string,
    urlParameters?: {},
  };
  [FrameMessageEnum.openInPCC]?: {
    title: string;
    url: string;
    direct?: boolean;
    pccDigitalBanko?: boolean;
  };
}
