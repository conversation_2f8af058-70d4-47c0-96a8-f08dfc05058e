export enum IncomingMessageEnum {
  refreshTokenChanged = 'refreshTokenChanged',
  mobileInfo = 'mobileInfo',
  loginResponse = 'loginResponse',
  loginStarted = 'loginStarted',
  appStorage = 'appStorage',
  permissionOk = 'permissionOk',
  showPermissionError = 'showPermissionError',
  uploadedSoundID = 'uploadedSoundID',
  currentLocation = 'currentLocation',
  startDownload = 'startDownload',
  finishDownload = 'finishDownload',
  paymentFinished = 'paymentFinished',
  webviewReopened = 'webviewReopened',
  systemFeatures = 'systemFeatures',
  borusanBlockedAction = 'borusanBlockedAction',
  soundRecordCollected = 'soundRecordCollected',
}
