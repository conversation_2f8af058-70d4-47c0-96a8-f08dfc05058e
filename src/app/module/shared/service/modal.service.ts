import { ApplicationRef, ComponentFactory, ComponentFactoryResolver, Injectable, Type, } from '@angular/core';
import { ErrorModalComponent } from '../component/error-modal/error-modal.component';
import { ModalComponent } from '../component/modal/modal.component';
import { Modal, ModalRef } from '../model/modal.model';
import { SuccessModalComponent } from '../component/success-modal/success-modal.component';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private modalContainer: HTMLElement;
  private modalContainerFactory: ComponentFactory<ModalComponent>;

  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private appRef: ApplicationRef
  ) {
    this.setupModalContainerFactory();
  }

  errorModal( data: {
      title?: string;
      message: string;
      translate?: boolean;
      backdropClose?: boolean;
      button?: string;
      buttonClick?: any;
    }
    // @ts-ignore:next-line
  ): ModalRef {
    return this.open(ErrorModalComponent, { ...data, status: true });
  }

  successModal(
    data: {
      title?: string;
      message: string;
      translate?: boolean;
      backdropClose?: boolean;
      button?: string;
      buttonClick?: any;
    },
    container = null
    // @ts-ignore:next-line
  ): ModalRef {
    setTimeout(() => {
      return this.open(SuccessModalComponent, { ...data, status: true }, container);
    }, 0);
  }

  open<T extends Modal>(component: Type<T>, inputs?: any, container = null): ModalRef {
    this.setupModalContainerDiv(container);

    const modalContainerRef = this.appRef.bootstrap(
      this.modalContainerFactory,
      this.modalContainer
    );

    const modalComponentRef = modalContainerRef.instance.createModal(component);

    if (inputs) {
      modalComponentRef.instance.onInjectInputs(inputs);
    }

    return new ModalRef(modalContainerRef, modalComponentRef);
  }

  private setupModalContainerDiv(container = null): void {
    this.modalContainer = document.createElement('div');
    (container || document.getElementsByTagName('body')[0])
      .appendChild(this.modalContainer);
  }

  private setupModalContainerFactory(): void {
    this.modalContainerFactory = this.componentFactoryResolver.resolveComponentFactory(
      ModalComponent
    );
  }
}
