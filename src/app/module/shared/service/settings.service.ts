import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { HttpResponse } from '../../../response/http.response';
import { SettingsResponse, SystemFeature } from '../model/settings.model';

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  constructor(
    private readonly http: HttpClient,
  ) {}

  systemFeatures(): Observable<SystemFeature[]> {
    return this.http
      .get<HttpResponse<SystemFeature[]>>(`${environment.api}/settings/systemFeatures`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getBasic(): Observable<SettingsResponse> {
    return this.http
      .get<HttpResponse<SettingsResponse>>(`${environment.api}/settings/basic`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getBorusanBlockedActions(): Observable<string[]> {
    return this.http
      .get<HttpResponse<string[]>>(`${environment.api}/borusan/blockedactions`)
      .pipe(
        map( (res) => {
          if (res.code === 0) {
            return res.data;
          }
          return null;
        })
      );
  }

  GetUserPermissions(): Observable<string[]> {
    return this.http
      .get<HttpResponse<string[]>>(`${environment.api}/permission/roles`)
      .pipe(
        map( (res) => {
          if (res.code === 0) {
            return res.data;
          }
          return null;
        })
      );
  }

}
