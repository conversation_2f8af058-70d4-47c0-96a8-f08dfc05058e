import { Injectable } from '@angular/core';
import { Store } from '@ngxs/store';
import {
  ChangeTokenAction,
  SetLoginResponseAction,
  UpdateLoadingAction,
  UpdateLoginStateAction
} from '../../authentication/action/login/login.actions';
import { IncomingMessageEnum } from '../enum/incoming-message.enum';
import {
  CommonStoreAction,
  CurrentLocationAction,
  FinishDownloadAction,
  PaymentFinishedAction,
  PermissionOkAction,
  SetAppStorageAction,
  ShowPermissionErrorAction,
  StartDownloadAction
} from '../state/common/common.actions';
import { LoginResponse } from '../../authentication/response/login.response';
import { CurrentLocationModel, CustomerRelationModel } from '../model/company.model';
import { Subject } from 'rxjs';
import { VideocallMessageService } from '../../live-support/service/videocall-message.service';
import { SystemFeature } from '../model/settings.model';
import { SetBorusanBlockedActions, SetSystemFeatureAction } from '../state/settings/settings.actions';
import { LoginType } from '../../authentication/enum/login-type.enum';

@Injectable({
  providedIn: 'root',
})
export class IncomingMessageService {
  private handlers: Subject<any>[] = [];

  constructor(
    private readonly store: Store,
    private readonly videocallMessageService: VideocallMessageService
  ) { }

  getHandlerFunction() {
    return this.handlePostMessage.bind(this);
  }

  handlePostMessage(e: MessageEvent) {
    console.log('RECEIVED message from WV ' + (e.data || e));
    try {
      if ((typeof e.data === 'string' && e.data?.startsWith('ASSISTBOX')) || (typeof e === 'object' && e.data?.event?.startsWith('ASSISTBOX'))) {
        console.log('Videocall message received from WV ' + JSON.stringify(e?.data));
        this.videocallMessageService.assistboxMessage(e?.data);
        return;
      }

      const event = typeof (e.data || e) === 'string'
        ? JSON.parse(e.data || e) : (e.data || e);

      if (!event.type) {
        return;
      }
      if (!(event.type in IncomingMessageEnum)) {
        console.log('event not found', event.type);
        return;
      }

      const that = this;
      if (typeof that[event.type] === 'function') {
        that[event.type](event.data);
      } else {
        console.log('ERROR INCOMING EVENT NOT FOUND: ' + event.type);
      }

      // external handler
      if (this.handlers[event.type]) {
        this.handlers[event.type].next(event.data);
      }
    } catch (err) {
      console.log('handle message error: ' + err.message, e.data || e);
    }
  }

  public subscribe(event, callback) {
    if (!this.handlers[event]) {
      this.handlers[event] = new Subject<string | object>();
    }
    return this.handlers[event].subscribe(callback);
  }

  permissionOk(data) {
    // 724 e post yolla
    this.store.dispatch(new PermissionOkAction(data));
  }

  showPermissionError(data) {
    this.store.dispatch(new ShowPermissionErrorAction(data));
  }

  refreshTokenChanged(data) {
    // Tokenı değiştir
    console.log('TOKEN', data);

    this.store.dispatch(new ChangeTokenAction(data));
  }

  mobileInfo(data: {
    version: string;
    timezone: string;
    userAgent: string;
    trackerUser: string;
    firebaseToken: string
    publicMenuHeaderCompany?: string
    publicmenuheadercompany?: string
    currentCustomer: CustomerRelationModel;
    currentRegion: string;
    countryCode: string;
    loginType: LoginType;
  }) {
    console.log('mobileIndo', data);
    this.version(data.version);
    this.timezone(data.timezone);
    this.userAgent(data.userAgent);
    this.firebaseToken(data.firebaseToken);
    this.publicMenuHeaderCompany(data.publicMenuHeaderCompany || data.publicmenuheadercompany);
    this.currentCustomer(data.currentCustomer);
    this.currentRegion(data.currentRegion);
    this.countryCode(data.countryCode);
    this.trackerUser(data.trackerUser);
    this.loginType(data.loginType);
  }

  currentCustomer(data) {
    this.store.dispatch(new CommonStoreAction({
      currentCustomer: data,
    }));
  }

  userAgent(data) {
    this.store.dispatch(new CommonStoreAction({
      userAgent: data,
    }));
  }

  timezone(data) {
    this.store.dispatch(new CommonStoreAction({
      timezoneId: data,
    }));
  }

  version(data) {
    this.store.dispatch(new CommonStoreAction({
      version: data,
    }));
  }

  firebaseToken(data) {
    this.store.dispatch(new CommonStoreAction({
      deviceToken: data,
    }));
  }

  trackerUser(data) {
    this.store.dispatch(new CommonStoreAction({
      trackerUser: data,
    }));
  }

  publicMenuHeaderCompany(data) {
    this.store.dispatch(new CommonStoreAction({
      publicMenuHeaderCompany: data,
    }));
  }

  currentRegion(data) {
    this.store.dispatch(new CommonStoreAction({
      currentRegion: data,
    }));
  }

  countryCode(data) {
    this.store.dispatch(new CommonStoreAction({
      countryCode: data,
    }));
  }

  loginStarted(data) {
    this.store.dispatch(new UpdateLoadingAction(true));
  }

  loginResponse(data: { success: boolean, loginResponse: LoginResponse }) {
    if (data.success) {
      return this.store.dispatch(new SetLoginResponseAction(data.loginResponse));
    }

    return this.store.dispatch(new UpdateLoadingAction(false));
  }

  appStorage(data) {
    return this.store.dispatch(new SetAppStorageAction(data));
  }

  currentLocation(data: CurrentLocationModel) {
    console.log('APP CurrentLocation: ', data);
    return this.store.dispatch(new CurrentLocationAction(data));
  }

  finishDownload(data: any) {
    return this.store.dispatch(new FinishDownloadAction(data));
  }

  startDownload(data: any) {
    return this.store.dispatch(new StartDownloadAction(data));
  }

  paymentFinished(data) {
    return this.store.dispatch(new PaymentFinishedAction(data));
  }

  webviewReopened(data: any) { }

  systemFeatures(systemFeatures: SystemFeature[]) {
    return this.store.dispatch(new SetSystemFeatureAction(systemFeatures));
  }

  borusanBlockedAction(borusanBlockedActions: string[]) {
    return this.store.dispatch(new SetBorusanBlockedActions(borusanBlockedActions));
  }

  private loginType(loginType: LoginType) {
    this.store.dispatch(new UpdateLoginStateAction({
      loginType
    }));
  }
}
