import { Injectable } from '@angular/core';
import { FrameMessageDataType, FrameMessageEnum } from '../enum/frame-message.enum';

@Injectable({
  providedIn: 'root',
})
export class MessageFrameService {
  constructor() {
  }

  /**
   * send message to top frame and App
   */
  public sendMessage<T extends FrameMessageEnum>(action: T, data: FrameMessageDataType<T> = null) {
    const message = {
      function: action,
      data,
    };

    const jMessage = JSON.stringify(message);
    console.log('SENT 724 postMessage: ' + jMessage);
    // console.log('window.self !== window.top', window.self !== window.top);

    if (window.self !== window.top) { //app serve ediyorsa
      window.parent.postMessage(jMessage, '*');
    } else {
      this.emitMessageToApp(jMessage);
    }
  }

  /**
   * Emit message to app
   */
  public emitMessageToApp(message) {
    const w = window as any;
    if (typeof w.<PERSON><PERSON>an !== 'undefined' && w.<PERSON><PERSON>?.postMessage) {
      console.log('(module) sent message to APP', message);
      w.Borusan.postMessage(message);
    } else if ((window as any).flutter_inappwebview) {
      (window as any).flutter_inappwebview.callHandler('Borusan', message)
        .then(result => {
          console.log('message delivered');
        });
    }

  }

  // public listenForMessage() {
  //   window.addEventListener('message', this.handleMessage, false);
  // }

  // public handleMessage(e) {
  //   console.log('angular received a message from top frame', e.data);
  // }
}
