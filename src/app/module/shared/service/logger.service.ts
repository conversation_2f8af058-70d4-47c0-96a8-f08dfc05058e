import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { LoggerDataModel } from '../model/logger-data.model';
import { HttpResponse } from '../../../response/http.response';
import { LoggerModel } from '../model/logger.model';
import { ConsoleErrorLoggerModel } from '../model/console-error-logger.model';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class LoggerService {
  constructor(
    private readonly http: HttpClient,
  ) {}

  public cateventlog(logData: LoggerDataModel) {
    return this.http.post<HttpResponse<LoggerModel>>(
      `${environment.logUrl}-waf`,
      logData,
      {
        headers: { TOKEN_FREE: 'true' }
      }
    );
  }

  public jsErrorLog(logData: ConsoleErrorLoggerModel) {
    return this.http.post<HttpResponse<LoggerModel>>(
      `${environment.logUrl}-js-console`,
      logData,
      {
        headers: { TOKEN_FREE: 'true' }
      }
    );
  }
}
