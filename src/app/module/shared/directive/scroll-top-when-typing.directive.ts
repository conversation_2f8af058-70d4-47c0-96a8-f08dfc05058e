import { AfterViewInit, Directive, Input, OnDestroy } from '@angular/core';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Subject } from 'rxjs';

@Directive({
  selector: '[catScrollTopWhenTyping]'
})
export class ScrollTopWhenTypingDirective implements AfterViewInit, OnDestroy {
  @Input() name;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly selectComponent: NgSelectComponent,
  ) {
  }

  ngAfterViewInit(): void {
    this.selectComponent.searchEvent
      .subscribe((term: string, items: any[]): void => {
        const toScrollTop = () => {
          const element = this.selectComponent?.element ? this.selectComponent.element.querySelector('ng-dropdown-panel div') : null;
          if (element) {
            element.scrollTop = 1;
          }
        };
        toScrollTop();

        setTimeout(toScrollTop, 100);
      });
  }


  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


}
