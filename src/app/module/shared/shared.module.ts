import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { ConnectDialogComponent } from './component/connect-dialog/connect-dialog.component';
import { BasicModalComponent } from './component/basic-modal/basic-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { AgreementModalComponent } from './component/agreement-modal/agreement-modal.component';
import { NgxLoadingModule } from 'ngx-loading';
import { EmptyContentComponent } from './component/empty-content/empty-content.component';
import { ErrorModalComponent } from './component/error-modal/error-modal.component';
import { ModalComponent } from './component/modal/modal.component';
import { CustomErrorHandlerService } from './custom-error.handler';
import { LoaderComponent } from './component/loader/loader.component';
import { SerialFormatPipe } from './pipe/serial-format.pipe';
import { WarningBoxComponent } from './component/warning-box/warning-box.component';
import { SuccessModalComponent } from './component/success-modal/success-modal.component';
import { InfoBoxComponent } from './component/info-box/info-box.component';
import { AgreementListComponent } from './component/agreement-list/agreement-list.component';
import { BigModalComponent } from './component/big-modal/big-modal.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MiniLoaderComponent } from './component/mini-loader/mini-loader.component';
import { SafeHtmlPipe } from './pipe/safe-html.pipe';
import { ErrorBoxComponent } from './component/error-box/error-box.component';
import { CurrenyIconPipe } from './pipe/curreny-icon.pipe';
import { InputMaxLengthDirective } from './directive/input-max-length.directive';
import { SortPipe } from './pipe/sort.pipe';
import { SafePipe } from './pipe/safe.pipe';
import { ScrollTopWhenTypingDirective } from './directive/scroll-top-when-typing.directive';
import { EquipmentRevisionCampaignComponent } from './component/equipment-revision-campaign/equipment-revision-campaign.component';
import { PullToRefreshComponent } from './component/pull-to-refresh/pull-to-refresh.component';
import { PullToRefreshModule } from '@piumaz/pull-to-refresh';
import { FullScreenModalComponent } from './component/fullscreen-modal/fullscreen-modal.component';
import { SkeletonLoaderComponent } from './component/skeleton-loader/skeleton-loader.component';
import { DotLoaderComponent } from './component/dot-loader/dot-loader/dot-loader.component';
import { HasPermissionsModule } from 'src/app/export/permissions/has-permissions.module';
import { ContactYourRepresentativeModalComponent } from './component/contact-your-representative-modal/contact-your-representative-modal.component';
import { OrderAgreementListComponent } from './component/order-agreement-list/order-agreement-list.component';
import { MarkdownHtmlPipe } from './pipe/markdown-html.pipe';
import { MarkdownSmallHtmlPipe } from './pipe/markdown-small-html.pipe';

@NgModule({
  declarations: [
    ConnectDialogComponent,
    BasicModalComponent,
    AgreementModalComponent,
    EmptyContentComponent,
    ErrorModalComponent,
    ModalComponent,
    LoaderComponent,
    SerialFormatPipe,
    WarningBoxComponent,
    SuccessModalComponent,
    InfoBoxComponent,
    AgreementListComponent,
    BigModalComponent,
    FullScreenModalComponent,
    MiniLoaderComponent,
    SafeHtmlPipe,
    MarkdownHtmlPipe,
    MarkdownSmallHtmlPipe,
    ErrorBoxComponent,
    CurrenyIconPipe,
    InputMaxLengthDirective,
    SortPipe,
    SafePipe,
    EquipmentRevisionCampaignComponent,
    ScrollTopWhenTypingDirective,
    PullToRefreshComponent,
    SkeletonLoaderComponent,
    DotLoaderComponent,
    ContactYourRepresentativeModalComponent,
    OrderAgreementListComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    NgxLoadingModule,
    ReactiveFormsModule,
    PullToRefreshModule,
    HasPermissionsModule
  ],
  exports: [
    ConnectDialogComponent,
    BasicModalComponent,
    AgreementModalComponent,
    EmptyContentComponent,
    ErrorModalComponent,
    LoaderComponent,
    SerialFormatPipe,
    WarningBoxComponent,
    InfoBoxComponent,
    AgreementListComponent,
    BigModalComponent,
    FullScreenModalComponent,
    ModalComponent,
    MiniLoaderComponent,
    SafeHtmlPipe,
    ErrorBoxComponent,
    CurrenyIconPipe,
    InputMaxLengthDirective,
    SortPipe,
    SafePipe,
    MarkdownHtmlPipe,
    MarkdownSmallHtmlPipe,
    EquipmentRevisionCampaignComponent,
    ScrollTopWhenTypingDirective,
    SuccessModalComponent,
    PullToRefreshComponent,
    SkeletonLoaderComponent,
    DotLoaderComponent,
    ContactYourRepresentativeModalComponent,
    OrderAgreementListComponent
  ],
  providers: [
    {
      provide: ErrorHandler,
      useClass: CustomErrorHandlerService,
    },
  ],
})
export class SharedModule {}
