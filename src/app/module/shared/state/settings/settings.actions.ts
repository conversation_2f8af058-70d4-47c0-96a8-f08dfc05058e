import { SettingsStateModel } from './settings.state';
import { SystemFeature } from '../../model/settings.model';

export class ContactWorkingHoursAction {
  public static readonly type = '[Common] contactWorkingHours';

  constructor() { }
}

export class SocialMediaAccountsAction {
  public static readonly type = '[Common] socialMedias';

  constructor() { }
}


export class SystemFeatureAction {
  public static readonly type = '[Common] systemFeature';

  constructor() {}
}

export class SetSystemFeatureAction {
  public static readonly type = '[Common] set systemFeature';

  constructor(public setSystemFeatures: SystemFeature[]) {}
}

export class BorusanBlockedActionsAction {
  public static readonly type = '[Common] Borusan Blocked Actions';
  constructor() {}
}

export class SetBorusanBlockedActions {
  public static readonly type = '[Common] Set Borusan Blocked Actions';
  constructor(public actions: string[]) {}
}

export class UserPermissionAction {
  public static readonly type = '[Common] User Permissions Actions';
  constructor() {}
}

export class GetMyUsersAction {
  public static readonly type = '[Common] myUsers';

  constructor() { }
}

export class UpdateMyUsersStateAction {
  public static readonly type = '[Common] update myUsers';

  constructor(public state: Partial<SettingsStateModel>) { }
}

export class GetBasisSettingsAction {
  public static readonly type = '[Common] basicSettings';

  constructor() { }
}
