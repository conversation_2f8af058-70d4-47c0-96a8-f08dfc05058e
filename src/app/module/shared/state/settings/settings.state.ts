import { Action, Selector, State, StateContext } from '@ngxs/store';
import {
  BorusanBlockedActionsAction,
  GetBasisSettingsAction,
  UserPermissionAction,
  SetBorusanBlockedActions,
  SetSystemFeatureAction,
  SystemFeatureAction
} from './settings.actions';
import { SettingsService } from '../../service/settings.service';
import { Injectable } from '@angular/core';
import { SettingsResponse, SystemFeature } from '../../model/settings.model';


export interface SettingsStateModel {
  systemFeatures: SystemFeature[];
  systemFeaturesLoading: boolean;
  basic: SettingsResponse;
  borusanBlockedActions: string[];
  userPermission: string[];
}

@State<SettingsStateModel>({
  name: 'settings',
  defaults: {
    systemFeatures: [],
    systemFeaturesLoading: false,
    basic: null,
    borusanBlockedActions: null,
    userPermission: null
  }
})
@Injectable()
export class SettingsState {

  constructor(
    private readonly settingsService: SettingsService,
  ) {
  }

  @Selector()
  public static systemFeatures(
    { systemFeatures }: SettingsStateModel): SystemFeature[] {
    return systemFeatures;
  }

  @Selector()
  public static systemFeaturesLoading(
    { systemFeaturesLoading }: SettingsStateModel): boolean {
    return systemFeaturesLoading;
  }

  @Selector()
  public static basic(
    { basic }: SettingsStateModel): SettingsResponse {
    return basic;
  }

  @Selector()
  public static borusanBlockedActions(
    { borusanBlockedActions }: SettingsStateModel): string[] {
    return borusanBlockedActions;
  }

  @Selector()
  public static userPermission(
    { userPermission }: SettingsStateModel): string[] {
    return userPermission;
  }

  @Action(SystemFeatureAction)
  public systemFeature(
    { patchState }: StateContext<SettingsStateModel>) {

    patchState({
      systemFeaturesLoading: true,
    });

    this.settingsService.systemFeatures().subscribe(systemFeatures => {
        return patchState({
          systemFeatures,
          systemFeaturesLoading: false,
        });
      },
      () => {
        patchState({
          systemFeaturesLoading: false,
        });
      });
  }

  @Action(SetSystemFeatureAction)
  setSystemFeature(
    { patchState }: StateContext<SettingsStateModel>,
    { setSystemFeatures }: SetSystemFeatureAction,
  ) {
    patchState({
      systemFeatures: setSystemFeatures,
    });
  }

  @Action(GetBasisSettingsAction)
  public GetBasisSettingsAction(
    { patchState, getState }: StateContext<SettingsStateModel>,
  ) {
    if(!getState().basic) {
      this.settingsService.getBasic().subscribe(basic => {
        return patchState({
          basic
        });
      });
    }
  }

  @Action(BorusanBlockedActionsAction)
  public BorusanBlockedActionsAction(
    { patchState }: StateContext<SettingsStateModel>,
  ) {
    this.settingsService.getBorusanBlockedActions().subscribe(res => {
      return patchState({
        borusanBlockedActions: res,
      });
    });
  }
  @Action(SetBorusanBlockedActions)
  public SetBorusanBlockedActionsAction(
    { patchState }: StateContext<SettingsStateModel>,
    { actions }: SetBorusanBlockedActions
  ) {
      return patchState({
        borusanBlockedActions: actions,
      });
  }
  @Action(UserPermissionAction)
  public userPermissionAction(
    { patchState }: StateContext<SettingsStateModel>,
  ) {
    this.settingsService.GetUserPermissions().subscribe((res: any) => {
      return patchState({
        userPermission: res.permissions
      });
    });
  }

}
