import { AppStorageModel, CommonStateModel, SessionStorageModel } from './common.state';
import { CurrentLocationModel } from '../../model/company.model';

export class CommonStoreAction {
  public static readonly type = '[Common] set state';

  constructor(public payload: Partial<CommonStateModel>) { }
}

export class UpdateAppStorageAction {
  public static readonly type = '[Common] update App storage';

  constructor(public appStorage: Partial<AppStorageModel>) { }
}
export class SetAppStorageAction {
  public static readonly type = '[Common] set App storage';

  constructor(public appStorage: AppStorageModel) { }
}
export class PermissionOkAction {
  public static readonly type = '[Common] permissionOk';

  constructor(public data: any) { }
}
export class ShowPermissionErrorAction {
  public static readonly type = '[Common] showPermissionError';

  constructor(public data: any) { }
}

export class CurrentLocationAction {
  public static readonly type = '[Common] Current Location';

  constructor(public data: CurrentLocationModel) { }
}

export class FinishDownloadAction {
  public static readonly type = '[Common] finish download';
  constructor(public data: CurrentLocationModel) { }
}

export class StartDownloadAction {
  public static readonly type = '[Common] start download';
  constructor(public data: CurrentLocationModel) { }
}

export class CurrentRegionAction {
  public static readonly type = '[Common] current region';
  constructor(public data: any) { }
}

export class CountryCodeAction {
  public static readonly type = '[Common] country code';
  constructor(public data: any) { }
}

export class PaymentFinishedAction {
  public static readonly type = '[Common] payment finished';
  constructor(public data: any) { }
}

export class PaymentFinishedRemoveAction {
  public static readonly type = '[Common] payment finished remove';
  constructor() { }
}

export class PageOpenedAction {
  public static readonly type = '[Common] page opened';
  constructor(public data: any) { }
}

export class PageOpenedClearAction {
  public static readonly type = '[Common] page opened clear';
  constructor() { }
}
