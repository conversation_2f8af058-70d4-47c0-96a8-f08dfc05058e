import { Action, Selector, State, StateContext } from '@ngxs/store';
import {
  CommonStoreAction,
  CountryCodeAction,
  CurrentLocationAction,
  CurrentRegionAction,
  FinishDownloadAction,
  PaymentFinishedAction,
  PaymentFinishedRemoveAction,
  PermissionOkAction,
  SetAppStorageAction,
  ShowPermissionErrorAction,
  StartDownloadAction,
  UpdateAppStorageAction,
  PageOpenedAction,
  PageOpenedClearAction
} from './common.actions';
import { Injectable } from '@angular/core';
import { MessageFrameService } from '../../service/message-frame.service';
import { FrameMessageEnum } from '../../enum/frame-message.enum';
import { CurrentLocationModel, CustomerRelationModel } from '../../model/company.model';

export interface AppStorageModel {
  serviceWarningShowed: string[];
}

export interface SessionStorageModel {
  serviceWarningShowed: string[];
}
export interface TrackerUserModel {
  uuid: string;
}

export interface CommonStateModel {
  userAgent: string;
  timezoneId: string;
  version: string;
  deviceToken: string;
  trackerUser: TrackerUserModel;
  appStorage: AppStorageModel;
  serviceWarningShowed: string[];
  currentCustomer: CustomerRelationModel;
  permissionOk: any;
  showPermissionError: any;
  publicMenuHeaderCompany: any;
  currentLocation: CurrentLocationModel;
  startDownload: any;
  finishDownload: any;
  currentRegion: any;
  countryCode: any;
  paymentFinished: {
    quotationNumber?: string;
    isPaymentClosedFromUser: boolean;
    isPaymentFinish: boolean;
    paymentWithError?: boolean;
  };
  openPage: string;
}

@State<CommonStateModel>({
  name: 'common',
  defaults: {
    userAgent: null,
    timezoneId: null,
    version: null,
    deviceToken: null,
    trackerUser: null,
    appStorage: null,
    serviceWarningShowed: [],
    currentCustomer: null,
    permissionOk: null,
    showPermissionError: null,
    publicMenuHeaderCompany: null,
    currentLocation: null,
    startDownload: null,
    finishDownload: null,
    currentRegion: null,
    countryCode: null,
    paymentFinished: null,
    openPage: null,
  },
})
@Injectable()
export class CommonState {
  constructor(private readonly messageFrameService: MessageFrameService) { }

  @Selector()
  public static getState(state: CommonStateModel) {
    return state;
  }

  @Selector()
  public static userAgent({ userAgent }: CommonStateModel): string {
    return userAgent;
  }

  @Selector()
  public static timezoneId({ timezoneId }: CommonStateModel): string {
    return timezoneId;
  }

  @Selector()
  public static version({ version }: CommonStateModel): string {
    return version;
  }

  @Selector()
  public static deviceToken({ deviceToken }: CommonStateModel): string {
    return deviceToken;
  }
  @Selector()
  public static trackerUser({ trackerUser }: CommonStateModel): TrackerUserModel {
    return trackerUser;
  }

  @Selector()
  public static publicMenuHeaderCompany({ publicMenuHeaderCompany }: CommonStateModel): string {
    return publicMenuHeaderCompany;
  }

  @Selector()
  public static appStorage({ appStorage }: CommonStateModel): AppStorageModel {
    return appStorage;
  }

  @Selector()
  public static permissionOk({ permissionOk }: CommonStateModel): any {
    return permissionOk;
  }
  @Selector()
  public static showPermissionError({ showPermissionError }: CommonStateModel): any {
    return showPermissionError;
  }

  @Selector()
  public static serviceWarningShowed({
    serviceWarningShowed,
  }: CommonStateModel): string[] {
    return serviceWarningShowed;
  }

  @Selector()
  public static currentCustomer({ currentCustomer }: CommonStateModel): CustomerRelationModel {
    return currentCustomer;
  }

  @Selector()
  public static currentLocation({ currentLocation }: CommonStateModel): {} {
    return currentLocation;
  }

  @Selector()
  public static startDownload({ startDownload }: CommonStateModel): {} {
    return startDownload;
  }

  @Selector()
  public static finishDownload({ finishDownload }: CommonStateModel): {} {
    return finishDownload;
  }

  @Selector()
  public static currentRegion({ currentRegion }: CommonStateModel): {} {
    return currentRegion;
  }

  @Selector()
  public static countryCode({ countryCode }: CommonStateModel): {} {
    return countryCode;
  }

  @Selector()
  public static paymentFinished({ paymentFinished }: CommonStateModel): {} {
    return paymentFinished;
  }

  @Action(CommonStoreAction)
  public add(
    { patchState }: StateContext<CommonStateModel>,
    { payload }: CommonStoreAction
  ) {
    patchState(payload);
  }

  @Action(SetAppStorageAction)
  public setAppStorage(
    { getState, patchState }: StateContext<CommonStateModel>,
    { appStorage }: SetAppStorageAction
  ) {
    patchState({
      appStorage,
    });
  }

  @Action(UpdateAppStorageAction)
  public updateAppStorage(
    { getState, patchState }: StateContext<CommonStateModel>,
    { appStorage }: UpdateAppStorageAction
  ) {
    const storage = { ...getState().appStorage, ...appStorage };
    patchState({
      appStorage: storage,
    });
    this.messageFrameService.sendMessage(FrameMessageEnum.appStorage, {
      ...getState().appStorage,
    });
  }

  @Action(PermissionOkAction)
  public permissionOk(
    { patchState }: StateContext<CommonStateModel>,
    { data }: PermissionOkAction
  ) {
    patchState({
      permissionOk: data,
    });
  }

  @Action(ShowPermissionErrorAction)
  public showPermissionError(
    { patchState }: StateContext<CommonStateModel>,
    { data }: ShowPermissionErrorAction
  ) {
    patchState({
      showPermissionError: data,
    });
  }

  @Action(CurrentLocationAction)
  public currentLocation(
    { patchState }: StateContext<CommonStateModel>,
    { data }: CurrentLocationAction
  ) {
    patchState({
      currentLocation: data,
    });
  }

  @Action(FinishDownloadAction)
  public FinishDownloadAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: FinishDownloadAction
  ) {
    patchState({
      finishDownload: data,
    });
  }

  @Action(StartDownloadAction)
  public StartDownloadAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: StartDownloadAction
  ) {
    patchState({
      startDownload: data,
    });
  }

  @Action(CurrentRegionAction)
  public CurrentRegionAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: CurrentRegionAction
  ) {
    patchState({
      currentRegion: data,
    });
  }

  @Action(CountryCodeAction)
  public CountryCodeAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: CountryCodeAction
  ) {
    patchState({
      countryCode: data,
    });
  }

  @Action(PaymentFinishedAction)
  public PaymentFinishedAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: PaymentFinishedAction
  ) {
    patchState({
      paymentFinished: data,
    });
  }

  @Action(PaymentFinishedRemoveAction)
  public PaymentFinishedRemoveAction(
    { patchState }: StateContext<CommonStateModel>
  ) {
    patchState({
      paymentFinished: null,
    });
  }

  @Action(PageOpenedClearAction)
  public OpenPageClear(
    { patchState }: StateContext<CommonStateModel>
    ) {
      patchState({
        openPage: null,
      });
    }

  @Action(PageOpenedAction)
  public OpenPage(
    { patchState }: StateContext<CommonStateModel>,
    { data }: PageOpenedAction
  ) {
    this.messageFrameService.sendMessage(FrameMessageEnum.pageOpened, {page: data});
    patchState({
      openPage: data,
    });
  }

}

