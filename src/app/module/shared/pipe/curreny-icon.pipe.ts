import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'currenyIcon'
})
export class CurrenyIconPipe implements PipeTransform {

  transform(value: string): string {
    const currenyIcons = {
      TL: 'icon-tl',
      USD: 'icon-usd',
      EUR: 'icon-euro',
      RUBLE: 'icon-ruble',
      TENGE: 'icon-tenge',
      MANAT: 'icon-manat',
      LARI: 'icon-lari',
    };
    return currenyIcons[value] ? currenyIcons[value] : currenyIcons.USD;
  }

}
