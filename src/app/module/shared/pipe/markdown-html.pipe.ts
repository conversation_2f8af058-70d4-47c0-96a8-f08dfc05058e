import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { convertMarkdownToHtml } from '../../../util/mardown-to-html.util';

@Pipe({
  name: 'markdownHtml'
})
export class MarkdownHtmlPipe implements PipeTransform {

  constructor(protected sanitizer: DomSanitizer) {}

  public transform(value: any): any {
    return convertMarkdownToHtml(value);

  }
}
