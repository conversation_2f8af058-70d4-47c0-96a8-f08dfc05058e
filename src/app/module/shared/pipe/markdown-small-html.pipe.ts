import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { convertMarkdownToHtml } from '../../../util/mardown-to-html.util';
import { convertMarkdownToSmallHtml } from '../../../util/mardown-to-small-html.util';

@Pipe({
  name: 'markdownSmallHtml'
})
export class MarkdownSmallHtmlPipe implements PipeTransform {

  constructor(protected sanitizer: DomSanitizer) {}

  public transform(value: any): any {
    return convertMarkdownToSmallHtml(value);

  }
}
