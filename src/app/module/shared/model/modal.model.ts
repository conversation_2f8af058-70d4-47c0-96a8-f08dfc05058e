import { ComponentRef } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { ModalComponent } from '../component/modal/modal.component';

export class ModalRef {
  private result$ = new Subject<any>();

  constructor(
    private modalContainer: ComponentRef<ModalComponent>,
    private modal: ComponentRef<Modal>
  ) {
    this.modal.instance.modalInstance = this;
    this.modal.instance?.disableBack();
  }

  close(output: any): void {
    this.result$.next(output);
    this.destroy$();
  }

  dismiss(output: any): void {
    this.result$.error(output);
    this.destroy$();
  }

  onResult(): Observable<any> {
    return this.result$.asObservable();
  }

  private destroy$(): void {
    this.modal.destroy();
    this.modalContainer.destroy();
    this.result$.complete();
  }
}

export abstract class Modal {
  modalInstance: ModalRef;

  abstract onInjectInputs(inputs: any): void;

  close(output?: any): void {
    this.modalInstance?.close(output);
  }

  dismiss(output?: any): void {
    this.modalInstance?.dismiss(output);
  }

  disableBack() {
  }
}
