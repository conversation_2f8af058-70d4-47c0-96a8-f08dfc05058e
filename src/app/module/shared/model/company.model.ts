import { CompanyModel } from '../../company/model/company.model';
import { CustomerModel } from '../../customer/model/customer.model';

export interface CustomerRelationModel {
  cityName: string;
  groupKey: string;
  groupTitle: string;
  crmRelatedPersonId: string;
  publicMenuHeaderCompany: string;
  customer: CustomerModel;
  companies: CompanyModel[];
  modules: any[];
  roleList?: any[];
  countryCode?: string;
}


export interface CurrentLocationModel {
  latitude: number;
  longitude: number;
  accuracy: number;
}

