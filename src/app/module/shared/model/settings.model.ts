export interface SettingsResponse {
  hideWorkingHoursOlderThanDays: number;
  passwordPolicies: PasswordPolicies[];
  cdnBaseUrl: string;
  defaults: any;
  emailRegex: string;
  firstUseUrl: string;
  passwordRegex: string;
  twoFactorAuthenticationRegex: string;
  serviceLocationRefreshTime: any;
  formParametersMaxCharacterLimit: any;
}

export interface PasswordPolicies {
  regex: string;
  description: string;
}

export interface SystemFeature {
  code: string;
  name: string;
  isEnabled: boolean;
}

export interface SocialMediaModel {
  name: string;
  url: string;
}
