import { animate, state, style, transition, trigger } from '@angular/animations';

export const modalAnimations =
  [
    trigger('modal', [
      state('void', style({ top: '20%', opacity: 0 })),
      state('*', style({ top: '7%', opacity: 1 })),
      transition('* => *', [animate('.05s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: .4 })),
      transition('* => *', [animate('.05s')]),
    ])
  ];


