@import "variable/bootstrap-variable";

.service-detail{
  padding: 20px;
  padding-bottom: 45px;
  height: 100vh;
  background-color:  #FAFAFAFA;

  .service-information-content{
      flex: 1;

      .service-request-card{
          background-color: #fff;
          margin-top: 1.5rem;
          border-radius: .5rem;
          position: relative;

          &-color {
            position: absolute;
            width: 10px;
            height: 100%;
            left: 0;
            top: 0;
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
            z-index: 1;

            &-green {
              background: #5d8d1c;
            }

            &-orange {
              background: $warning;
            }
          }
      }


      .two-dots {
          padding-left: 25px;

          &:before {
              display: inline-block;
              content: ":";
              margin-left: -25px;
              padding-left: 10px;
              padding-right: 10px;
          }
      }

      .work-order-attachments{
          padding: 22px;
          padding-bottom: 100px;
      }
  }
}

.header{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}


.download-file{
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
}


