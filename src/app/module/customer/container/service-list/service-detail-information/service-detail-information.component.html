<div class="service-detail">
  <div class="service-information-content">
    <header class="header">
      <div>
        <a (click)="navigateToBack()">
          <i class="icon icon-back"></i>
        </a>
      </div>
      <div class="h5 mb-0 text-center">
        {{ "_service_detail" | translate }}
      </div>
    </header>
    <div class="service-request-card">
      <div
        [style.background-color]="serviceDetail?.serviceStatusColor"
        class="service-request-card-color"></div>

      <div class="p-4">

        <div>
          <div class="d-flex align-items-center justify-content-between mb-2 overflow-hidden">
            <div class="font-weight-semi-bold h6 mb-0">
              {{ serviceDetail?.model || ("_component" | translate) }}
            </div>
            <div
              [style.color]="serviceDetail?.serviceStatusColor"
              class="font-weight-semi-bold"
            >
              {{ this.getStatusLabel(serviceDetail) | translate }}
            </div>
          </div>
          <div class="row no-gutters">
            <div class="col-5">
              {{ "_service_number" | translate }}
            </div>
            <div class="col-7 two-dots">
              {{ serviceDetail?.serviceNumber || "-" }}
            </div>
          </div>
          <div class="row no-gutters">
            <div class="col-5">{{ "_machine_serial_number" | translate }}</div>
            <div class="col-7 two-dots">
              {{
                (serviceDetail?.equipmentSerialNumber | serialFormat) || "-"
              }}
            </div>
          </div>
          <ng-container *ngIf="!serviceDetail?.isCrc">
            <div
              [ngSwitch]="serviceDetail?.workOrderStatus?.serviceApplication"
              class="row no-gutters"
            >
              <div *ngSwitchCase="'Cdoms'" class="col-5">
                {{ "_acceptance_date" | translate }}
              </div>
              <div *ngSwitchCase="'Weking'" class="col-5">
                {{ "_planned_date" | translate }}
              </div>
              <div *ngSwitchCase="'Sim'" class="col-5">
                {{ "_start_date" | translate }}
              </div>
              <div *ngSwitchCase="'WO'" class="col-5">
                {{ "_request_date" | translate }}
              </div>
              <div *ngSwitchDefault class="col-5">
                {{ "_request_date" | translate }}
              </div>
              <div class="col-7 two-dots">
                {{ serviceDetail?.startDate | date : "dd.MM.yyyy" || "-" }}
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="serviceDetail?.isCrc">
            <div class="row no-gutters">
              <div class="col-5">
                {{
                  (serviceDetail?.workOrderStatus?.endDate
                    ? "_dispatch_date"
                    : "_acceptance_date"
                  ) | translate
                }}
              </div>
              <div class="col-7 two-dots">
                {{
                  (serviceDetail?.workOrderStatus?.endDate
                    ? serviceDetail?.workOrderStatus?.endDate
                    : serviceDetail?.startDate
                  ) | date : "dd.MM.yyyy" || "-"
                }}
              </div>
            </div>
          </ng-container>
        </div>

        <div *ngIf="serviceDetail?.workOrderStatus">
          <div
            class="row no-gutters"
            *ngIf="serviceDetail?.workOrderStatus?.currentStatusName"
          >
            <div class="col-5">{{ "_status" | translate }}</div>
            <div class="col-7 two-dots">
              {{ serviceDetail?.workOrderStatus?.currentStatusName }}
            </div>
          </div>
        </div>

        <!-- ? Service Location Tracking -->
        <div
          *ngIf="
            serviceDetail?.workOrderStatus?.hasArventoNode ||
            serviceDetail?.workOrderStatus?.servicePlateNumber
          "
        >
          <div class="row no-gutters">
            <div class="col-5">{{ "_service_car_plate" | translate }}</div>
            <div class="col-7 two-dots d-flex">
              <div class="flex-grow-1">
                <div>
                  {{
                    serviceDetail?.workOrderStatus?.servicePlateNumber || "-"
                  }}
                </div>
                <div
                  catUserClick
                  [section]="'SERVICE'"
                  [subsection]="'SERVICE_MAP'"
                  [data]="{ serviceNumber: serviceDetail?.serviceNumber }"
                  (click)="onMap(serviceDetail)"
                  *ngIf="
                    serviceDetail?.workOrderStatus?.hasArventoNode &&
                    serviceLocationShow
                  "
                  class="text-success"
                  style="word-wrap: break-word"
                >
                  {{ "_service_map_location_tracking" | translate }}
                </div>
              </div>
              <div
                catUserClick
                [section]="'SERVICE'"
                [subsection]="'SERVICE_MAP'"
                [data]="{ serviceNumber: serviceDetail?.serviceNumber }"
                (click)="onMap(serviceDetail)"
                *ngIf="
                  serviceDetail?.workOrderStatus?.hasArventoNode &&
                  serviceLocationShow
                "
                class="d-flex justify-content-center align-items-center"
              >
                <div class="align-items-center">
                  <img height="26" [src]="mapLocation" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="work-order-attachments">
      <div class="h6">{{ "_description" | translate }}</div>
      <div class="font-size-14px" style="word-break: break-all">
        {{ serviceDetail?.description || "-" }}
      </div>
      <div *ngIf="serviceDetail?.workOrderStatus">
        <div
          class="pt-2"
          *ngIf="serviceDetail?.workOrderStatus?.attachments.length > 0"
        >
          <div *ngFor="let attachment of getAttachments(serviceDetail)">
            <div class="row no-gutters d-flex align-items-center py-2">
              <div
                [id]="attachment.id"
                catUserClick
                [section]="'SERVICE_LIST'"
                [subsection]="'ATTACHMENT_DOWNLOAD'"
                [data]="{ attachmentId: attachment.id }"
                catDownloadFile
                class="row m-0"
                [downloadType]="DownloadTypeEnum.attachment"
                [downloadParams]="{
                  attachment: attachment,
                  serviceOrganization: serviceDetail?.serviceOrganization,
                  workOrderNumber:
                    serviceDetail?.workOrderStatus?.workOrderNumber
                }"
                (downloadLoading)="downloading($event)"
              >
                <div
                  class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                  [class.spinner]="isDownloading(attachment.id)"
                >
                  <i
                    class="icon icon-spinner8"
                    *ngIf="isDownloading(attachment.id)"
                  ></i>
                  <i
                    class="icon icon-download"
                    *ngIf="!isDownloading(attachment.id)"
                  ></i>
                  <a class="d-none" [download]="attachment.id"></a>
                </div>
                <div>
                  <div
                    class="attachment-name text-info ml-3 font-size-14px font-weight-bold"
                  >
                     {{ serviceAttachmentKey(attachment) | translate }}
                  </div>
                  <div
                    *ngIf="attachment.dateUpload"
                    class="text-muted font-size-12px ml-3"
                  >
                    {{
                      customDate(attachment.dateUpload) | date : "shortDate"
                    }}
                  </div>
                </div>
              </div>
              <div
                class="ml-auto"
                *ngIf="isDownloading(attachment.id) && cancelDownloadVersion"
                (click)="cancelDownloadFile(attachment.id)"
              >
                <i class="icon icon-x"></i>
              </div>
            </div>
          </div>
          <div
            (click)="lessMoreFunc(serviceDetail?.serviceNumber)"
            *ngIf="serviceDetail?.workOrderStatus?.attachments?.length > 5"
            class="mt-1 text-center"
          >
            <div class="font-weight-medium">
              {{ (loadMore[serviceDetail?.serviceNumber] ? "_less" : "_more") | translate }}
            </div>
          </div>
        </div>
        <div class="pt-2 border-top" *ngIf="serviceDetail?.surveys as survey">
          <div
            catUserClick
            [section]="'SERVICE_LIST'"
            [subsection]="'SURVEY_CLICK'"
            [data]="{ surveyId: survey.id }"
            class="row no-gutters d-flex align-items-center py-2"
            (click)="openSurvey(survey)"
          >
            <div
              class="survey border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
            >
              <i class="icon icon-contract"></i>
            </div>

            <div
              class="survey-name text-info ml-3 font-size-14px font-weight-bold"
            >
              {{ "_service_survey" | translate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
