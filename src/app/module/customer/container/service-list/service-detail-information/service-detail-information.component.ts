import { Component, OnInit, AfterViewInit, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { StartDownloadAction } from 'src/app/module/shared/state/common/common.actions';
import { environment } from 'src/environments/environment';
import { DefinitionService } from 'src/app/module/definition/service/definition.service';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import * as moment from 'moment';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { ServiceDetailedRequestAttachment, ServiceDetailedRequestModel, Survey } from '../../../model/service-request.model';
import { getServiceAttachmentKey } from '../../../../../util/service-attachment.util';

@Component({
  selector: 'cat-service-detail-information',
  templateUrl: './service-detail-information.component.html',
  styleUrls: ['./service-detail-information.component.scss']
})
export class ServiceDetailInformationComponent implements OnInit {
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  protected subscriptions$: Subject<boolean> = new Subject();

  @Input()
  serviceDetail: ServiceDetailedRequestModel;

  mapLocation = `${environment.assets}/map_location.png`;
  warningIcon = `${environment.assets}/warning.svg`;
  getFormErrorMessage = getFormErrorMessage;

  loadMore: any = {};
  loadingAttachment: any[] = [];
  DownloadTypeEnum = DownloadTypeEnum;
  serviceLocationShow = true;
  featureSoundDiagnostics = false;
  expertiseShow: boolean;
  cancelDownloadVersion = false;

  constructor(
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly definitionService: DefinitionService,
  ) { }

  ngOnInit() {
    this.cancelDownloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          this.featureSoundDiagnostics = systemFeature('sound_diagnostics', features, true);
          this.expertiseShow = systemFeature('inspection_forms', features, true);
          this.serviceLocationShow = systemFeature('service_location_tracking', features, true);
        }
    });
  }

  openSurvey(survey: Survey) {
    this.frameService.sendMessage(FrameMessageEnum.openSurvey, survey);
  }

  getAttachments(service: ServiceDetailedRequestModel) {
    return service.workOrderStatus.attachments.length <= 5
    || this.loadMore[service.serviceNumber] ?
      service.workOrderStatus.attachments : service.workOrderStatus.attachments.slice(0, 3);
  }

  lessMoreFunc(serviceNumber: any) {
    this.loadMore[serviceNumber] = !this.loadMore[serviceNumber];
    if (!this.loadMore[serviceNumber]) {
      this.loadingAttachment = [];
    }
  }

  downloading(attachment) {
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  getStatusLabel(serviceRequest: ServiceDetailedRequestModel) {
    switch (serviceRequest?.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
  }

  onMap(serviceRequest: ServiceDetailedRequestModel) {
    this.definitionService
      .checkWorkingHours(serviceRequest.serviceOrganization)
      .subscribe((res) => {
        if (res.isWorkingHour) {
          this.router
            .navigate(
              [
                '/',
                ...environment.rootUrl.split('/'),
                'customer',
                'service-list',
                'map',
                serviceRequest.serviceNumber,
              ],
              {
                queryParams: {
                  ...this.route.snapshot.queryParams,
                },
              },
            )
            .then();
        }
      });
  }

  customDate(value: string) {
    // ? Weking Gelen Tarih Formatı 09.12.2021 11:20
    // ? Cdoms Gelen Tarih Formatı 20210903111723
    // ? KZ icin gelen format 20211803111723 !!IPTAL

    let date = moment(value, 'DD.MM.YYYY HH:mm');
    if (date.isValid()) {
      return date.toISOString();
    }
    // date = moment(value, this.company?.countryCode === 'KZ' ?
    //   'YYYYDDMMHHmmss' : 'YYYYMMDDHHmmss');
    date = moment(value, 'YYYYMMDDHHmmss');
    if (date.isValid()) {
      return date.toISOString();
    }

    return '';
  }

  navigateToBack() {
    window.history.back();
  }


  serviceAttachmentKey(attachment: ServiceDetailedRequestAttachment) {
    if (attachment.uploadTypeDescription === 'SERVICE_FORM') {
      return '_service_form';
    }

    return getServiceAttachmentKey(attachment.uploadType);
  }
}
