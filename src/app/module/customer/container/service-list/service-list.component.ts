import { After<PERSON>onte<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HostListener, OnChanges, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { LoginState } from '../../../authentication/state/login/login.state';
import { ServiceDetailedRequestAttachment, ServiceDetailedRequestModel, Survey } from '../../model/service-request.model';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { DefinitionService } from 'src/app/module/definition/service/definition.service';
import { PagingModel } from '../../../definition/model/paging.model';
import { DownloadTypeEnum } from '../../../../export/media/enum/download-type.enum';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { SettingsState } from '../../../shared/state/settings/settings.state';
import { SystemFeature } from '../../../shared/model/settings.model';
import { systemFeature } from '../../../shared/util/system-feature.util';
import { SystemFeatureAction } from '../../../shared/state/settings/settings.actions';
import { filter, skip, take, takeUntil } from 'rxjs/operators';
import { AppState } from '../../../../state/app/app.state';
import * as moment from 'moment';
import { CompanyModel } from '../../../company/model/company.model';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { TranslateService } from '@ngx-translate/core';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { PageOpenedAction, StartDownloadAction } from 'src/app/module/shared/state/common/common.actions';
import { GetServiceDetailedListAction, GetWorkOrderFilterFieldsAction } from '../../action/service.action';
import { ServiceState } from '../../state/service.state';
import { FilterFieldsModel, FilterItemModel } from '../../model/filter.model';
import { FilterFieldModelEnum, FilterModalComponent } from '../../component/filter-modal/filter-modal.component';
import { clearUrlParamsOnHistory, replaceHistoryState } from '../../../../util/history.util';
import { PagesEnum } from 'src/app/module/shared/enum/pages.enum';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { getServiceAttachmentKey } from '../../../../util/service-attachment.util';

@Component({
  selector: 'cat-service-list',
  templateUrl: './service-list.component.html',
  styleUrls: ['./service-list.component.scss'],
  animations: [
    // trigger('detail', [
    //   state('void', style({ opacity: 0, height: 0 })),
    //   state('*', style({ opacity: 1, height: '*' })),
    //   transition('* => *', [animate('.35s')]),
    // ]),
    trigger('loadMore', [
      state('void', style({ height: 0, opacity: 0 })),
      state('*', style({ height: '*', opacity: 1 })),
      transition('* => *', [animate('.2s')]),
    ]),
  ],
})
export class ServiceListComponent implements OnInit, OnDestroy, OnChanges, AfterContentChecked {
  mapLocation = `${environment.assets}/map_location.png`;

  @Select(ServiceState.getWorkOrderSearchFields)
  wordOrderSearchFields$: Observable<FilterFieldsModel[]>;
  wordOrderSearchFields: FilterFieldsModel[];
  wordorderFieldsEnum: FilterFieldModelEnum = FilterFieldModelEnum.workorder;
  hasFiltered = false;
  workorderSearchItems: FilterItemModel[] = [];
  buildedFilter: FilterItemModel[] = [];
  PermissionEnum = PermissionEnum;

  @Select(ServiceState.requestServiceDetailedList)
  requestServiceList$: Observable<ServiceDetailedRequestModel[]>;
  requestServiceList: ServiceDetailedRequestModel[];

  @Select(ServiceState.requestServiceListLoading)
  requestServiceListLoading$: Observable<boolean>;

  @Select(ServiceState.requestServiceListPaging)
  requestServiceListPaging$: Observable<PagingModel>;

  @Select(AppState.history)
  history$: Observable<any>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  loadMore: any = {};
  soundModal = false;
  loadingAttachment: any[] = [];
  cameraErrorModal = false;
  cameraErrorModalMessage = '';
  workOrderLoading: { [key: string]: boolean } = {};

  private customerNumber: string;
  page = 1;
  private serviceListLoading: boolean;
  paging: PagingModel;
  DownloadTypeEnum = DownloadTypeEnum;
  protected subscriptions$: Subject<boolean> = new Subject();

  featureSoundDiagnostics = false;
  expertiseShow: boolean;
  serviceLocationShow = true;
  systemFeatureServiceDetail: boolean;
  equipmentWithSoundDiag = true;
  cancelDonwloadVersion = false;
  askToTechnician = false;
  private company: CompanyModel;
  private errorModal: any;

  @ViewChild('filterModal')
  protected filterModalComponent: FilterModalComponent;

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly frameService: MessageFrameService,
    private readonly modalService: ModalService,
    private readonly definitionService: DefinitionService,
    private readonly translateService: TranslateService,
    private readonly ref: ChangeDetectorRef,
  ) { }

  ngOnInit() {
    this.store.dispatch(new PageOpenedAction(PagesEnum.serviceListPage));
    this.buildQueryFilters();
    this.cancelDonwloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .pipe(skip(1))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.frameService.sendMessage(data.action)
          });
        });
    if (customer) {
      this.company = company;
      this.customerNumber = customer?.customerNumber;

      setTimeout(() => {
        if (!this.store.selectSnapshot(ServiceState.requestServiceDetailedList)?.length && !this.workorderSearchItems?.length) {
          this.loadServiceList();
        }
      }, 0);

      this.requestServiceListLoading$.subscribe(i => this.serviceListLoading = i);
      this.requestServiceListPaging$.subscribe(i => this.paging = i || this.paging);

      this.store.dispatch(new SystemFeatureAction());
      this.requestServiceList$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(list => {
          if (list.length >= 0) {
            this.requestServiceList = list;
          }
        });
      this.systemFeatures$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(features => {
          if (features) {
            this.featureSoundDiagnostics = systemFeature('sound_diagnostics', features, true);
            this.expertiseShow = systemFeature('inspection_forms', features, true);
            this.serviceLocationShow = systemFeature('service_location_tracking', features, true);
            this.systemFeatureServiceDetail = systemFeature('service_detail', features, false);
          }
        });
      this.route.queryParams.subscribe(params => {
        if (params?.action === 'sound-diagnostic') {
          this.soundModal = true;
        }
      });

    }

    // ###  history store
    this.router.events
      .pipe(takeUntil(this.subscriptions$))
      .pipe(filter(e => e instanceof NavigationStart))
      .subscribe((val) => {
        console.log('events', val);
        this.storeHistory();
      });

    this.history$
      .pipe(takeUntil(this.subscriptions$), take(1))
      .subscribe((stt: any) => {
        // console.log('pop state', stt);
        if (stt) {
          // restore paging
          this.page = stt.page;
          if (stt?.workorderSearchItems) {
            this.clearFilters();
            this.buildedFilter = stt?.workorderSearchItems;
            this.workorderSearchItems = stt?.workorderSearchItems;
            this.hasFiltered = !!stt?.workorderSearchItems.length;
            this.loadServiceList();
          }
          // restore scroll
          setTimeout(() => {
            document.documentElement.scrollTop = stt.scrollPosition;
            this.storeHistory();
          });
          // because of animation 0.2-second delay
          setTimeout(() => {
            document.documentElement.scrollTop = stt.scrollPosition;
          }, 205);
        }
      });

    this.getWorkOrderFilterFieds();
  }

  getWorkOrderFilterFieds() {
    this.store.dispatch(new GetWorkOrderFilterFieldsAction());
    this.wordOrderSearchFields$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(fields => {
        if (fields?.length) {
          this.wordOrderSearchFields = fields;
        }
      });
  }

  selectedFilterFields(e: FilterItemModel[]) {
    if (JSON.stringify(this.workorderSearchItems) !== JSON.stringify(e)) {
      this.page = 1;
      this.workorderSearchItems = e;
      this.loadServiceList();
      this.storeHistory();
    }
  }

  clearFilters(): void {
    this.page = 1;
    this.hasFiltered = false;
    // this.loadServiceList();
    this.filterModalComponent?.clearFilter();
  }

  loadServiceList() {
    this.store.dispatch(new GetServiceDetailedListAction({
      page: this.page,
      top: 10,
      items: this.workorderSearchItems
    }));
  }

  showCamera(equipment: ServiceDetailedRequestModel): boolean {
    return (
      equipment.isCrc &&
      equipment.workOrderStatus &&
      equipment.workOrderStatus.areaInfos.length > 0 &&
      equipment.workOrderStatus.areaInfos.some(
        (info) => info.activeCameras.length > 0,
      )
    );
  }

  requestService() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-service',
      ])
      .then();
  }

  startExpertise() {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'expertise',
      'init',
    ], {
      queryParams: { backButton: 1 },
    }).then();
  }

  onLive(serviceRequest: ServiceDetailedRequestModel) {
    this.definitionService
      .checkWorkingHours(serviceRequest.serviceOrganization)
      .subscribe((res) => {
        if (res.isWorkingHour) {
          this.router
            .navigate(
              [
                '/',
                ...environment.rootUrl.split('/'),
                'customer',
                'service-list',
                'camera-list',
                serviceRequest.serviceNumber,
              ],
              {
                queryParams: {
                  ...this.route.snapshot.queryParams,
                  serviceOrganization: serviceRequest.serviceOrganization,
                  isCrc: serviceRequest.isCrc,
                },
              },
            )
            .then();
        } else {
          this.cameraErrorModal = true;
          this.cameraErrorModalMessage = res.message;
        }
      });
  }

  onMap(serviceRequest: ServiceDetailedRequestModel) {
    this.definitionService
      .checkWorkingHours(serviceRequest.serviceOrganization)
      .subscribe((res) => {
        if (res.isWorkingHour) {
          this.router
            .navigate(
              [
                '/',
                ...environment.rootUrl.split('/'),
                'customer',
                'service-list',
                'map',
                serviceRequest.serviceNumber,
              ],
              {
                queryParams: {
                  ...this.route.snapshot.queryParams,
                },
              },
            )
            .then();
        }
      });
  }

  getStatusLabel(serviceRequest: ServiceDetailedRequestModel) {
    // do we need to translate
    switch (serviceRequest.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
    // return serviceRequest.serviceStatus !== 'InProgress';
  }

  // downloadFile(attachment: WorkOrderAttachmentsModel, serviceOrganization) {
  //   this.loadingAttachment = attachment.id;
  //   this.mediaService
  //     .getAttachment(attachment.id, serviceOrganization)
  //     .pipe(
  //       map((response: any) => {
  //         console.log('rest', response);
  //         const dataType = response.type;
  //         const binaryData = [];
  //         binaryData.push(response);
  //         return new Blob(binaryData, { type: dataType });
  //       }),
  //     )
  //     .subscribe(
  //       (blob: Blob) => {
  //         const reader = new FileReader();
  //         reader.readAsDataURL(blob);
  //         reader.onload = () => {
  //           this.loadingAttachment = undefined;
  //           this.frameService.sendMessage(FrameMessageEnum.openTeklif, {
  //             filename: attachment.name,
  //             contentType: blob.type,
  //             file: reader.result,
  //           });
  //         };
  //       },
  //       () => {
  //         this.loadingAttachment = undefined;
  //       },
  //     );
  // }


  @HostListener('window:scroll', ['$event'])
  onScrollEvent() {
    const pos = (document.documentElement.scrollTop || document.body.scrollTop) + document.documentElement.clientHeight;
    const max = document.documentElement.scrollHeight;

    if (pos + 100 >= max && !this.serviceListLoading) {
      // console.log('scroll end', [pos, max]);
      // console.log('paging end', [this.paging.pageNumber, this.paging.totalCount]);
      if (this.paging?.pageNumber * this.paging?.pageSize < this.paging?.totalCount) {
        this.page++;
        this.loadServiceList();
      }
    }
  }

  openSurvey(survey: Survey) {
    this.frameService.sendMessage(FrameMessageEnum.openSurvey, survey);
  }

  getAttachments(service: ServiceDetailedRequestModel) {
    return service.workOrderStatus.attachments.length < 5
    || this.loadMore[service.serviceNumber] ?
      service.workOrderStatus.attachments : service.workOrderStatus.attachments.slice(0, 5);
  }

  lessMoreFunc(serviceNumber: any) {
    this.loadMore[serviceNumber] = !this.loadMore[serviceNumber];
    if (!this.loadMore[serviceNumber]) {
      this.loadingAttachment = [];
    }
  }


  private storeHistory() {
    history.scrollRestoration = 'auto';
    const historyState = {
      workorderSearchItems: this.workorderSearchItems,
      page: this.page,
      scrollPosition: document.documentElement.scrollTop,
    };
    console.log('store history', historyState);
    replaceHistoryState(historyState);
  }

  protected buildFilter(filters) {
    return Object.entries(filters).map(([key, value]) => ({ fieldName: key, fieldValue: value }));
  }

  protected buildQueryFilters() {
    const { hasServiceNumber } = this.route.snapshot.queryParams;
    console.log('query params', hasServiceNumber);

    const urlFilters: any = {};
    if (hasServiceNumber) {
      urlFilters.workOrderNumber = hasServiceNumber;
    }
    if (!Object.values(urlFilters).length) {
      return;
    }
    this.selectedFilterFields(this.buildFilter(urlFilters));
    this.buildedFilter = this.buildFilter(urlFilters);
    this.hasFiltered = true;
    clearUrlParamsOnHistory(['hasServiceNumber']);
  }

  customDate(value: string) {
    // ? Weking Gelen Tarih Formatı 09.12.2021 11:20
    // ? Cdoms Gelen Tarih Formatı 20210903111723
    // ? KZ icin gelen format 20211803111723 !!IPTAL

    let date = moment(value, 'DD.MM.YYYY HH:mm');
    if (date.isValid()) {
      return date.toISOString();
    }
    // date = moment(value, this.company?.countryCode === 'KZ' ?
    //   'YYYYDDMMHHmmss' : 'YYYYMMDDHHmmss');
    date = moment(value, 'YYYYMMDDHHmmss');
    if (date.isValid()) {
      return date.toISOString();
    }

    return '';
  }

  downloading(attachment) {
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  navigateAskToTechnicianTab(workOrderId) {
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'service',
          'detail',
          workOrderId,
        ],
      )
      .then();
  }

  ngAfterContentChecked(): void {
    this.ref.detectChanges();
  }

  ngOnChanges(): void {
    this.ref.detectChanges();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  serviceAttachmentKey(attachment: ServiceDetailedRequestAttachment) {
    if (attachment.uploadTypeDescription === 'SERVICE_FORM') {
      return '_service_form';
    }

    return getServiceAttachmentKey(attachment.uploadType);
  }
}
