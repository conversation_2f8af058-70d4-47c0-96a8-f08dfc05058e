import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { interval, Observable, Subject, Subscription } from 'rxjs';
import snq from 'snq';
import { HttpResponse } from 'src/app/response/http.response';
import { ServiceDetailedRequestModel } from '../../../model/service-request.model';
import { WorkOrderModel } from '../../../model/work-order.model';
import { takeUntil } from 'rxjs/operators';
import { GetWorkOrderListAction } from '../../../action/service.action';
import { ServiceState } from '../../../state/service.state';
@Component({
  selector: 'cat-camera-list',
  templateUrl: './camera-list.component.html',
  styleUrls: ['./camera-list.component.scss'],
})
export class CameraListComponent implements OnInit, On<PERSON><PERSON>roy {
  @Select(ServiceState.requestServiceDetailedList)
  requestServiceList$: Observable<ServiceDetailedRequestModel[]>;
  @Select(ServiceState.workOrderLoading)
  workOrderLoading$: Observable<boolean>;

  @Select(ServiceState.workOrder) workOrder$: Observable<WorkOrderModel>;
  serviceData: ServiceDetailedRequestModel;
  activeWorkOrderNumber: string;
  activeCameraId: string;
  activeCameraTitle: string;
  status: string;

  subscription: Subscription = new Subscription();
  serviceOrganization: string;
  cameraErrorModal = false;
  cameraErrorModalMessage = '';

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    const { id } = this.route.snapshot.params;
    const { serviceOrganization, isCrc } = this.route.snapshot.queryParams;
    this.serviceOrganization = serviceOrganization;
    // this.state$ = this.route.snapshot.extras.statepipe(map(() => window.history.state));
    this.requestServiceList$.subscribe((res) => {
      this.serviceData = res.find((r) => r.serviceNumber === id);
    });
    // console.log('params', this.route.snapshot.params);
    // console.log('id', id);
    this.getWorkOrders(id, isCrc, serviceOrganization);
    interval(1000 * 60)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        this.getWorkOrders(id, isCrc, serviceOrganization);
      });
  }

  getWorkOrders(id, isCrc, serviceOrganization) {
    this.store
      .dispatch(new GetWorkOrderListAction(id, isCrc, serviceOrganization))
      .subscribe(() => {
        let workOrder = this.store.selectSnapshot(ServiceState.workOrder);
        if (!workOrder) { // if backend not work
          workOrder = this.serviceData.workOrderStatus as any;
        }
        this.status = workOrder?.currentStatusName;
        this.activeWorkOrderNumber = snq(() => workOrder.workOrderNumber);

        this.activeCameraId = workOrder?.areaInfos.find(area => area?.activeCameras?.[0])?.activeCameras?.[0];
        // this.activeCameraId = snq(
        //   () => workOrder.areaInfos[0].activeCameras[0]
        // );
        this.activeCameraTitle =
          snq(() => workOrder?.areaInfos[0].areaName) + ' 0';
      });
  }

  onCloseWindow() {
    window.history.back();
    // kameradan ana sayfaya atma sorunu devam ederse düzeltilecek
    // this.router
    //   .navigate(
    //     ['/', ...environment.rootUrl.split('/'), 'customer', 'service-list'],
    //     {
    //       queryParams: this.route.snapshot.queryParams,
    //     }
    //   )
    //   .then();
  }

  onClickSnapshot(workOrderNumber: string, cameraId: string, title: string) {
    this.activeWorkOrderNumber = workOrderNumber;
    this.activeCameraId = cameraId;
    this.activeCameraTitle = title;
  }


  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onError(err: HttpResponse<null>) {
    this.cameraErrorModal = true;
    this.cameraErrorModalMessage = err.message;
  }
}
