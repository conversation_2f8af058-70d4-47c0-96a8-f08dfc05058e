@import "variable/bootstrap-variable";

:host {
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: $body-bg;
  z-index: 1;
  overflow: auto;
}

.snapshot {
  &-item {
    width: 90px;
    min-height: 55px;
    // max-height: 90px;
    margin-right: 10px;
    .border {
      //height: 100%;
      min-height: 55px;
    }
    cat-snapshot {
      width: 100%;
      //height: 100%;
    }
  }

  &-big {
    .camera-wrapper{
      min-height: 210px;
      display: flex;
      align-items: center;
    }
    cat-snapshot {
      display: block;
      min-height: 187px;
      width: 100%;
    }
  }
}

.page-title {
  position: absolute;
  left: 16px;
  top: 0;
  z-index: 2;
  text-shadow: 0 0 1px white;
}

.two-dots {
  //padding-left: 4px;

  &:before {
    display: inline-block;
    content: ":";
    //margin-left: -15px;
    padding-left: 2px;
    padding-right: 2px;
  }
}
.col-6{
  padding-left: 0;
}
@media only screen and (max-width: 359px) {
  .col-6{
    flex: 100%;
    max-width: 100%;
  }
}
