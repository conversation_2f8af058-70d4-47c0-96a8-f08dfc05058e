<div class="px-3 pb-5">
  <div class="h4 py-4 mb-0 text-center nav-back">
    <i (click)="onCloseWindow()" class="icon icon-back mr-2 float-left"></i>
    {{ "_live_watch" | translate }}
  </div>
  <!-- <ngx-loading [show]="workOrderLoading$ | async"></ngx-loading> -->
  <div class="pb-4 snapshot-big" [ngClass]="{ 'pt-5': !activeCameraId }">
    <div class="camera-wrapper border d-flex justify-content-center p-1 mb-2">
      <cat-snapshot [workOrderNumber]="activeWorkOrderNumber" [id]="activeCameraId" [title]="activeCameraTitle"
        [serviceOrganization]="serviceOrganization" (onError)="onError($event)" [refresh]="true"
        [slowlyFrequency]="true">
      </cat-snapshot>
    </div>
    <div class="camera-info-area" *ngIf="serviceData">
      <!-- <div class="text-center font-size-13px font-weight-semi-bold mb-2">
        {{ activeCameraTitle }}
      </div> -->
      <div class="row no-gutters font-size-11px mb-2">
        <div class="col d-flex">
          <div class="row no-gutters w-100">
            <div class="col-6 font-weight-semi-bold">
              {{ "_service_number" | translate }}
            </div>
            <div class="col-6 two-dots">
              {{ serviceData.serviceNumber || "-" }}
            </div>
          </div>
        </div>
        <div class="col d-flex">
          <div class="row no-gutters w-100">
            <div class="col-6 font-weight-semi-bold">
              {{ "_model" | translate }}
            </div>
            <div class="col-6 two-dots">
              {{ serviceData.model || ("_component" | translate) }}
            </div>
          </div>
        </div>
      </div>

      <div class="row no-gutters font-size-11px mb-2">
        <div class="col d-flex">
          <div class="row no-gutters w-100">
            <div class="col-6 font-weight-semi-bold">
              {{ "_machine_serial_number" | translate }}
            </div>
            <div class="col-6 two-dots">
              {{ serviceData.equipmentSerialNumber | serialFormat }}
            </div>
          </div>
        </div>
        <div class="col d-flex">
          <ng-container *ngIf="!serviceData?.isCrc">
            <div [ngSwitch]="serviceData?.workOrderStatus?.serviceApplication" class="row no-gutters w-100">
              <div *ngSwitchCase="'Cdoms'" class="col-5 font-weight-semi-bold">{{ "_acceptance_date" | translate }}</div>
              <div *ngSwitchCase="'Weking'" class="col-5 font-weight-semi-bold">{{ "_planned_date" | translate }}</div>
              <div *ngSwitchCase="'Sim'" class="col-5 font-weight-semi-bold">{{ "_start_date" | translate }}</div>
              <div *ngSwitchCase="'WO'" class="col-5 font-weight-semi-bold">{{ "_request_date" | translate }}</div>
              <div *ngSwitchDefault class="col-5 font-weight-semi-bold">{{ "_request_date" | translate }}</div>
              <div class="col-7 two-dots">{{ serviceData.startDate | date: "dd.MM.yyyy" || "-" }}</div>
            </div>
          </ng-container>
          <ng-container *ngIf="serviceData?.isCrc">
            <div class="row no-gutters w-100">
              <div class="col-5 font-weight-semi-bold">{{ (serviceData?.workOrderStatus?.endDate ? "_dispatch_date" : "_acceptance_date") | translate }}</div>
              <div class="col-7 two-dots">{{ (serviceData?.workOrderStatus?.endDate ? serviceData?.workOrderStatus?.endDate : serviceData?.startDate) | date: "dd.MM.yyyy" || "-" }}</div>
            </div>
          </ng-container>
        </div>
      </div>

      <div class="row no-gutters font-size-11px">
        <div class="col-6 d-flex">
          <div class="row no-gutters w-100">
            <div class="col-6 font-weight-semi-bold">
              {{ "_status" | translate }}
            </div>
            <div class="col-6 two-dots">
              {{ status }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-container *ngIf="workOrder$ | async as workOrder">
    <ng-container *ngFor="let areaInfo of workOrder?.areaInfos">
      <ng-container *ngIf="areaInfo.activeCameras?.length">
        <h5 class="font-weight-semi-bold mb-0">{{ areaInfo.areaName }}</h5>
        <div class="pt-3 pb-4">
          <div *ngIf="
              areaInfo.activeCameras?.length > 0;
              else activeCameraNotFound
            " class="d-flex">
            <ng-container *ngFor="
                let cameraId of areaInfo.activeCameras;
                index as cameraIndex
              ">
              <div class="snapshot-item">
                <div class="border d-flex justify-content-center p-1 mb-2"
                  [class.border-warning]="activeCameraId === cameraId">
                  <cat-snapshot (click)="
                      onClickSnapshot(
                        workOrder.workOrderNumber,
                        cameraId,
                        areaInfo.areaName + ' ' + cameraIndex
                      )
                    " [workOrderNumber]="workOrder.workOrderNumber" [id]="cameraId"
                    [serviceOrganization]="serviceOrganization" (onError)="onError($event)"
                    [title]="areaInfo.areaName + ' ' + cameraIndex"></cat-snapshot>
                </div>
                <!-- <div class="text-center font-size-11px">
                  {{ areaInfo.areaName + " " + cameraIndex }}
                </div> -->
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</div>
<ng-template #activeCameraNotFound>
  <div class="text-secondary text-center">
    {{ "_active_camera_not_found" | translate }}
  </div>
</ng-template>
<!-- <ng-template #chooseCamera>
  <div class="text-secondary text-center">
    {{ "_choose_camera_description" | translate }}
  </div>
</ng-template> -->

<cat-basic-modal *ngIf="cameraErrorModal" [status]="!!cameraErrorModal" (statusChange)="onCloseWindow()">
  <div class="camera-error-message p-4">
    {{ cameraErrorModalMessage }}
  </div>
</cat-basic-modal>
