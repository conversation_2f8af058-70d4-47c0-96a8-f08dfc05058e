import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ServiceDetailedRequestAttachment,
  ServiceDetailedRequestModel,
  ServiceDetailedRequestWorkOrderStatus,
  Survey
} from '../../../model/service-request.model';
import { Select, Store } from '@ngxs/store';
import { GetWorkOrderFromQrAction } from '../../../action/service.action';
import { ServiceState } from '../../../state/service.state';
import { map } from 'rxjs/operators';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { StartDownloadAction } from 'src/app/module/shared/state/common/common.actions';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import * as moment from 'moment';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { Observable } from 'rxjs';
import { DefinitionService } from 'src/app/module/definition/service/definition.service';
import { environment } from 'src/environments/environment';
import { LogService } from '../../../service/log.service';
import { getServiceAttachmentKey } from '../../../../../util/service-attachment.util';

@Component({
  selector: 'cat-qr-detail',
  templateUrl: './qr-detail.component.html',
  styleUrls: ['./qr-detail.component.scss']
})
export class QrDetailComponent implements OnInit {

  @Select(ServiceState.serviceDetailFromQrLoading)
  serviceDetailFromQrLoading$: Observable<boolean>;

  mapLocation = `${environment.assets}/map_location.png`;
  cancelDonwloadVersion = false;

  serviceRequest: any;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly definitionService: DefinitionService,
    private readonly router: Router,
    private readonly logger: LogService
  ) { }
  DownloadTypeEnum = DownloadTypeEnum;
  loadMore: any = {};
  loadingAttachment: any[] = [];
  loading: boolean;
  serviceOrgId: string;


  ngOnInit() {

    this.logger.log('QR_DETAIL', 'QR_DETAIL_PUBLIC_PAGE', {
    }).subscribe();

    const { workOrderNumber } = this.route.snapshot.params;
    this.cancelDonwloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.serviceDetailFromQrLoading$.subscribe(i => (this.loading = i));
    if(workOrderNumber){
      this.store.dispatch(new GetWorkOrderFromQrAction(workOrderNumber))
      .pipe(map(() => this.store.selectSnapshot(ServiceState.serviceDetailFromQr)))
      .subscribe((state: any) => {
        this.serviceRequest = state;
      },
      error => {
        console.error(error);
      },
      );
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  downloading(attachment) {
    this.applyDisabledClassToNonDownloadButtons();
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading) {
      this.loadingAttachment.splice(0, this.loadingAttachment.length);
    }
    if (!this.isDownloadingAny()) {
      this.enableNonDownloadButtons();
    }
  }

  getAttachments(service: ServiceDetailedRequestModel) {
    return service.workOrderStatus.attachments.length <= 5
    || this.loadMore[service.serviceNumber] ?
      service.workOrderStatus.attachments : service.workOrderStatus.attachments.slice(0, 5);
  }

  lessMoreFunc(serviceNumber: any) {
    this.loadMore[serviceNumber] = !this.loadMore[serviceNumber];
    if (!this.loadMore[serviceNumber]) {
      this.loadingAttachment = [];
    }
  }

  customDate(value: string) {
    // ? Weking Gelen Tarih Formatı 09.12.2021 11:20
    // ? Cdoms Gelen Tarih Formatı 20210903111723
    // ? KZ icin gelen format 20211803111723 !!IPTAL

    let date = moment(value, 'DD.MM.YYYY HH:mm');
    if (date.isValid()) {
      return date.toISOString();
    }
    date = moment(value, 'YYYYMMDDHHmmss');
    if (date.isValid()) {
      return date.toISOString();
    }

    return '';
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.enableNonDownloadButtons();
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  openSurvey(survey: Survey) {
    this.frameService.sendMessage(FrameMessageEnum.openSurvey, survey);
  }

  navigateToBack() {
    window.history.back();
  }

  getStatusLabel(serviceRequest: ServiceDetailedRequestModel) {
    switch (serviceRequest?.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
  }

  onMap(serviceRequest: ServiceDetailedRequestModel) {
    this.definitionService
      .checkWorkingHours(serviceRequest.serviceOrganization)
      .subscribe((res) => {
        if (res.isWorkingHour) {
          this.router
            .navigate(
              [
                '/',
                ...environment.rootUrl.split('/'),
                'customer',
                'service-list',
                'map',
                serviceRequest.serviceNumber,
              ],
              {
                queryParams: {
                  ...this.route.snapshot.queryParams,
                },
              },
            )
            .then();
        }
      });
  }

  applyDisabledClassToNonDownloadButtons() {
    const downloadButtonIds = this.serviceRequest.workOrderStatus.attachments
      .map((history) => history.id)
      .filter((id) => id !== null);

    const allButtons = document.getElementsByClassName('download-file-button');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const id = button.getAttribute('id');
      if (id && !downloadButtonIds.includes(+id.split('_')[1])) {
        button.classList.add('disabled');
      }
    }
  }
  isDownloadingAny() {
    return this.loadingAttachment.length > 0;
  }
  enableNonDownloadButtons() {
    const allButtons = document.getElementsByClassName('download-file-button');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      button.classList.remove('disabled');
    }
  }
  back(){
    window.history.back();
  }


  serviceAttachmentKey(attachment: ServiceDetailedRequestAttachment) {
    if (attachment.uploadTypeDescription === 'SERVICE_FORM') {
      return '_service_form';
    }

    return getServiceAttachmentKey(attachment.uploadType);
  }
}
