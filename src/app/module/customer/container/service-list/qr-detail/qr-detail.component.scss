@import "variable/bootstrap-variable";

.service-request {
  &-item {
    background: #ffffff;
    mix-blend-mode: normal;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;

      &-green {
        background: #5d8d1c;
      }

      &-orange {
        background: $warning;
      }
    }

    .two-dots {
      padding-left: 25px;

      &:before {
        display: inline-block;
        content: ":";
        margin-left: -25px;
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }
}

.btn-live {
  font-weight: 600;
  font-size: 13px;
  line-height: 19px;
  color: #4a8eb0;

  i {
    font-size: 20px;
    margin-right: 9px;
  }
}

.loading {
  min-height: 70px;
  position: relative;
  margin: 0 15px 15px 0;
  padding: 15px;
}

.service-btn {
  width: 50%;
  padding-top: 9px;
  padding-bottom: 9px;
  border-radius: 6px;
}

.sound-btn {
  width: calc(50% - 65px);
  //padding-top: 9px;
  //padding-bottom: 9px;
  border-radius: 6px;
  background-color: #ffa300;
}

.sound-button-text {
  line-height: 30px;
  color: white;
  text-align: center;
  //border: 1px solid green;
  padding: 0;

  p {
    margin: 0;
    //font-size: 11px;
    font-size: min(max(2vw, 11px), 14px);

    line-height: 1em;
    display: inline-block;
    vertical-align: middle;
  }

  i {
    font-size: 18px;
  }
}

.wrench-empty {
  font-size: 40px;
}

.download-file {
  width: 54px;
  height: 54px;
  background-color: #f5f5f5;
  font-size: 18px;
}

.survey {
  width: 40px;
  height: 40px;
  background-color: #f5f5f5;
  font-size: 18px;
}

.list-count {
  font-weight: 600;
  font-size: 16px;
  line-height: 18px;
  text-align: center;
  color: #505050;
}

/* Safari 10.1 */
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) and (not (stroke-color: transparent)) {
    .sound-button-text > p {
      font-size: 12px;
    }
  }
}

.service-menu-button{
  box-shadow: none;
  line-height: 1.5em;
  font-size: 20px;
}

.dropdown-after-none::after{
  display: none;
}

.mini-loading-area {
  height: 55px;
  width: 100%;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.download-button-padding {
  padding-bottom: 38px;
}

.doesnt-exist {
  height: calc(100vh + -237px);
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #495E68;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &-text {
    font-weight: bold;
    font-size: 26px;
    width: 80%;
    text-align: center;
    line-height: 1.2;
    color: black;
  }
  i {
    font-size: 100px;
  }
}
