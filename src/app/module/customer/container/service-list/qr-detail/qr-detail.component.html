<div class="mb-3 service-request-item overflow-hidden m-3" *ngIf="!loading && serviceRequest">
  <div [style.background-color]="serviceRequest?.serviceStatusColor" class="service-request-item-color"></div>
<div class="p-4">
  <div>
    <div class="d-flex justify-content-between mb-2 overflow-hidden">
      <div class="font-weight-semi-bold h6">{{ serviceRequest?.model || ("_component" | translate) }}</div>
      <div [style.color]="serviceRequest?.serviceStatusColor" class="font-weight-semi-bold">{{
        (this.getStatusLabel(serviceRequest)) | translate }}</div>
    </div>
    <div class="row no-gutters">
      <div class="col-5">
        {{ "_service_number" | translate }}
      </div>
      <div class="col-7 two-dots">{{ serviceRequest?.serviceNumber || "-" }}</div>
    </div>
    <div class="row no-gutters">
      <div class="col-5">{{ "_machine_serial_number" | translate }}</div>
      <div class="col-7 two-dots">{{ (serviceRequest?.equipmentSerialNumber | serialFormat) || "-" }}</div>
    </div>
    <ng-container *ngIf="!serviceRequest?.isCrc">
      <div [ngSwitch]="serviceRequest?.workOrderStatus?.serviceApplication" class="row no-gutters">
        <div *ngSwitchCase="'Cdoms'" class="col-5">{{ "_acceptance_date" | translate }}</div>
        <div *ngSwitchCase="'Weking'" class="col-5">{{ "_planned_date" | translate }}</div>
        <div *ngSwitchCase="'Sim'" class="col-5">{{ "_start_date" | translate }}</div>
        <div *ngSwitchCase="'WO'" class="col-5">{{ "_request_date" | translate }}</div>
        <div *ngSwitchDefault class="col-5">{{ "_request_date" | translate }}</div>
        <div class="col-7 two-dots">{{ serviceRequest?.startDate | date: "dd.MM.yyyy" || "-" }}</div>
      </div>
    </ng-container>
    <ng-container *ngIf="serviceRequest?.isCrc">
      <div class="row no-gutters">
        <div class="col-5">
          {{ (serviceRequest?.workOrderStatus?.endDate ? "_dispatch_date" : "_acceptance_date") | translate }}
        </div>
        <div class="col-7 two-dots">{{
          (serviceRequest?.workOrderStatus?.endDate ? serviceRequest?.workOrderStatus?.endDate :
          serviceRequest?.startDate) | date: "dd.MM.yyyy" || "-" }}
        </div>
      </div>
    </ng-container>
  </div>

  <div *ngIf="serviceRequest?.workOrderStatus">
    <div class="row no-gutters" *ngIf="serviceRequest.workOrderStatus?.currentStatusName">
      <div class="col-5">{{ "_status" | translate }}</div>
      <div class="col-7 two-dots">{{ serviceRequest.workOrderStatus.currentStatusName }}</div>
    </div>
  </div>

  <!-- ? Service Location Tracking -->
  <div
    *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode || serviceRequest?.workOrderStatus?.servicePlateNumber">
    <div class="row no-gutters">
      <div class="col-5">{{ "_service_car_plate" | translate }}</div>
      <div class="col-7 two-dots d-flex">
        <div class="flex-grow-1">
          <div>{{ serviceRequest?.workOrderStatus?.servicePlateNumber || '-' }}</div>
          <div catUserClick [section]="'SERVICE'" [subsection]="'SERVICE_MAP'"
                [data]="{ serviceNumber: serviceRequest.serviceNumber }" (click)="onMap(serviceRequest)"
                *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode"
                class="text-success" style="word-wrap: break-word;">{{ '_service_map_location_tracking' |
            translate }}</div>
        </div>
        <div catUserClick [section]="'SERVICE'" [subsection]="'SERVICE_MAP'"
              [data]="{ serviceNumber: serviceRequest.serviceNumber }" (click)="onMap(serviceRequest)"
              *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode"
              class="d-flex justify-content-center align-items-center">
          <div class="align-items-center">
            <img height="26" [src]="mapLocation" alt="">
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row no-gutters">
    <div class="col-5">{{ "_description" | translate }}</div>
    <div class="col-7 two-dots" style="word-break: break-all;">{{ serviceRequest?.description || "-" }}</div>
  </div>

  <div *ngIf="serviceRequest?.workOrderStatus">
    <div class="pt-3" *ngIf="serviceRequest?.workOrderStatus?.attachments?.length > 0">
      <div *ngFor="
      let attachment of getAttachments(serviceRequest)
    ">
        <div class="row no-gutters d-flex align-items-center py-2">
          <div [id]="attachment.id" catUserClick [section]="'QR_DETAIL'"
               [subsection]="'ATTACHMENT_DOWNLOAD'" [data]="{attachmentId: attachment.id}" catDownloadFile
               class="row m-0"
               [downloadType]="DownloadTypeEnum.public" [downloadParams]="{
                          attachment: attachment,
                          serviceOrganization: serviceRequest.serviceOrganization,
                          workOrderNumber: serviceRequest.workOrderStatus.workOrderNumber
                        }" (downloadLoading)="downloading($event)">
            <div
              class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
              [class.spinner]="isDownloading(attachment.id)">
              <i class="icon icon-spinner8" *ngIf="isDownloading(attachment.id)"></i>
              <i class="icon icon-download" *ngIf="!isDownloading(attachment.id)"></i>
              <a class="d-none" [download]="attachment.id"></a>
            </div>
            <div>
              <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                {{ serviceAttachmentKey(attachment) | translate }}
              </div>
              <div *ngIf="attachment.dateUpload" class="text-muted font-size-12px ml-3">
                {{ customDate(attachment.dateUpload) | date: 'shortDate' }}
              </div>
            </div>
          </div>
          <div class="ml-auto" *ngIf="isDownloading(attachment.id)"
               (click)="cancelDownloadFile(attachment.id)">
            <i class="icon icon-x"></i>
          </div>
        </div>
      </div>
      <div (click)="lessMoreFunc(serviceRequest?.serviceNumber)"
            *ngIf="serviceRequest?.workOrderStatus?.attachments?.length > 5" class="mt-1 text-center"
            [ngClass]="{
              'disabled': this.loadingAttachment.length
            }"
            >
        <div class="font-weight-medium">
          {{ (loadMore[serviceRequest.serviceNumber] ? '_less' : '_more') | translate }}
          <!--                    <i class="icon icon-chevron-down"></i>-->
        </div>
      </div>
    </div>
    <div class="pt-2 border-top" *ngIf="serviceRequest.surveys as survey">
      <div catUserClick [section]="'SERVICE_LIST'" [subsection]="'SURVEY_CLICK'"
            [data]="{ surveyId: survey.id }" class="row no-gutters d-flex align-items-center py-2"
            (click)="openSurvey(survey)">
        <div
          class="survey border text-info border-info rounded-circle d-flex justify-content-center align-items-center">
          <i class="icon icon-contract"></i>
        </div>

        <div class="survey-name text-info ml-3 font-size-14px font-weight-bold">
          {{ "_service_survey" | translate }}
        </div>
      </div>
    </div>
  </div>
</div>
</div>

<ng-container *ngIf="!serviceRequest && !loading">
  <div class="doesnt-exist">
    <div class="d-flex justify-content-center">
      <i class="icon icon-repair"></i>
    </div>

    <div class="doesnt-exist-text mt-2">
      {{ '_workorder_qr_doesnt_exist' | translate }}
    </div>
    <!-- <div class="mt-3 w-75">
      <div
        (click)="back()"
        class="btn btn-warning btn-gradient btn-block text-white shadow"
        style="border-radius: 6px;"
      >
        {{ "_go_back" | translate }}
      </div>
    </div> -->
  </div>
</ng-container>

<cat-loader [show]="loading"></cat-loader>
