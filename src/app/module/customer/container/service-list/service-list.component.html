<div class="px-3 py-4">
  <!--  <div class="d-flex mb-3 cursor-pointer">-->
  <!--    <h4>{{'_request_service_list' | translate}}</h4>-->
  <!--  </div>-->

  <ng-container *ngIf="(requestServiceList?.length >= 0) || hasFiltered else emptyList">
    <div class="d-flex justify-content-end mb-4">
      <div [hasPermission]="PermissionEnum.RequestsService" catUserClick [section]="'SERVICE'" [subsection]="'REQUEST_BUTTON'" (click)="requestService()"
            class="service-btn btn btn-info btn-sm px-1 mr-2"
            [class.flex-grow-1]="!(featureSoundDiagnostics && equipmentWithSoundDiag)">
        {{ "_request_service" | translate }}
      </div>
      <div [hasPermission]="PermissionEnum.SoundDiagnosticsCheck" *ngIf="featureSoundDiagnostics && equipmentWithSoundDiag" (click)="soundModal = true"
            class="cursor-pointer d-inline-flex sound-btn px-1 py-1 justify-content-center align-items-center">
        <div class="sound-button-text">
          <p><i class="text-white icon icon-sound cursor-pointer"></i></p>
        </div>
        <div class="ml-2 sound-button-text text-wrap text-left">
          <p>
            {{ "_sound_diagnostic" | translate }}
          </p>
        </div>
      </div>
      <div class="d-flex justify-content-end">
        <div class="list-count d-flex justify-content-center align-items-center ml-2">
          <!-- ? Work Order List Filter -->
          <ng-container *ngIf="(wordOrderSearchFields?.length > 0 && requestServiceList?.length >= 0 ) || hasFiltered else showRepairIcon">
            <cat-filter-modal
              #filterModal
              [(filtered)]="hasFiltered"
              [totalCount]="paging?.totalCount"
              [filterFieldModel]="wordorderFieldsEnum"
              [filterFields]="wordOrderSearchFields"
              [specialHiddenFieldsList]="['search']"
              [modalZIndexUpLoading]="false"
              [filter]="buildedFilter"
              (applyFilter)="selectedFilterFields($event)"
            ></cat-filter-modal>
          </ng-container>
          <ng-template #showRepairIcon>
            <i class="icon icon-repair mr-1 font-weight-bold"></i>
          </ng-template>

        </div>
        <div [hasPermission]="PermissionEnum.RequestsInspection" *ngIf="expertiseShow" class="btn-group float-right ml-2" ngbDropdown role="group"
              aria-label="Button group with nested dropdown">
          <button class="dropdown-after-none btn btn-sm dropdown-toggle-split service-menu-button py-0 px-1 border-0"
                  ngbDropdownToggle><i class="icon icon-dot3 font-weight-bold"></i></button>
          <div class="equipment-menu mt-1 dropdown-menu" ngbDropdownMenu>
            <button catUserClick [section]="'SERVICE'" [subsection]="'REQUEST_SERVICE_START_EXPERTISE_BUTTON_CLICK'"
                    class="btn my-1 align-items-center" *ngIf="expertiseShow" ngbDropdownItem (click)="startExpertise()">
                    <i class="icon icon-search pr-1"></i>
              {{ "_start_expertise" | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <ng-container *ngIf="requestServiceList?.length else filterListEmpty">
      <div *ngFor="let serviceRequest of requestServiceList">
        <div class="mb-3 service-request-item overflow-hidden">
          <div [style.background-color]="serviceRequest?.serviceStatusColor" class="service-request-item-color"></div>
          <div class="p-4">
            <div>
              <div class="d-flex justify-content-between mb-2 overflow-hidden">
                <div class="font-weight-semi-bold h6">{{ serviceRequest?.model || ("_component" | translate) }}</div>
                <div [style.color]="serviceRequest?.serviceStatusColor" class="font-weight-semi-bold">{{
                  (this.getStatusLabel(serviceRequest)) | translate }}</div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_service_number" | translate }}
                </div>
                <div class="col-7 two-dots">{{ serviceRequest?.serviceNumber || "-" }}</div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">{{ "_machine_serial_number" | translate }}</div>
                <div class="col-7 two-dots">{{ (serviceRequest?.equipmentSerialNumber | serialFormat) || "-" }}</div>
              </div>
              <ng-container *ngIf="!serviceRequest?.isCrc">
                <div [ngSwitch]="serviceRequest?.workOrderStatus?.serviceApplication" class="row no-gutters">
                  <div *ngSwitchCase="'Cdoms'" class="col-5">{{ "_acceptance_date" | translate }}</div>
                  <div *ngSwitchCase="'Weking'" class="col-5">{{ "_planned_date" | translate }}</div>
                  <div *ngSwitchCase="'Sim'" class="col-5">{{ "_start_date" | translate }}</div>
                  <div *ngSwitchCase="'WO'" class="col-5">{{ "_request_date" | translate }}</div>
                  <div *ngSwitchDefault class="col-5">{{ "_request_date" | translate }}</div>
                  <div class="col-7 two-dots">{{ serviceRequest.startDate | date: "dd.MM.yyyy" || "-" }}</div>
                </div>
              </ng-container>
              <ng-container *ngIf="serviceRequest?.isCrc">
                <div class="row no-gutters">
                  <div class="col-5">
                    {{ (serviceRequest?.workOrderStatus?.endDate ? "_dispatch_date" : "_acceptance_date") | translate }}
                  </div>
                  <div class="col-7 two-dots">{{
                    (serviceRequest?.workOrderStatus?.endDate ? serviceRequest?.workOrderStatus?.endDate :
                    serviceRequest?.startDate) | date: "dd.MM.yyyy" || "-" }}
                  </div>
                </div>
              </ng-container>
            </div>

            <div *ngIf="serviceRequest.workOrderStatus">
              <div class="row no-gutters" *ngIf="serviceRequest.workOrderStatus?.currentStatusName">
                <div class="col-5">{{ "_status" | translate }}</div>
                <div class="col-7 two-dots">{{ serviceRequest.workOrderStatus.currentStatusName }}</div>
              </div>
            </div>

            <!-- ? Service Location Tracking -->
            <div
              [hasPermission]="PermissionEnum.ServiceTrackLocation"
              *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode || serviceRequest?.workOrderStatus?.servicePlateNumber">
              <div class="row no-gutters">
                <div class="col-5">{{ "_service_car_plate" | translate }}</div>
                <div class="col-7 two-dots d-flex">
                  <div class="flex-grow-1">
                    <div>{{ serviceRequest?.workOrderStatus?.servicePlateNumber || '-' }}</div>
                    <div catUserClick [section]="'SERVICE'" [subsection]="'SERVICE_MAP'"
                          [data]="{ serviceNumber: serviceRequest.serviceNumber }" (click)="onMap(serviceRequest)"
                          *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode && serviceLocationShow"
                          class="text-success" style="word-wrap: break-word;">{{ '_service_map_location_tracking' |
                      translate }}</div>
                  </div>
                  <div catUserClick [section]="'SERVICE'" [subsection]="'SERVICE_MAP'"
                        [data]="{ serviceNumber: serviceRequest.serviceNumber }" (click)="onMap(serviceRequest)"
                        *ngIf="serviceRequest?.workOrderStatus?.hasArventoNode && serviceLocationShow"
                        class="d-flex justify-content-center align-items-center">
                    <div class="align-items-center">
                      <img height="26" [src]="mapLocation" alt="">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row no-gutters">
              <div class="col-5">{{ "_description" | translate }}</div>
              <div class="col-7 two-dots" style="word-break: break-all;">{{ serviceRequest.description || "-" }}</div>
            </div>

            <div>
              <div class="pt-2" *ngIf="serviceRequest.workOrderStatus && serviceRequest.workOrderStatus.attachments.length > 0">
                <div *ngFor="
                let attachment of getAttachments(serviceRequest)
              ">
                  <div class="row no-gutters d-flex align-items-center py-2">
                    <div [id]="attachment.id" [@loadMore] catUserClick [section]="'SERVICE_LIST'"
                          [subsection]="'ATTACHMENT_DOWNLOAD'" [data]="{attachmentId: attachment.id}" catDownloadFile
                          class="row m-0"
                          [downloadType]="DownloadTypeEnum.attachment" [downloadParams]="{
                          attachment: attachment,
                          serviceOrganization: serviceRequest.serviceOrganization,
                          workOrderNumber: serviceRequest.workOrderStatus.workOrderNumber
                        }" (downloadLoading)="downloading($event)">
                      <div
                        class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                        [class.spinner]="isDownloading(attachment.id)">
                        <i class="icon icon-spinner8" *ngIf="isDownloading(attachment.id)"></i>
                        <i class="icon icon-download" *ngIf="!isDownloading(attachment.id)"></i>
                        <a class="d-none" [download]="attachment.id"></a>
                      </div>
                      <div>
                        <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                          {{ serviceAttachmentKey(attachment) | translate }}
                        </div>
                        <div *ngIf="attachment.dateUpload" class="text-muted font-size-12px ml-3">
                          {{ customDate(attachment.dateUpload) | date: 'shortDate' }}
                        </div>
                      </div>
                    </div>
                    <div class="ml-auto" *ngIf="isDownloading(attachment.id) && cancelDonwloadVersion"
                          (click)="cancelDownloadFile(attachment.id)">
                      <i class="icon icon-x"></i>
                    </div>
                  </div>
                </div>
                <div (click)="lessMoreFunc(serviceRequest.serviceNumber)"
                      *ngIf="serviceRequest.workOrderStatus.attachments.length > 5" class="mt-1 text-center">
                  <div class="font-weight-medium">
                    {{ (loadMore[serviceRequest.serviceNumber] ? '_less' : '_more') | translate }}
                    <!--                    <i class="icon icon-chevron-down"></i>-->
                  </div>
                </div>
              </div>
              <div [hasPermission]="PermissionEnum.ServiceSurvey" class="pt-2 border-top" *ngIf="serviceRequest?.surveys">
                <div catUserClick [section]="'SERVICE_LIST'" [subsection]="'SURVEY_CLICK'"
                      [data]="{ surveyId: serviceRequest?.surveys?.id }" class="row no-gutters d-flex align-items-center py-2"
                      (click)="openSurvey(serviceRequest?.surveys)">
                  <div
                    class="survey border text-info border-info rounded-circle d-flex justify-content-center align-items-center">
                    <i class="icon icon-contract"></i>
                  </div>

                  <div class="survey-name text-info ml-3 font-size-14px font-weight-bold">
                    {{ "_service_survey" | translate }}
                  </div>
                </div>
              </div>
            </div>
            <button
              *ngIf="systemFeatureServiceDetail"
              (click)="navigateAskToTechnicianTab(serviceRequest?.serviceNumber)"
              class="btn btn-sm text-info font-weight-bold pl-0"
            >
              {{ '_show_detail' | translate }}
            </button>
          </div>

          <div *ngIf="workOrderLoading[serviceRequest.serviceNumber]"
                class="loading d-flex justify-content-center align-items-center">
            <ngx-loading [show]="workOrderLoading[serviceRequest.serviceNumber]"></ngx-loading>
          </div>

          <div *ngIf="showCamera(serviceRequest)" catClickStopPropagation catUserClick
                [hasPermission]="PermissionEnum.ServiceCrcCamera"
                [section]="'SERVICE'"
                [subsection]="'WATCH_LIVE'" [data]="{
              serviceNumber: serviceRequest?.serviceNumber,
              equipmentNumber: serviceRequest?.equipmentNumber,
              equipmentSerialNumber: serviceRequest?.equipmentSerialNumber
            }"
                (click)="onLive(serviceRequest)"
                class="btn btn-block btn-lg rounded-0 border-top d-flex justify-content-center align-items-center btn-live ng-tns-c70-0">
            <i class="icon icon-camera"></i> {{ "_live_watch" | translate }}
          </div>
        </div>
      </div>
    </ng-container>

    <ng-template #filterListEmpty>
      <div class="text-center" *ngIf="!(requestServiceListLoading$ | async)">
        <cat-empty-content [iconName]="'repair'" [message]="'_request_service_filter_list_is_empty'" [extraMessage]="'_update_criteria_and_try_again'">

          <!-- ? Filter Clear Button -->
          <button *ngIf="hasFiltered"
                  catUserClick
                  [section]="'SERVICE'"
                  [subsection]="'REQUEST_SERVICE_CLEAR_FILTERS_BUTTON_CLICK'"
                  (click)="clearFilters()"
                  class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
            {{ "_clear_filters" | translate }}
          </button>
          <!-- ? Request Service Button -->
          <button *ngIf="!hasFiltered" [hasPermission]="PermissionEnum.RequestsService" catUserClick [section]="'SERVICE'" [subsection]="'REQUEST_BUTTON'" (click)="requestService()"
                  class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
            {{ "_request_service" | translate }}
          </button>
          <!-- ? Sounddiag Button -->
          <button *ngIf="!hasFiltered && featureSoundDiagnostics && equipmentWithSoundDiag"
                  catUserClick
                  [section]="'SERVICE'"
                  [subsection]="'REQUEST_SERVICE_SOUND_DIAGNOSTİC_BUTTON_BUTTON_CLICK'"
                  (click)="soundModal = true"
                  class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
            {{ "_sound_diagnostic" | translate }}
          </button>
          <!-- ? Start Expertise Button -->
          <button *ngIf="expertiseShow"
                  catUserClick
                  [section]="'SERVICE'"
                  [subsection]="'REQUEST_SERVICE_EMPTY_LIST_START_EXPERTISE_BUTTON_CLICK'"
                  (click)="startExpertise()"
                  class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
            {{ "_start_expertise" | translate }}
          </button>
        </cat-empty-content>
      </div>
    </ng-template>


  </ng-container>
  <cat-loader [show]="page == 1 && ( requestServiceListLoading$ | async)"></cat-loader>
  <div class="mini-loading-area" *ngIf="page > 1 && ( requestServiceListLoading$ | async)">
    <cat-loader [show]="true" bgColor="transparent" [overlay]="false"></cat-loader>
  </div>
</div>
<router-outlet></router-outlet>
<cat-equipment-select-modal
  (closeModal)="soundModal = false"
  [status]="soundModal"
  (equipmentListChanged)="equipmentWithSoundDiag = !!$event?.length">
</cat-equipment-select-modal>

<ng-template #emptyList>
  <div class="text-center" *ngIf="!(requestServiceListLoading$ | async)">
    <cat-empty-content [iconName]="'repair'" [message]="'_no_equipment_in_service'">
      <button [hasPermission]="PermissionEnum.RequestsService" catUserClick [section]="'SERVICE'" [subsection]="'REQUEST_BUTTON'" (click)="requestService()"
              class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
        {{ "_request_service" | translate }}
      </button>
      <!--      catUserClick-->
      <!--      [section]="'SERVICE'"-->
      <!--      [subsection]="'SOUND_DIAGNOSTIC_BUTTON'"-->
      <button *ngIf="featureSoundDiagnostics && equipmentWithSoundDiag"
              catUserClick
              [section]="'SERVICE'"
              [subsection]="'REQUEST_SERVICE_SOUND_DIAGNOSTİC_BUTTON_BUTTON_CLICK'"
              (click)="soundModal = true"
              class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
        {{ "_sound_diagnostic" | translate }}
      </button>
      <button *ngIf="expertiseShow"
              catUserClick
              [section]="'SERVICE'"
              [subsection]="'REQUEST_SERVICE_EMPTY_LIST_START_EXPERTISE_BUTTON_CLICK'"
              (click)="startExpertise()"
              class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mb-2">
        {{ "_start_expertise" | translate }}
      </button>
    </cat-empty-content>
  </div>
</ng-template>

<cat-basic-modal *ngIf="cameraErrorModal" [(status)]="!!cameraErrorModal">
  <div class="camera-error-message p-4">
    {{ cameraErrorModalMessage }}
  </div>
</cat-basic-modal>
