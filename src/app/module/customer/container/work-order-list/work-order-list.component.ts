import { Component } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CustomerModel } from '../../model/customer.model';
import { CustomerState } from '../../state/customer.state';

@Component({
  selector: 'cat-work-order-list',
  templateUrl: './work-order-list.component.html',
  styleUrls: ['./work-order-list.component.scss'],
})
export class WorkOrderListComponent {
  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;
}
