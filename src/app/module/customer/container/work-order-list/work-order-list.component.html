<div class="px-3 py-4">
  <ng-container *ngIf="customer$ | async as customer">
    <ng-container *ngIf="customer?.details?.workOrders; else emptyList">
      <div
        *ngFor="let workOrder of customer?.details?.workOrders; last as last"
        [class.border-bottom]="!last"
        class="card bg-transparent mb-3">
        <div class="card-body">
          <div class="pb-3">
            <h6 class="mb-0">İş Emri Numarası</h6>
            <div>{{workOrder?.workOrderNumber}}</div>
          </div>
          <div class="pb-3">
            <h6 class="mb-0"><PERSON><PERSON></h6>
            <div>{{workOrder?.serialNumber | serialFormat}}</div>
          </div>
          <div>
            <h6 class="mb-0">Açıklama</h6>
            <div>{{workOrder?.description}}</div>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>
</div>
<ng-template #emptyList>
  <div class="text-center">
    {{'_work_order_list_empty' | translate}}
  </div>
</ng-template>
