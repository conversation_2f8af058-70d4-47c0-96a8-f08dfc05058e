.detail-detail-modal {
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: white;
}

.total-debt-bg {
  //background-color: #EEF2F4;
}

.ng-select.company-drp ::ng-deep .ng-select-container  {
  border-radius: 6px;
}

.inline-header {
  height: 30px;
  //background-color: #FFA300;
}

.overflow-header {
 padding-top: 8px;
}

.fin-title {
  color : #505050;
}

.right-box {
  line-height: 1.2;
}

.finance-item-row{
  position: relative;
  &-icon{
    position: absolute;
    right: 0;
  }
}

::ng-deep body {
  overflow: hidden;
}

.financial-information {
  .bottom-buttons {
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    position: absolute;
    animation: animate-financial-information 2s 2;
    animation-direction: alternate;
    animation-iteration-count: 1;
    bottom: 30px;
  }
}

@keyframes animate-financial-information {
  from {
    bottom: -50px;
  }
  to {
    bottom: 30px;
  }
}

.financial-information-main {
  height: 99vh;
}

::ng-deep .financial-total-accordion #id-0-header {
  //display: none;
}

::ng-deep .financial-total-accordion .card,
::ng-deep .financial-total-accordion .btn,
::ng-deep .financial-total-accordion .btn:focus,
::ng-deep .financial-total-accordion .border-bottom,
::ng-deep .financial-total-accordion .card-header {
  border: none;
  padding: 0;
  margin: 0;
  background: none;
  color: inherit;
  display: inherit;
  position: inherit;
  min-width: inherit;
  border-radius: inherit;
  flex-direction: inherit;
  justify-content: inherit;
  align-content: inherit;
  float: inherit;
  font-weight: inherit;
  width: 100%;
  box-shadow: none;
}

::ng-deep .financial-total-accordion .finance-item-row,
::ng-deep .financial-total-accordion .fin-title{
  align-items: inherit !important;
  flex: inherit !important;
}
::ng-deep .financial-total-accordion .card-body {
  border-bottom: 1px solid #dee2e6 !important;
  padding-left: 0;
  padding-right: 0;
}

