<div class="financial-information-main">
  <div class="inline-header mx-0 mt-2">
    <div class="overflow-header px-3">
      <ng-container *ngTemplateOutlet="companySelect"></ng-container>
    </div>
  </div>
  <div class="px-4 py-4 pt-5">
    <ng-container *ngIf="totalItems$ | async as financialInformation">
      <div>
        <div class="h6 text-info mb-0">
          {{'_my_debts' | translate}}
        </div>
        <div class="font-weight-semi-bold mx-n3 px-3">
          <ng-container
            *ngTemplateOutlet="itemRow; context: {item: { title: '_total_debts', code: 'totalDebt'}, arrow: true, bigItem: true, borderBottom: true}">
          </ng-container>
        </div>
        <div class="mt-n1">
          <ng-container *ngFor="let item of debtItems">
            <div [ngClass]="item?.code == 'totalDebt' ? 'font-weight-semi-bold mx-n3 px-3' : 'mt-n1'">
              <ng-container *ngIf="financialInformation[item?.code + suffix]?.length > 0">
                <ng-container *ngTemplateOutlet="itemRow; context: {item: item, arrow: true, borderBottom: true, bigItem: true}">
                </ng-container>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </div>
      <div *ngIf="paymentToolsStatus(financialInformation)">
        <div class="h6 text-info mt-5 mb-2">
          {{'_payments' | translate}}
        </div>
        <ng-container *ngFor="let item of paymentItems">
          <ng-container *ngIf="financialInformation[item?.code + suffix]?.length > 0">
            <ng-container *ngTemplateOutlet="itemRow; context: {item: item, arrow: true, borderBottom: true, bigItem: true}"></ng-container>
          </ng-container>
        </ng-container>
      </div>
    </ng-container>
  </div>
</div>
<div class="financial-information" *ngIf="showMutabakat && mutabakatLang">
  <div class="bottom-buttons w-100">
    <div class="mx-3 d-flex flex-row justify-content-center">
      <div
      [hasPermission]="PermissionEnum.FinancialReconciliationSend"
        *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitReconciliationAnswer) >= 0)"
        catUserClick [section]="'FINANCIAL_INFORMATION'" [subsection]="'STATEMENT_INQUIRY'"
        class="mr-3 btn btn-warning text-white" (click)="openExtractModal()">
        {{ '_statement_inquiry' | translate }}
      </div>
      <div
        *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ViewReconciliationList) >= 0)"
        catUserClick [hasPermission]="PermissionEnum.FinancialReconciliation" [section]="'FINANCIAL_INFORMATION'" [subsection]="'RECONCILIATION_PAGE_OPEN'"
        class="btn btn-warning text-white" (click)="openMutakabatPage()">
        {{ '_open_mutabakat_page' | translate }}
        <i class="icon icon-chevron-right mx-1"></i>
      </div>
    </div>
  </div>
</div>
<div *ngIf="detailModalStatus" [@details] class="detail-detail-modal px-3 py-2 overflow-auto">
  <div class="d-flex  justify-content-between mb-3 cursor-pointer h4 nav-back">
    <div class="p-2">
      <i (click)="closeDetailModal()" class="icon icon-back mr-2"></i>
    </div>

    <div class="p-2 mr-4">
      {{detailModalItem?.title | translate}}
    </div>
    <div class="p-2 "></div>
  </div>
  <ng-container *ngTemplateOutlet="companySelect"></ng-container>
  <div class="font-weight-semi-bold total-debt-bg px-1 border-bottom">
    <!--    <ng-container *ngTemplateOutlet="itemRow; context: {item: { title: '_total', code: 'totalDebt' }, detail: true }"></ng-container>-->
    <ng-container *ngTemplateOutlet="itemRow; context: {item: { title: '_total', code: detailModalItem?.code }, detail: true }">
    </ng-container>
  </div>
  <div class="px-1 border-bottom">
    <ng-container *ngIf="(totalItems$ | async)?.totalDebtSummaryByCostCenter as byCostCenterData">
      <ng-container *ngFor="let data of byCostCenterData">
        <ng-container *ngTemplateOutlet="itemRow; context: {item: { title: data?.costCenterDescription, code: 'totalDebtByCostCenter', amount: data?.amount, currency: data?.currency}, detail: false }">
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
  <div *ngFor="let item of companyData(detailModalItem?.code)| async; last as last">
    <div>
      <div *ngIf="isDepthItem(detailModalItem?.code)" class="py-1 px-1">
        <div class="d-flex align-items-center justify-content-between py-2">
          <div class="flex-fill fin-title pr-3">
            <div>
              <ng-container>
                {{item.documentNumber}}
                <span class="font-size-13px">
                  / {{item.valueDate | date: "shortDate"}}
                </span>
              </ng-container>
            </div>
            <div class="font-size-13px ">
              {{ getStringToShortAndDot('_financial_due_date' | translate ) }}:
              {{item.dueDate | date: "shortDate"}} - {{item.companyName}}
            </div>
          </div>
          <div class="flex-grow-0 text-right right-box">
            <div>
              {{item?.amount | number}} {{item?.currency}}
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="isPaymentItem(detailModalItem.code)" class="py-1 px-1">
        <div class="d-flex align-items-center justify-content-between py-2">
          <div class="flex-fill fin-title">
            <div>
              <ng-container *ngIf="item.documentNumber">{{item.documentNumber}}</ng-container>
              <ng-container *ngIf="item.chequeNumber">{{item.chequeNumber}}</ng-container>
            </div>
            <div class="font-size-13px ">
              {{item.valueDate | date: "shortDate"}}
            </div>
          </div>
          <div class="flex-grow-0 text-right right-box">
            <div>
              {{item?.amount | number}} {{item?.currency}}
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>
</div>
<ng-template #itemRow let-arrow="arrow" let-bigItem="bigItem" let-borderBottom="borderBottom" let-item="item" let-detail="detail">
  <div *ngIf="(totalItems$ | async) as financialInformation" [ngClass]="{'border-bottom': borderBottom, 'py-3': bigItem, 'py-1': !bigItem}">
    <!--    <div (click)="openDetailModal(item, financialInformation[item?.code + suffix]?.length)" *ngIf="item.title"-->
    <div catUserClick [section]="'FINANCIAL_INFORMATION'" [subsection]="'SUB_DETAIL'" [data]="{type:item.code}"
      (click)="openDetailModal(item, item.code == 'totalDebtByCostCenter' ? 0 : 1)" *ngIf="item.title"
      class="d-flex align-items-center justify-content-between py-2 finance-item-row">
      <ng-container *ngIf="item.code === 'totalDebtByCostCenter' else otherDetailData">
        <ng-container  *ngIf="item?.title && item?.amount !== null">
          <div class="flex-fill fin-title">
            {{item.title | translate}}
          </div>
          <div class="flex-grow-0 text-right pr-4">
            {{item?.amount | number}} {{item?.currency}}
          </div>
        </ng-container>
      </ng-container>
      <ng-template #otherDetailData>
        <ng-container *ngIf="(financialInformation[item?.code + suffix]?.length && item.code !== 'totalDebyByCostCenter')">
          <div class="flex-fill fin-title">
            {{item.title | translate}}
          </div>
          <!--        <ng-container *ngIf="item?.code === 'totalDebt' else otherDebt">-->
          <!--          <div class="flex-grow-0 text-right" [ngClass]="{ 'pr-4': !detail }">-->
          <!--            <div *ngFor="let summaryItem of financialInformation[item?.code + totalDebyByCostCenter]">-->
          <!--              {{summaryItem?.amount | number}} {{summaryItem?.currency}}-->
          <!--            </div>-->
          <!--            <div *ngIf="financialInformation[item?.code + totalDebyByCostCenter]?.length === 0">-->
          <!--              0 TL-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          <div *ngIf="arrow" class="px-0 font-size-12px flex-grow-0 finance-item-row-icon">-->
          <!--            <i class="icon icon-chevron-right text-info"></i>-->
          <!--          </div>-->
          <!--        </ng-container>-->
          <!--        <ng-template #otherDebt>-->
          <div class="flex-grow-0 text-right" [ngClass]="{ 'pr-4': !detail }">
            <div *ngFor="let summaryItem of financialInformation[item?.code + suffix]">
              {{summaryItem?.amount | number}} {{summaryItem?.currency}}
            </div>
            <div *ngIf="financialInformation[item?.code + suffix]?.length === 0">
              0 TL
            </div>
          </div>
          <div *ngIf="arrow" class="px-0 font-size-12px flex-grow-0 finance-item-row-icon">
            <i class="icon icon-chevron-right text-info"></i>
          </div>
          <!--        </ng-template>-->
        </ng-container>
        <div *ngIf="!financialInformation[item?.code + suffix]?.length">
          <ng-container *ngIf="!!financialInformation[item?.code + suffix]">
            {{'_no_depth_found' | translate}}
          </ng-container>

          <ng-container *ngIf="!financialInformation[item?.code + suffix]">
            {{'_information_not_found' | translate}}
          </ng-container>
        </div>


      </ng-template>

    </div>
  </div>
</ng-template>
<ng-template #companySelect>
  <ng-select [searchable]="false" [clearable]="false" (change)="onChangeCompany($event)" [placeholder]="'_all' | translate"
    [(ngModel)]="companySelected" class="company-drp">
    <ng-option [value]="'_all'">{{"_all" | translate}}</ng-option>
    <ng-option *ngFor="let companyName of companyList" [value]="companyName">{{companyName}}</ng-option>
  </ng-select>
</ng-template>
<cat-basic-modal [(status)]="showExtractModal" [headerText]="'_statement_inquiry'">
  <cat-financial-extract></cat-financial-extract>
</cat-basic-modal>
<cat-loader [show]="(financialInformationLoading$ | async)"></cat-loader>
