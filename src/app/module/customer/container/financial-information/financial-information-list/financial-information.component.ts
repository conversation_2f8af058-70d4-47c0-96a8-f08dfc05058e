import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import snq from 'snq';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { CompanyDetails, CostCenterRow, FinancialInformationSummaryModel, FinancialModel } from '../../../model/financial.model';
import { map } from 'rxjs/operators';
import { CustomerModel } from '../../../model/customer.model';
import { CompanyModel } from '../../../../company/model/company.model';
import { systemFeature } from '../../../../shared/util/system-feature.util';
import { SystemFeature } from '../../../../shared/model/settings.model';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { SystemFeatureAction } from '../../../../shared/state/settings/settings.actions';
import { environment } from '../../../../../../environments/environment';
import { FinancialState } from '../../../state/financial.state';
import { GetFinancialInformationAction } from '../../../action/financial.actions';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { TranslateService } from '@ngx-translate/core';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-financial-information',
  templateUrl: './financial-information.component.html',
  styleUrls: ['./financial-information.component.scss'],
  animations: [
    trigger('details', [
      state('void', style({ left: '100%' })),
      state('*', style({ left: '0' })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class FinancialInformationComponent implements OnInit {
  @Select(FinancialState.financialInformation)
  financialInformation$: Observable<FinancialModel>;

  @Select(FinancialState.financialInformationLoading)
  financialInformationLoading$: Observable<boolean>;

  debtItems: { title: string; code: string; }[] = [
    // { title: '_my_payments_due_today', code: 'currentDebt' },
    // { title: '_my_overdue_payments', code: 'overdueDebt' },
    // { title: '_my_unpaid_payments', code: 'forwardTermDebt' },
  ];
  paymentItems: { title: string; code: string; }[] = [
    { title: '_my_czech', code: 'cheque' },
    { title: '_my_stocks', code: 'bond' },
    { title: '_my_advance_payments', code: 'advance' },
  ];
  detailModalItem: { title: string; code: string; };
  detailModalStatus: boolean;

  companyList: string[] = [];
  totalItems$: Observable<FinancialInformationSummaryModel>;
  companyDetails$: Observable<CompanyDetails[]>;

  suffix = 'Summary';
  totalDebtByCostCenterData: CostCenterRow[];
  companySelected: string;
  private customer: CustomerModel;
  private company: CompanyModel;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  @Select(SettingsState.systemFeaturesLoading)
  systemFeaturesLoading$: Observable<boolean>;
  showMutabakat = false;

  showExtractModal = false;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  mutabakatLang: string;
  PermissionEnum = PermissionEnum;
  // statementInquiryLang: string;
  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly translateService: TranslateService,
  ) {
    this.route.queryParams.subscribe(q => {
      this.detailModalStatus = Boolean('detailModalStatus' in q ? parseInt(q.detailModalStatus, 10) : 0);
      // this.detailModalStatus = Boolean('detailModalStatus' in q ? parseInt(q.detailModalStatus) : 0);
    });
  }

  ngOnInit() {
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    if (loginResponse) {
      this.store.dispatch(new GetFinancialInformationAction(snq(() => loginResponse.customer?.customerNumber)));
    }
    this.customer = this.store.selectSnapshot(LoginState.customer);
    this.company = this.store.selectSnapshot(LoginState.company);

    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$.subscribe(features => {
      if (features) {
        const financialDetails = systemFeature('financial_debt_details', features, false);
        this.showMutabakat = systemFeature('reconciliation', features, false);
        if (financialDetails) {
          this.debtItems = [
            { title: '_my_payments_due_today', code: 'currentDebt' },
            { title: '_my_overdue_payments', code: 'overdueDebt' },
            { title: '_my_unpaid_payments', code: 'forwardTermDebt' },
          ];
        }
      }
    });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    // generate total page items
    this.totalItems$ = this.financialInformation$.pipe(map(val => {
      this.totalDebtByCostCenterData = val?.totalDebtSummaryByCostCenter;
      return val;
    }));

    // generate total page items
    this.companyDetails$ = this.financialInformation$.pipe(map(val => {
      return val?.companyDetails;
    }));

    // generate company list on dropdown
    this.financialInformation$.subscribe((financialInformation: FinancialModel) => {
      if (financialInformation) {
        this.companyList = financialInformation.companyDetails?.map((item: CompanyDetails) => {
          return item.companyName;
        });
      }
      this.mutabakatLang = this.translateService.instant('_open_mutabakat_page');
    });

  }

  openMutakabatPage() {
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'mutabakatlar'
        ],
      )
      .then();
  }

  openDetailModal(detailModalItem: { title: string; code: string; }, detailItemLength: number) {
    // if (detailItemLength > 0 && detailModalItem.title !== '_total') {
    if (detailItemLength > 0 && detailModalItem.title !== '_total') {
      this.detailModalItem = detailModalItem;
      // this.detailModalStatus = true;
      this.router.navigate([], {
        queryParams: { detailModalStatus: 1 },
        queryParamsHandling: 'merge',
      }).then();
    }
  }

  closeDetailModal() {
    // this.detailModalStatus = false;
    window.history.back();
  }

  paymentToolsStatus(financialInformation) {
    return this.paymentItems.filter(item => {
      return financialInformation[item?.code + 'Summary']?.length > 0;
    }).length > 0;
  }

  onChangeCompany($event: any) {

    this.totalItems$ = this.financialInformation$.pipe(
      map((financialInformation: FinancialModel) => {
          if ($event === '_all') {
            return financialInformation;
          }
          return financialInformation.companyDetails.find((companyDetail: CompanyDetails) => {
            this.totalDebtByCostCenterData = financialInformation?.totalDebtSummaryByCostCenter;
            return companyDetail.companyName === $event;
          });
        },
      ),
    );

  }

  openExtractModal() {
    this.showExtractModal = true;
  }

  companyData(code: string) {
    return this.companyDetails$.pipe(map((compDetails: CompanyDetails[]) => {

      return compDetails?.reduce((acc, compItem) => {

        compItem[code + 'Details']?.map(item => {

          if ((this.companySelected && this.companySelected !== '_all') && this.companySelected !== compItem.companyName) {
            return acc;
          }

          acc.push({
            companyName: compItem.companyName,
            ...item,
          });
        });
        return acc;
      }, []);
    }));
  }

  isDepthItem(code: string) {
    return this.debtItems.find(item => item.code === code);
  }

  isPaymentItem(code: string) {
    return this.paymentItems.find(item => item.code === code);
  }

  getStringToShortAndDot(str) {
    return str.split(' ').map(n => n[0]).join('.');
  }
}
