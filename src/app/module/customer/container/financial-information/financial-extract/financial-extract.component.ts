import { Component, Input, OnInit } from '@angular/core';
import { MessageFrameService } from '../../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../../shared/enum/frame-message.enum';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngxs/store';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { UserModel } from '../../../../authentication/model/user.model';
import { isShowFormError } from '../../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { validateAllFormFields } from '../../../../../util/validate-all-form-fields.util';
import { FinancialService } from '../../../service/financial.service';
import { LogService } from '../../../service/log.service';

@Component({
  selector: 'cat-financial-extract',
  templateUrl: './financial-extract.component.html',
  styleUrls: ['./financial-extract.component.scss']
})
export class FinancialExtractComponent implements OnInit {
  @Input()
  inPage = false;
  extractForm: FormGroup = new FormGroup({
    startDate: new FormControl('', [Validators.required]),
    endDate: new FormControl('', [Validators.required]),
    CurrencyType: new FormControl(null, [Validators.required]),
    Email: new FormControl('', [
      Validators.required,
      Validators.email
    ])
  });

  currencyTypes = [
      {
        id: '1',
        name: 'TL'
      }, {
        id: '2',
        name: 'USD'
      }, {
        id: '3',
        name: 'EUR'
      }, {
        id: '4',
        name: 'GBT'
      }
  ];

  user: UserModel;
  emailChangeDisable = true;
  sendLoading = false;
  showSendSuccessMessage = false;
  sendShowError = false;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isOpenDropdown: boolean;

  constructor(
    private readonly messageFrameService: MessageFrameService,
    private readonly store: Store,
    private financialService: FinancialService,
    private readonly logger: LogService,
  ) {
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData() {
    this.user = this.store.selectSnapshot(LoginState.user);
    if (this.user) {
      this.extractForm.controls.Email.setValue(this.user.email);
    }
  }

  clearForm() {
    this.extractForm.reset();
    if (this.user) {
      this.extractForm.controls.Email.setValue(this.user.email);
    }
    this.showSendSuccessMessage = false;
    this.sendShowError = false;
    // this.extractForm.controls.EndDate.setValue(new Date().toISOString().slice(0, 10));
  }

  submitForm() {
    if (this.extractForm.valid) {
      console.log('extractForm: ', this.extractForm);
      const formData = {
        startDate: this.extractForm.value.startDate,
        endDate: this.extractForm.value.endDate,
        currency: this.extractForm.value?.CurrencyType
      };

      this.sendLoading = true;
      this.logger.action('FINANCIAL_INFORMATION', 'FINANCIAL_EXTRACT_REQUEST_FORM', formData).subscribe();
      this.financialService.reconciliationStatementSend(formData)
        .subscribe(send => {
            if (send) {
              this.sendLoading = false;
              this.showSendSuccessMessage = true;
            } else {
              this.sendLoading = false;
              this.sendShowError = true;
            }
          },
          () => {
            this.sendLoading = false;
          });
    } else {
      validateAllFormFields(this.extractForm);
    }
  }

  startDateValue(e) {
    this.extractForm.controls.startDate.setValue(e);
  }

  endDateValue(e) {
    this.extractForm.controls.endDate.setValue(e);
  }

  navigateToBack() {
    // if public page
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    this.back();
  }

  back() {
    window.history.back();
  }

  openDropdown() {
    this.isOpenDropdown = true;
  }

  closeDropdown() {
    this.isOpenDropdown = false;
  }
}
