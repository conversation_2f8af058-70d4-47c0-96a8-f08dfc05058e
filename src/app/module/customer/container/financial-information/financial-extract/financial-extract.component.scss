.special-mh-open {
  height: 460px;
}
.full-page{
  height: calc(100vh - 120px);
}
.financial-extract {

  input {
    color-scheme: light only;
  }

  .ng-select ::ng-deep .ng-select-container {
    border-radius: 6px;
    border: 1px solid #D7E5EA;
  }

  .ng-select ::ng-deep .ng-dropdown-panel.ng-select-bottom {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    text-align: left;
  }
}

.after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.success-message {
  font-size: 26px;
  font-weight: 700;
  line-height: 1;
}

.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.message-class {
  font-size: 60px;
  padding-top: 1px;
}

.z-index-over-loader {
  z-index: 10001;
}

.download-file {
  width: max-content;
  height: max-content;
  text-align: center;
  display: flex;
  justify-content: center;
  margin-top: 2px;
  border: 2px;
}

.mailsender {
  min-width: 75%;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
}

.load-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

::ng-deep .ngb-dp-arrow-btn {
  margin: 2px !important;
  padding: 0 !important;
  z-index: 1;
  width: 17px !important;
}

::ng-deep .ngb-dp-arrow {
  flex: none  !important;
  margin: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  width: 16px !important;
}

::ng-deep .ngb-dp-header{
  border-radius: 16px 16px 0 0 !important;
}

::ng-deep .custom-select {
  background: #EEF2F4 url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") right 0.55rem center/8px 10px no-repeat !important;
}

.border-radius-6px {
  border-radius: 6px;
}

.display-none {
  display: none;
}
