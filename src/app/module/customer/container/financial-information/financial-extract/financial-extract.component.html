<div class="financial-extract px-4" [class.full-page]="inPage" [class.special-mh-open]="isOpenDropdown && !inPage">
  <form [formGroup]="extractForm">
    <div class="form-group">
      <input [attr.disabled]="emailChangeDisable" class="form-control" [placeholder]="'_email' | translate"
             type="text" name="email" id="Email"
             formControlName="Email">
      <div
        [ngClass]="{ 'd-block': isShowError(extractForm.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(extractForm.controls.Email) | translate }}
      </div>
    </div>
    <div>
      <cat-date-picker-range
        [form]="extractForm"
        (fromDateValue)="startDateValue($event)"
        (toDateValue)="endDateValue($event)"
      ></cat-date-picker-range>
    </div>
    <!--    <div class="d-flex flex-row flex-wrap justify-content-between form-group">-->
    <!--      <label>-->
    <!--        {{ "_start_date" | translate }}-->
    <!--      </label>-->
    <!--      <div class="w-100 d-flex flex-row flex-wrap justify-content-between">-->
    <!--        <input type="date" class="form-control mb-2" name="startDate" id="startDate" formControlName="startDate">-->
    <!--      </div>-->
    <!--      <div-->
    <!--        [ngClass]="{ 'd-block': isShowError(extractForm.controls.startDate) || isShowError(extractForm.controls.endDate) }"-->
    <!--        class="invalid-feedback pl-3"-->
    <!--      >-->
    <!--        {{ getFormErrorMessage(extractForm.controls.startDate) | translate }}-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="d-flex flex-row flex-wrap justify-content-between form-group">-->
    <!--      <label>-->
    <!--        {{ "_end_date" | translate }}-->
    <!--      </label>-->
    <!--      <div class="w-100 d-flex flex-row flex-wrap justify-content-between">-->
    <!--        <input type="date" class="form-control mb-2" name="endDate" id="endDate" formControlName="endDate">-->
    <!--      </div>-->
    <!--      <div-->
    <!--        [ngClass]="{ 'd-block': isShowError(extractForm.controls.endDate) || isShowError(extractForm.controls.endDate) }"-->
    <!--        class="invalid-feedback pl-3"-->
    <!--      >-->
    <!--        {{ getFormErrorMessage(extractForm.controls.endDate) | translate }}-->
    <!--      </div>-->
    <!--    </div>-->
    <div *ngIf="currencyTypes?.length" class="form-group">
      <ng-select [items]="currencyTypes"
                 bindLabel="name"
                 bindValue="id"
                 [searchable]="false"
                 [clearable]="false"
                 [placeholder]="'_currency_type' | translate"
                 (open)="openDropdown()"
                 (close)="closeDropdown()"
                 formControlName="CurrencyType">
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(extractForm.controls.CurrencyType) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(extractForm.controls.CurrencyType) | translate }}
      </div>
    </div>
    <div class="form-group d-flex flex-row flex-nowrap justify-content-between">
<!--      <input type="submit" [value]="'_clear_form' | translate"-->
<!--             class="form-control btn btn-secondary btn-sm text-white mb-2 mr-1-1"-->
<!--             (click)="clearForm()" [disabled]="sendLoading">-->
      <button [disabled]="!extractForm?.valid || sendLoading || showSendSuccessMessage" (click)="submitForm()"
              class="form-control btn btn-warning btn-sm text-white mb-2 load-button" [ngClass]="{
                'display-none': showSendSuccessMessage
              }">
        {{'_send_email' | translate }}
        <div
          class="download-file text-lg-right rounded-circle d-flex justify-content-center align-items-center ml-2"
          [class.spinner]="sendLoading">
          <i class="icon icon-spinner8 border-light" *ngIf="sendLoading"></i>
        </div>
      </button>
      <button class="form-control btn btn-success btn-sm text-white mb-2 load-button" *ngIf="showSendSuccessMessage">
        {{'_mail_sended' | translate }}
      </button>
    </div>
    <div class="form-group d-flex flex-row flex-nowrap">
      <!-- <div *ngIf="showSendSuccessMessage" class="modal-btn btn btn-gradient btn-block text-white shadow mb-3 px-4 btn-success border-radius-6px"> -->
        <!-- {{ '_mail_sended' | translate }} -->
      <!-- </div> -->
      <div *ngIf="sendShowError" class="modal-btn btn btn-gradient btn-block text-white shadow mb-3 px-4 btn-danger border-radius-6px">
        {{ '_fail_send_email' | translate }}
      </div>
    </div>
  </form>
</div>

