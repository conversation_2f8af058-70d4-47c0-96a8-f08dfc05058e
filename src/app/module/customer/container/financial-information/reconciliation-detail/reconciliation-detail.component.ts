import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FrameMessageEnum } from '../../../../shared/enum/frame-message.enum';
import { MessageFrameService } from '../../../../shared/service/message-frame.service';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { FinancialState } from '../../../state/financial.state';
import {
  MutabakatDetailModal,
  MutabakatDetailRequestModel,
  MutabakatListModel,
} from '../../../model/financial.model';
import { ClearMutabakatDetailAction, GetMutabakatDetailAction, GetMutabakatListAction } from '../../../action/financial.actions';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { FinancialService } from '../../../service/financial.service';
import { isShowFormError } from '../../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { MutabakatStatusEnum } from 'src/app/module/definition/enum/mutabakat-type.enum';
import { LogService } from '../../../service/log.service';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-reconciliation-detail',
  templateUrl: './reconciliation-detail.component.html',
  styleUrls: ['./reconciliation-detail.component.scss']
})
export class ReconciliationDetailComponent implements OnInit, OnDestroy {

  @Input()
  showInPage = false;
  @Output()
  closeReconciliationDetail: EventEmitter<boolean> = new EventEmitter<boolean>();
  showHeader = false;

  @ViewChild('reason') reasonInput: ElementRef;

  @Select(FinancialState.mutabakatList)
  mutabakatList$: Observable<MutabakatListModel[]>;
  mutabakatList: MutabakatListModel[];
  findMutabakat: MutabakatListModel;

  @Select(FinancialState.mutabakatDetail)
  mutabakatDetail$: Observable<MutabakatDetailModal>;
  @Input()
  mutabakatDetail: MutabakatDetailModal = null;

  @Select(FinancialState.mutabakatLoading)
  mutabakatLoading$: Observable<boolean>;

  @Select(FinancialState.mutabakatReplyLoading)
  mutabakatReplyLoading$: Observable<boolean>;

  mutabakatDetailRequestData: MutabakatDetailRequestModel;
  statusEnum = MutabakatStatusEnum;

  showMutabakatReplyModal = false;
  showReplySuccessModal = false;
  openSenderInfoModal = false;


  private subscriptions$: Subject<boolean> = new Subject();
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  PermissionEnum = PermissionEnum;

  constructor(
    private readonly messageFrameService: MessageFrameService,
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly financialService: FinancialService,
    private readonly logger: LogService,
  ) { }

  ngOnInit(): void {
    if (!this.mutabakatDetail) {
      this.route.queryParams.subscribe(x => {
        this.showHeader = x.showHeader === 'true';
        this.mutabakatDetailRequestData = {
          formNumber: x?.formNumber,
          guuid: x?.guuid,
          partnerNumber: x?.partnerNumber,
          sequenceNumber: x?.sequenceNumber
        };
      });
      this.mutabakatSubs();
      this.mutabakatList = this.store.selectSnapshot(FinancialState.mutabakatList);
      if (!this.mutabakatList?.length) {
        this.store.dispatch(new GetMutabakatListAction());
      }
    }
  }

  mutabakatReply() {
    this.showMutabakatReplyModal = true;
  }

  getMutabakatDetail() {
    if (this.findMutabakat) {
      this.store.dispatch(new GetMutabakatDetailAction(
        {
          formNumber: this.findMutabakat.formNumber,
          guuid: this.findMutabakat.guuid,
          partnerNumber: this.findMutabakat?.partnerNumber,
          sequenceNumber: this.findMutabakat.sequenceNumber
        }
      ));
    }
  }

  mutabakatSubs() {
    this.mutabakatList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(mutabakatlar => {
        if (mutabakatlar && mutabakatlar?.length) {
          this.mutabakatList = mutabakatlar;
          this.findMutabakat = mutabakatlar.find(x =>
            x.guuid === this.mutabakatDetailRequestData?.guuid &&
            x.formNumber === this.mutabakatDetailRequestData?.formNumber &&
            x.sequenceNumber === this.mutabakatDetailRequestData?.sequenceNumber &&
            x.partnerNumber === this.mutabakatDetailRequestData?.partnerNumber
          );
          this.getMutabakatDetail();
        }
      });
    this.mutabakatDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(detail => {
        if (detail) {
          this.mutabakatDetail = detail;
        }
      });
  }

  setStatusColor(status: number): string {
    const statusColor = this.statusEnum[status];
    return statusColor ? statusColor : 'dark';
  }

  onCall(phone: string) {
    this.logger
      .action('RECONCILIATION', 'RECONCILIATION_PHONE_CLICK', { phone })
      .subscribe();
    this.messageFrameService.sendMessage(FrameMessageEnum.call_phone, { phone });
    this.logger.action('RECONCILIATION', 'CALL_PHONE_CLICK', { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('RECONCILIATION', 'RECONCILIATION_MAIL_CLICK', { mail })
      .subscribe();
    this.messageFrameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
    this.logger.action('RECONCILIATION', 'MAIL_CLICK', { mail });
  }

  replySend(e: any) {
    if (e) {
      this.showMutabakatReplyModal = false;
      this.showReplySuccessModal = true;
      this.store.dispatch(new GetMutabakatDetailAction({
        formNumber: this.mutabakatDetail?.item?.formNumber,
        guuid: this.mutabakatDetail?.item?.guuid,
        partnerNumber: this.mutabakatDetail?.item?.partnerNumber,
        sequenceNumber: this.mutabakatDetail?.item?.sequenceNumber,
      }));
      this.logger.action('RECONCILIATION', 'REPLY_SEND', {
        formNumber: this.mutabakatDetail?.item?.formNumber,
        guuid: this.mutabakatDetail?.item?.guuid,
        partnerNumber: this.mutabakatDetail?.item?.partnerNumber,
        sequenceNumber: this.mutabakatDetail?.item?.sequenceNumber
      }).subscribe();
    }
  }

  navigateToBack() {
    this.store.dispatch(new ClearMutabakatDetailAction());
    if (this.showInPage) {
      this.closeReconciliationDetail.emit(true);
      return;
    }
    window.history.back();
  }

  ngOnDestroy(): void {
    this.store.dispatch(new ClearMutabakatDetailAction());
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
