<div class="pt-3" [class]="{'pb-5' : showHeader}">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="showHeader">
    <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
    {{ "_mutabakatlar" | translate }}
  </div>
  <div class="mutabakat-detail-page px-4 pb-3">
    <div class="h4 pb-4 mb-0 text-center nav-back">
      <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
      {{ '_mutabakat_detail' | translate }}
    </div>
    <div class="card h-100">
      <div class="card-header py-2 bg-{{setStatusColor(mutabakatDetail?.item?.status)}}"></div>
      <div class="card-body p-0">
        <table class="table table-striped mb-0">
          <tbody>
          <tr>
            <td class="py-3 font-weight-bold text-wrap">{{'_mutabakat_form_number' | translate }}</td>
            <td class="py-3">{{ mutabakatDetail?.item?.formNumber || '-' }}</td>
          </tr>
          <tr>
            <td class="py-3 font-weight-bold">{{'_mutabakat_status' | translate }}</td>
            <td *ngIf="mutabakatDetail?.item?.status"
                class="py-3 text-{{setStatusColor(mutabakatDetail?.item?.status)}}">
              {{ ('_mutabakat_status_' + mutabakatDetail?.item?.status) | translate }}
            </td>
          </tr>
          <tr>
            <td class="py-3 font-weight-bold text-wrap">{{'_mutabakat_end_date' | translate }}</td>
            <td class="py-3">{{ (mutabakatDetail?.item?.termEndDate | date) || '-' }}</td>
          </tr>
          <tr *ngIf="mutabakatDetail?.lines">
            <td colspan="2" class="py-3">
              <span class="font-weight-bold">{{'_mutabakat_currency_lines' | translate }}</span>
              <ul *ngFor="let lines of mutabakatDetail?.lines" class="mb-0">
                <li *ngIf="lines?.amount >= 0">
                    {{lines.amount === 0 ? "0,0" : lines.amount}} {{lines?.amountCurrency}}
                  <span *ngIf="lines?.amountCurrency !== 'TRY'">
                    ({{lines?.amountInTRY === 0 ? "0,0" : lines?.amountInTRY}} TRY)
                  </span>
                  <span *ngIf="lines?.amount" [ngClass]="{'text-success': lines?.amount < 0, 'text-warning' : lines?.amount >= 0 }">
                    ({{ (lines.amount < 0 ? '_mutabakat_claim' : '_mutabakat_dept') | translate }})
                  </span>
                </li>
              </ul>
            </td>
          </tr>
          <tr *ngIf="mutabakatDetail?.sender?.email" (click)="openSenderInfoModal = true">
            <td class="py-3 font-weight-bold text-wrap">{{'_email' | translate }}</td>
            <td class="py-3 d-flex">
              {{ mutabakatDetail?.sender?.email }}
              <i class="icon icon-chevron-right ml-auto"></i>
            </td>
          </tr>
          <tr>
            <td class="py-3 font-weight-bold text-wrap">{{'_total_amount' | translate }}</td>
            <td class="py-3">
                {{mutabakatDetail?.totalLine?.amountInTRY === 0 ? "0,0" : mutabakatDetail?.totalLine?.amountInTRY}}
              <span *ngIf="mutabakatDetail?.totalLine?.amountInTRY !== null"> TRY</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div *ngIf="mutabakatDetail?.item?.canBeReplied" class="card-footer bg-white">
        <div class="text-center" [style.height.px]="48">
          <button *ngIf="mutabakatDetail?.item?.canBeReplied" class="modal-btn btn btn-warning btn-gradient btn-block rounded-lg text-white shadow mb-3 mt-0 px-4"
                  (click)="mutabakatReply()">
            {{ '_mutabakat_reply' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<cat-basic-modal [(status)]="showMutabakatReplyModal">
  <cat-reconciliation-reply [mutabakatDetail]="mutabakatDetail" (replySended)="replySend($event)">
  </cat-reconciliation-reply>
</cat-basic-modal>

<cat-success-modal [(status)]="showReplySuccessModal" [message]="'_general_successfully_send_form'">
</cat-success-modal>

<cat-basic-modal [(status)]="openSenderInfoModal" [backdropClose]="true" *ngIf="mutabakatDetail?.sender">
  <div class="mb-3">
    <div *ngIf="mutabakatDetail?.sender">
      <ul class="service-person">
        <li class="service-person-item">
          <div class="service-person-item-name">
            {{ mutabakatDetail?.sender?.name }}
          </div>
          <div class="pssr-title">
            {{ mutabakatDetail?.sender?.companyName }}
          </div>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item"
                (click)="onCall(mutabakatDetail?.sender?.phone)">
              <div class="icon-area">
                <i class="icon icon-phone-call"></i>
              </div>
              {{ mutabakatDetail?.sender?.phone }}
            </li>
          </ul>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item" (click)="onMail(mutabakatDetail?.sender?.email)">
              <div class="icon-area">
                <i class="icon icon-contact"></i>
              </div>
              {{ mutabakatDetail?.sender?.email }}
            </li>
          </ul>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item">
              <div class="icon-area">
                <i class="icon icon-location"></i>
              </div>
              {{ mutabakatDetail?.sender?.address }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</cat-basic-modal>
<cat-loader [show]="(mutabakatLoading$ | async) || (mutabakatReplyLoading$ | async)">
</cat-loader>
