@import "variable/icon-variable";
@import "variable/bootstrap-variable";

.mutabakat-detail-page {
  height: calc(100vh - 73px);

  table:first-child tr td:first-child {
    max-width: 110px;
  }

  .download-file {
    width: max-content;
    height: max-content;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 2px;
    border: 2px;
  }

  .mailsender {
    min-width: 75%;
    width: auto;
    text-align: center;
    display: flex;
    justify-content: center;
  }

  .load-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}

.ng-select ::ng-deep .ng-select-container  {
  border-radius: 6px;
  border: 1px solid #D7E5EA;
}

.icon-agreed {
  color: $success;
  &:before {
    content: $icon-success;
  }
}
.icon-disagree {
  color: $danger;
  &:before {
    content: $icon-x;
  }
}
.icon-not_related {
  color: $warning;
  &:before {
    content: $icon-persons;
  }
}
.service-person {
  margin: 0;
  padding: 0;
  list-style: none;
  border-bottom: 1px solid #dbdbdb;

  &-item {
    border-top: 1px solid #dbdbdb;
    padding: 1.25rem;

    &-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #505050;
    }

    &-detail {
      margin: 0;
      padding: 0;
      list-style: none;

      &-item {
        overflow-wrap: anywhere;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: #505050;
        margin-top: 1rem;

        .icon-area {
          margin-right: 1rem;
          border-radius: 50%;
          background-color: #ebebeb;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0.625rem;
        }
      }
    }
  }
}
