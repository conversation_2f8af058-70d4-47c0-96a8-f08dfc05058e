@import "variable/bootstrap-variable";

.reconciliation-class {
  ::ng-deep.mat-tab-label-container {
    margin-bottom: 16px;
  }

  ::ng-deep .mat-tab-label.mat-tab-label-active {
    border-bottom: 3px solid #FFA300;
    background-color: transparent;
    font-weight: 700;
    opacity: 1;
  }
  .mutabakat-list{
    height: calc(100vh - 125px);
    &-item {
      background: #ffffff;
      mix-blend-mode: normal;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      position: relative;

      &-color {
        position: absolute;
        width: 10px;
        height: 100%;
        left: 0;
        top: 0;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        z-index: 1;

        &-green {
          background: #5d8d1c;
        }

        &-orange {
          background: $warning;
        }
      }

      .two-dots {
        padding-left: 25px;

        &:before {
          display: inline-block;
          content: ":";
          margin-left: -25px;
          padding-left: 10px;
          padding-right: 10px;
        }
      }
    }
  }
  ::ng-deep .mat-tab-labels > .mat-tab-label:first-child {
    padding: 0 !important;
    margin: 0 !important;
    min-width: 20px !important;
    max-width: 30px !important;
  }
}
