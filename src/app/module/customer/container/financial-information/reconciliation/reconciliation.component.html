<div class="pt-3" [class]="{'pb-5' : showHeader}" [class.d-none]="showMutabakatDetail">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="showHeader">
    <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
    {{ "_mutabakatlar" | translate }}
  </div>
  <div class="reconciliation-class">
      <mat-tab-group mat-align-tabs="center" [selectedIndex]="1" disableRipple>
        <mat-tab *ngIf="!showHeader" disabled>
          <ng-template mat-tab-label>
          <span style="width: 30px">
            <i class="icon icon-back mr-2 float-left" style="color: black; font-weight: 900;" (click)="navigateToBack()"></i>
          </span>
          </ng-template>
        </mat-tab>
        <mat-tab [label]="'_mutabakatlar' | translate"
                 *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ViewReconciliationList) >= 0)">
          <div class="mutabakat-list px-4" *ngIf="mutabakatList?.length else emptyMutabakat">
            <div class="pb-4">
              <ng-container *ngFor="let mutabakat of mutabakatList">
                <div class="mb-3 mutabakat-list-item overflow-hidden" (click)="goMutabakatDetail(mutabakat)">
                  <div class="mutabakat-list-item-color bg-{{setStatusColor(mutabakat?.status)}}"></div>
                  <div class="px-4 py-3">
                    <div class="d-flex justify-content-between mb-2 overflow-hidden">
                      <div class="font-weight-semi-bold h6">
                        {{ mutabakat?.formNumber || '-' }}
                      </div>
                      <div class="font-weight-semi-bold text-{{setStatusColor(mutabakat?.status)}}">
                        <i class="icon icon-{{setStatusIcon(mutabakat?.status)}}"></i>
                      </div>
                      <i class="icon icon-chevron-right"></i>
                    </div>
                    <div class="row no-gutters">
                      <div class="col-5">
                        {{ '_mutabakat_end_date' | translate }}
                      </div>
                      <div class="col-7 two-dots">
                        {{ mutabakat?.termEndDate | date }}
                      </div>
                    </div>
                    <div *ngIf="mutabakat?.companyName" class="row no-gutters">
                      {{ mutabakat?.companyName | slice :0: 25 }}
                      {{ mutabakat?.companyName?.length >= 25 ? '...' : ''}}
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
          <ng-template #emptyMutabakat>
            <div class="text-center" *ngIf="!loading">
              <cat-empty-content [iconName]="'contract'" [message]="'_mutabakat_list_empty'">
              </cat-empty-content>
            </div>
          </ng-template>
        </mat-tab>
        <mat-tab [label]="'_statement_inquiry' | translate"
                 *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitReconciliationAnswer) >= 0) && (FinancialReconciliationSend | async)">
          <ng-container class="mutabakat-list">
            <cat-financial-extract [inPage]="true"></cat-financial-extract>
          </ng-container>
        </mat-tab>
      </mat-tab-group>
  </div>
</div>
<cat-reconciliation-detail *ngIf="showMutabakatDetail"
                           [mutabakatDetail]="mutabakatDetail"
                           [showInPage]="true"
                           (closeReconciliationDetail)="closeMutabakatDetail($event)">
</cat-reconciliation-detail>

<cat-loader [show]="loading"></cat-loader>
