import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MessageFrameService } from '../../../../shared/service/message-frame.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { FinancialState } from '../../../state/financial.state';
import { MutabakatDetailModal, MutabakatListModel } from '../../../model/financial.model';
import { takeUntil } from 'rxjs/operators';
import { GetMutabakatDetailAction, GetMutabakatListAction } from '../../../action/financial.actions';
import { MutabakatStatusEnum } from 'src/app/module/definition/enum/mutabakat-type.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { LogService } from '../../../service/log.service';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';

@Component({
  selector: 'cat-reconciliation',
  templateUrl: './reconciliation.component.html',
  styleUrls: ['./reconciliation.component.scss']
})
export class ReconciliationComponent implements OnInit, OnDestroy {
  @Input()
  showHeader = false;

  user: any;
  @Select(FinancialState.mutabakatList)
  mutabakatList$: Observable<MutabakatListModel[]>;
  mutabakatList: MutabakatListModel[];

  @Select(FinancialState.mutabakatDetail)
  mutabakatDetail$: Observable<MutabakatDetailModal>;
  @Input()
  mutabakatDetail: MutabakatDetailModal = null;
  showMutabakatDetail = false;

  @Select(FinancialState.mutabakatLoading)
  mutabakatLoading$: Observable<boolean>;
  loading: boolean;

  statusEnum = MutabakatStatusEnum;
  PermissionEnum = PermissionEnum;

  private subscriptions$: Subject<boolean> = new Subject();
  borusanBlockedActions: string[];
  FinancialReconciliationSend: Observable<boolean>;
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  constructor(
    private readonly messageFrameService: MessageFrameService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly store: Store,
    private readonly logger: LogService,
    private readonly permissionService: HasPermissionsService
  ) {
  }

  ngOnInit(): void {
    const {
      showHeader
    } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    this.mutabakatListGet();
    this.mutabakatSubs();
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    this.FinancialReconciliationSend = this.permissionService.hasPermission(PermissionEnum.FinancialReconciliationSend)
  }

  goMutabakatDetail(mutabakat: MutabakatListModel) {
    this.showMutabakatDetail = true;
    const detailReq = {
          formNumber: mutabakat?.formNumber,
          guuid: mutabakat?.guuid,
          partnerNumber: mutabakat?.partnerNumber,
          sequenceNumber: mutabakat?.sequenceNumber
        };
    this.store.dispatch(new GetMutabakatDetailAction(detailReq));
    this.mutabakatDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe( detail => {
        if (detail) {
          this.mutabakatDetail = detail;
        }
      });
    this.logger.action('RECONCILIATION', 'DETAIL_OPEN', detailReq).subscribe();
  }

  mutabakatListGet() {
    this.store.dispatch(new GetMutabakatListAction());
  }

  mutabakatSubs() {
    this.mutabakatList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(mutabakatlar => {
        if (mutabakatlar && mutabakatlar?.length) {
          this.mutabakatList = mutabakatlar;
        }
      });
    this.mutabakatLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(load => {
        this.loading = load;
      });
  }

  setStatusColor(status: number): string {
    const statusColor = this.statusEnum[status];
    return statusColor ? statusColor : 'dark';
  }

  setStatusIcon(status: number): string {
    const statusIcon = this.statusEnum[status];
    const iconList = {
      warning: 'contact',
      success: 'success',
      danger: 'x',
      human: 'persons',
      secondary: 'eye'
    };
    return iconList[statusIcon];
  }

  navigateToBack() {
    window.history.back();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  closeMutabakatDetail(e: any) {
    console.log('close Detail', e);
    this.store.dispatch(new GetMutabakatListAction());
    this.showMutabakatDetail = false;
  }
}
