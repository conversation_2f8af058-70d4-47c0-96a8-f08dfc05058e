import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MutabakatReplyActionEnum } from 'src/app/module/definition/enum/mutabakat-type.enum';
import { isShowFormError } from '../../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import {
  MutabakatDetailModal,
  MutabakatReplyReasonListModel,
  MutabakatReplyRequestModel,
} from '../../../model/financial.model';
import { validateAllFormFields } from '../../../../../util/validate-all-form-fields.util';
import { FinancialService } from '../../../service/financial.service';
import { Select, Store } from '@ngxs/store';
import {
  ClearMutabakatReplyDetailAction,
  GetMutabakatReplyReasonsAction,
  SendMutabakatReplyAction
} from '../../../action/financial.actions';
import { FinancialState } from '../../../state/financial.state';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { LogService } from '../../../service/log.service';

@Component({
  selector: 'cat-reconciliation-reply',
  templateUrl: './reconciliation-reply.component.html',
  styleUrls: ['./reconciliation-reply.component.scss']
})
export class ReconciliationReplyComponent implements OnInit {
  @Input()
  mutabakatDetail: MutabakatDetailModal;

  @Output()
  replySended: EventEmitter<boolean> = new EventEmitter<boolean>();

  minHeight = 15;

  replyForm = new FormGroup({
    action: new FormControl(null, [Validators.required]),
    reason: new FormControl(null, []),
    description: new FormControl(null, []),
  });
  MutabakatReplyActionEnum = MutabakatReplyActionEnum;

  @Select(FinancialState.mutabakatReplySendStatus)
  mutabakatReplySendStatus$: Observable<any>;
  mutabakatReplyLoading = false;
  needDescription = false;

  @Select(FinancialState.mutabakatReplyReasonsList)
  mutabakatReplyReasonList$: Observable<MutabakatReplyReasonListModel[]>;
  mutabakatReplyReasonList: MutabakatReplyReasonListModel[];

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  borusanBlockedActions: string[] = null;
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly financialService: FinancialService,
    private readonly store: Store,
    private readonly logger: LogService,
  ) { }

  ngOnInit(): void {
    this.mutabakatReplyReasonList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((list) => {
        if (list && list?.length > 1) {
          this.replyForm.get('reason').setValidators(Validators.required);
        } else if (list?.length === 1 && list[0].needDescription) {
          this.replyForm.get('description').setValidators(Validators.required);
        } else {
          this.replyForm.patchValue({
            reason: null,
            description: null
          });
          this.replyForm.get('reason').setValidators(null);
        }
        this.mutabakatReplyReasonList = list;
      });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  sendMutabakatReply() {
    if (this.replyForm.valid) {
      const replyData: MutabakatReplyRequestModel = {
        formNumber: this.mutabakatDetail.item.formNumber,
        guuid: this.mutabakatDetail.item.guuid,
        sequenceNumber: this.mutabakatDetail.item.sequenceNumber,
        action: this.replyForm.value.action,
        reason: typeof this.replyForm.value?.reason === 'string'
          ? this.replyForm.value?.reason
          : this.replyForm.value?.description,
        description: this.replyForm.value?.description,
        partnerNumber: this.mutabakatDetail.item.partnerNumber
      };
      console.log(replyData);
      this.store.dispatch(new SendMutabakatReplyAction(replyData));
      this.mutabakatReplySendStatus$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((status) => {
          if (status) {
            this.replySended.emit(true);
          }
        });
      this.logger.action('RECONCILIATION', 'REPLY_FORM', replyData).subscribe();
    } else {
      validateAllFormFields(this.replyForm);
    }
  }

  reasonChange(e: any) {
    this.needDescription = false;
    this.replyForm.get('description').setValidators(null);
    this.replyForm.patchValue( {
      description: null,
    });
    this.needDescription = this.mutabakatReplyReasonList?.find(r => r.value === e)?.needDescription;
    this.needDescription = this.needDescription === undefined
      ? this.mutabakatReplyReasonList?.find(r => r.value === null)?.needDescription
      : this.needDescription;
    if (this.needDescription) {
      this.replyForm.get('description').setValidators(Validators.required);
    }
  }

  actionChange(e: any) {
    this.clearMutabakat();
    this.store.dispatch(new GetMutabakatReplyReasonsAction(e));
  }

  openedAction(e?: boolean) {
    this.minHeight = e ? 35 : this.minHeight;
  }

  closedAction() {
    this.minHeight = 15;
  }

  clearMutabakat() {
    this.store.dispatch(new ClearMutabakatReplyDetailAction());
    this.replyForm.patchValue({
      reason: null,
      description: null
    });
    this.replyForm.get('description').setValidators(null);
    this.replyForm.get('reason').setValidators(null);
  }
}
