<form [formGroup]="replyForm">
  <div class="my-3" [style.min-height.vh]="minHeight">
    <div class="form-group">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [clearable]="false"
        [placeholder]="'_mutabakat_reply_dropdown' | translate"
        [dropdownPosition]="'bottom'"
        (change)="actionChange($event)"
        (open)="openedAction()"
        (close)="closedAction()"
        formControlName="action"
      >
        <ng-option
          *ngFor="let replyEnum of MutabakatReplyActionEnum | enumToArray"
          [value]="replyEnum.key"
          class="text-wrap"
        >
          {{ ('_mutabakat_reply_' + replyEnum.value)| translate }}
        </ng-option>
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(replyForm.controls.action) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(replyForm.controls.action) | translate }}
      </div>
    </div>
    <div class="form-group" *ngIf="mutabakatReplyReasonList?.length > 1">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [clearable]="false"
        [placeholder]="'_mutabakat_reply' | translate"
        [dropdownPosition]="'bottom'"
        (change)="reasonChange($event)"
        (open)="openedAction(true)"
        (close)="closedAction()"
        formControlName="reason"
      >
        <ng-option
          *ngFor="let reply of mutabakatReplyReasonList"
          [value]="reply.value"
          class="text-wrap"
        >
          {{ reply.text }}
        </ng-option>
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(replyForm.controls.reason) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(replyForm.controls.reason) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        [name]="'description'"
        [placeholder]="'_description' | translate"
        class="form-control form-control"
        formControlName="description"
        minlength="2"
        maxlength="500"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(replyForm.controls.description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(replyForm.controls.description) | translate }}
      </div>
    </div>
  </div>
  <button (click)="sendMutabakatReply()" [disabled]="mutabakatReplyLoading
          || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitReconciliationAnswer) >= 0)"
          class="modal-btn btn btn-warning btn-gradient btn-block rounded-lg text-white shadow mb-3 mt-0 px-4">
    <div class="w-100 d-flex justify-content-center align-content-center">
      {{ '_send' | translate }}
      <div
        class="download-file text-lg-right rounded-circle d-flex justify-content-center align-items-center ml-2"
        [class.spinner]="mutabakatReplyLoading">
        <i class="icon icon-spinner8 border-light" *ngIf="mutabakatReplyLoading"></i>
      </div>
    </div>
  </button>
</form>
