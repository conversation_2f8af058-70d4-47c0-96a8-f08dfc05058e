import { Component, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { CustomerState } from '../../state/customer.state';
import { CustomerStateModel } from '../../state/customer.state';
import { CompanyInformationsAction } from '../../action/customer.actions';
import { map } from 'rxjs/operators';

@Component({
  selector: 'cat-company-informations',
  templateUrl: './company-informations.component.html',
  styleUrls: ['./company-informations.component.scss']
})
export class CompanyInformationsComponent implements OnInit {

  constructor(
    private readonly store: Store,
  ) { }
  companyInformations: any;
  customer: CustomerStateModel;
  loading: boolean;

  ngOnInit() {
    this.loading = true;
    this.store.dispatch(new CompanyInformationsAction())
    .pipe(map(() => this.store.selectSnapshot(CustomerState.getCompanyInformations)))
    .subscribe((result: any) => {
      this.companyInformations = JSON.parse(result?.companyInfo);
      this.loading = false;
    },
    error => {
      console.error(error);
      this.loading = false;
      // this.error = true;
    },
    );
  }
  navigateToBack() {
    window.history.back();
  }
}
