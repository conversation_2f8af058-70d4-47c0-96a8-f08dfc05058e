<div class="company-info" *ngIf="!loading && companyInformations">
  <div class="company-info-header">
    <div class="company-info-header-title">
      {{ '_company_title' | translate }}
    </div>
    <div class="company-info-header-body" >
      <i class="icon icon-company mr-3 pt-2"></i>
      <div class="company-info-header-body-text">
        {{ companyInformations.BrandName }}
      </div>
    </div>
  </div>
</div>
<div class="temp" *ngIf="!loading && companyInformations"></div>

<div class="main mx-3 pb-2" *ngIf="!loading && companyInformations">
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards mx-1">
          <div class="info-cards-title">
            {{ '_mersis_number' | translate }}:
          </div>
          <div class="info-cards-body" [class]="companyInformations?.MersisNumber.length > 22 ? 'break-word': ''">
            {{ companyInformations.MersisNumber }}
          </div>
        </div>
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_registration_number' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.RegistrationNumber }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_address' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.Address }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards mx-1">
          <div class="info-cards-title">
            {{ '_phone' | translate }}:
          </div>
          <div class="info-cards-body">
          {{ companyInformations.Phone }}
          </div>
        </div>
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_supervisor' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.Supervisor }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_email' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.Email }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_brand_name' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.BrandName }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-center w-100">
        <div class="info-cards">
          <div class="info-cards-title">
            {{ '_people_in_charge' | translate }}:
          </div>
          <div class="info-cards-body">
            {{ companyInformations.PeopleinCharge }}
          </div>
        </div>
      </div>
</div>

<div class="doesnt-exist" *ngIf="!loading && !companyInformations">
  <div class="d-flex justify-content-center">
    <i class="icon icon-company mr-3 pt-2" style="font-size: 90px; color: black;"></i>
  </div>

  <div class="doesnt-exist-text mt-2">
    {{ '_company_info_doesnt_exist' | translate }}
  </div>

  <div class="mt-3 w-75">
    <div
    (click)="navigateToBack()"
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    style="border-radius: 6px;"
  >
    {{ "_go_back" | translate }}
  </div>
  </div>

</div>
