.company-info {
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 181.5px;
  z-index: -1 !important;
  &:before {
    content: "";
    position: absolute;
    border-bottom-right-radius: 69px !important;
    background-color: #EEF2F4;
    height: 78%;
    width: 100%;
    z-index: -1 !important;
  }
  &-header {
    display: flex;
    flex-direction: column;
    color: black;
    margin-top: 26px;
    position: relative;
    margin-left: 47px;
    &-title {
      margin-left: 60px;
      font-weight: 500;
      font-size: 12px;
      line-height: 24px;
      color: #8F8F8F;
    }
    &-body {
      display: flex;
      margin-top: 7px;
      &-text {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #495E68;
        width: 243px;
      }
    }
  }

}

.main {
  position: relative;
  top: 162px;
  left: 0;
  z-index: -2 !important;
}

.temp {
  background-color: #EEF2F4;
  position: absolute;
  top: 141px;
  width: 25%;
  height: 49px;
  z-index: -999 !important;
  &:after {
    content: "";
    position: absolute;
    border-top-left-radius: 69px !important;
    background-color: white;
    height: 100%;
    width: 100%;
    z-index: -999 !important;
  }
}

.info-cards {
  background: rgba(238, 242, 244, 0.45);
  box-shadow: 0px 1px 4px #EAEAEA, inset 0px 1px 4px rgba(255, 255, 255, 0.76);
  border-radius: 5px;
  width: 100%;
  padding: 7px;
  min-height: 63px;
  height: auto;
  max-height: 89px;
  margin: 5px;

  &-title {
    font-weight: 400;
    font-size: 10px;
    line-height: 24px;
    color: #FFA300;
  }
  &-body {
    font-weight: 600;
    font-size: 12px;
    line-height: 18px;
    color: #495E68;
  }
}

.icon {
  font-size: 30px;
  margin-left: 14px;
}

.break-word {
  word-break: break-all;
}

.doesnt-exist {
  height: calc(100vh + -138px);
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #495E68;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &-text {
    font-weight: bold;
    font-size: 26px;
    width: 80%;
    text-align: center;
    line-height: 1.2;
    color: black;
  }
}
