import { Component, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CustomerState } from '../../state/customer.state';

@Component({
  selector: 'cat-customer-layout',
  templateUrl: './customer-layout.component.html',
  styleUrls: ['./customer-layout.component.scss'],
})
export class CustomerLayoutComponent implements OnInit {
  @Select(CustomerState.customerDetailLoading)
  loading$: Observable<boolean>;

  ngOnInit(): void {
  }
}
