import { Component, <PERSON>ementRef, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { LogService } from '../../service/log.service';
import { ServiceService } from '../../service/service.service';
import {
  GetWorkOrderChatMessagesAction,
  ResetWorkOrderChatMessagesAction
} from '../../action/service.action';
import { Select, Store } from '@ngxs/store';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { interval, Observable, Subject, Subscription } from 'rxjs';
import { ServiceDetailedRequestModel } from '../../model/service-request.model';
import { ServiceState } from '../../state/service.state';
import { takeUntil } from 'rxjs/operators';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';

@Component({
  selector: 'cat-service-chat',
  templateUrl: './service-chat.component.html',
  styleUrls: ['./service-chat.component.scss']
})
export class ServiceChatComponent implements OnInit, OnDestroy {
  @Select(ServiceState.workOrderChatMessages)
  workOrderChatMessages$: Observable<any>;

  @Select(ServiceState.workOrderChatMessagesLoading)
  workOrderChatMessagesLoading$: Observable<boolean>;

  @Select(ServiceState.workOrderLoading)
  workOrderLoading$: Observable<boolean>;

  form: FormGroup = new FormGroup({
    Message: new FormControl(null, [Validators.required, Validators.pattern('^(?!\\s*$).+')]),
  });
  private pollingInterval = 10000;
  private pollingSubscription: Subscription | undefined;

  protected subscriptions$: Subject<boolean> = new Subject();

  @ViewChild('scrollContainer') scrollContainer: ElementRef;

  getFormErrorMessage = getFormErrorMessage;
  warningIcon = `${environment.assets}/warning.svg`;
  formSendStatus = false;
  loading = true;
  serviceDetail: ServiceDetailedRequestModel;
  chatMessages = null;
  messageLength = false;
  showEmptyMessage = false;

  constructor(
    private readonly log: LogService,
    private readonly serviceService: ServiceService,
    private readonly store: Store
  ) { }

  ngOnInit() {
    this.serviceDetail = this.store.selectSnapshot(ServiceState.serviceDetail);

    this.store.dispatch(new GetWorkOrderChatMessagesAction(this.serviceDetail, true));
    this.startPolling();
    this.workOrderChatMessages$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data.messages) {
          const reversableMessage = [...data.messages];
          const groupedMessages = reversableMessage
            .sort((a, b) => new Date(b.createdDate + 'T' + b.createdTime).getTime() - new Date(a.createdDate + 'T' + a.createdTime).getTime())
            .reverse()
            .reduce((groupedMessages, currentMessage) => {
              const currentDate = currentMessage.createdDate;
              if (!groupedMessages[currentDate]) {
                groupedMessages[currentDate] = [];
              }
              groupedMessages[currentDate].push(currentMessage);
              return groupedMessages;
            }, {});
          this.chatMessages = groupedMessages;
          this.messageLength = data?.messages?.length > 0;
          this.loading = false;
        }
      });
  }

  startPolling(): void {
    this.pollingSubscription = interval(this.pollingInterval)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        this.store.dispatch(new GetWorkOrderChatMessagesAction(this.serviceDetail, false));
      });
  }

  stopPolling(): void {
    this.pollingSubscription.unsubscribe();
  }

  getScrollHeight() {
    if (!this.scrollContainer) {
      return;
    }
    const containerElement: HTMLElement = this.scrollContainer.nativeElement;

    containerElement.scrollTop = containerElement.scrollHeight + 60;
  }

  sendMessage() {
    const { value } = this.form;
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);
    const formBody = {
      workOrderNumber: this.serviceDetail?.serviceNumber,
      workOrderGuid: this.serviceDetail?.workOrderId,
      message: value?.Message,
      customerId: customer?.id,
      companyId: company?.id
    };

    this.log
      .action(LogSectionEnum.SERVICE, 'ADD_SERVICE_WORK_ORDER_CHAT_MESSAGE', formBody)
      .subscribe();

    if (this.form.valid) {
      this.loading = true;
      this.serviceService.createNewWorkOrderChatMessage(formBody).subscribe(
        () => {
          this.formSendStatus = true;
          this.form.reset();
          this.loading = false;
          this.store.dispatch(new GetWorkOrderChatMessagesAction(this.serviceDetail, true))
            .subscribe(() => {
              this.getScrollHeight();
            });
        },
      );
    } else {
      validateAllFormFields(this.form);
    }
  }

  convertTimeHourAndMinute(time: string) {
    const splitTime = time.split(':');

    const hour = splitTime[0];
    const minute = splitTime[1];

    return `${hour}:${minute}`;
  }

  navigateToBack() {
    this.store.dispatch(new ResetWorkOrderChatMessagesAction());
    window.history.back();
  }

  ngOnDestroy() {
    this.stopPolling();
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
