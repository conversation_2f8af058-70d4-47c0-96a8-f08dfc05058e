<div class="ask-to-service">
  <div class="chat-content">
    <div class="chat-content-top">
      <header class="header">
        <div>
          <a (click)="navigateToBack()">
            <i class="icon icon-back"></i>
          </a>
        </div>
        <div class="h5 mb-0 text-center">
          {{ "_ask_to_service" | translate }}
        </div>
      </header>
      <cat-info-box [title]="'_info' | translate">
        {{ "_work_order_information" | translate }}
        <hr class="my-1">
        <span class="mr-3">
          {{ "_service_number" | translate }}: {{ serviceDetail?.serviceNumber }}
        </span>
        <span>
          {{ "_machine_serial_number" | translate }}: {{ (serviceDetail?.equipmentSerialNumber | serialFormat) || "-" }}
        </span>
      </cat-info-box>
    </div>
    <div *ngIf="messageLength" #scrollContainer class="chat-container" [scrollTop]="scrollContainer?.scrollHeight + 60">
      <div class="chat" *ngFor="let data of chatMessages | keyvalue">
        <div class="chat-date">
          <div class="date text-nowrap">
            {{data.key}}
          </div>
        </div>
        <div class="py-3">
          <div class="messages" *ngFor="let message of data.value">
            <div class="person-message-content">
              <div class="message" [ngClass]="{'borusan-user': message?.senderBorusanUsername}">
                <span *ngIf="message.senderBorusanUsername" class="font-size-12px borusan-slug">
                  BorusanCat
                </span>
                {{ message?.message }}
              </div>
              <div class="message-date" [ngClass]="{'borusan-message-date': message?.senderBorusanUsername}">
                {{ convertTimeHourAndMinute(message?.createdTime) }}
              </div>
            </div>
        </div>
        </div>
      </div>
    </div>
    <div class="empty-message" *ngIf="!messageLength && !(workOrderChatMessagesLoading$ | async) && !loading">
      <img [src]=warningIcon alt="warning" width="48" height="48">
      {{ "_empty_work_order_chat" | translate }}
    </div>
  </div>
  <div class="message-content">
    <form class="message-form d-flex" (submit)="sendMessage()" [formGroup]="form">
      <div class="form-group flex-grow-1 mb-0">
        <input
          [name]="'Message'"
          catInputLength
          [placeholder]="'_text_message' | translate"
          class="form-control message-input"
          formControlName="Message"
          type="text"
          maxlength="300"
        />
        <div
          [ngClass]="{
            'd-block':
              !form.get('Message').valid && form.get('Message').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Message) | translate }}
        </div>
      </div>
      <button class="btn btn-sm btn-warning send-message-btn">
        <i class="icon icon-redo-right"></i>
      </button>
    </form>
  </div>
</div>
<cat-loader [show]="loading || (workOrderChatMessagesLoading$ | async) || (workOrderLoading$ | async)"></cat-loader>
