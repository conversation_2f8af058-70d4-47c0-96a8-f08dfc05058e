/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { CmPseComponent } from './cm-pse.component';

describe('LuckyDaysComponent', () => {
  let component: CmPseComponent;
  let fixture: ComponentFixture<CmPseComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CmPseComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CmPseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
