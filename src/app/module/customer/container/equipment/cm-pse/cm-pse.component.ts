import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { LogService } from '../../../service/log.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { validateAllFormFields } from '../../../../../util/validate-all-form-fields.util';
import { EquipmentModel } from '../../../model/equipment.model';
import { PseService } from '../../../service/pse.service';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { Store } from '@ngxs/store';

@Component({
  selector: 'cat-cm-pse',
  templateUrl: './cm-pse.component.html',
  styleUrls: ['./cm-pse.component.scss']
})
export class CmPseComponent implements OnInit {

  @Input()
  equipmentDetail: EquipmentModel;

  @Output()
  statusChange: EventEmitter<boolean> = new EventEmitter();

  loading: boolean = false;
  form: FormGroup = new FormGroup({
    Description: new FormControl(null, [Validators.required]),
  });
  formSendStatus = false;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  language: any;

  constructor(
    protected readonly pseService: PseService,
    protected readonly logger: LogService,
    protected readonly store: Store,
  ) { }

  ngOnInit() {
    this.language = this.store.selectSnapshot(LoginState.language);
  }

  onSubmitForm() {
    if (this.form.valid) {
      const { value } = this.form;
      this.formSendStatus = false;
      this.loading = true;
      this.logger.action('CM_PSE', 'SEND_FORM').subscribe();
      this.pseService
        .cmPseSubmit({
          serialNumber: this.equipmentDetail.serialNumber,
          Note: value.Description,
        })
        .subscribe(
          (val) => {
            if (val.code === 0) {
              this.formSendStatus = true;
            }
            this.loading = false;
          },
          () => {
            this.formSendStatus = false;
            this.loading = false;
          }
        );
    } else {
      validateAllFormFields(this.form);
    }
  }

  onSendModalChange($event: boolean) {
    this.statusChange.emit($event);
  }
}
