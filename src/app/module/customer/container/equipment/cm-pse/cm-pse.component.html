<div class="cmPSECampaign">
  <div class="cmPSECampaign-container">
    <div class="content">
      <cat-info-box>
        {{ '_cm_pse_intro_text' | translate }}
      </cat-info-box>
      <div class="mt-3 content-message">
        {{ language == 'tr' ? equipmentDetail.cmPseMessages.tr : equipmentDetail.cmPseMessages.en }}
      </div>

      <div class="">
        <form (submit)="onSubmitForm()" [formGroup]="form">
          <div class="form-group mt-2 my-3">
            <textarea
              appInputMaxLength
              [name]="'Description'"
              [placeholder]="'_you_can_add_your_notes' | translate"
              [rows]="4"
              class="form-control"
              formControlName="Description"
              minlength="3"
            ></textarea>
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Description) | translate }}
            </div>
          </div>

          <input
            [value]="'_send' | translate"
            class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg my-4"
            type="submit"
          />
        </form>
        <cat-loader [show]="loading"></cat-loader>
      </div>

    </div>
  </div>
  <div class="footer">
  </div>
  <cat-loader [show]="loading"></cat-loader>
</div>
<cat-success-modal
  [status]="formSendStatus"
  (statusChange)="onSendModalChange($event)"
  message="_successfully_send_form"
></cat-success-modal>
