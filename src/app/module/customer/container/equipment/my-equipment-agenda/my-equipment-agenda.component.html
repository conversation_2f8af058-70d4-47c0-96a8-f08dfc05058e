<div class="equipment-agenda d-flex flex-column">
  <div class="px-3 pb-4 equipment-agenda-top">
    <div class="h5 py-4 mb-0 d-flex align-items-center font-weight-normal">
      <i (click)="navigateToBack()" class="icon mr-3 icon-back"></i>
      {{ "_my_equipment_agenda" | translate }}
    </div>
    <div class="d-flex align-items-center justify-content-between mt-3 equipment-alias-edit-container">
      <div>
        <div
          *ngIf="!isEditEquipmentAlias"
          class="h6 mb-0 d-flex align-items-end equipment-alias"
        >
          {{equipmentAgendaDetails?.alias}}
        </div>
        <form
          class="d-flex align-items-center"
          *ngIf="isEditEquipmentAlias"
          (submit)="onSubmitForm()"
          [formGroup]="form"
        >
          <div class="form-group mb-0 position-relative ">
            <input
              [name]="'EquipmentAlias'"
              class="form-control equipment-alias-input"
              formControlName="EquipmentAlias"
              type="text"
              maxlength="30"
            />
            <div
              [ngClass]="{
                'd-block': isShowError(form.controls.EquipmentAlias)
              }"
              class="invalid-feedback pl-3 position-absolute"
              style="bottom: 2rem;"
            >
              {{ getFormErrorMessage(form.controls.EquipmentAlias) | translate }}
            </div>
          </div>
          <div class="form-btns d-flex align-items-center ml-3">
            <button
              (click)="showEquipmentAliasInput()"
              class="form-btn close-btn btn btn-sm"
            >
              <i class="icon icon-x"></i>
            </button>
            <button
              type="submit"
              class="form-btn submit-btn btn btn-sm btn-info"
            >
              <i class="icon icon-success"></i>
            </button>
          </div>
        </form>
        <span class="font-size-13px">
          {{ equipment?.serialNumber | serialFormat }}
        </span>
        <div *ngIf="equipmentAgendaDetails?.hasCheckIn" class="text-warning font-size-13px">
          {{ '_approved_status' | translate}}:
          {{'_check_in' | translate}}
        </div>
      </div>
      <div *ngIf="!isEditEquipmentAlias && featureAgendaChangeAlias" [hasPermission]="PermissionEnum.EquipmentAgendaSetAlias" (click)="showEquipmentAliasInput()">
        <i
          class="icon icon-edit"
          [class]="isEditEquipmentAlias ? 'equipment-alias-editing' : ''"
        ></i>
      </div>
    </div>
    <div class="border-bottom d-flex align-items-center justify-content-center">
      <a
        (click)="navigateMyEquipmentAgendaCalendar()"
        [ngClass]="{ 'active-calendar-btn': isActiveRoute === 2 }"
        class="btn d-flex align-items-center calendar-btn btn-sm font-size-15px"
      >
        <i class="icon icon-calendar mr-2"></i>
        {{ '_calendar' | translate }}
      </a>
    </div>
    <div class="mt-4 d-flex align-items-center route-btn-container">
        <a
        [hasPermission]="PermissionEnum.EquipmentAgendaAddNote"
          (click)="navigateMyEquipmentAgendaNotes()"
          [ngClass]="{ 'active-btn': isActiveRoute === 0}"
          class="req-btn btn  font-size-12px"
        >
          <i class="icon icon-contract mr-2"></i>
          {{ "_add_note" | translate }}
        </a>
        <a
        [hasPermission]="PermissionEnum.EquipmentAgendaCheckinout"
        (click)="navigateCheckInOrOut()"
        [ngClass]="{ 'active-btn': isActiveRoute === 1 || checkInFormStatus , 'active-checkout-btn': equipmentAgendaDetails?.hasCheckIn && isActiveRoute === 1 || checkInFormStatus, 'checkout-btn': equipmentAgendaDetails?.hasCheckIn}"
        class="req-btn btn  font-size-12px"
      >
        <i class="icon icon-file mr-2"></i>
        {{ (equipmentAgendaDetails?.hasCheckIn && checkedInEquipments.length &&
          equipmentAgendaDetails?.checkedInUserId === user?.id) ?
          ('_check_out' | translate) : ('_check_in' | translate) }}
      </a>
        <a
          [hasPermission]="PermissionEnum.RequestsInspection"
          (click)="navigateToInspectionHistory()"
          class="req-btn btn  font-size-12px"
        >
        <div class="icon-expertise mr-2">
          <img src={{equipmentInspectionIconWhite}} alt="inspection" width="20" height="20">
        </div>
          {{ '_inspection' | translate }}
        </a>
    </div>
  </div>
  <div class="flex-grow-1 position-relative equipment-agenda-bottom">
    <router-outlet></router-outlet>
  </div>
</div>
<cat-loader [show]="(equipmentDetailLoading$ | async) || (equipmentAgendaDetailLoading$ | async) || (equipmentAgendaLoading$ | async) || loading "></cat-loader>
