.equipment-agenda{
    min-height: 100vh;

    &-top{
        position: relative;
        background-color: #ffffff;
        z-index: 1;
        
        &::before{
            content: " ";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            border-bottom-left-radius: 2rem;
            background-color: #FFFFFF;
        }

        .equipment-alias-edit-container{
            padding: 10px 15px;
            border: 1px solid #eeeeee;
            box-shadow: 0px 6px 4px 0px #00000008;
            border-radius: 6px;
            min-height: 100px;
        }

        .route-btn-container{
            gap: .5rem;
            overflow-x: auto;
            overscroll-behavior-x: contain;
            justify-content: space-between;
            padding-bottom: 4px;
        }

        .req-btn{
            // width: 105px;
            // min-width: 105px;
            height: 38px;
            box-shadow: 0px 2px 6px 0px #00000014;
            border-radius: 55px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            padding: 2px 15px 2px 2px !important;
            white-space: nowrap;

            &.active-btn{
                border-color: #FFA300;
            }

            .icon-contract, .icon-expertise, .icon-file{
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background-color: #FFA300;
                display: flex;
                align-items: center;
                justify-content: center;

                &::before{
                    color: #ffffff;
                    font-size: 13px;
                }
            }

            // &.active-checkout-btn{
            //     border-color: #3f7895;

            // }
            // &.checkout-btn{
            //     .icon-file{
            //         background-color: #3f7895;
            //     }
            // }
        }

        .calendar-btn{
            padding: 10px 40px;
            padding-top: 20px;
            font-size: 15px;
            color: #A7A7A7;
            font-weight: bold;
            border-bottom: 1px solid transparent;
            border-radius: 0;

            .icon-calendar{

                &::before{
                    color: #A7A7A7;
                    font-size: 20px;
                }
            }
        }

        .active-calendar-btn{
            border-bottom-color: #2c2c2c;
            color: #2c2c2c;

            .icon-calendar{

                &::before{
                    color: #2c2c2c;
                }
            }
        }

        .form-btns{

            .form-btn{
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
            }

            .close-btn{
                background-color: #d9d9d9;
                margin-right: 12px !important;
            }

            .submit-btn{
                background-color: #FFA300;
                border: 0;
            }
        }


        .equipment-alias{
            height: 1.5rem !important;
        }

        .equipment-alias-input, .equipment-alias{
            height: 2rem;
        }

        .equipment-alias-input{
            width: 220px;
        }

    }

    &-bottom{
        background-color: white;
        z-index: 1;


        &::before{
            content: " ";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            border-top-right-radius: 2rem;
            background-color: #ffffff;
        }
    }
}

.form-preview, .after-form-send {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #FAFAFA;
    z-index: 123;
  }
  
  .after-form-send-content {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100vw;
    transform: translateY(-50%);
  }
  .success-message{
    font-size: 26px;
    font-weight: 700;
  }
  .icon-message-success {
    font-size: 60px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }