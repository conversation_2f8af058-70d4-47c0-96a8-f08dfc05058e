import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Store, Select } from '@ngxs/store';
import { EquipmentState } from '../../../state/equipment.state';
import { Observable, Subject, timer } from 'rxjs';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { EquipmentService } from '../../../service/equipment.service';
import { GetEquipmentAgendaDetails, GetEquipmentAgendaHistory, GetEquipmentDetailAction } from '../../../action/equipment.action';
import { LogService } from '../../../service/log.service';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { SystemFeatureAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { takeUntil } from 'rxjs/operators';
import { EquipmentModel } from '../../../model/equipment.model';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { TranslateService } from '@ngx-translate/core';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-my-equipment-agenda',
  templateUrl: './my-equipment-agenda.component.html',
  styleUrls: ['./my-equipment-agenda.component.scss'],
})
export class MyEquipmentAgendaComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.equipmentAgendaDetails)
  equipmentAgendaDetails$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaHistory)
  equipmentAgendaHistory$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaLoading)
  equipmentAgendaLoading$: Observable<boolean>;
  @Select(EquipmentState.equipmentAgendaDetailLoading)
  equipmentAgendaDetailLoading$: Observable<boolean>;
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;
  @Select(EquipmentState.checkedInEquipments)
  checkedInEquipments$: Observable<any[]>;
  @Select(EquipmentState.equipmentDetailLoading)
  equipmentDetailLoading$: Observable<boolean>;

  equipmentInspectionIconWhite = environment.assets + '/equipment_inspect_icon.svg';
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isEditEquipmentAlias = false;
  loading = false;
  isActiveRoute = 5
  patchValues: any = {}
  equipmentAgendaDetails: any;
  checkInFormStatus = 0
  featureAgendaChangeAlias = true
  equipmentAgendaHistory: any;
  checkInAndCheckOutControl: any;
  checkedInEquipments: any[] = [];
  equipment: any;
  form: FormGroup = new FormGroup({
    EquipmentAlias: new FormControl(null, [Validators.required, Validators.pattern('^(?!\\s*$).+')]),
  });
  user: UserModel;
  PermissionEnum = PermissionEnum;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly equipmentService: EquipmentService,
    private readonly log: LogService,
    private readonly route: ActivatedRoute,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    const { id } = this.route.snapshot.params;

    this.route.queryParams.subscribe((q) => {
      this.checkInFormStatus = 'checkInFormStatus' in q ? parseInt(q.checkInFormStatus, 2) : 0;
      this.isActiveRoute = 'isActiveRoute' in q ? parseInt(q.isActiveRoute, 3) : 5;
    });

    this.store.dispatch(new GetEquipmentDetailAction(id));
    this.equipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data){
          this.equipment = data;
          this.store.dispatch(new GetEquipmentAgendaDetails(data?.serialNumber, data?.model, data?.equipmentNumber))
            .subscribe(() => {
              this.cdr.detectChanges();
            });
        }
      });

    this.equipmentAgendaDetails$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data){
          this.store.dispatch(new GetEquipmentAgendaHistory(data?.serialNumber, id, new Date()));
          this.equipmentAgendaDetails = data;
          this.patchValues.EquipmentAlias = data?.alias;
          this.form.patchValue(this.patchValues);
        }
      });

    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          this.featureAgendaChangeAlias = systemFeature('agenda_change_alias', features, false);
        }
      });

    //this.store.dispatch(new GetEquipmentAgendaHistory(this.equipment?.serialNumber, this.equipment?.equipmentNumber, new Date()));
    this.equipmentAgendaHistory$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.equipmentAgendaHistory = data;
      });
    this.checkedInEquipments$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.checkedInEquipments = data
    })

    this.user = this.store.selectSnapshot(LoginState.user);
  }

  // get equipment2() {
  //   return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  // }

  onSubmitForm() {
    console.log(this.form);

    const formBody = {
      EquipmentAgendaId: this.equipmentAgendaDetails.equipmentAgendaId,
      Alias: this.form.value.EquipmentAlias,
    };

    this.log
    .action(LogSectionEnum.EQUIPMENT, 'CHANGE_EQUIPMENT_ALIAS', {...formBody, serialNumber: this.equipment?.serialNumber}).subscribe();


    if (this.form.valid) {
      this.loading = true;
      this.equipmentService
        .equipmentAgendaSetNewAlias(formBody)
        .subscribe(() => {
          this.form.reset();
          this.store.dispatch(
          new GetEquipmentAgendaDetails(this.equipment?.serialNumber, this.equipment?.model, this.equipment?.equipmentNumber));
          this.loading = false;
          this.isEditEquipmentAlias = false;
        });
    } else {
      validateAllFormFields(this.form);
    }
  }

  showEquipmentAliasInput() {
    this.isEditEquipmentAlias = !this.isEditEquipmentAlias;
  }

  navigateMyEquipmentAgendaCheckIn() {
      this.router
        .navigate([
          '/',
          ...environment.rootUrl.split('/'),
          'form',
          'request-my-equipment-agenda',
          this.equipment?.equipmentNumber,
          'check-in',
        ], { replaceUrl: true, queryParams: { checkInFormStatus: 1, isActiveRoute: 1 }})
        .then();
  }
  navigateCheckInOrOut(){
    const checkInControl = this.checkedInEquipments.length && this.equipmentAgendaDetails?.checkedInUserId !== this.user?.id;

    if(!this.equipmentAgendaDetails?.hasCheckIn && this.checkedInEquipments.length || checkInControl){
      return this.modalService.errorModal({message: this.translateService.instant('_has_checkedIn_equipment')});
    }

    if(this.checkedInEquipments.length) return this.navigateMyEquipmentAgendaCheckOut()

    return this.navigateMyEquipmentAgendaCheckIn()
  }

  navigateMyEquipmentAgendaCheckOut() {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'form',
      'request-my-equipment-agenda',
      this.equipment?.equipmentNumber,
      'check-out',
    ], { replaceUrl: true, queryParams: { isActiveRoute: 1 } })
    .then();
  }

  navigateMyEquipmentAgendaNotes() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-my-equipment-agenda',
        this.equipment?.equipmentNumber,
        'notes',
      ], { replaceUrl: true, queryParams: { isActiveRoute: 0 } })
      .then();
  }

  navigateMyEquipmentAgendaCalendar(){
    this.router
    .navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'form',
      'request-my-equipment-agenda',
      this.equipment?.equipmentNumber,
      'calendar'
    ], { replaceUrl: true, queryParams: { isActiveRoute: 2 }})
    .then();
  }

  navigateToInspectionHistory(){
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'expertise',
      'init',
      this.equipment?.equipmentNumber
    ], {
      queryParams: { backButton: 1 }
    }).then();
  }

  navigateToBack() {
    window.history.back();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
