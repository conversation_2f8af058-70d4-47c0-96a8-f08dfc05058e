import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { takeUntil } from 'rxjs/operators';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { GetAllCountryListAction } from 'src/app/module/definition/action/definition.actions';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { Country } from 'src/app/module/definition/model/country.model';
import { DefinitionState } from 'src/app/module/definition/state/definition.state';
import { FormService } from 'src/app/module/form/service/form/form.service';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { EquipmentModel } from '../../../model/equipment.model';
import { LogService } from '../../../service/log.service';
import { EquipmentState } from '../../../state/equipment.state';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { LoginState } from '../../../../authentication/state/login/login.state';

@Component({
  selector: 'cat-equipment-update',
  templateUrl: './equipment-update.component.html',
  styleUrls: ['./equipment-update.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class EquipmentUpdateComponent implements OnInit, OnDestroy {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;


  form: FormGroup = new FormGroup({
    CountryCode: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, []),
  });

  formSendStatus: boolean;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly formService: FormService,
    private readonly store: Store,
    private readonly log: LogService,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.autoSelectCountry();
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

  }

  navigateToBack() {
    window.history.back();
  }


  onSubmitForm() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }

    this.sendForm();
  }

  sendForm() {
    const { value } = this.form;

    const { id } = this.route.snapshot.params;

    this.log.action(LogSectionEnum.EQUIPMENT, 'UPDATE_EQUIPMENT', {
      SerialNumber: value.SerialNumber,
      CountryCode: value.CountryCode,
      Description: value.Description,
      EquipmentNumber: id
    }).subscribe();
    const company = this.store.selectSnapshot(LoginState.company);

    this.formService
      .equipmentUpdate({
        CompanyId: company?.id,
        CompanyName: company?.name,

        countryCode: value.CountryCode,
        description: value.Description,
        EquipmentNumber: id,
      })
      .subscribe(
        () => {
          this.formSendStatus = true;
        },
        () => {
          this.formSendStatus = false;
        }
      );
  }

  protected autoSelectCountry() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        this.countryList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe((countries) => {
            if (!countries?.length) {
              return;
            }
            let countryCode = currentCustomer?.groupKey;
            this.equipmentDetail$
              .pipe(takeUntil(this.subscriptions$))
              .subscribe((equipment) => {
                if (!equipment) {
                  return;
                }

                if (equipment?.location?.locationName) {
                  const found = countries.find(item => item.name === equipment?.location?.locationName);
                  countryCode = found?.code || countryCode;
                }
                this.form.patchValue({ CountryCode: countryCode });

              });
          });
      });
  }
  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


}
