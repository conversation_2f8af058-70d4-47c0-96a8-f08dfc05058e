import { Component, Input, OnInit } from '@angular/core';
import { getFormErrorMessage } from '../../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../../util/is-show-form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { validateAllFormFields } from '../../../../../util/validate-all-form-fields.util';
import { Select, Store } from '@ngxs/store';
import { DefinitionState } from '../../../../definition/state/definition.state';
import { Observable } from 'rxjs';
import { Country } from '../../../../definition/model/country.model';
import { FormService } from '../../../../form/service/form/form.service';
import { GetAllCountryListAction } from '../../../../definition/action/definition.actions';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { LogSectionEnum } from '../../../../definition/enum/log-section.enum';
import { LogService } from '../../../service/log.service';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { LoginState } from '../../../../authentication/state/login/login.state';

@Component({
  selector: 'cat-equipment-create-request',
  templateUrl: './equipment-create-request.component.html',
  styleUrls: ['./equipment-create-request.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class EquipmentCreateRequestComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  @Input() headerVisible = true;
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;


  form: FormGroup = new FormGroup({
    Model: new FormControl(null, [Validators.required]),
    SerialNumber: new FormControl(null, [Validators.required]),
    CountryCode: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, []),
  });

  formSendStatus: boolean;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  constructor(
    private readonly formService: FormService,
    private readonly store: Store,
    private readonly log: LogService
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  navigateToBack() {
    window.history.back();
  }


  onSubmitForm() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }

    this.sendForm();
  }

  sendForm() {
    const { value } = this.form;

    this.log.action(LogSectionEnum.EQUIPMENT, 'ADD_EQUIPMENT', {
      SerialNumber: value.SerialNumber,
    }).subscribe();
    const company = this.store.selectSnapshot(LoginState.company);

    this.formService
      .equipmentCreate({
        CompanyId: company?.id,
        CompanyName: company?.name,

        model: value.Model,
        serialNumber: value.SerialNumber,
        countryCode: value.CountryCode,
        description: value.Description,
      })
      .subscribe(
        () => {
          this.formSendStatus = true;
        },
        () => {
          this.formSendStatus = false;
        }
      );
  }

}
