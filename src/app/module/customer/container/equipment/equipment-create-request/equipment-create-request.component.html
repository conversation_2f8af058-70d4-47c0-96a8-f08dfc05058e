<div *ngIf="!formSendStatus" class="px-4 pb-5 tablet-short-form-container">
  <div class="h4 py-4 mb-0 text-center nav-back">
    <i
      *ngIf="headerVisible"
      class="icon icon-back mr-2 float-left"
      (click)="navigateToBack()"
    ></i>
    {{ "_add_equipment" | translate }}
  </div>
  <form (submit)="onSubmitForm()" [formGroup]="form" class="tablet-form">
    <cat-info-box [title]="'_info' | translate">
      {{ "_create_equipment_form_info" | translate }}
    </cat-info-box>

    <div class="form-group">
      <input
        catInputLength
        [name]="'Model'"
        [placeholder]="'_model' | translate"
        class="form-control form-control"
        formControlName="Model"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Model) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Model) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        catInputLength
        [name]="'SerialNumber'"
        [placeholder]="'_serial_number' | translate"
        class="form-control form-control"
        formControlName="SerialNumber"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.SerialNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.SerialNumber) | translate }}
      </div>
    </div>
    <div class="form-group">
      <ng-select
        [searchable]="true"
        [placeholder]="'_country' | translate"
        [clearable]="false"
        [dropdownPosition]="'bottom'"
        formControlName="CountryCode"
      >
        <ng-option
          *ngFor="let country of countryList$ | async"
          [value]="country.code"
          >{{ country.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
      </div>
    </div>
    <div class="form-group textarea-form-element-container">
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control textarea-form-element"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>

    <input
      [value]="'_send' | translate"
      [disabled]="(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitCreateEquipmentForm) >= 0)"
      class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
      type="submit"
    />
  </form>
</div>
<div *ngIf="formSendStatus" [@preview] class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_successfully_send_form" | translate }}
    </div>
    <!-- <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      *ngIf="navigatedPage !== 'Contact'"
      (click)="navigateToBack()"
    >
      {{ "_back_to_equipment_detail" | translate }}
    </div> -->
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="navigateToBack()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
