@import "variable/bootstrap-variable";
.service-history {
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  z-index: 1;
  background-color: #fafafa;

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }
    }

    .two-dots {
      padding-left: 25px;

      &:before {
        display: inline-block;
        content: ':';
        margin-left: -25px;
        padding-left: 10px;
        padding-right: 10px;
      }
    }

    .answer-counts{

      .count-box{
        width: 50px;
        height: 20px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .red-box{
        color: red;
        background: #FFDDDD;
      }
      
      .yellow-box{
        color: #ffa300;
        background: #FFEED3;
      }
    }
  }
}

.offer {
  &-title {
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 100%;
  }

  &-text {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 24px;

    .inspection-date-text{
      color: #A7A7A7;
    }

    .working-hours{
      margin-left: 35px;
    }
  }


  &-dropdown {
    .dropdown-item {
      padding: 0.5rem 1.5rem;
    }

    .dropdown-item.active {
      background-color: var(--warning);
    }

    .dropdown-toggle {
      box-shadow: none;
      color: #4A8EB0;
    }
    .text-warning .dropdown-toggle{
      color: var(--warning);
    }

    .dropdown-toggle::after {
      display: none !important;
    }

    .icon-message-success {
      font-size: 16px;
      background: var(--success);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .icon-edit:before {
      font-size: 16px;
      color: #ffa300;
    }

    .icon-close-circle {
      font-size: 16px;
      color: #DA3A3C;
    }
  }
}

.download-file {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  font-size: 18px;
}
.doesnt-exist {
  height: calc(100vh + -237px);
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #495E68;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &-text {
    font-weight: bold;
    font-size: 26px;
    width: 80%;
    text-align: center;
    line-height: 1.2;
    color: black;
  }
  img {
    height: 100px;
    width: 100px;
    filter: invert(100%);
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}


.download-btn{
  background: #FFFFFF;
  mix-blend-mode: normal;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 35px 8px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}