<div class="service-history">
  <div class="pr-3 pl-3 pt-4">
    <div class="h4 nav-back mb-4">
      <i (click)="back()" class="icon icon-back mr-2"></i>
      {{ "_inspection_history" | translate }}
    </div>
    <ng-container *ngIf="equipmentInspectionHistories?.length">
      <div class="d-flex mb-3" *ngFor="let equipmentInspectionHistory of equipmentInspectionHistories">
        <div class="service-history-item flex-grow-1 overflow-hidden font-size-13px">
          <div class="px-4 py-3">
            <div class="d-flex justify-content-between align-items-center">
              <div class="offer-text">
                <div *ngIf="equipmentInspectionHistory?.completedDate" class="d-flex align-items-center">
                  <div class="d-flex flex-column">
                    <div class="text-nowrap inspection-date-text">
                      {{ "_inspection_date" | translate }}
                    </div>
                    <span class="font-weight-bold">
                      {{ equipmentInspectionHistory?.completedDate | date : "dd.MM.yyyy" }}
                    </span>
                  </div>
                  <div class="d-flex flex-column working-hours">
                    <div class="text-nowrap inspection-date-text">
                      {{ "_working_hours" | translate }}
                    </div>
                    <span class="font-weight-bold">
                      {{ equipmentInspectionHistory?.workingHours }}
                    </span>
                  </div>
                </div>

                <div class="answer-counts d-flex mt-4">
                  <div *ngIf="equipmentInspectionHistory?.redCount > 0" class="count-box font-weight-bold red-box mr-3">
                    {{ equipmentInspectionHistory?.redCount }}
                  </div>
                  <div *ngIf="equipmentInspectionHistory?.orangeCount > 0" class="count-box font-weight-bold yellow-box">
                    {{ equipmentInspectionHistory?.orangeCount }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="download-btn">
          <div [class.spinner]="isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
            <div
              catDownloadFile
              [downloadType]="DownloadTypeEnum.inspectionHistory"
              [downloadParams]="{
                formId: equipmentInspectionHistory?.formId,
                portalUserInspectionFormId:
                equipmentInspectionHistory?.portalUserInspectionFormId,
                language: language,
                isMasked: 'true'
              }"
              (downloadLoading)="downloading($event)"
              class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
              [class.spinner]="
                isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)"
              [id]="'downloadButton_' + equipmentInspectionHistory?.portalUserInspectionFormId"
            >
              <i
                class="icon icon-spinner8"
                *ngIf="isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
              </i>
              <i
                class="icon icon-download"
                *ngIf="!isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
              </i>
              <a
                class="d-none"
                [download]="equipmentInspectionHistory?.portalUserInspectionFormId">
              </a>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="!equipmentInspectionHistories?.length && !equipmentInspectionHistoriesLoading">
      <div class="doesnt-exist">
        <div class="d-flex justify-content-center">
          <img [src]="equipmentInspectionIconWhite"  alt="Inspection"/>
        </div>

        <div class="doesnt-exist-text mt-2">
          {{ '_inspection_history_doesnt_exist' | translate }}
        </div>

        <div class="mt-3 w-75">
          <div
            (click)="back()"
            class="btn btn-warning btn-gradient btn-block text-white shadow"
            style="border-radius: 6px;"
          >
            {{ "_go_back" | translate }}
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
<cat-loader [show]="equipmentInspectionHistoriesLoading"></cat-loader>
