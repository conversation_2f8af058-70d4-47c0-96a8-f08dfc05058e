import { Component, OnDestroy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { EquipmentState } from '../../../state/equipment.state';
import { Observable, Subject } from 'rxjs';
import { EquipmentModel } from '../../../model/equipment.model';
import { GetEquipmentInspectionHistoryAction } from '../../../action/equipment.action';
import { takeUntil } from 'rxjs/operators';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'cat-inspection-history',
  templateUrl: './inspection-history.component.html',
  styleUrls: ['./inspection-history.component.scss']
})
export class InspectionHistoryComponent implements OnInit, OnDestroy {

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentInspectionHistory)
  equipmentInspectionHistory$: Observable<any>;

  @Select(EquipmentState.equipmentInspectionHistoryLoading)
  equipmentInspectionHistoryLoading$: Observable<any>;

  serialNumber: string;
  equipmentInspectionHistories: any[] = [];
  equipmentInspectionHistoriesLoading: boolean;
  DownloadTypeEnum = DownloadTypeEnum;
  loadingAttachment: any[] = [];
  language: string;
  constructor(
    private readonly store: Store,
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  equipmentInspectionIconWhite = environment.assets + '/equipment_inspect_icon.svg';

  ngOnInit() {
    this.equipmentInspectionHistoryLoading$.subscribe(i => (this.equipmentInspectionHistoriesLoading = i));
    this.language = this.store.selectSnapshot(LoginState.language);
    this.equipmentDetail$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((equipment) => {
      if (equipment) {
        this.store.dispatch(new GetEquipmentInspectionHistoryAction(equipment?.serialNumber));
      }
    });
    this.equipmentInspectionHistory$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(e => {
      this.equipmentInspectionHistories = e;
    });

  }

  back(){
    window.history.back();
  }
  downloading(attachment) {
    this.applyDisabledClassToNonDownloadButtons();
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
    if (!this.isDownloadingAny()) {
      this.enableNonDownloadButtons();
    }
  }
  applyDisabledClassToNonDownloadButtons() {
    const downloadButtonIds = this.equipmentInspectionHistories
      .map((history) => history.portalUserInspectionFormId)
      .filter((id) => id !== null);

    const allButtons = document.getElementsByClassName('download-file');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const id = button.getAttribute('id');
      if (id && !downloadButtonIds.includes(+id.split('_')[1])) {
        button.classList.add('disabled');
      }
    }
  }
  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  isDownloadingAny() {
    return this.loadingAttachment.length > 0;
  }

  enableNonDownloadButtons() {
    const allButtons = document.getElementsByClassName('download-file');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      button.classList.remove('disabled');
    }
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
