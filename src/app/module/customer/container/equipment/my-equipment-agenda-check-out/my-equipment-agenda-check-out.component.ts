import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store, Select } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { EquipmentState } from '../../../state/equipment.state';
import { Validators, FormGroup, FormControl } from '@angular/forms';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { GetCheckedInEquipmentsAction, GetEquipmentAgendaDetails, GetEquipmentAgendaHistory } from '../../../action/equipment.action';
import { Observable, Subject } from 'rxjs';
import { LogService } from '../../../service/log.service';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { EquipmentService } from '../../../service/equipment.service';
import { TranslateService } from '@ngx-translate/core';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cat-my-equipment-agenda-check-out',
  templateUrl: './my-equipment-agenda-check-out.component.html',
  styleUrls: ['./my-equipment-agenda-check-out.component.scss'],
})
export class MyEquipmentAgendaCheckOutComponent implements OnInit {
  @Select(EquipmentState.equipmentAgendaDetails)
  equipmentAgendaDetails$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaHistory)
  equipmentAgendaHistory$: Observable<any>;
  @Select(EquipmentState.checkedInEquipments)
  checkedInEquipments$: Observable<any[]>;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  equipmentAgendaDetails: any;
  user: UserModel;
  checkedInEquipments: any[] = []
  loading = false;
  minDate = this.translateService.instant('_min_date')
  formSendStatus = false;
  checkIns = false

  form: FormGroup = new FormGroup({
    Description: new FormControl(null, [Validators.required, Validators.pattern('^(?!\\s*$).+')]),
    ShortName: new FormControl(null)
  });

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly log: LogService,
    private readonly equipmentService: EquipmentService,
    private readonly translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.equipmentAgendaDetails$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.equipmentAgendaDetails = data;
    });

    this.checkedInEquipments$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.checkedInEquipments = data
    })

    this.loadForm();
  }

  loadForm() {
    this.user = this.store.selectSnapshot(LoginState.user);
    const patchValues: any = {};
    if (this.user) {
      patchValues.ShortName = this.checkedInEquipments[0]?.shortName;
    }

    this.form.patchValue(patchValues);
  }

  sendEquipmentAgendaCheckOutForm() {
    console.log(this.form.value);
    const { value } = this.form;

    const formBody = {
      EquipmentAgendaId: this.equipmentAgendaDetails?.equipmentAgendaId,
      SerialNumber: this.equipment?.serialNumber,
      FirstName: this.user?.firstName,
      LastName: this.user?.lastName,
      UseEndDate: new Date(),
      Description: value.Description,
    };

    this.log
      .action(
        LogSectionEnum.EQUIPMENT,
        'ADD_EQUIPMENT_AGENDA_CHECK_OUT',
        formBody
      )
      .subscribe();

    if (this.form.valid) {
      this.loading = true;
      this.equipmentService.equipmentAgendaCreateCheckOut(formBody).subscribe(
        () => {
          this.formSendStatus = true;
          this.form.reset();
          this.loadForm();
          this.store.dispatch(new GetEquipmentAgendaHistory(this.equipment?.serialNumber, this.equipment?.equipmentNumber, new Date()));
          this.store.dispatch(new GetCheckedInEquipmentsAction())
          this.store.dispatch(new GetEquipmentAgendaDetails(this.equipment?.serialNumber, this.equipment?.model, this.equipment?.equipmentNumber));
          this.loading = false;
        },
        () => {
          this.formSendStatus = false;
          this.loading = false;
        }
      );
    } else {
      validateAllFormFields(this.form);
    }
  }

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  // showEquipmentAgendaCheckInForm() {
  //   this.router
  //     .navigate(
  //       [
  //         '/',
  //         ...environment.rootUrl.split('/'),
  //         'form',
  //         'request-my-equipment-agenda',
  //         this.equipment?.equipmentNumber,
  //         'check-out',
  //       ],
  //       {
  //         queryParams: { formStatus: 1 },
  //       }
  //     )
  //     .then();
  // }

  navigateEquipmentAgendaCheckIns() {
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'form',
          'request-my-equipment-agenda',
          this.equipment?.equipmentNumber,
          'check-in',
        ],
        {
          queryParams: { isActiveRoute: 1 },
          replaceUrl: true
        }
      )
      .then();
  }

  closeModal() {
    this.navigateEquipmentAgendaCheckIns();
    this.formSendStatus = false;
    window.scroll({ top: 0 });
  }
}
