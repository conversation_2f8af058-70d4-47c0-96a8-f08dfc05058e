<div class="my-equipment-agenda-check-out px-4 pb-4">
  <div class="h5 mb-3 pb-2 border-bottom">
    {{ '_check_out_procedures' | translate }}
  </div>
  <form
    class="form-check-out"
    (submit)="sendEquipmentAgendaCheckOutForm()"
    [formGroup]="form"
  >
    <!-- <div class="form-group">
      <div class="h6">{{ "_equipment_user" | translate }}</div>
      <input
        catInputLength
        [name]="'Name'"
        [placeholder]="'_name' | translate"
        class="form-control name-input"
        formControlName="Name"
        type="text"
        minlength="3"
        readonly
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Surname'"
        [placeholder]="'_surname' | translate"
        class="form-control surname-input"
        formControlName="Surname"
        type="text"
        minlength="2"
        readonly
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div> -->

    <!-- <div class="h5">
      {{ "_equipment_usage_expire_date" | translate }}
    </div>
    <div class="form-group">
      <input
        type="date"
        class="form-control mb-2"
        name="EndDate"
        id="EndDate"
        formControlName="EndDate"
        class="form-control date-input"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.EndDate) }"
        class="invalid-feedback pl-3"
      >
        {{ form.controls.EndDate.errors?.minDate ? minDate.replace('X', (lastCheckInElement?.checkInDate | date: "dd.MM.yyyy" )) : getFormErrorMessage(form.controls.EndDate) | translate }}
      </div>
    </div> -->
    <div class="form-group">
      <div class="h6">{{ "_equipment_user" | translate }}</div>
      <input
        catInputLength
        [name]="'ShortName'"
        [placeholder]="'_name' | translate"
        class="form-control name-input"
        formControlName="ShortName"
        type="text"
        minlength="3"
        readonly
      />
    </div>

    <div class="form-group">
      <div class="h6">
        {{ "_description" | translate }}
      </div>
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        class="form-control"
        formControlName="Description"
        [rows]="3"
        type="text"
        minlength="2"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <button type="submit" class="btn btn-warning text-white btn-gradient btn-block text-white shadow rounded-lg">
      {{ '_check_out' | translate }}
    </button>
    <div class="checkOut-warning" *ngIf="!checkedInEquipments.length">
      {{ '_checkOut_warning' | translate }}
    </div>
  </form>
</div>
<cat-loader [show]="loading"></cat-loader>
<div *ngIf="formSendStatus" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_success_check_out_message" | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="closeModal()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
