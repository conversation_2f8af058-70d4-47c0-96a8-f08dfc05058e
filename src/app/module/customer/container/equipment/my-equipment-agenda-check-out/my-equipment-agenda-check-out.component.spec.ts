import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MyEquipmentAgendaCheckOutComponent } from './my-equipment-agenda-check-out.component';

describe('MyEquipmentAgendaCheckOutComponent', () => {
  let component: MyEquipmentAgendaCheckOutComponent;
  let fixture: ComponentFixture<MyEquipmentAgendaCheckOutComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MyEquipmentAgendaCheckOutComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MyEquipmentAgendaCheckOutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
