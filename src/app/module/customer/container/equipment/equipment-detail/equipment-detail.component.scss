@import "variable/bootstrap-variable";

.modal-btn {
  border-radius: 6px;
}

.equipment {
  background-color: $body-bg;

  //full fill
  display: flex;
  flex-flow: column;
  min-height: 100vh;
  // .dropdown-menu {
  //   right: 0 !important;
  //   left: auto !important;
  // }
  &-top, &-bottom {
    position: relative;
    background-color: #DDDDDD;
    flex-grow: 1;

    &:before {
      content: ' ';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
    }
  }

  &-menu {
    //padding: 0;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.15);;

    &-element {
      //background: $danger;
      color: $danger;
    }

    &-button {
      //background: red;
      box-shadow: none;
      //border: 1px solid red;
      font-size: 18px;
      line-height: 1em;

    }

  }

  &-top {
    background-color: #EEF2F4;

    &:before {
      background-color: white;
      border-bottom-left-radius: 30px;
    }

    &.no-radius:before {
      background-color: white;
      border-bottom-left-radius: 0;
    }
  }

  &-bottom {
    background-color: white;

    &:before {
      background-color: #EEF2F4;
      border-top-right-radius: 30px;
    }
  }

  .z-index-1 {
    position: relative;
    z-index: 1;
  }
}


.service-history {
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  z-index: 1;
  background-color: $body-bg;

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }
    }

    .two-dots {
      padding-left: 25px;

      &:before {
        display: inline-block;
        content: ':';
        margin-left: -25px;
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }
}

.repair-icon {
  box-sizing: border-box;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 4px rgba(0, 0, 0, 0.03);
  border-radius: 6px;
  // width: 50px;
  // height: 40px;
  padding: 5px;
  z-index: 1;

  i {
    font-size: 18px;
  }
}

.sound-icon {
  border: 0;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  width: 105px;
  height: 31px;
  padding: 3px;
  position: absolute;
  right: 0;
  top: 16px;
  z-index: 2;

  i {
    font-size: 20px;
  }

  &-text {
    font-size: 11px;
    line-height: 0.8rem;
    color: $warning;
  }

  &-circle {
    width: 26px !important;
    height: 26px !important;
    background: $warning;
    flex-shrink: 0;
    padding-top: 2px;
    padding-left: 5px;
  }
}
.boom-guru-icon {
  border: 0;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  width: 105px;
  padding: 3px;
  position: absolute;
  right: 0;
  top: 51px;
  z-index: 2;

  height: max-content;
  display: flex !important;
  align-items: center;
  background-color: $warning;

  i {
    font-size: 20px;
  }

  &-text {
    font-size: 11px;
    line-height: 1rem;
    color: white;
  }

  &-circle {
    width: 26px !important;
    height: 26px !important;
    background: white;
    flex-shrink: 0;
    padding-top: 2px;
    padding-left: 3px;
    i {
      color: $warning !important;
    }
  }
}



.info-icons{
  border: 0;
  width: max-content;
  height: 40px;
  position: absolute;
  right: 0;
  top: 218px;
  z-index: 2;
}
.equipment-inspect-icon {
  border: 0;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  width: 105px;
  height: 31px;
  padding: 3px;
  position: absolute;
  left: 0;
  top: 16px;
  z-index: 2;

  img{
    position: absolute;
    left: 6px;
    top: 5px;
  }

  i {
    font-size: 20px;
  }

  &-text {
    font-size: 11px;
    line-height: 0.8rem;
    color: $info;
  }

  &-circle {
    width: 26px !important;
    height: 26px !important;
    background: $info;
    flex-shrink: 0;
    padding-top: 3px;
    padding-left: 3px;
  }
}

.fit-btn{
  width: 135px;
}


.videocall-icon {
  border: 0;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  padding-left: 3px;
  width: 111px;
  height: 31px;
  padding: 3px;
  border: 1px solid transparent;
  position: absolute;
  left: 0;
  top: 170px;
  z-index: 2;
  transition: border 3s ease-in-out;
  animation: blink 3s infinite;

  i {
    font-size: 18px;
    color: #FFFFFF;
  }

  &-text {
    font-size: 11px;
    line-height: 0.8rem;
    color: $danger;
  }

  &-circle {
    width: 26px !important;
    height: 26px !important;
    background: $danger;
    flex-shrink: 0;
    padding-top: 5px;
    padding-left: 4.5px;
  }
}

.equipment-remove {
  position: relative;
  text-align: right;

  //bottom: calc(20px + env(safe-area-inset-bottom, 0px));
  //right: 20px;
  color: grey;

  &-bg {
    background-color: #EEF2F4;
  }

}


.product-link {
  color: #737373;
}

:host ::ng-deep .tooltip .tooltip-inner {
  background-color: #444444;
}

:host ::ng-deep .tooltip .arrow::before {
  border-left-color: #444444;
}
.btn-warning{
  &:focus ,&:hover  {
    background: $warning;
    border-color: $warning;
  }

}
.offer {
  &-title {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
  }

  &-text {
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 24px;
  }
}

.download-file {
  width: 54px;
  height: 54px;
  background-color: #F5F5F5;
  font-size: 18px;
}

.dropdown .dropdown-toggle::after{
  vertical-align: middle;
}

.discount-circle-icon-16{
  border: 2px solid #5E9731;
  color: #5E9731;
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bolder;
  font-size: 13px;
}

.cat-app-button{
  display: inline-flex;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  --cat-app-padding: 0.25rem;
  height: 31px;
  padding: var(--cat-app-padding);
  border: 1px solid #2c2c2c;
  img {
    height: calc(31px - (var(--cat-app-padding) * 2));
  }
  &:hover{
    background-color: #e3e3e3;
    border-color: #000000;
  }
}

.text-yellow{
  color: var(--yellow);
}

ngb-accordion .card {
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.work-plan-popup{
  padding-top: 30px;
  padding-bottom: 30px;
}

.work-plan-button {
  box-sizing: border-box;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 4px rgba(0, 0, 0, 0.03);
  border-radius: 6px;
  padding: 5px;
  z-index: 2;
  height: 45px;
  margin-right: 1px;
}

@keyframes blink {
  0%, 100% {
    border: 1px solid transparent;
  }
  50% {
    border: 1px solid $danger;
  }
}

:host ::ng-deep cat-repair-modal{
  .modal-backdrop{
    z-index: 999;
  }
  .modal{
    z-index: 999;
  }
}

.w-95 {
  width: 95%;
}

.mda-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 4px rgba(0, 0, 0, 0.03);
  margin-right: .25rem;
}
