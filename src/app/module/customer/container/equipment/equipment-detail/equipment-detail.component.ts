import { animate, state, style, transition, trigger } from '@angular/animations';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import snq from 'snq';
import { v4 as uuid } from 'uuid';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { takeUntil } from 'rxjs/operators';
import { MediaService } from 'src/app/export/media/service/media/media.service';
import { Location } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { getOS } from 'src/app/util/os.util';
import { AppState } from '../../../../../state/app/app.state';
import { SettingsService } from 'src/app/module/shared/service/settings.service';
import { ImageSizeEnum } from 'src/app/export/media/enum/image-size.enum';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { CompanyModel } from 'src/app/module/company/model/company.model';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { FormService } from 'src/app/module/form/service/form/form.service';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { SettingsResponse, SystemFeature } from 'src/app/module/shared/model/settings.model';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import {
  CommonStoreAction,
  PageOpenedAction,
  PageOpenedClearAction,
  ShowPermissionErrorAction,
  StartDownloadAction
} from 'src/app/module/shared/state/common/common.actions';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { environment } from 'src/environments/environment';
import { CustomerModel } from '../../../model/customer.model';
import {
  EquipmentInspectionModel,
  EquipmentLocationModel,
  EquipmentMDAPmPlannerModel,
  EquipmentModel,
  EquipmentQuotationModel,
  EquipmentServiceHistoryModel,
  EquipmentSosAnalyzesModel,
  workOrderPlanModel
} from '../../../model/equipment.model';
import { LogService } from '../../../service/log.service';
import { VideocallService } from '../../../../live-support/service/videocall.service';
import { CustomerState } from '../../../state/customer.state';
import { EquipmentState } from '../../../state/equipment.state';
import { PagesEnum } from 'src/app/module/shared/enum/pages.enum';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import {
  EquipmentWorkOrderPlanAction,
  GetEquipmentDetailAction,
  GetEquipmentDiagnosticDataPagingAction,
  GetEquipmentInspectionsAction,
  GetEquipmentMDAPmPlannerAction,
  GetEquipmentQuotationsAction,
  GetEquipmentServiceHistoryAction,
  GetEquipmentSosAnalyzesAction,
} from '../../../action/equipment.action';
import { OfferCategoriesEnum } from '../../../../definition/enum/offerList.enum';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { SSORedirectKeys } from '../../../../shared/enum/sso-pats.enum';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';

@Component({
  selector: 'cat-equipment-detail',
  templateUrl: './equipment-detail.component.html',
  styleUrls: ['./equipment-detail.component.scss'],
  animations: [
    trigger('serviceHistory', [
      state('void', style({ left: '100%' })),
      state('*', style({ left: '0' })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class EquipmentDetailComponent implements OnInit, OnDestroy, OnChanges {
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentInspectionResults)
  equipmentInspectionResults$: Observable<EquipmentInspectionModel[]>;

  @Select(EquipmentState.equipmentDetailLoading)
  equipmentDetailLoading$: Observable<boolean>;

  @Select(EquipmentState.EquipmentMDAPmPlannerLoading)
  EquipmentMDAPmPlannerLoading$: Observable<boolean>;

  @Select(EquipmentState.inspectionResultsLoading)
  inspectionResultsLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentSosAnalyzes)
  equipmentSosAnalyzes$: Observable<EquipmentSosAnalyzesModel[]>;

  @Select(EquipmentState.equipmentMDAPmaPlanner)
  equipmentMDAPmPlanner$: Observable<EquipmentMDAPmPlannerModel[]>;

  @Select(EquipmentState.equipmentSosAnalyzesLoading)
  equipmentSosAnalyzesLoading$: Observable<boolean>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  @Select(EquipmentState.equipmentPSE)
  equipmentPSE$: Observable<any>;

  @Select(EquipmentState.equipmentLocation)
  equipmentLocation$: Observable<any>;

  @Select(EquipmentState.equipmentLocationLoading)
  equipmentLocationLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentWorkOrder)
  equipmentWorkOrder$: Observable<workOrderPlanModel[]>;

  @Select(SettingsState.userPermission)
  userPermission$: Observable<string[]>;

  @Select(EquipmentState.equipmentServiceHistory)
  equipmentServiceHistory$: Observable<EquipmentServiceHistoryModel[]>;

  @Select(EquipmentState.equipmentServiceHistoryLoading)
  equipmentServiceHistoryLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentQuotation)
  equipmentQuotations$: Observable<EquipmentQuotationModel[]>;

  @Select(EquipmentState.equipmentQuotationsLoading)
  equipmentQuotationsLoading$: Observable<boolean>;

  @Select(SettingsState.basic)
  basic$: Observable<SettingsResponse>;

  videocallLoading = false;
  isSparePartModule: any;
  videocallAvailableModal = false;
  loadingAttachment: any[] = [];
  DownloadTypeEnum = DownloadTypeEnum;

  // Boom Guru Submit Modal
  showBoomGuruSubmitModal = false;
  equipmentSosAnalyzes: EquipmentSosAnalyzesModel[];
  equipmentMDAPmPlanner: EquipmentMDAPmPlannerModel[];
  equipmentInspectionResults: EquipmentInspectionModel[];
  serviceHistoryStatus: boolean;
  inspectResults: boolean;
  HasPSECampaign: boolean;
  pSECampaignModal: boolean;
  cmPSEModal: boolean;
  equipmentPSE: any;
  featurePseCampaign: boolean = true;
  featureCmPse: boolean;
  featureSoundRecordCollecting: boolean;
  featureWorkOrderPlan: boolean = true;

  OfferCategoriesEnum = OfferCategoriesEnum;

  user: UserModel;
  sosAnalyzesDownloadLoading: string[] = [];
  haveDownload: boolean;
  analayzesHistoryStatus: boolean;
  mdaPmPlannerStatus: boolean;
  repairStatus: boolean;
  imageSizeEnum = ImageSizeEnum;
  equipmentLocations = [];
  relatedLocationField: EquipmentLocationModel;

  equipmentFaultCode = true;
  showFaultCodeModal = false;

  private errorModal: any;
  private customer: CustomerModel;

  private company: CompanyModel;
  private soundSending: boolean;
  private equipmentNumber: string;
  private serialNumber: string;
  removeModal: boolean;
  removeModalLoading: boolean;
  // tslint:disable: no-inferrable-types
  hideWorkingHoursOlderThanDays: number = 30;
  featureSoundDiagnostics: boolean = true;
  mdaShowStatus: boolean = true;
  cancelDonwloadVersion: boolean = false;
  equipmentAgendaSystemFeature = true;
  EquipmentSoundDiagCheckPermission: boolean;
  EquipmentSoundDiagFeedbackPermission: boolean;
  psePermission: boolean;
  campaignPermission: boolean;
  equipmentCostDetailSystemFeature = true;
  catInspectionDownloadButtons: boolean;
  systemFeatureCatPccVideoCall = false;
  systemFeatureCatVisionLinkVideoCall = false;
  featureBoomGuru: boolean = true;


  catAppShow: boolean = true;
  catVisionLinkEnabled = true;
  isSAMLLogin = false;
  catCompatiblePartsShow = false;
  catAppImage = `${environment.assets}/badges/cat-app.svg`;
  ladyBirdIcon = environment.assets + '/pse-boom360.svg';
  cmPseIcon = environment.assets + '/torch.png';
  pseFlag = environment.assets + '/pse-flag.svg';
  mdaFlag = environment.assets + '/defence.svg';
  // tslint:enable: no-inferrable-types

  equipmentInspectionIconWhite = environment.assets + '/equipment_inspect_icon.svg';
  equipmentInspectionIconBlack = environment.assets + '/equipment_inspect_icon_black.svg';
  equipmentInspectionHistory = environment.assets + '/clock.svg';

  source: string;
  sourceRoot: string;

  isVideocall = false;
  warrantiesStatus = false;
  imageHeight = 230;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  connectionModalStatus: boolean;
  PermissionEnum = PermissionEnum;
  currentCustomer: any;
  pseRequestId: string;
  showQuotationSection = false;
  equipmentWorkOrder: workOrderPlanModel[];
  workOrderPlanPopup: boolean = false;
  workOrderPlansIcon = environment.assets + '/service-car-.svg';
  isCATEquipment = false;
  equipmentServiceHistory: EquipmentServiceHistoryModel[];
  equipmentQuotations: EquipmentQuotationModel[];


  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly frameService: MessageFrameService,
    private readonly logger: LogService,
    private readonly formService: FormService,
    private readonly modalService: ModalService,
    private readonly elementRef: ElementRef,
    private readonly videocallService: VideocallService,
    private readonly translateService: TranslateService,
    private readonly mediaService: MediaService,
    private readonly location: Location,
    private readonly settingsService: SettingsService,
    private readonly ref: ChangeDetectorRef
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  permissions: string[] = [];

  ngOnInit() {
    this.userPermission$.subscribe(data => {
      this.permissions = data;
      this.isSparePartModule = this.isPermissionInclues(PermissionEnum.Ecommerce);
      this.psePermission = this.isPermissionInclues(PermissionEnum.EquipmentDetailPse);
      this.campaignPermission = this.isPermissionInclues(PermissionEnum.EquipmentDetailCampaign);
      this.EquipmentSoundDiagCheckPermission = this.isPermissionInclues(PermissionEnum.SoundDiagnosticsCheck);
      this.EquipmentSoundDiagFeedbackPermission = this.isPermissionInclues(PermissionEnum.SoundDiagnosticsFeedback);
    });

    this.equipmentLocation$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data) {
          this.equipmentLocations = data.equipmentLocations;
        }
      });

    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new ShowPermissionErrorAction(null));
    this.store.dispatch(new PageOpenedClearAction());
    this.store.dispatch(new PageOpenedAction(PagesEnum.equipmentDetailPage));
    this.cancelDonwloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.basic$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data) {
          this.hideWorkingHoursOlderThanDays = Number(data.hideWorkingHoursOlderThanDays);
        }
      });
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.frameService.sendMessage(data.action)
          });
        });
    const { id } = this.route.snapshot.params;
    this.equipmentNumber = id;
    const { isServiceIndicator } = this.route.snapshot.queryParams;
    const { source, sourceRoot } = this.route.snapshot.queryParams;
    this.source = source || 'Equipment';
    this.sourceRoot = sourceRoot || 'Equipment';
    if (id) {
      this.store.dispatch(new GetEquipmentDetailAction(snq(() => id)));
    }
    if (isServiceIndicator) {
      this.repairStatus = true;
      this.location.replaceState(this.location.path().replace('isServiceIndicator=true', ''), '');
    }
    (window as any).t = this.store.selectSnapshot(CommonState.appStorage);
    const serviceWarningShowed = this.store.selectSnapshot(CommonState.serviceWarningShowed) || [];
    this.customer = this.store.selectSnapshot(LoginState.customer);
    this.company = this.store.selectSnapshot(LoginState.company);
    this.user = this.store.selectSnapshot(LoginState.user);
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    this.equipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((equipment) => {
        if (equipment) {
          // this.store.dispatch(new GetEquipmentAgendaDetails(equipment?.serialNumber, equipment?.model, equipment?.equipmentNumber));
          //this.store.dispatch(new GetEquipmentAgendaHistory(equipment?.serialNumber, equipment?.equipmentNumber, new Date()));
          this.HasPSECampaign = equipment?.hasPseCampaign;
          this.pseRequestId = equipment.pseRequestId;
          this.serialNumber = equipment?.serialNumber;
          this.isCATEquipment = equipment?.brand === 'CATERPILLAR'; // TODO move to enum

          this.loadEquipmentSosAnalyzes(equipment.serialNumber);
          this.loadEquipmentMDAPmPlanner(equipment.serialNumber);
          this.loadEquipmentInspectionResults(equipment.serialNumber);
          this.loadWorkOrderPlan(equipment.serialNumber);

          this.store.dispatch(new GetEquipmentDiagnosticDataPagingAction(equipment.equipmentNumber, equipment.serialNumber, 13, 1));
          if (['red', 'orange', 'yellow'].indexOf(equipment?.revisionIndicator) !== -1) {
            if (serviceWarningShowed.indexOf(equipment.equipmentNumber) === -1) {
              this.repairStatus = true;
              this.updateServiceWarningState([...serviceWarningShowed, equipment.equipmentNumber]);
            }
          }
          if (equipment?.warranties?.length) {
            const nowDate = new Date();
            this.warrantiesStatus = !!(equipment.warranties.filter(x => (new Date(x?.endDate)) > nowDate)?.length);
          }
          this.logger
            .action(LogSectionEnum.EQUIPMENT, 'DETAIL', {
              equipmentNumber: this.equipmentNumber,
              serialNumber: this.serialNumber
            })
            .subscribe();

          this.store.dispatch(new GetEquipmentServiceHistoryAction(equipment?.equipmentNumber));
          this.store.dispatch(new GetEquipmentQuotationsAction(equipment?.serialNumber));
        }
      });

    //this.store.dispatch(new SystemFeatureAction());
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.featurePseCampaign = systemFeature('pse_campaign', features, true);
        this.featureCmPse = systemFeature('cm_pse', features, true);
        this.featureSoundRecordCollecting = systemFeature('sound_record_collecting', features, false);
        this.featureSoundDiagnostics = systemFeature('sound_diagnostics', features, true);
        this.featureBoomGuru = systemFeature('boom_guru', features, true);
        this.catAppShow = systemFeature('cat_app', features, true);
        this.catInspectionDownloadButtons = systemFeature('download_button', features, false);
        this.catVisionLinkEnabled = systemFeature('cat_visionlink', features, true);
        this.catCompatiblePartsShow = systemFeature('compatible_parts', features, false);

        this.isVideocall = systemFeature('videocall', features, true)
          && !(this.store.selectSnapshot(SettingsState.borusanBlockedActions)?.indexOf(BorusanBlockedActionsEnum.TechnicalSupportVideoCall) >= 0);

        this.showQuotationSection = systemFeature('equipment_quotation_section', features, true);
        this.equipmentAgendaSystemFeature = systemFeature('equipment_agenda', features, true);
        this.featureWorkOrderPlan = systemFeature('work_order_plan', features, true);
        this.equipmentCostDetailSystemFeature = systemFeature('equipment_cost_detail', features, true);

        if (this.user?.isLdapLogin) {
          this.featureSoundRecordCollecting = systemFeature('sound_record_collecting_borusan_user', features, false);
          this.featureSoundDiagnostics = systemFeature('sound_diagnostics_borusan_user', features, false);

        }

        this.systemFeatureCatPccVideoCall = systemFeature('cat_pcc_video_call', features, false);
        this.systemFeatureCatVisionLinkVideoCall = systemFeature('cat_vision_link_video_call', features, false);
      }
    });
    this.currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);

    // artık yeni permission'dan alıyoruz bu bilgileri
    // this.isSparePartModule = !!this.currentCustomer?.roleList?.filter(x => x === 'ManageSpareParts')?.length;

    this.equipmentServiceHistory$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.equipmentServiceHistory = data;
        }
      });

    this.equipmentQuotations$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.equipmentQuotations = data;
        }
      });
    this.isSAMLLogin = this.store.selectSnapshot(LoginState.isSAMLLogin);
  }

  loadEquipmentMDAPmPlanner(serialNumber: string) {
    this.store.dispatch(new GetEquipmentMDAPmPlannerAction(serialNumber));
    this.equipmentMDAPmPlanner$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => this.equipmentMDAPmPlanner = data);
  }

  loadEquipmentSosAnalyzes(serialNumber: string) {
    this.store.dispatch(new GetEquipmentSosAnalyzesAction(serialNumber));
    this.equipmentSosAnalyzes$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.equipmentSosAnalyzes = data;
      });
  }

  loadEquipmentInspectionResults(serialNumber: string) {
    this.store.dispatch(new GetEquipmentInspectionsAction(serialNumber));
    this.equipmentInspectionResults$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.equipmentInspectionResults = data;
      });
  }

  lastEquipment(serviceHistory: EquipmentServiceHistoryModel[]) {
    if (serviceHistory?.length > 0) {
      return [...serviceHistory].pop();
    }
    return null;
  }

  lastResult(result: any[]) {
    if (result?.length > 0) {
      return [...result].pop();
    }
    return null;
  }

  firstResult(result: any[]) {
    if (result?.length > 0) {
      return [...result].shift();
    }
    return null;
  }

  address(addressId) {
    const customer = this.store.selectSnapshot(CustomerState.customer);
    if (snq(() => customer.details.addresses)) {
      return customer.details.addresses.find((item) => +item.id === +addressId);
    }
    return null;
  }

  navigateToBack() {
    window.history.back();
  }

  requestService() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-service',
        this.equipment?.equipmentNumber,
      ], {
        queryParams: {
          source: this.source || 'Equipment',
          sourceRoot: 'Equipment'
        }
      })
      .then();
  }

  soundButtonClick() {
    if (this.featureSoundDiagnostics && this.equipment?.isSoundDiagEnabled && this.featureSoundRecordCollecting) {
      return this.goSoundDiagnosticStepPage();
    } else if (this.featureSoundRecordCollecting && this.EquipmentSoundDiagFeedbackPermission) {
      this.startRecordUpload();
    } else if (this.featureSoundDiagnostics && this.equipment?.isSoundDiagEnabled && this.EquipmentSoundDiagCheckPermission) {
      this.soundDiagnostic(this.equipment);
    }
  }


  goSoundDiagnosticStepPage() {

    const transactionId = uuid();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'SOUND_DIAGNOSTIC_STEP_PAGE_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'expertise',
        'sound-diagnostic-step',
      ], {
        queryParams: {
          backButton: 1
        }
      })
      .then();
  }

  startRecordUpload() {

    const transactionId = uuid();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'START_RECORD_UPLOAD_DIRECT_ENTER_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'expertise',
        'diagnostic-record',
      ], {
        queryParams: {
          backButton: 1
        }
      })
      .then();
  }

  requestMda() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-mda',
        this.equipment?.equipmentNumber,
      ])
      .then();
  }

  requestEquipmentPart() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-equipment-part',
        this.equipment?.equipmentNumber,
      ])
      .then();
  }

  requestMyEquipmentAgenda() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-my-equipment-agenda',
        this.equipment?.equipmentNumber,
        'calendar'
      ], { queryParams: { isActiveRoute: 2 } })
      .then();
  }

  requestEquipmentCostDetail() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'cost-detail',
        this.equipment?.serialNumber
      ])
      .then();
  }

  onCloseServiceHistory() {
    this.serviceHistoryStatus = false;
  }

  openServiceHistory() {
    this.serviceHistoryStatus = true;
  }

  openDiagnosticDataPage() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'diagnostic-data',
      ], {
        queryParams: {
          backButton: 1
        }
      })
      .then();
  }

  openPSECampaignPage() {
    this.pSECampaignModal = true;
    //   if(this.HasPSECampaign){
    //     if(this.pSECampaignModal){
    //       this.startPolling(this.pseRequestId);
    //     }
    // }
    document.documentElement.style.overflow = 'hidden';
  }

  openCmPSEModal() {
    this.cmPSEModal = true;
    document.documentElement.style.overflow = 'hidden';
  }

  repairStatusModal() {
    this.repairStatus = true;
  }

  closePSECampaignModal() {
    this.pSECampaignModal = false;
    document.documentElement.style.overflow = 'auto';
  }

  openInspectionHistoryPage() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'inspection-history',
      ], {
        queryParams: {
          backButton: 1,
        }
      })
      .then();
  }

  onCloseInspectResults() {
    this.inspectResults = false;
  }

  openInspectResults() {
    this.inspectResults = true;
  }

  onCloseAnalyzesHistory() {
    this.analayzesHistoryStatus = false;
  }

  openAnalyzesHistory() {
    this.analayzesHistoryStatus = true;
  }

  openMDAPmPlanner() {
    this.mdaPmPlannerStatus = true;
  }

  onCloseMDAPmPlanner() {
    this.mdaPmPlannerStatus = false;
  }

  getSosAnalyzePdf(serialNumber: string, compName: string, analyzeDocumentNumber: string) {
    this.sosAnalyzesDownloadLoading = [analyzeDocumentNumber];
    console.log('getSosAnalyzePdf', serialNumber, ' - ', compName, -analyzeDocumentNumber);
    const analyzePdf = this.mediaService.getSosAnalyzePdf(serialNumber, compName).subscribe(pdfData => {
      this.sosAnalyzesDownloadLoading = [];
      return pdfData;
    });
    console.log('PDF DATA', analyzePdf);
  }

  soundDiagnostic(equipmentDetail: EquipmentModel) {
    if (this.soundSending) {
      return;
    }
    this.soundSending = true;
    setTimeout(() => this.soundSending = false, 1000);

    const transactionId = uuid();

    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'SOUND_DIAGNOSTIC_DIRECT_ENTER_BUTTON', {
        source: 'Equipment',
        transactionId,
        equipmentNumber: this.equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();

    this.frameService.sendMessage(FrameMessageEnum.voice_record, {
      EquipmentNumber: equipmentDetail?.equipmentNumber,
      SerialNumber: equipmentDetail.serialNumber,
      CustomerNumber: this.customer?.customerNumber,
      Source: 'Equipment',
      TransactionId: transactionId,
    });
  }

  onClickLocation(equipmentNumber: string) {
    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'OPEN_MAP', {
        equipment: equipmentNumber,
        serialNumber: this.serialNumber
      })
      .subscribe();
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'equipment-map',
          equipmentNumber,
        ],
        { relativeTo: this.route },
      );
  }

  private updateServiceWarningState(serviceWarningShowed: string[]) {
    this.store.dispatch(new CommonStoreAction({
      serviceWarningShowed,
    }));
  }

  removeEquipment(equipmentDetail: EquipmentModel) {
    const company = this.store.selectSnapshot(LoginState.company);
    this.removeModalLoading = true;
    this.formService.equipmentRemove({
      CompanyId: company?.id,
      CompanyName: company?.name,
      equipmentNumber: equipmentDetail.equipmentNumber,
      countryCode: 'TR', // TODO buna ihtiyac olmamali
      description: ''
    }).subscribe(() => {
      this.removeModal = false;
      this.removeModalLoading = false;
      this.modalService.successModal({
        message: '_successfully_send_form',
        button: '_ok',
        translate: true
      }, this.elementRef.nativeElement);

    }, () => {
      this.removeModal = false;
      this.removeModalLoading = false;
      this.modalService.errorModal({
        message: '_general_error_message'
      });
    });
  }

  toUpdateEquipment() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'equipment-update',
        this.equipment.equipmentNumber
      ])
      .then();
  }


  startExpertise() {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'expertise',
      'init',
      this.equipment.equipmentNumber
    ], {
      queryParams: { backButton: 1 }
    }).then();
  }

  toLiveSupportPage() {
    if (this.store.selectSnapshot(AppState.connectionModalStatus)) {
      this.connectionModalStatus = true;
      return;
    }
    this.videocallLoading = true;
    this.videocallSubs('SWAT');
  }


  videocallSubs(queueName) {
    this.videocallLoading = true;
    this.videocallAgentReturn(queueName)
      .subscribe(agent => {
        this.logger.action('EQUIPMENT', 'VIDEOCALL_AGENT_AVAILABLE', {
          agentCount: agent.availableAgentCount,
          queueName,
        });
        if (agent?.availableAgentCount === 0) {
          if (agent?.backupQueueName) {
            this.videocallSubs(agent.backupQueueName);
            return;
          }
          this.videocallLoading = false;
          this.modalService.errorModal({
            message: this.translateService.instant('_videocall_not_have_available_text'),
            backdropClose: true,
          });
        } else if (agent) {
          this.videocallLoading = false;
          this.routeToVideocallForm();
        }
        return null;
      }, () => {
        this.videocallLoading = false;
      });
  }

  videocallAgentReturn(queueName) {
    return this.videocallService.getVideocallAvailableAgent({ queueName });
  }

  routeToVideocallForm() {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'live-support'
    ], {
      queryParams: {
        serialNumber: this.equipment.serialNumber,
        equipmentNumber: this.equipment.equipmentNumber,
        backButton: 1
      }
    }).then();
  }

  getImageHeight(imageHeight: number) {
    if (typeof (imageHeight) === 'number') {
      this.imageHeight = imageHeight;
    }
  }

  getBottomIconTop() {
    return this.imageHeight - 50;
  }

  getStatusLabel(history: any) {
    // do we need to translate
    switch (history.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
  }

  iconClockVisible(workingDate, workingHours) {
    if (!workingDate || workingHours === 0) {
      return false;
    }
    const daysAgo =
      (new Date(Date.now()).getTime() -
        new Date(workingDate.split('T')[0]).getTime()) /
      (1000 * 3600 * 24);
    return daysAgo < this.hideWorkingHoursOlderThanDays;
  }

  catAppClick() {
    if (this.catVisionLinkEnabled && this.isSAMLLogin) {
      this.openVisionLink();
      this.frameService.sendMessage(FrameMessageEnum.openVlSurvey, { urlPath: '/pcc-vlink-survey', urlParameters: { position: 'VisionLinkEnd'} });
      return;
    }

    const version = appVersionController(this.store.selectSnapshot(CommonState.version), 'openCatApp');
    let url: string;
    if (getOS() === 'Android') {
      url = 'market://details?id=com.cat.digital.mobile.visionlink';
    } else if (getOS() === 'IOS') {
      url = 'https://apps.apple.com/us/app/cat-visionlink/id1574373872';
    } else {
      url = 'https://play.google.com/store/apps/details?id=com.cat.digital.mobile.visionlink';
    }
    // if (version) { // cat url change remotevedz
    //   this.frameService.sendMessage(FrameMessageEnum.openCatApp, { url });
    // } else {
    this.frameService.sendMessage(FrameMessageEnum.openStore, { url });
    this.frameService.sendMessage(FrameMessageEnum.openVlSurvey, { urlPath: '/pcc-vlink-survey' });
    // }
  }

  protected openVisionLink() {
    this.frameService.sendMessage(FrameMessageEnum.openInPCC, {
      title: this.translateService.instant('Cat® VisionLink'), // TODO translate key
      url: SSORedirectKeys.VisionLink,
      direct: true,
      pccDigitalBanko: this.systemFeatureCatVisionLinkVideoCall
    });
  }

  catLandingClick() {
    const lang = this.store.selectSnapshot(LoginState.language);

    this.frameService.sendMessage(FrameMessageEnum.openInPCC, {
      title: this.translateService.instant('_cat_compatible_parts'),
      url: SSORedirectKeys.partsCatComLanding.replace('{{LANG}}', lang).replace('{{SERIAL}}', this.equipment.serialNumber),
      pccDigitalBanko: this.systemFeatureCatPccVideoCall
    });
  }


  downloading(attachment) {
    this.haveDownload = true;
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
    if (this.loadingAttachment.length > 1) {
      this.cancelDownloadFile(attachment.data.id);
    } else if (this.loadingAttachment.length === 0) {
      this.haveDownload = false;
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
      this.haveDownload = false;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.ref.detectChanges();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  closeVideocallModal() {
    this.videocallAvailableModal = false;
  }

  isPermissionInclues(permission: string): boolean {
    return this.permissions?.length && this.permissions.includes(permission);
  }

  isDropdownEmpty() {
    if (this.isPermissionInclues(PermissionEnum.RequestsInspection) ||
      this.isPermissionInclues(PermissionEnum.DocumentsInspectionHistory) ||
      this.isPermissionInclues(PermissionEnum.RequestsUpdateEquipment) ||
      this.isPermissionInclues(PermissionEnum.RequestsDeleteEquipment)) {
      return true;
    }
    return false;
  }

  accessEquipmentLocation(serialNumber) {
    const relatedLocationField = this.equipmentLocations?.find(l => l.serialNumber === serialNumber);

    this.relatedLocationField = relatedLocationField;

    if (this.equipmentLocations?.length > 0 && relatedLocationField?.longitude && relatedLocationField?.latitude) {
      return true;
    }

    return false;
  }


  loadWorkOrderPlan(serialNumber: string) {
    this.store.dispatch(new EquipmentWorkOrderPlanAction(serialNumber));
    this.equipmentWorkOrder$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.equipmentWorkOrder = data;
      });
  }

  setworkOrderPlanPopup() {
    this.workOrderPlanPopup = true;
  }

  boomGuruClick() {
    this.showBoomGuruSubmitModal = true;
  }

  closeBoomGuruSubmitModal() {
    this.showBoomGuruSubmitModal = false;
  }

  onBoomGuruSubmitSuccess() {
    this.showBoomGuruSubmitModal = false;
    // Show success message or perform any other actions after successful submission
    this.modalService.successModal({
        message: '_boom_guru_submit_success',
        button: '_ok',
        translate: true
      }
    );
  }
}
