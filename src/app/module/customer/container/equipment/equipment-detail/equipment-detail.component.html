<div *ngIf="equipmentDetail$ | async as equipmentDetail" class="equipment">
  <div class="px-3 py-4 equipment-top" [ngClass]="{
      'no-radius':
        !lastEquipment(equipmentServiceHistory) &&
        equipmentServiceHistory?.length &&
        equipmentQuotations?.length
    }">
    <div class="h4 nav-back">
      <i (click)="navigateToBack()" class="icon icon-back position-relative"></i>
    </div>
    <div class="z-index-1">
      <div class="mb-4 mx-auto d-flex align-items-center justify-content-center">
        <div (click)="startExpertise()"
             [hasPermission]="PermissionEnum.RequestsInspection"
             catUserClick
             [section]="'EQUIPMENT'"
             [subsection]="'EQUIPMENT_START_EXPERTISE_BUTTON_CLICK'"
             [data]="{
                equipmentNumber: equipment.equipmentNumber,
                serialNumber: equipment?.serialNumber
             }"
             [ngClass]="{'fit-btn': expertiseText.scrollWidth > 65}"
             class="equipment-inspect-icon bg-white cursor-pointer d-inline-flex">
          <div class="rounded-circle equipment-inspect-icon-circle">
            <img src="{{ equipmentInspectionIconWhite }}" height="22" width="22" alt=""/>
          </div>

          <div #expertiseText class="ml-2 my-auto equipment-inspect-icon-text text-wrap text-left">
            {{ "_start_expertise" | translate }}
          </div>
        </div>

        <div
          [hasPermission]="PermissionEnum.DocumentsInspectionHistory"
          (click)="openInspectionHistoryPage()"
          [ngClass]="{'fit-btn': inspectionHistoryText.scrollWidth > 65}"
          class="equipment-inspect-icon bg-white cursor-pointer d-inline-flex" style="top: 51px;"
        >
          <div class="rounded-circle equipment-inspect-icon-circle">
            <img src="{{ equipmentInspectionHistory }}" height="22" width="22" alt=""
                 style="left: 5px;filter: invert(100%);"/>
          </div>

          <div #inspectionHistoryText class="ml-2 my-auto equipment-inspect-icon-text text-wrap text-left">
            {{ "_inspection_history" | translate }}
          </div>
        </div>
        <div>
          <!-- borusanlı ve normal kontrolü -->
          <div *ngIf="(featureSoundRecordCollecting && EquipmentSoundDiagFeedbackPermission) ||
                    (equipmentDetail?.isSoundDiagEnabled && EquipmentSoundDiagCheckPermission && featureSoundDiagnostics)">
            <div [ngClass]="{'fit-btn': soundDiagnosticText.scrollWidth > 61}" (click)="soundButtonClick()"
                 class="sound-icon bg-white cursor-pointer d-inline-flex" style="height: max-content;
          display: flex !important;
          align-items: center;">
              <div class="rounded-circle sound-icon-circle">
                <i class="icon icon-sound cursor-pointer"></i>
              </div>
              <div #soundDiagnosticText class="ml-2 sound-icon-text text-wrap text-left">
                {{ "_sound_diagnostic" | translate }}
              </div>
            </div>
          </div>

          <!--          <div *ngIf="featureBoomGuru">-->
          <!--            <div [ngClass]="{'fit-btn': soundDiagnosticText.scrollWidth > 61}" (click)="boomGuruClick()"-->
          <!--                 class="boom-guru-icon cursor-pointer d-inline-flex" style="">-->
          <!--              <div class="rounded-circle boom-guru-icon-circle">-->
          <!--                <i class="icon icon-plus "></i>-->
          <!--              </div>-->
          <!--              <div #soundDiagnosticText class="ml-2 boom-guru-icon-text text-wrap text-left">-->
          <!--                {{ "_boom_guru" | translate }}-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </div>-->

        </div>
        <div *ngIf="isVideocall"
             [hasPermission]="PermissionEnum.VideoCallTechnicalSupport"
             catUserClick [section]="'EQUIPMENT'"
             [subsection]="'EQUIPMENT_LIVE_SUPPORT_BUTTON_CLICK'"
             [data]="{
                equipmentNumber: equipment.equipmentNumber,
                serialNumber: equipment?.serialNumber
              }"
             (click)="toLiveSupportPage()"
             class="videocall-icon bg-white cursor-pointer d-inline-flex align-items-center"
             [style.top.px]="getBottomIconTop()">
          <div class="rounded-circle videocall-icon-circle">
            <i class="icon icon-videocall cursor-pointer"></i>
          </div>

          <div class="ml-2 videocall-icon-text text-wrap text-left d-flex align-items-center">
            {{ "_live_support" | translate }}
          </div>
        </div>
        <div class="info-icons cursor-pointer info-icons-text d-flex flex-row-reverse"
             [style.top.px]="getBottomIconTop()">
          <div *ngIf="equipment?.equipmentNumber">
            <cat-fuel-level [equipmentNumber]="equipment?.equipmentNumber"></cat-fuel-level>
          </div>
          <div *ngIf="equipment.isProductLink" class="product-link d-inline-block" style="height:44px">
            <div class="d-flex justify-content-center h-100" [ngbTooltip]="'_connected_equipment' | translate"
                 tooltipClass="wifi-icon"
                 placement="left" triggers="click">
              <i class="icon icon-wifi repair-icon bg-white d-flex justify-content-center align-items-center"
                 [style.color]="equipment?.productLinkColor"></i>
            </div>
          </div>
        </div>
        <cat-image-preview [id]="equipment.productHierarchy" [model]="equipment.model"
                           [imageSize]="imageSizeEnum.mobilefullsize"
                           [title]="equipmentDetail.brand + ' ' + equipmentDetail.model"
                           (imageHeight)="getImageHeight($event)">
        </cat-image-preview>
      </div>
      <div>
        <div class="d-flex justify-content-between">
          <div class="text-secondary text-left">
            <div class="font-weight-bold">
              {{ equipmentDetail.brand }}
              {{ equipmentDetail.model }}
              <div class="btn-group float-right" ngbDropdown placement="bottom-right bottom-left bottom" role="group"
                   aria-label="Button group with nested dropdown" *ngIf="isDropdownEmpty()">
                <button class="btn btn-sm dropdown-toggle-split equipment-menu-button py-0 px-1"
                        ngbDropdownToggle></button>
                <div class="equipment-menu mt-0 dropdown-menu" ngbDropdownMenu>
                  <button catUserClick
                          [section]="'EQUIPMENT'"
                          [subsection]="'EQUIPMENT_START_EXPERTISE_BUTTON_CLICK'"
                          [data]="{
                            equipmentNumber: equipment.equipmentNumber,
                            serialNumber: equipment?.serialNumber
                          }"
                          [hasPermission]="PermissionEnum.RequestsInspection"
                          class="pl-2" ngbDropdownItem (click)="startExpertise()">
                    <i class="icon icon-search pr-2"></i>
                    {{ "_start_expertise" | translate }}
                  </button>
                  <button [hasPermission]="PermissionEnum.DocumentsInspectionHistory" ngbDropdownItem
                          (click)="openInspectionHistoryPage()" class="pl-2">
                    <i class="icon icon-clock pr-2" style="font-size: 15px; color: black;"></i>
                    {{ "_inspection_history" | translate }}
                  </button>
                  <button [hasPermission]="PermissionEnum.RequestsUpdateEquipment" catUserClick [section]="'EQUIPMENT'"
                          [subsection]="'EQUIPMENT_UPDATE_INFO_BUTTON_CLICK'"
                          [data]="{
                      equipmentNumber: equipment.equipmentNumber,
                      serialNumber: equipment?.serialNumber
                    }" class="pl-2" ngbDropdownItem (click)="toUpdateEquipment()">
                    <i class="icon icon-edit pr-2"></i>
                    {{ "_update_info" | translate }}
                    <!--                    <i class="icon icon-x font-size-10px font-weight-bold"></i>-->
                  </button>
                  <button *ngIf="featureBoomGuru"
                          catUserClick [section]="'EQUIPMENT'"
                          [subsection]="'BOOM_GURU'"
                          [data]="{
                      equipmentNumber: equipment.equipmentNumber,
                      serialNumber: equipment?.serialNumber
                    }" class="pl-2" ngbDropdownItem (click)="boomGuruClick()">
                    <i class="icon icon-clock pr-2"></i>
                    {{ "_boom_guru" | translate }}
                  </button>

                  <button [hasPermission]="PermissionEnum.RequestsDeleteEquipment" catUserClick [section]="'EQUIPMENT'"
                          [subsection]="
                      'EQUIPMENT_REMOVE_FROM_INVENTORY_BUTTON_CLICK'
                    " [data]="{
                      equipmentNumber: equipment.equipmentNumber,
                      serialNumber: equipment?.serialNumber
                    }" ngbDropdownItem (click)="removeModal = true" class="equipment-menu-element pl-2">
                    <i class="icon icon-x pr-2"></i>
                    {{ "_remove_from_inventory" | translate }}
                  </button>

                </div>
              </div>
            </div>
            <div class="font-size-13px text-secondary text-left mb-2">
              {{ equipmentDetail?.serialNumber | serialFormat }}
            </div>
          </div>


          <div class="d-flex justify-content-between">
            <div *ngIf="equipmentDetail.isMda" class="mda-icon" (click)="openMDAPmPlanner()">
              <img [src]="mdaFlag" style="height: 24px; width: 24px;">
            </div>
            <!-- CM PSE  -->
            <div class="cursor-pointer info-icons-text d-flex mr-1">
              <div
                *ngIf="(equipmentDetail.isCmPse && featureCmPse)" class="d-inline-block"
                style="height:44px">
                <div class="d-flex justify-content-center h-100"
                     (click)="openCmPSEModal()"
                     catUserClick [section]="'PSE_CAMPAIGN_CM'" [subsection]="'PSE_CAMPAIGN_CM_BUTTON_CLICK'"
                     [data]="{equipmentNumber: this.equipmentDetail?.equipmentNumber}"
                     placement="left" triggers="click">
                  <img [src]="cmPseIcon"
                       class="icon icon-wifi repair-icon bg-white d-flex justify-content-center align-items-center"
                       alt="ladyBirdIcon" style="height: 40px; width: 40px;">
                </div>
              </div>
            </div>
            <div *ngIf="featureWorkOrderPlan && equipmentWorkOrder && equipmentWorkOrder.length > 0"
                 class="work-plan-button d-inline-block" (click)="setworkOrderPlanPopup()">
              <div class="d-flex justify-content-center h-100">
                <img [src]="workOrderPlansIcon">
              </div>
            </div>
            <div *ngIf="
                (equipmentDetail.revisionIndicator === 'orange' ||
                equipmentDetail.revisionIndicator === 'yellow' ||
                equipmentDetail.revisionIndicator === 'red' ||
                equipmentDetail.equipmentRevisionCampaign && campaignPermission ) ||
                (HasPSECampaign && featurePseCampaign && isSparePartModule && psePermission)

              " class="repair-icon bg-white cursor-pointer mb-2" [ngClass]="{
                'text-warning': equipmentDetail.revisionIndicator === 'orange',
                'text-yellow': equipmentDetail.revisionIndicator === 'yellow',
                'text-danger': equipmentDetail.revisionIndicator === 'red'
              }">

              <div class="d-flex flex-row text-center">
                <!-- PSE ICON -->
                <div [hasPermission]="PermissionEnum.EquipmentDetailPse"
                     *ngIf="HasPSECampaign && featurePseCampaign && isSparePartModule"
                     (click)="openPSECampaignPage()"
                     catUserClick [section]="'PSE_CAMPAIGN'" [subsection]="'PSE_CAMPAIGN_BUTTON_CLICK'"
                     [data]="{equipmentNumber: this.equipmentDetail?.equipmentNumber}"
                >
                  <div class="d-flex flex-column justify-content-center text-warning mr-2" [ngClass]="{
                    'border-right pr-2 mr-2':  equipmentDetail?.revisionIndicator || equipmentDetail?.equipmentRevisionCampaign
                  }">
                    <div class="d-flex justify-content-center">
                      <img [src]="ladyBirdIcon" alt="ladyBirdIcon" style="height: 18px; width: 18px;">
                    </div>
                    <div class="d-flex justify-content-center">
                      <span class="font-size-11px" style="width: max-content;">
                        {{ '_pse_title' | translate }}
                      </span>
                    </div>
                  </div>
                </div>
                <!-- MC PSE ICON -->
                <!--  TODo               [hasPermission]="PermissionEnum.EquipmentDetailCmPse"-->
                <!--                <div

                                     *ngIf="(equipmentDetail.isCmPse && featureCmPse)"
                                     (click)="openCmPSEModal()"
                                catUserClick [section]="'CM_PSE'" [subsection]="'CM_PSE_BUTTON_CLICK'"
                                [data]="{equipmentNumber: this.equipmentDetail?.equipmentNumber}"
                                >
                                  <div class="d-flex flex-column justify-content-center text-warning " [ngClass]="{
                                    'border-left pl-2 ml-2': HasPSECampaign && featurePseCampaign && isSparePartModule,
                                    'border-right pr-2 mr-2':  equipmentDetail?.revisionIndicator || equipmentDetail?.equipmentRevisionCampaign
                                  }">
                                    <div class="d-flex justify-content-center">
                                      <img [src]="cmPseIcon" alt="ladyBirdIcon" style="height: 18px; width: 18px;">
                                    </div>
                                    <div class="d-flex justify-content-center">
                                      <span class="font-size-11px" style="width: max-content;">
                                        {{ '_cm_pse_title_small' | translate }}
                                      </span>
                                    </div>
                                  </div>
                                </div>-->


                <!-- Muneccim Campaign -->
                <div [hasPermission]="PermissionEnum.EquipmentDetailCampaign"
                     *ngIf="equipmentDetail.equipmentRevisionCampaign" (click)="repairStatusModal()" catUserClick
                     [section]="'EQUIPMENT'" [subsection]="'SERVICE_WARNING_CLICK'" [data]="{
                  equipmentNumber: equipmentDetail?.equipmentNumber,
                  serialNumber: equipmentDetail?.serialNumber
                }">
                  <div class="d-flex flex-column justify-content-center text-success" [ngClass]="{
                      'border-right pr-2 mr-2':
                        equipmentDetail.revisionIndicator === 'orange' ||
                        equipmentDetail.revisionIndicator === 'yellow' ||
                        equipmentDetail.revisionIndicator === 'red'
                    }">
                    <div [ngSwitch]="
                        equipmentDetail?.equipmentRevisionCampaign?.type
                      " class="d-flex justify-content-center">
                      <i *ngSwitchCase="'_campaign'" class="icon icon-campaign"></i>
                      <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon"></i>
                      <i *ngSwitchCase="'_discount'" class="icon {{
                          equipment?.equipmentRevisionCampaign?.currency
                            | currenyIcon
                        }}"></i>
                      <span *ngSwitchCase="'_percent_discount'" style="line-height: initial">%
                        {{
                          equipmentDetail?.equipmentRevisionCampaign?.amount
                        }}</span>
                      <i *ngSwitchDefault class="icon icon-campaign"></i>
                    </div>
                    <div class="d-flex justify-content-center">
                      <span class="font-size-11px" style="width: max-content;">
                        {{
                          equipmentDetail?.equipmentRevisionCampaign?.type
                            | translate
                        }}
                      </span>
                    </div>
                  </div>
                </div>
                <!-- Muneccim Warning -->
                <div *ngIf="
                    equipmentDetail.revisionIndicator === 'orange' ||
                    equipmentDetail.revisionIndicator === 'yellow' ||
                    equipmentDetail.revisionIndicator === 'red'
                  " (click)="repairStatusModal()" catUserClick [section]="'EQUIPMENT'"
                     [subsection]="'SERVICE_WARNING_CLICK'" [data]="{
                    equipmentNumber: equipmentDetail?.equipmentNumber,
                    serialNumber: equipmentDetail?.serialNumber
                  }">
                  <div class="d-flex justify-content-center">
                    <i class="icon icon-repair"></i>
                  </div>
                  <div class="d-flex justify-content-center">
                    <span class="font-size-11px" style="width:auto; max-width: auto;">
                      {{ "_service!" | translate }}
                    </span>
                  </div>
                </div>
              </div>

            </div>
            <div></div>
          </div>
        </div>

        <div class="d-flex">
          <div class="pr-4 font-size-13px text-secondary" *ngIf="
              iconClockVisible(
                equipmentDetail?.workingHourDate,
                equipmentDetail?.workingHours
              )
            ">
            <i class="icon icon-clock mr-1"></i>
            {{ equipmentDetail.workingHours }}
            {{ "_hour" | translate }}
            <ng-container *ngIf="equipmentDetail.workingHourDate">
              ({{ equipmentDetail.workingHourDate | date: "dd.MM.yyyy" }})
            </ng-container>
            <!--            {{ equipmentDetail.workingHoursUnit | translate }}-->
          </div>
          <div
            *ngIf="accessEquipmentLocation(equipment?.serialNumber)
            || (equipment?.location &&
            equipment?.location.latitude &&
            equipment?.location.longtitude)"
            class="font-size-13px text-secondary"
            (click)="onClickLocation(equipment?.equipmentNumber)"
          >
            <i class="icon icon-location mr-1"></i>
            {{ (relatedLocationField?.country || equipmentDetail?.location?.locationName) }}
          </div>
        </div>
        <div class="d-flex justify-content-end">
          <div [hasPermission]="PermissionEnum.EquipmentDetailWarnings" *ngIf="equipmentDetail?.hasDiagnosticData"
               class="product-link d-inline-block ml-1 mr-1">
            <div class="d-flex justify-content-center" (click)="openDiagnosticDataPage()">
              <i class="icon icon-diagnostics repair-icon"></i>
            </div>
          </div>
          <!-- *ngIf="user.isLdapLogin" -->
          <!-- <div class="product-link d-inline-block ml-2 mr-2 bg-black" *ngIf="user.isLdapLogin && featureSoundRecordCollecting">
            <div class="d-flex justify-content-center" (click)="startRecordUpload()" >
              <i class="icon icon-sound text-dark repair-icon"></i>
            </div>
          </div> -->
        </div>
      </div>

      <div class="d-flex flex-column mt-4 mb-1">
        <div [hasPermission]="PermissionEnum.RequestsService" catUserClick [section]="'EQUIPMENT'"
             [subsection]="'SERVICE_REQUEST_BUTTON'" [data]="{
            equipmentNumber: equipmentDetail?.equipmentNumber,
            serialNumber: equipmentDetail?.serialNumber
          }" (click)="requestService()"
             class="min-width-200 btn btn-info btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg">
          {{ "_request_service" | translate }}
        </div>
        <div [hasPermission]="PermissionEnum.RequestsSparePart" catUserClick [section]="'EQUIPMENT'"
             [subsection]="'REQUEST_EQUIPMENT_PART'" [data]="{
            equipmentNumber: equipmentDetail?.equipmentNumber,
            serialNumber: equipmentDetail?.serialNumber
          }" (click)="requestEquipmentPart()"
             class="min-width-200 btn btn-info btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg">
          {{ "_order_parts_btn" | translate }}
        </div>
        <div [hasPermission]="PermissionEnum.RequestsMda" *ngIf="mdaShowStatus" catUserClick [section]="'EQUIPMENT'"
             [subsection]="'REQUEST_MDA'" [data]="{
            equipmentNumber: equipmentDetail?.equipmentNumber,
            serialNumber: equipmentDetail?.serialNumber
          }" (click)="requestMda()"
             class="min-width-200 btn btn-info btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg">
          {{ "_create_mda_request" | translate }}
        </div>
        <div [hasPermission]="PermissionEnum.EquipmentAgendaDetails" *ngIf="equipmentAgendaSystemFeature" catUserClick
             [section]="'EQUIPMENT'" [subsection]="'REQUEST_MY_EQUIPMENT_AGENDA'" [data]="{
          equipmentNumber: equipmentDetail?.equipmentNumber,
          serialNumber: equipmentDetail?.serialNumber
        }" (click)="requestMyEquipmentAgenda()"
             class="min-width-200 btn btn-warning text-white btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg">
          <i class="icon icon-contract"></i>
          {{ "_my_equipment_agenda" | translate }}
        </div>
        <div
          *ngIf="equipmentCostDetailSystemFeature"
          catUserClick [section]="'EQUIPMENT'"
          [subsection]="'COST_DETAIL'"
          [data]="{
        equipmentNumber: equipmentDetail?.equipmentNumber,
        serialNumber: equipmentDetail?.serialNumber}"
          (click)="requestEquipmentCostDetail()"
          class="min-width-200 btn btn-warning text-white btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg"
        >
          {{ "_equipment_cost_detail" | translate }}
        </div>
        <div *ngIf="catAppShow && isCATEquipment" catUserClick [section]="'EQUIPMENT'" [subsection]="'CAT_APP_CLICK'"
             [data]="{
            equipmentNumber: equipmentDetail?.equipmentNumber,
            serialNumber: equipmentDetail?.serialNumber
          }" (click)="catAppClick()"
             class="cat-app-button min-width-200 btn btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg justify-content-center">
          <div class="rounded-circle">
            <img class="rounded-circle" [src]="catAppImage" alt="Cat® App"/>
          </div>
          <div class="ml-2 text-wrap">Cat® VisionLink</div>
        </div>
        <div *ngIf="catCompatiblePartsShow && isCATEquipment && isSAMLLogin" catUserClick [section]="'EQUIPMENT'"
             [subsection]="'CAT_LANDING_CLICK'"
             [data]="{
            serialNumber: equipmentDetail?.serialNumber
          }" (click)="catLandingClick()"
             class="cat-app-button min-width-200 btn btn-sm font-weight-semi-bold py-1 mb-3 px-4 rounded-lg justify-content-center">
          <div class="rounded-circle">
            <img class="rounded-circle" [src]="catAppImage" alt="Cat® App"/>
          </div>
          <div class="ml-2 text-wrap"> {{ '_cat_compatible_parts' | translate }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="equipment-bottom">
    <!-- ? Equipment Service Histroy -->
    <div [hasPermission]="PermissionEnum.DocumentsServiceHistory" class="pl-3 pr-3 pt-4 border"
         *ngIf="lastEquipment(equipmentServiceHistory)">
      <div class="z-index-1">
        <div class="mb-3 mt-2" style="display: grid; grid-template-columns: auto auto;">
          <div class="font-weight-bold">
            {{ "_service_information" | translate }}
          </div>
          <div (click)="openServiceHistory()" catUserClick [section]="'EQUIPMENT'" [subsection]="'SERVICE_HISTORY'"
               [data]="{
              equipmentNumber: equipmentDetail?.equipmentNumber,
              serialNumber: equipmentDetail?.serialNumber
            }"
               class="btn btn-link font-weight-semi-bold p-0 text-info small d-flex align-items-center justify-content-end"
               *ngIf="equipmentServiceHistory?.length">
            <div class="text-right mr-2">
              {{ "_show_service_history" | translate }}
            </div>
            <i class="icon icon-chevron-right"></i>
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-3 font-weight-semi-bold">
            {{ "_acceptance" | translate }}
          </div>
          <div class="col-4 pl-0">
            :
            {{ lastEquipment(equipmentServiceHistory).startDate | date: "dd.MM.yyyy" }}
          </div>
          <div class="col-5 font-weight-semi-bold text-right"
               [style.color]="lastEquipment(equipmentServiceHistory)?.serviceStatusColor">
            {{ getStatusLabel(lastEquipment(equipmentServiceHistory)) | translate }}
          </div>
        </div>
        <div class="description mb-2 font-size-14px text-secondary">
          <div class="font-weight-semi-bold">
            {{ "_description" | translate }}
          </div>
          {{ lastEquipment(equipmentServiceHistory).description }}
        </div>
      </div>
      <div class=" z-index-1 border-bottom p-3"></div>
    </div>
    <!-- ? Inspect Results -->
    <div [hasPermission]="PermissionEnum.DocumentsCatInspections" class="pl-3 pr-3 pt-4 "
         *ngIf="equipmentInspectionResults?.length">
      <div class="z-index-1" *ngIf="firstResult(equipmentInspectionResults)">
        <div class="mb-3 mt-2 row mx-0" style="display: grid; grid-template-columns: auto auto;">
          <div class="font-weight-bold align-self-center px-0">
            {{ "_inspection_results" | translate }}
          </div>
          <div (click)="openInspectResults()" catUserClick [section]="'EQUIPMENT_DETAIL'"
               [subsection]="'INSPECTION_RESULT_CLICK'"
               class="btn btn-link font-weight-semi-bold p-0 text-info small d-flex align-items-center justify-content-end"
               *ngIf="equipmentInspectionResults?.length">
            <div class="text-right mr-2">
              {{ "_show_inspection_results" | translate }}
            </div>
            <i class="icon icon-chevron-right"></i>
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-4 font-weight-semi-bold">
            {{ "_end_date" | translate }}
          </div>
          <div class="col-4 pl-0">
            :
            {{ firstResult(equipmentInspectionResults)?.finishedDate | date: "dd.MM.yyyy" }}
          </div>
          <div *ngIf="!firstResult(equipmentInspectionResults)?.status" class="col-5"></div>
          <div class="col-4 font-weight-semi-bold text-right" *ngIf="firstResult(equipmentInspectionResults)?.status"
               [style]="{'color': firstResult(equipmentInspectionResults)?.statusColor}">
            {{ firstResult(equipmentInspectionResults)?.status | translate }}
          </div>
          <div class="col-4 font-weight-semi-bold">
            {{ "_form_name" | translate }}
          </div>
          <div class="col-6 pl-0">
            :
            {{ firstResult(equipmentInspectionResults)?.formName }}
          </div>

          <div class="col-4 font-weight-semi-bold">
            {{ "_approved_status" | translate }}
          </div>
          <div class="col-6 pl-0 ">
            :
            {{ firstResult(equipmentInspectionResults)?.overallResponse }}
          </div>
        </div>
        <!-- catInspection pdf download start -->

        <div class="row no-gutters d-flex align-items-center py-2 w-95"
             *ngIf="firstResult(equipmentInspectionResults) && catInspectionDownloadButtons">
          <div
            [id]="firstResult(equipmentInspectionResults)?.inspectionNumber"
            catUserClick
            [section]="'CAT_INSPECTION'"
            [subsection]="'PDF_DOWNLOAD'"
            [data]="{
              dCode: firstResult(equipmentInspectionResults)?.dCode,
              inspectionNumber: firstResult(equipmentInspectionResults)?.inspectionNumber
            }"
            catDownloadFile
            [downloadType]="DownloadTypeEnum.catInspection"
            [haveDownload]="haveDownload"
            [downloadParams]="{
              dCode: firstResult(equipmentInspectionResults)?.dCode,
              inspectionNumber: firstResult(equipmentInspectionResults)?.inspectionNumber
            }"
            (downloadLoading)="downloading($event)"
            class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
            [class.spinner]="
              isDownloading(firstResult(equipmentInspectionResults)?.inspectionNumber)
            ">
            <i class="icon icon-spinner8"
               *ngIf="isDownloading(firstResult(equipmentInspectionResults)?.inspectionNumber)"></i>
            <i class="icon icon-download"
               *ngIf="!isDownloading(firstResult(equipmentInspectionResults)?.inspectionNumber)"></i>
            <a class="d-none" [download]="firstResult(equipmentInspectionResults)?.inspectionNumber"></a>
          </div>
          <div>
            <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
              {{ "_cat_inspection_pdf_download" | translate }}
            </div>
          </div>
          <div class="ml-auto"
               *ngIf="isDownloading(firstResult(equipmentInspectionResults)?.inspectionNumber) &&
               cancelDonwloadVersion"
               (click)=" cancelDownloadFile(firstResult(equipmentInspectionResults)?.inspectionNumber) ">
            <i class="icon icon-x"></i>
          </div>
        </div>
        <!-- catInspection pdf download end -->

        <!-- <div class="description mb-2 font-size-14px text-secondary"> -->
        <!-- <div class="font-weight-semi-bold"> -->
        <!-- {{ "_description" | translate }} -->
        <!-- </div> -->
        <!-- {{ lastEquipment(equipmentDetail).description }} -->
        <!-- </div> -->
      </div>
      <div class=" z-index-1 border-bottom p-3"></div>
    </div>
    <!-- ? Equipment Quotation -->
    <div [hasPermission]="PermissionEnum.EquipmentDetailSpecialDeals" class="pl-3 pr-3 pt-4 z-index-1"
         *ngIf="showQuotationSection && equipmentQuotations?.length"
         [ngClass]="{ 'non-service-offer': equipmentDetail?.serviceHistory }">
      <div class="font-weight-bold ng-tns-c159-0 mb-4">
        {{ "_equiment_quotations" | translate }}
      </div>
      <div *ngFor="let quotations of equipmentQuotations">
        <div class="h6 offer-title text-info mb-1 mt-4">
          {{ quotations.processTypeDescription | translate }}
        </div>
        <div class="d-flex align-items-center pd-4 pb-4 border-bottom border-1">
          <div class="offer-text d-flex flex-fill text-secondary">
            <div class="mr-2 pr-1">
              <div class="mb-1 font-weight-semi-bold">
                {{ "_offer_no" | translate }}
              </div>
              <div *ngIf="quotations?.quotationValidDate && quotations?.processType !== OfferCategoriesEnum.CVA_OFFER"
                   class="font-weight-semi-bold">
                {{ "_validity_date" | translate }}
              </div>
            </div>
            <div>
              <div class="mb-1">
                <span class="mr-2 pr-1">:</span>
                {{ quotations?.quotationNumber }}
              </div>
              <div *ngIf="quotations?.quotationValidDate && quotations?.processType !== OfferCategoriesEnum.CVA_OFFER">
                <span class="mr-2 pr-1">:</span>
                {{ quotations?.quotationValidDate | date: "dd.MM.yyyy" }}
              </div>
            </div>
          </div>
          <div catUserClick [section]="'OFFER'" [subsection]="'OFFER_DOWNLOAD'"
               [data]="{ quotationNumber: quotations?.quotationNumber, guid: quotations?.guid }"
               catDownloadFile
               [downloadType]="DownloadTypeEnum.offer"
               [haveDownload]="haveDownload"
               [downloadParams]="{ quotationNumber: quotations?.quotationNumber, guid: quotations?.guid }"
               (downloadLoading)="downloading($event)"
               class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
               [class.spinner]="isDownloading(quotations?.quotationNumber)">
            <i class="icon icon-spinner8" *ngIf="isDownloading(quotations?.quotationNumber)"></i>
            <i class="icon icon-download" *ngIf="!isDownloading(quotations?.quotationNumber)"></i>
            <a class="d-none" [download]="quotations?.quotationNumber"></a>
          </div>
        </div>
      </div>
    </div>
    <!-- ? Equipment SOS Analayzes -->
    <div [hasPermission]="PermissionEnum.DocumentsSosAnalyzes" class="pl-3 pr-3 pt-4"
         *ngIf="equipmentSosAnalyzes?.length">
      <div class="z-index-1">
        <div class="mb-3 mt-2 mx-0" style="display: grid; grid-template-columns: auto auto;">
          <div class="font-weight-bold align-self-center text-left px-0">
            {{ "_equipment_sos_analyzes" | translate }}
          </div>
          <div (click)="openAnalyzesHistory()" catUserClick [section]="'SOS_ANALYZES'"
               [subsection]="'SOS_ANALYZES_DETAIL'"
               [data]="{ equipmentNumber: equipmentDetail?.equipmentNumber }"
               class="btn btn-link font-weight-semi-bold p-0 text-info small d-flex align-items-center justify-content-end"
               *ngIf="equipmentSosAnalyzes?.length">
            <div class="text-right mr-2">
              {{ "_show_equipment_sos_analyzes" | translate }}
            </div>
            <i class="icon icon-chevron-right"></i>
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-4 font-weight-semi-bold">
            {{ "_equipment_analyzes_date" | translate }}
          </div>
          <div class="col-5 pl-0">
            :
            {{ equipmentSosAnalyzes[0]?.analyzeDate | date: "dd.MM.yyyy" }}
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-4 font-weight-semi-bold">
            {{ "_analyzes_document_number" | translate }}
          </div>
          <div class="col-5 pl-0">
            :
            {{ equipmentSosAnalyzes[0]?.analyzeDocumentNumber }}
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-4 font-weight-semi-bold">
            {{ "_compartment" | translate }}
          </div>
          <div class="col-8 pl-0">
            :
            {{ equipmentSosAnalyzes[0]?.compDescription }}
          </div>
        </div>
        <!-- sosAnalyzes pdf download start -->

        <div class="row no-gutters d-flex align-items-center py-2" *ngIf="equipmentSosAnalyzes">
          <div [id]="equipmentSosAnalyzes[0]?.analyzeDocumentNumber" catUserClick [section]="'SOS_ANALYZES'"
               [subsection]="'PDF_DOWNLOAD'"
               [data]="{
              analyzeDocumentNumber:
                equipmentSosAnalyzes[0]?.analyzeDocumentNumber
            }" catDownloadFile
               [downloadType]="DownloadTypeEnum.sosAnalyzePdf"
               [haveDownload]="haveDownload"
               [downloadParams]="{
              serialNumber: equipmentDetail?.serialNumber,
              analyzeDocumentNumber:
                equipmentSosAnalyzes[0]?.analyzeDocumentNumber
            }" (downloadLoading)="downloading($event)"
               class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
               [class.spinner]="
              isDownloading(equipmentSosAnalyzes[0]?.analyzeDocumentNumber)
            ">
            <i class="icon icon-spinner8" *ngIf="
                isDownloading(equipmentSosAnalyzes[0]?.analyzeDocumentNumber)
              "></i>
            <i class="icon icon-download" *ngIf="
                !isDownloading(equipmentSosAnalyzes[0]?.analyzeDocumentNumber)
              "></i>
            <a class="d-none" [download]="equipmentSosAnalyzes[0]?.analyzeDocumentNumber"></a>
          </div>
          <div>
            <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
              {{ "_sos_analyze_pdf_download" | translate }}
            </div>
          </div>
          <div class="ml-auto" *ngIf="
              isDownloading(equipmentSosAnalyzes[0]?.analyzeDocumentNumber) &&
              cancelDonwloadVersion
            " (click)="
              cancelDownloadFile(equipmentSosAnalyzes[0]?.analyzeDocumentNumber)
            ">
            <i class="icon icon-x"></i>
          </div>
        </div>
        <!-- sosAnalyzes pdf download end -->
      </div>
      <div class=" z-index-1 border-bottom p-3"></div>
    </div>
    <!-- ? Equipment MDA Pm Planner -->
    <div [hasPermission]="PermissionEnum.DocumentsMdaPlan" class="pl-3 pr-3 pt-4" *ngIf="equipmentMDAPmPlanner?.length">
      <div class="z-index-1">
        <div class="service-history-item-color">
        </div>
        <div class="mb-3 mt-2 mx-0" style="display: grid; grid-template-columns: auto auto;">
          <div class="font-weight-bold col px-0">
            {{ "_equipment_mda_pm_planner" | translate }}
          </div>
          <div (click)="openMDAPmPlanner()" catUserClick [section]="'MDA_PM_PLANNER'"
               [subsection]="'MDA_PM_PLANNER_DETAIL'"
               [data]="{ equipmentNumber: equipmentDetail?.equipmentNumber }"
               class="btn btn-link font-weight-semi-bold p-0 text-info small align-self-start d-flex align-items-center justify-content-end"
               *ngIf="equipmentMDAPmPlanner?.length">
            <div class="text-right mr-2">
              {{ "_show_equipment_sos_analyzes" | translate }}
            </div>
            <i class="icon icon-chevron-right"></i>
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col pl-0 mr-2 text-right" [style.color]="
          equipmentMDAPmPlanner[0]?.workOrderStatus === 'closed' ? '#5D8D1C' :
          equipmentMDAPmPlanner[0]?.workOrderStatus === 'estimate' ? '#FFA300' :
          equipmentMDAPmPlanner[0]?.workOrderStatus === 'cancel' ? '#DA3A3C' : '#6c757d'">
            {{ equipmentMDAPmPlanner[0]?.workOrderStatusText }}
          </div>
        </div>
        <div class="row font-size-14px text-secondary mb-2">
          <div class="col-6 font-weight-semi-bold">
            {{ "_contract_number" | translate }}
          </div>
          <div class="col-6 pl-0">
            :
            {{ equipmentMDAPmPlanner[0]?.contractNumber }}
          </div>
        </div>
        <div *ngIf="equipmentMDAPmPlanner[0]?.workOrderStartDate !== '0000-00-00'"
             class="row font-size-14px text-secondary mb-2">
          <div class="col-6 font-weight-semi-bold">
            {{ "_start_date" | translate }}
          </div>
          <div class="col-6 pl-0">
            :
            {{ equipmentMDAPmPlanner[0]?.workOrderStartDate | date: "dd.MM.yyyy" }}
          </div>
        </div>
        <div *ngIf="equipmentMDAPmPlanner[0]?.workOrderPlanDate !== '0000-00-00'"
             class="row font-size-14px text-secondary mb-2">
          <div class="col-6 font-weight-semi-bold">
            {{ "_end_date" | translate }}
          </div>
          <div class="col-6 pl-0">
            :
            {{ equipmentMDAPmPlanner[0]?.workOrderPlanDate | date: "dd.MM.yyyy" }}
          </div>
        </div>
      </div>
      <div class=" z-index-1 border-bottom p-3"></div>
    </div>
    <!-- ? Eğer Data Yoksa Bu Blok Gösterilmeye Başlar -->
    <div [hasPermission]="PermissionEnum.DocumentsServiceHistory" class="pl-3 pr-3 pt-4"
         *ngIf="!equipmentServiceHistory?.length">
      <div class="z-index-1">
        <div class="d-flex justify-content-between mb-3 mt-2">
          <div class="font-weight-bold">
            {{ "_service_information" | translate }}
          </div>
          <div class="pse-skeleton-loader" style="width: 125px;" *ngIf="(equipmentServiceHistoryLoading$ | async)">
            <cat-skeleton-loader></cat-skeleton-loader>
          </div>
          <div class="btn font-weight-semi-bold text-secondary p-0 small text-right col-6"
               *ngIf="(!equipmentServiceHistory?.length) && !(equipmentServiceHistoryLoading$ | async)">
            {{ "_no_service_history" | translate }}
          </div>
        </div>
      </div>
    </div>
    <div [hasPermission]="PermissionEnum.DocumentsCatInspections" class="pl-3 pr-3 pt-4"
         *ngIf="!equipmentInspectionResults?.length">
      <div class="z-index-1">
        <div class="d-flex justify-content-between mb-3 mt-2">
          <div class="font-weight-bold">
            {{ "_inspection_results" | translate }}
          </div>
          <div class="pse-skeleton-loader" style="width: 125px;" *ngIf="(inspectionResultsLoading$ | async)">
            <cat-skeleton-loader></cat-skeleton-loader>
          </div>
          <div class="btn font-weight-semi-bold text-secondary p-0 small text-right col-6"
               *ngIf="(!equipmentInspectionResults?.length) && !(inspectionResultsLoading$ | async)">
            {{ "_no_inspection_results" | translate }}
          </div>
        </div>
      </div>
    </div>
    <div [hasPermission]="PermissionEnum.EquipmentDetailSpecialDeals" class="pl-3 pr-3 pt-4 z-index-1"
         *ngIf="!equipmentQuotations?.length && showQuotationSection">
      <div class="z-index-1">
        <div class="d-flex justify-content-between mb-3 mt-2">
          <div class="font-weight-bold">
            {{ "_equiment_quotations" | translate }}
          </div>
          <div class="pse-skeleton-loader" style="width: 125px;"
               *ngIf="showQuotationSection && (equipmentQuotationsLoading$ | async)">
            <cat-skeleton-loader></cat-skeleton-loader>
          </div>
          <div class="btn font-weight-semi-bold text-secondary p-0 small text-right"
               *ngIf="(!equipmentQuotations?.length) && !(equipmentQuotationsLoading$ | async)">
            {{ "_no_equiment_quotations" | translate }}
          </div>
        </div>
      </div>
    </div>
    <div [hasPermission]="PermissionEnum.DocumentsSosAnalyzes" class="pl-3 pr-3 pt-4"
         *ngIf="!equipmentSosAnalyzes?.length">
      <div class="z-index-1">
        <div class="d-flex justify-content-between mb-3 mt-2">
          <div class="font-weight-bold">
            {{ "_equipment_sos_analyzes" | translate }}
          </div>
          <div class="pse-skeleton-loader" style="width: 125px;" *ngIf="(equipmentSosAnalyzesLoading$ | async)">
            <cat-skeleton-loader></cat-skeleton-loader>
          </div>
          <div class="btn font-weight-semi-bold text-secondary p-0 small text-right col-6"
               *ngIf="(!equipmentSosAnalyzes?.length) && !(equipmentSosAnalyzesLoading$ | async)">
            {{ "_no_equipment_sos_analyzes" | translate }}
          </div>
        </div>
      </div>
    </div>
    <div [hasPermission]="PermissionEnum.DocumentsMdaPlan" class="pl-3 pr-3 pt-4"
         *ngIf="!equipmentMDAPmPlanner?.length">
      <div class="z-index-1">
        <div class="service-history-item-color">
        </div>
        <div class="d-flex justify-content-between mb-3 mt-2">
          <div class="font-weight-bold">
            {{ "_equipment_mda_pm_planner" | translate }}
          </div>
          <div class="pse-skeleton-loader" style="width: 125px;" *ngIf="(EquipmentMDAPmPlannerLoading$ | async)">
            <cat-skeleton-loader></cat-skeleton-loader>
          </div>
          <div class="btn font-weight-semi-bold text-secondary p-0 small text-right"
               *ngIf="(!equipmentMDAPmPlanner?.length) && !(EquipmentMDAPmPlannerLoading$ | async)">
            {{ "_no_equipment_mda_pm_planner" | translate }}
          </div>
        </div>
      </div>
    </div>
    <div class="py-3"></div>
  </div>
  <!-- ? Equipment Service History Modal -->
  <div *ngIf="serviceHistoryStatus" [@serviceHistory] class="service-history">
    <div class="pr-3 pl-3 pt-4">
      <div class="h4 nav-back mb-4">
        <i (click)="onCloseServiceHistory()" class="icon icon-back mr-2"></i>
        {{ "_service_history" | translate }}
      </div>
      <ng-container *ngIf="equipmentServiceHistory?.length">
        <div *ngFor="let serviceHistoryItem of equipmentServiceHistory">
          <div class="mb-3 service-history-item overflow-hidden">
            <div [style.background-color]="serviceHistoryItem?.serviceStatusColor"
                 class="service-history-item-color"></div>
            <div class="p-4">
              <div class="d-flex justify-content-between mb-2 overflow-hidden">
                <div class="font-weight-semi-bold h6">
                  {{ equipmentDetail.model }}
                </div>
                <div [style.color]="serviceHistoryItem?.serviceStatusColor" class="font-weight-semi-bold">
                  {{ this.getStatusLabel(serviceHistoryItem) | translate }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_machine_serial_number" | translate }}
                </div>
                <div class="col-7 two-dots break-word">
                  {{ equipmentDetail?.serialNumber | serialFormat }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_start_date" | translate }}
                </div>
                <div class="col-7 two-dots break-word">
                  {{ serviceHistoryItem.startDate | date: "dd.MM.yyyy" }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_service_number" | translate }}
                </div>
                <div class="col-7 two-dots break-word">
                  {{ serviceHistoryItem.serviceNumber }}
                </div>
              </div>
              <!-- <div class="row no-gutters">
                <div class="col-5">{{ "_service_type" | translate }}</div>
                <div class="col-7 two-dots">
                  {{ serviceHistoryItem.serviceType }}
                </div>
              </div> -->
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_description" | translate }}
                </div>
                <div class="col-7 two-dots break-word">
                  {{ serviceHistoryItem.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <!-- ? Cat Inspect Results -->
  <div class="service-history" *ngIf="inspectResults" [@serviceHistory]>
    <div class="pr-3 pl-3 pt-4">
      <div class="h4 nav-back mb-4">
        <i (click)="onCloseInspectResults()" class="icon icon-back mr-2"></i>
        {{ "_inspection_results" | translate }}
      </div>
      <ng-container *ngIf="equipmentInspectionResults?.length">
        <div *ngFor="let equipmentInspection of equipmentInspectionResults">
          <div class="mb-3 service-history-item overflow-hidden font-size-13px">
            <div style="background-color: #FFA300;" class="service-history-item-color"></div>
            <div class="px-4 pt-4 pb-2">
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_inspection_number" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentInspection?.inspectionNumber | serialFormat }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_inspection_type" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentInspection?.typeName }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_inspection_name" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentInspection?.formName }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_model" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentInspection?.model }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_general_information" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentInspection?.overallResponse }}
                </div>
              </div>
            </div>
            <div class="row no-gutters d-flex align-items-center w-95 pt-2 pb-4 pl-4"
                 *ngIf="equipmentInspection && catInspectionDownloadButtons">
              <div class="row m-0 align-items-center" [id]="equipmentInspection?.inspectionNumber" catUserClick
                   [section]="'CAT_INSPECTION'" [subsection]="'PDF_DOWNLOAD'"
                   [data]="{
                  dCode:
                  equipmentInspection?.dCode
                }" catDownloadFile
                   [downloadType]="DownloadTypeEnum.catInspection"
                   [haveDownload]="haveDownload"
                   [downloadParams]="{
                  dCode: equipmentInspection?.dCode,
                  inspectionNumber: equipmentInspection.inspectionNumber
                }" (downloadLoading)="downloading($event)"
                   class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                   [class.spinner]="
                  isDownloading(equipmentInspection?.inspectionNumber)
                ">
                <i class="icon icon-spinner8" *ngIf="
                    isDownloading(equipmentInspection?.inspectionNumber)
                  "></i>
                <i class="icon icon-download" *ngIf="
                    !isDownloading(equipmentInspection?.inspectionNumber)
                  "></i>
                <a class="d-none" [download]="equipmentInspection?.inspectionNumber"></a>
              </div>
              <div>
                <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                  {{ "_cat_inspection_pdf_download" | translate }}
                </div>
              </div>
              <div class="ml-auto" *ngIf="
                  isDownloading(equipmentInspection?.inspectionNumber) &&
                  cancelDonwloadVersion
                " (click)="
                  cancelDownloadFile(equipmentInspection?.inspectionNumber)
                ">
                <i class="icon icon-x"></i>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <!-- ? Equipment SOS Analayzes History Modal -->
  <div *ngIf="analayzesHistoryStatus" [@serviceHistory] class="service-history">
    <div class="pr-3 pl-3 pt-4">
      <div class="h4 nav-back mb-4">
        <i (click)="onCloseAnalyzesHistory()" class="icon icon-back mr-2"></i>
        {{ "_equipment_sos_analyzes" | translate }}
      </div>
      <ng-container *ngIf="!(equipmentSosAnalyzesLoading$ | async); else sosDetailsLoading">
        <div *ngFor="let sosAnalyzes of equipmentSosAnalyzes">
          <div class="mb-3 service-history-item overflow-hidden">
            <!-- Analyzes Status -->
            <!-- <div
              [ngClass]="{
                'service-history-item-color-green':
                sosAnalyzes.analtzeStatus === 3,
                'service-history-item-color-orange':
                sosAnalyzes.analtzeStatus === 1 ||
                sosAnalyzes.analtzeStatus === 2
              }"
              class="service-history-item-color"
            ></div> -->
            <div class="p-4">
              <div class="d-flex justify-content-between mb-2 overflow-hidden">
                <div class="font-weight-semi-bold h6">
                  {{ equipmentDetail.model }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_machine_serial_number" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ equipmentDetail?.serialNumber | serialFormat }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_equipment_analyzes_date" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ sosAnalyzes.analyzeDate | date: "dd.MM.yyyy" }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_analyzes_document_number" | translate }}
                </div>
                <div class="col-7 two-dots">
                  {{ sosAnalyzes.analyzeDocumentNumber }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-5">
                  {{ "_compartment" | translate }}
                </div>
                <span class="two-dots"></span>
                <div class="col-5">
                  {{ sosAnalyzes.compDescription }}
                </div>
              </div>
              <!-- sosAnalyzes pdf download start -->
              <div class="row no-gutters d-flex align-items-center py-2" *ngIf="equipmentSosAnalyzes">
                <div class="row m-0 align-items-center" [id]="sosAnalyzes?.analyzeDocumentNumber" catUserClick
                     [section]="'SOS_ANALYZES'"
                     [subsection]="'PDF_DOWNLOAD'" [data]="{
                    analyzeDocumentNumber: sosAnalyzes?.analyzeDocumentNumber
                  }"
                     catDownloadFile
                     [downloadType]="DownloadTypeEnum.sosAnalyzePdf"
                     [haveDownload]="haveDownload"
                     [downloadParams]="{
                    serialNumber: equipmentDetail?.serialNumber,
                    analyzeDocumentNumber: sosAnalyzes?.analyzeDocumentNumber
                  }"
                     (downloadLoading)="downloading($event)">
                  <div
                    class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                    [class.spinner]="
                      isDownloading(sosAnalyzes?.analyzeDocumentNumber)
                    ">
                    <i class="icon icon-spinner8" *ngIf="isDownloading(sosAnalyzes?.analyzeDocumentNumber)"></i>
                    <i class="icon icon-download" *ngIf="!isDownloading(sosAnalyzes?.analyzeDocumentNumber)"></i>
                    <a class="d-none" [download]="sosAnalyzes?.analyzeDocumentNumber"></a>
                  </div>
                  <div>
                    <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                      {{ "_sos_analyze_pdf_download" | translate }}
                    </div>
                  </div>
                </div>
                <div class="ml-auto" *ngIf="
                    isDownloading(sosAnalyzes?.analyzeDocumentNumber) &&
                    cancelDonwloadVersion
                  " (click)="
                    cancelDownloadFile(sosAnalyzes?.analyzeDocumentNumber)
                  ">
                  <i class="icon icon-x"></i>
                </div>
              </div>
              <!-- sosAnalyzes pdf download end -->
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #sosDetailsLoading>
        <div [class.small-spin]="equipmentSosAnalyzesLoading$ | async">
          <ngx-loading [show]="true" [config]="{ fullScreenBackdrop: false }"></ngx-loading>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- ? Equipment MDA Pm Planner Modal -->
  <div *ngIf="mdaPmPlannerStatus" [@serviceHistory] class="service-history">
    <div class="pr-3 pl-3 pt-4">
      <div class="h4 nav-back mb-4">
        <i (click)="onCloseMDAPmPlanner()" class="icon icon-back mr-2"></i>
        {{ "_equipment_mda_pm_planner" | translate }}
      </div>
      <ng-container *ngIf="equipmentMDAPmPlanner$ | async; else mdaPmPlannerLoading">
        <div *ngFor="let pmPlanner of equipmentMDAPmPlanner">
          <div class="mb-3 service-history-item overflow-hidden">
            <div [style.background-color]="
              pmPlanner?.workOrderStatus === 'estimate' ? '#FFA300' :
              pmPlanner?.workOrderStatus === 'closed' ? '#5D8D1C' :
              pmPlanner?.workOrderStatus === 'cancel' ? '#DA3A3C' : '#6c757d'" class="service-history-item-color"></div>
            <div class="p-4">
              <div class="d-flex flex-row flex-nowrap justify-content-between">
                <div class="col-5">
                  {{ pmPlanner.order }}
                </div>
                <div *ngIf="pmPlanner.isNextProcess" class="col-7 text-right">
                  {{ "_next_process" | translate }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-7">
                  {{ "_work_order_number" | translate }}
                </div>
                <div class="col-5 two-dots">
                  {{ pmPlanner.workOrderNumber }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-7">
                  {{ "_status" | translate }}
                </div>
                <div class="col-5 two-dots">
                  {{ pmPlanner.workOrderStatusText }}
                </div>
              </div>
              <div class="row no-gutters">
                <div class="col-7">
                  {{ "_planned_work_order_hours" | translate }}
                </div>
                <div class="col-5 two-dots">
                  {{ pmPlanner.plannedHour }}
                </div>
              </div>
              <div *ngIf="pmPlanner.workOrderPlanDate !== '0000-00-00'" class="row no-gutters">
                <div class="col-7">
                  {{ "_planned_date" | translate }}
                </div>
                <div class="col-5 two-dots">
                  {{ pmPlanner.workOrderPlanDate }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #mdaPmPlannerLoading>
        <div [class.small-spin]="equipmentMDAPmPlanner$ | async">
          <ngx-loading [show]="true" [config]="{ fullScreenBackdrop: false }"></ngx-loading>
        </div>
      </ng-template>
    </div>
  </div>
  <!-- Videocall Agent Modal -->
  <cat-basic-modal [(status)]="videocallAvailableModal" [backdropClose]="true" [showCloseButton]="true"
                   (statusChange)="closeVideocallModal()">
    <div class="d-flex flex-column align-items-center justify-content-center pb-3">
      <h4 class="text-warning">
        {{ '_warning' | translate }}
      </h4>
      <p class="px-4 mb-4 text-center">
        {{ '_videocall_not_have_available_text' | translate }}
      </p>
      <div (click)="closeVideocallModal()"
           class="modal-btn btn btn-warning btn-gradient btn-block text-white shadow mb-3 px-4 ng-tns-c178-2">
        {{ '_close' | translate }}
      </div>
    </div>
  </cat-basic-modal>
  <!-- ? Equipment Remove Modal -->
  <cat-basic-modal *ngIf="removeModal" [(status)]="removeModal" [headerText]="'_remove_from_inventory' | translate">
    <div class="mb-3">
      <div>
        {{ "_remove_inventory_text" | translate }}
      </div>

      <div class="mx-auto text-center mt-4">
        <button catUserClick [section]="'EQUIPMENT'" [subsection]="'CREATE_REMOVE_REQUEST'"
                [data]="{ EquipmentSerialNumber: equipmentDetail.serialNumber }"
                class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow"
                (click)="removeEquipment(equipmentDetail)"
                [disabled]="this.removeModalLoading || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitDeleteEquipmentForm) >= 0)">
          {{ "_remove_equipment" | translate }}
        </button>
      </div>
    </div>
  </cat-basic-modal>

  <cat-basic-modal *ngIf="pSECampaignModal" [headerText]="'_pse_title'" [classItems]="ladyBirdIcon"
                   [(status)]="pSECampaignModal" (statusChange)="closePSECampaignModal()">
    <cat-lucky-days [requestId]="pseRequestId" (errorPse)="closePSECampaignModal()"></cat-lucky-days>
  </cat-basic-modal>

  <cat-basic-modal *ngIf="cmPSEModal" [headerText]="'_cm_pse_title'" [classItems]="cmPseIcon" [(status)]="cmPSEModal"
                   (statusChange)="closePSECampaignModal()">
    <cat-cm-pse [equipmentDetail]="equipmentDetail" (statusChange)="cmPSEModal = $event"></cat-cm-pse>
  </cat-basic-modal>


  <cat-basic-modal *ngIf="equipmentWorkOrder && equipmentWorkOrder.length > 0" [(status)]="workOrderPlanPopup"
                   [headerText]="'_work_plan_title' | translate">

    <div class="mb-3 work-plan-popup">
      <div class="d-flex justify-content-between align-items-center card m-2 p-2">
        <div class="offer-text">
          <div class="mr-2 pr-1 d-flex flex-fill text-wrap">
            <div class="mb-1 text-nowrap">
              {{ "_work_plan_start" | translate }}
            </div>
            <span class="mr-2 pr-1">:</span>{{ equipmentWorkOrder[0].startDate }}
          </div>
          <div class="mr-2 pr-1 d-flex flex-fill">
            <div class="mb-1"></div>
            {{ "_work_plan_finish" | translate }}
            <span class="mr-2 pr-1">:</span>{{ equipmentWorkOrder[0].endDate }}
          </div>
          <div class="mr-2 pr-1 d-flex flex-fill">
            <div class="mb-1"></div>
            {{ "_work_plan_status" | translate }}
            <span class="mr-2 pr-1">:</span>{{ equipmentWorkOrder[0].statusDescription }}
          </div>
        </div>
      </div>
    </div>
  </cat-basic-modal>


  <cat-loader *ngIf="this.removeModalLoading" [show]="removeModalLoading"></cat-loader>
</div>

<!-- ? Equipment Repair Modal -->
<cat-repair-modal (closeModal)="repairStatus = false" source="Muneccim" [sourceRoot]="sourceRoot"
                  [equipment]="equipment" [repairStatus]="repairStatus"
                  (connectionModal)="connectionModalStatus = $event"></cat-repair-modal>
<cat-connect-dialog [status]="connectionModalStatus" [backdropClose]="false"></cat-connect-dialog>
<cat-loader [show]="(equipmentDetailLoading$ | async) || videocallLoading"></cat-loader>

<!-- Boom Guru Submit Modal -->
<cat-basic-modal [(status)]="showBoomGuruSubmitModal">
  <cat-boom-guru-submit
    *ngIf="showBoomGuruSubmitModal"
    [showCancel]="true"
    [serialNumber]="equipment?.serialNumber"
    (close)="closeBoomGuruSubmitModal()"
    (success)="onBoomGuruSubmitSuccess()">
  </cat-boom-guru-submit>
</cat-basic-modal>
