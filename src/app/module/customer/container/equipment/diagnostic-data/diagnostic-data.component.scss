@import "variable/bootstrap-variable";

.fault-list::ng-deep ngb-accordion .card {
  border-radius: 0px !important;
  border: 1px solid rgba(0, 0, 0, 0.041);
  margin: 0 auto;
}

.fault-list::ng-deep ngb-accordion .card-body {
  padding: 0.7em;
}

.fault-list::ng-deep ngb-accordion .card-header {
  border-radius: 0;
  padding: 0;
  background-color: #f9f9f9;
  border: none;

  .btn {
    width: 100%;
    float: left;
    text-align: left !important;
    box-shadow: none;
    border-radius: 0;
    //border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);

  }

  .btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
  }


  button:not(.collapsed) {
    font-weight: 600;
    //background-color: #F5F4F4;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.075);

    //color: #4a8eb0;

    .icon-chevron-down {
      transform: rotate(-180deg);
    }
  }

  .icon-chevron-down {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }

}

.fault-list::ng-deep ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}


.fault-list ngb-accordion:first-child .card {
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.fault-list {
  overflow: auto;
  height: 100%;
}

.custom-card-text {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-card-body {
  background-color: #f3f3f3;
  border-radius: 8px !important;
}

.custom-card {
  border: 0 !important;
  padding: 1px !important;
  height: 60px;
  width: max-content;
  font-size: min(max(3vw, 12px), 15px);
  margin: 0 auto;
}
.fault-list-history{
  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 58px;
      left: 0;
      top: 0;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }
      &-red {
        background: $danger;
      }
    }
  }
}
.service-person {
  margin: 0;
  padding: 0;
  list-style: none;
  border-bottom: 1px solid #dbdbdb;

  &-item {
    border-top: 1px solid #dbdbdb;
    padding: 1.25rem;

    &-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #505050;
    }

    &-detail {
      margin: 0;
      padding: 0;
      list-style: none;

      &-item {
        overflow-wrap: anywhere;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: #505050;
        margin-top: 1rem;

        .icon-area {
          margin-right: 1rem;
          border-radius: 50%;
          background-color: #ebebeb;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0.625rem;
        }
      }
    }
  }
}

.action-button {
  padding: 0.3rem 1rem;
}

.pssr-title {
  font-weight: normal;
  font-size: 11px;
}
.break-word{
  word-break: break-all;
}

.phone-btn {
  width: calc(50% - 65px);
  //padding-top: 9px;
  //padding-bottom: 9px;
  border-radius: 6px;
  background-color: #ffa300;
}

.phone-button-text {
  line-height: 30px;
  color: white;
  text-align: center;
  //border: 1px solid green;
  padding: 0;

  p {
    margin: 0;
    //font-size: 11px;
    font-size: min(max(2vw, 11px), 14px);
    overflow-wrap: anywhere;
    line-height: 1em;
    display: inline-block;
    vertical-align: middle;
  }

  i {
    font-size: 18px;
  }
}

