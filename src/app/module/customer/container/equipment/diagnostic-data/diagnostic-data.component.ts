import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, Host<PERSON><PERSON>ener, ViewChild, ElementRef, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { EquipmentState } from '../../../state/equipment.state';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { DiagnosticData, EquipmentModel } from '../../../model/equipment.model';
import { takeUntil } from 'rxjs/operators';
import { PagingModel } from 'src/app/module/definition/model/paging.model';
import { GetEquipmentDiagnosticDataPagingAction } from '../../../action/equipment.action';
import { Customer<PERSON>odel, PssrList } from '../../../model/customer.model';
import { CustomerState } from '../../../state/customer.state';
import { LogService } from '../../../service/log.service';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { CustomerDetailAction } from '../../../action/customer.actions';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-diagnostic-data',
  templateUrl: './diagnostic-data.component.html',
  styleUrls: ['./diagnostic-data.component.scss']
})
export class DiagnosticDataComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentDiagDataLoading)
  equipmentDiagnosticDataLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentDiagDataList)
  equipmentDiagnosticDataList$: Observable<any>;

  @Select(EquipmentState.equipmentDiagDataPaging)
  equipmentDiagnosticDataPaging$: Observable<PagingModel>;

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  PermissionEnum = PermissionEnum;


  @ViewChild('list') listElement: ElementRef;
  equipmentNumber: any;
  equipmentDiagnosticDataLoading: boolean;
  equipmentDiagnosticDataList: any;
  equipmentDiagnosticData: DiagnosticData[];
  paging: PagingModel;
  private page = 1;
  equipment: any;
  currentCustomer: CustomerRelationModel;
  serialNumber: string;
  source: any;
  contactModal = false;
  faultHistory: boolean;
  pssrList: PssrList[] | any = [];
  customerNumber: string;
  isSparePartModule: boolean;
  constructor(
    private readonly logger: LogService,
    private readonly elementRef: ElementRef,
    private readonly store: Store,
    private readonly router: Router,
    private readonly frameService: MessageFrameService,
    private readonly ref: ChangeDetectorRef,
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);
    if (this.currentCustomer?.customer) {
      setTimeout(() => {
        this.store.dispatch(
          new CustomerDetailAction(this.currentCustomer.customer?.customerNumber)
        );
      }, 0);
      this.customerNumber = this.currentCustomer.customer?.customerNumber;
      // this.isSparePartModule = !!this.currentCustomer?.roleList?.filter(x => x === 'ManageSpareParts')?.length;
    }

    this.equipmentDetail$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((equipment) => {
      if (equipment) {
        this.equipment = equipment;
        this.serialNumber = equipment?.serialNumber;
        this.loadData(this.equipment.equipmentNumber, this.serialNumber);
      }
    });


    this.equipmentDiagnosticDataLoading$.subscribe(i => (this.equipmentDiagnosticDataLoading = i));
    this.equipmentDiagnosticDataPaging$
    .subscribe(paging => {
      if (paging) {
        this.paging = paging;
        this.page = this.paging?.pageNumber;
        setTimeout(i => {
          if (this.listElement && this.listElement?.nativeElement.clientHeight && window.innerHeight > this.listElement?.nativeElement.clientHeight) {
            this.nextPage();
          }
        });
        this.equipmentDiagnosticDataList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe(e => {
            this.equipmentDiagnosticDataList = e;
          });
      }
    });
    this.customer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customer => {
        if (customer) {
          this.pssrList = customer.details?.pssrList.filter(pssr => {
            if (pssr.titles.find(data => data === 'PSSR')) {
              return pssr.mailList.length || pssr.telephoneList.length;
            }
            return false;
          });
        }
      });

  }

  @HostListener('window:scroll', ['$event'])
  onScrollEvent($event) {
    const pos = (document.documentElement.scrollTop || document.body.scrollTop) + document.documentElement.clientHeight;
    const max = document.documentElement.scrollHeight;

    if (pos + 100 >= max && !this.equipmentDiagnosticDataLoading) {
      this.nextPage();
    }
  }

  loadData(equipmentNumber, serialNumber) {
    this.store.dispatch(new GetEquipmentDiagnosticDataPagingAction(equipmentNumber, serialNumber, 13, this.page));
  }

  private nextPage() {
    if (this.paging?.pageNumber * this.paging?.pageSize < this.paging?.totalCount) {
      this.page++;
      this.loadData(this.equipment.equipmentNumber, this.serialNumber);
    }
  }
  openSparePartOrderDetail() {
    this.frameService.sendMessage(FrameMessageEnum.openSparePart, {
      user: this.currentCustomer,
    });
    return;
  }

  requestService() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-service',
        this.equipment?.equipmentNumber,
      ], {
        queryParams: {
          source: this.source || 'Equipment',
          sourceRoot: 'Equipment'
        }
      })
      .then();
  }
  onCloseFaultHistory() {
    history.back();
  }



  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
  ngAfterContentChecked(): void {
    this.ref.detectChanges();
  }

  onCall(phone: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }
}
