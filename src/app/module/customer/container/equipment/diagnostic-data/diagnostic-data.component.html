  <div class="service-history" #list>
    <div class="pt-4" >
      <div class="h4 nav-back mb-4 ml-4">
        <i (click)="onCloseFaultHistory()" class="icon icon-back mr-2"></i>
        {{ "_faults" | translate }}
      </div>
      <div *ngIf="equipmentDiagnosticDataList?.length"  >
        <div *ngFor="let item of equipmentDiagnosticDataList;let i=index" class="fault-list" style="min-height: max-content;" >
          <ngb-accordion [closeOthers]="true" >
            <ngb-panel id="id-0">
              <ng-template ngbPanelTitle>
                <div class="fault-list-history-item-color" [ngClass]="{
                  'fault-list-history-item-color-green': item.level == 'Low',
                  'fault-list-history-item-color-orange': item.level == 'Medium',
                  'fault-list-history-item-color-red': item.level == 'High',
                  'bg-secondary': item.level == ''
               }">
                </div>
                <div class="d-flex justify-content-between">
                  <div class="d-flex flex-row w-100 justify-content-between mb-2">
                    <b class="m-auto" style="color: black;">{{item.mid}}-{{item.cId}}-{{item.fmi}}</b>
                  </div>
                  <div class="d-flex"><i class="text-decoration-none icon icon-chevron-down"></i></div>
                </div>
              </ng-template>
              <ng-template ngbPanelContent>
                <div class="answer fit-body" style="width: 100%">
                  <div class="d-flex flex-row mb-2">
                    <div class="col-12"><b>{{item.mid}}-{{item.cId}}-{{item.fmi}}</b></div>
                    <!-- <i class="icon icon-dot3 text-primary"></i> -->
                  </div>
                  <div class="d-flex flex-row mb-2">
                    <div class="col-12">
                      <div>{{item.description}}</div>
                    </div>
                  </div>
                  <div class="d-flex flex-row mb-2" *ngIf="item.level!==''">
                    <div class="col-5">{{ "_priority_level" | translate }}:</div>
                    <div [ngClass]="{
                        'text-success': item.level == 'Low',
                        'text-warning': item.level == 'Medium',
                        'text-danger': item.level == 'High'
                      }">
                      {{item.level==='Low'? ('_low' | translate):item.level==='Medium'?('_medium' | translate):item.level==='High'?('_high' | translate):""}}
                    </div>

                  </div>
                  <div class="d-flex flex-row mb-2">
                    <div class="col-5">{{ "_date" | translate }}:</div>
                    <div> {{ item.date | date: "dd.MM.yyyy" }}</div>
                  </div>
                  <div class="d-flex flex-row mb-2">
                    <div class="col-5">{{ "_working_hours" | translate }}:</div>
                    <div>{{ equipment?.workingHours }}</div>
                  </div>
                  <div class="d-flex">
                    <div class="card custom-card" [hasPermission]="PermissionEnum.RequestsService">
                      <div class="card-body text-center custom-card-body">
                        <!-- <h6 class="card-title"></h6> -->
                        <b class="card-text custom-card-text" (click)="requestService()">
                          {{ "_create_service_request" | translate }}
                        </b>
                      </div>
                    </div>
                    <div class="card custom-card">
                      <div class="card-body text-center custom-card-body">
                        <!-- <h6 class="card-title"></h6> -->
                        <b class="card-text custom-card-text" (click)="contactModal = true">
                          {{ "_contact_with_customer_service" | translate }}
                        </b>
                      </div>
                    </div>
                    <div class="card custom-card" [hasPermission]="PermissionEnum.Ecommerce">
                      <div class="card-body text-center custom-card-body">
                        <!-- <h6 class="card-title"></h6> -->
                        <b class="card-text custom-card-text" (click)="openSparePartOrderDetail()">
                          {{ "_boom_shop" | translate }}
                        </b>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
            </ngb-panel>
          </ngb-accordion>
        </div>
      </div>
    </div>
  </div>
<div>
  <cat-loader [show]=" equipmentDiagnosticDataLoading"></cat-loader>
</div>
<!-- pssr -->
<cat-basic-modal *ngIf="contactModal" [(status)]="contactModal" [headerText]="'_contact_with_customer_service' | translate">
  <div class="mb-3">
    <div *ngIf="pssrList">
      <ul class="service-person">
        <li class="service-person-item" *ngFor="let persons of pssrList">
          <div class="service-person-item-name">
            {{ persons.pssrName }}
          </div>
          <div class="pssr-title">
            <ng-container *ngFor="let title of persons.titles; let i = index">
              {{ "_customer_title_" + title | translate }}
              {{ i !== persons.titles.length - 1 ? "-" : "" }}
            </ng-container>
          </div>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item" *ngFor="let phones of persons.telephoneList"
              (click)="onCall(phones.telephoneNumber)">
              <div class="icon-area">
                <i class="icon icon-phone-call"></i>
              </div>
              {{ phones.telephoneNumberShort || phones.telephoneNumber }}
            </li>
          </ul>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item" *ngFor="let mail of persons.mailList" (click)="onMail(mail.mailAdress)">
              <div class="icon-area">
                <i class="icon icon-contact"></i>
              </div>
              {{ mail.mailAdress }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</cat-basic-modal>
