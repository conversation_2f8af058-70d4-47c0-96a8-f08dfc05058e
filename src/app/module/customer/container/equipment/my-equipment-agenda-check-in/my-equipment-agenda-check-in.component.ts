import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store, Select } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { EquipmentState } from '../../../state/equipment.state';
import { Validators, FormGroup, FormControl } from '@angular/forms';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { Observable, Subject } from 'rxjs';
import { LogService } from '../../../service/log.service';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import {
  GetCheckedInEquipmentsAction,
  GetEquipmentAgendaDetails,
  GetEquipmentAgendaHistory,
} from '../../../action/equipment.action';
import { EquipmentService } from '../../../service/equipment.service';
import { takeUntil } from 'rxjs/operators';
import { CustomerModel } from '../../../model/customer.model';
import { CustomerState } from '../../../state/customer.state';

@Component({
  selector: 'cat-my-equipment-agenda-check-in',
  templateUrl: './my-equipment-agenda-check-in.component.html',
  styleUrls: ['./my-equipment-agenda-check-in.component.scss'],
})
export class MyEquipmentAgendaCheckInComponent implements OnInit {
  @Select(EquipmentState.equipmentAgendaHistory)
  equipmentAgendaHistory$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaDetails)
  equipmentAgendaDetails$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaLoading)
  equipmentAgendaLoading$: Observable<boolean>;
  @Select(EquipmentState.checkedInEquipments)
  checkedInEquipments$: Observable<any[]>;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, [Validators.required, Validators.pattern('^(?!\\s*$).+')]),
    //StartDate: new FormControl(null, [Validators.required]),
  });
  checkInsFilterForm: FormGroup = new FormGroup({
    StartDate: new FormControl(null, [Validators.required]),
  });
  equipmentAgendaDetails: any;
  user: UserModel;
  equipmentAgendaHistory: any;
  filteredCheckIns: any = [];
  checkedInEquipments: any[] = []
  loading = false;
  formSendStatus = false;
  checkInsStatus = 0;
  otherCustomerCheckin = false;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly log: LogService,
    private readonly equipmentService: EquipmentService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((q) => {
      this.checkInsStatus =
        'checkInsStatus' in q ? parseInt(q.checkInsStatus, 2) : 0;
    });

    this.loadForm();

    this.equipmentAgendaHistory$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.equipmentAgendaHistory = data;
        this.filteredCheckIns = data?.checkIns;
    });

    this.equipmentAgendaDetails$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.equipmentAgendaDetails = data;
    });

    this.checkedInEquipments$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.checkedInEquipments = data
    })
    
    this.user = this.store.selectSnapshot(LoginState.user);
  }

  loadForm() {
    this.user = this.store.selectSnapshot(LoginState.user);
    const patchValues: any = {};
    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
    }

    this.form.patchValue(patchValues);
  }

  // filterCheckIns() {
  //   console.log(this.checkInsFilterForm.value);
  //   const { value } = this.checkInsFilterForm

  //   if (value.StartDate) {
  //     const filteredCheckIns = this.equipmentAgendaHistory.checkIns
  //     .filter(checkIn => checkIn?.checkInDate?.substr(0, 10) === value.StartDate || 
  //       checkIn?.checkOutDate?.substr(0, 10) === value.StartDate
  //     )
  //     this.filteredCheckIns = filteredCheckIns 
  //   } else {
  //     this.filteredCheckIns = this.equipmentAgendaHistory.checkIns
  //   }
  // }

  checkInControl(){
    if(!this.form.valid) return validateAllFormFields(this.form);

    if ((!this.user?.isLdapLogin && this.user?.id === this.equipmentAgendaDetails.checkedInUserId) ||
        !this.equipmentAgendaDetails?.hasCheckIn ||
        (this.user?.isLdapLogin && !this.equipmentAgendaDetails?.isBorusanUserHasCheckin)
    ){
      this.otherCustomerCheckin = false;
      this.sendEquipmentAgendaCheckInForm();
    } else {
      this.otherCustomerCheckin = true;
    }
  }

  closeCheckoutModal(){
    this.otherCustomerCheckin = false;
  }

  replaceCheckInText(text, name){
    return text.replace('X', `${name}`);
  }

  sendEquipmentAgendaCheckInForm() {
    console.log(this.form.value);
    const { value } = this.form;

    const formBody = {
      EquipmentAgendaId: this.equipmentAgendaDetails?.equipmentAgendaId,
      SerialNumber: this.equipment?.serialNumber,
      FirstName: value?.Name,
      LastName: value?.Surname,
      UseStartDate: new Date(),
      Description: value.Description,
    };

    this.log
      .action(
        LogSectionEnum.EQUIPMENT,
        'ADD_EQUIPMENT_AGENDA_CHECK_IN',
        formBody
      )
      .subscribe();

    if (this.form.valid) {
      this.loading = true;
      this.equipmentService.equipmentAgendaCreateCheckIn(formBody).subscribe(
        () => {
          this.formSendStatus = true;
          this.form.reset();
          this.loadForm();
          this.loading = false;
          this.otherCustomerCheckin = false;
          this.store.dispatch(new GetEquipmentAgendaHistory(this.equipment?.serialNumber, this.equipment?.equipmentNumber, new Date()));
          this.store.dispatch(new GetCheckedInEquipmentsAction())
          this.store.dispatch(new GetEquipmentAgendaDetails(this.equipment?.serialNumber, this.equipment?.model, this.equipment?.equipmentNumber))},
        () => {
          this.formSendStatus = false;
          this.loading = false;
        }
      );
    } else {
      validateAllFormFields(this.form);
    }
  }

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  navigateEquipmentAgendaCheckIns() {
      this.router
        .navigate(
          [
            '/',
            ...environment.rootUrl.split('/'),
            'form',
            'request-my-equipment-agenda',
            this.equipment?.equipmentNumber,
            'check-in',
          ],
          {
            replaceUrl: true,
            queryParams: { isActiveRoute: 1 } 
          }
        )
        .then();
  }

  navigateCheckInOrOut(){
    if(this.checkedInEquipments.length) return this.navigateMyEquipmentAgendaCheckOut()

    return this.navigateEquipmentAgendaCheckIns()
  }

  navigateMyEquipmentAgendaCheckOut() {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'form',
      'request-my-equipment-agenda',
      this.equipment?.equipmentNumber,
      'check-out',
    ], { replaceUrl: true, queryParams: { isActiveRoute: 1 } })
    .then();
  }

  closeModal() {
    this.navigateMyEquipmentAgendaCheckOut()
    this.formSendStatus = false;
    window.scroll({ top: 0 });
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
