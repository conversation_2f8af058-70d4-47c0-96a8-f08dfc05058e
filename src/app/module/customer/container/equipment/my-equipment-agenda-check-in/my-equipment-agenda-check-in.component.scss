.my-equipment-agenda-check-in{
    height: 100%;
    overflow: auto;
    
    .equipment-checkIn-list{
        width: 100%;
        
        .date-input{
            background-color: white;
            height: 40px;
            flex: 1;
        }

        .customer-info{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .checkIn-warning{
        color: red;
    }

    .equipment-checkIn-btn-container{

        .show-equipment-checkIns{
            background-color: white;
            border-radius: 24px;
            width: 40px;
            height: 22px;
            border: 1px solid #dddddd;
    
            &.btn-equipment-checkIns{
                background-color: #4A8EB0;
                color: white;
            }
        }
    }


    .form-check-in{
        textarea, .date-input{
            background-color: #EEF2F4;
        }

        .name-input, .surname-input{
            background-color: #EEF2F4;
        }

    }
}
.form-preview, .after-form-send {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #FAFAFA;
  }
  
  .after-form-send-content {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100vw;
    transform: translateY(-50%);
  }
  .success-message{
    font-size: 26px;
    font-weight: 700;
  }
  .icon-message-success {
    font-size: 60px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }