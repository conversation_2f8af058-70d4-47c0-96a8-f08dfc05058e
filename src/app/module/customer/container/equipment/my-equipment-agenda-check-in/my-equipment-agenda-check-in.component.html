<div class="my-equipment-agenda-check-in px-4 pb-4">
  <div class="mb-2 d-flex align-items-center equipment-checkIn-btn-container">
    <!-- <button
      (click)="navigateCheckInOrOut()"
      class="btn btn-sm show-equipment-checkIns d-flex align-items-center justify-content-center ml-auto"
      [ngClass]="{ 'btn-equipment-checkIns': checkInsStatus }"
    >
      <i class="icon icon-clock"></i>
    </button> -->
  </div>

  <div class="h5 mb-3 pb-2 border-bottom">
    {{ '_check_in_procedures' | translate }}
  </div>

  <!-- <div class="equipment-checkIn-list" *ngIf="checkInsStatus">
    <form
      [formGroup]="checkInsFilterForm"
      (submit)="filterCheckIns()"
      class="d-flex align-items-center"
    >
      <div class="form-group mb-0 flex-grow-1 mr-1 position-relative">
        <input
          type="date"
          class="form-control mb-2"
          name="StartDate"
          id="StartDate"
          formControlName="StartDate"
          class="form-control date-input"
        />
      </div>
      <button type="submit" class=" btn btn-info btn-sm btn-gradient text-white shadow rounded-lg">
        {{ '_list' | translate }}
      </button>
    </form>
    <div *ngFor="let detail of filteredCheckIns" class="list-element border-bottom">
      <div class="list-group my-3">
        <div class="h5 mb-3 mb-2">
          {{ '_equipment_user' | translate }}
        </div>
        <div class="row">
          <div class="h6 col">
            {{ '_name' | translate }}
          </div>
          <span class="col customer-info">: {{detail.firstName}}</span>
        </div>
        <div class="row">
          <div class="h6 col">
            {{ '_surname' | translate }}
          </div>
          <span class="col customer-info">: {{detail.lastName}}</span>
        </div>
      </div>
      <div class="list-group mt-3">
        <div class="h6 mb-3">
          {{ '_equipment_usage_detail' | translate }}
        </div>
        <div class="row">
          <div class="font-size-14px font-weight-bold col">
            {{ '_equipment_usage_start_date' | translate }}
          </div>
          <span class="col h6">: {{detail.checkInDate | date: 'yyyy-MM-dd'}}</span>
        </div>
        <div class="row">
          <div class="font-size-14px font-weight-bold col">
            {{ '_equipment_usage_expire_date' | translate }}
          </div>
          <span class="col h6">: {{detail.checkOutDate | date: 'yyyy-MM-dd'}} </span>
        </div>
      </div>
      <div class="list-group mt-3">
        <div class="h5 mb-3">
          {{ '_description' | translate }}
        </div>
        <p>
          {{detail.checkInDescription}}
        </p>
      </div>
    </div>
  </div> -->

  <form
    class="form-check-in"
    (submit)="checkInControl()"
    [formGroup]="form"
  >
    <div class="form-group">
      <div class="h5">{{ "_equipment_user" | translate }}</div>
      <input
        catInputLength
        [name]="'Name'"
        [placeholder]="'_name' | translate"
        class="form-control name-input"
        formControlName="Name"
        type="text"
        minlength="3"
        maxlength="30"
        readonly
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Surname'"
        [placeholder]="'_surname' | translate"
        class="form-control surname-input"
        formControlName="Surname"
        type="text"
        minlength="2"
        maxlength="30"
        readonly
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div>

    <!-- <div class="h5">
      {{ "_equipment_usage_start_date" | translate }}
    </div>
    <div class="form-group">
      <input
        type="date"
        class="form-control mb-2"
        name="StartDate"
        id="StartDate"
        formControlName="StartDate"
        class="form-control date-input"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.StartDate) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.StartDate) | translate }}
      </div>
    </div> -->

    <div class="form-group">
      <div class="h6">
        {{ "_description" | translate }}
      </div>
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        class="form-control"
        formControlName="Description"
        [rows]="3"
        type="text"
        minlength="2"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <button type="submit" [disabled]="checkedInEquipments.length" class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg">
      {{ '_check_in' | translate }}
    </button>
    <div class="checkIn-warning" *ngIf="checkedInEquipments.length">
      {{ '_checkIn_warning' | translate }}
    </div>
  </form>
</div>
<cat-loader [show]="loading || (equipmentAgendaLoading$ | async)"></cat-loader>
<div *ngIf="formSendStatus" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_success_check_in_message" | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="closeModal()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>

<cat-basic-modal [(status)]="otherCustomerCheckin">
  <div class="p-2 text-center mb-2">
    {{ replaceCheckInText(("_check_out_info" | translate), equipmentAgendaDetails?.shortName)}}
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button (click)="closeCheckoutModal()" class="col btn btn-sm btn-secondary text-white mr-3">
      {{ '_cancel' | translate }}
    </button>
    <button (click)="sendEquipmentAgendaCheckInForm()" class="col btn btn-sm btn-warning text-white">
      {{ '_confirm' | translate }}
    </button>
  </div>
</cat-basic-modal>
