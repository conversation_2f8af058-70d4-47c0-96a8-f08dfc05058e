import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MyEquipmentAgendaCheckInComponent } from './my-equipment-agenda-check-in.component';

describe('MyEquipmentAgendaCheckInComponent', () => {
  let component: MyEquipmentAgendaCheckInComponent;
  let fixture: ComponentFixture<MyEquipmentAgendaCheckInComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MyEquipmentAgendaCheckInComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MyEquipmentAgendaCheckInComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
