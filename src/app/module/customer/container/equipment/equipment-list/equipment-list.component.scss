@import "variable/bootstrap-variable";

.search-area {
  input {
    padding-left: 47px;
  }

  i {
    position: absolute;
    left: 16px;
    //top: 50%;
    top: 26px;
    font-size: 18px;
    line-height: 18px;
    height: 18px;
    margin-top: -9px;
  }
}

.equipment-count {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  color: #505050;
  min-width: 50px;
}

.filter-button {
  border: 0;
  background: #e4e8eb;
  color: #4a8eb0;
  font-weight: 600;
  width: 50%;
  font-size: 13px;
  line-height: 1.2rem;
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;

  &-first {
    border-radius: 4px 0 0 4px;
  }

  &-last {
    border-radius: 0 4px 4px 0;
  }
}

.search-add {
  padding-top: 9px;
  font-size: 24px;
  font-weight: 300;
  line-height: 2.4rem;
}

.search-input:focus {
  box-shadow: none;
}

// .repair-icon {
  // position: absolute;
  // top: 0;
  // left: 0;
  // z-index: 2;
// }

.product-link {
  color: #737373;
}

:host ::ng-deep .tooltip .tooltip-inner {
  background-color: #444444;
}

:host ::ng-deep .tooltip .arrow::before {
  border-left-color: #444444;
}

.add-equipment {
  text-align: center;
  color: #505050;
}

.max-width-180px {
  max-width: 180px;
}

.text-yellow{
  color: var(--yellow);
}

.mini-loading-area {
  height: 55px;
  width: 100%;
}

.ladyBirdIcon{
  height: 15px;
}
