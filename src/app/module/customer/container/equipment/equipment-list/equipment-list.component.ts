import { animate, state, style, transition, trigger } from '@angular/animations';
import { AfterContentChecked, AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Select, Store } from '@ngxs/store';
import { fromEvent, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, take, takeUntil, tap } from 'rxjs/operators';
import { ImageSizeEnum } from 'src/app/export/media/enum/image-size.enum';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { PagingModel } from 'src/app/module/definition/model/paging.model';
import { SettingsService } from 'src/app/module/shared/service/settings.service';
import { AppState } from 'src/app/state/app/app.state';
import { environment } from 'src/environments/environment';
import { GetEquipmentListFilterAction, GetEquipmentNewSearchAction } from '../../../action/equipment.action';
import { EquipmentModel, EquipmentLocationModel } from '../../../model/equipment.model';
import { FilterFieldsModel, FilterItemModel } from '../../../model/filter.model';
import { LogService } from '../../../service/log.service';
import { EquipmentState } from '../../../state/equipment.state';
import { FilterModalComponent } from '../../../component/filter-modal/filter-modal.component';
import { clearUrlParamsOnHistory, replaceHistoryState } from '../../../../../util/history.util';
import { PageOpenedAction } from 'src/app/module/shared/state/common/common.actions';
import { PagesEnum } from 'src/app/module/shared/enum/pages.enum';
import { GetEquipmentLocationAction } from '../../../action/equipment.action';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SettingsResponse } from 'src/app/module/shared/model/settings.model';
@Component({
  selector: 'cat-equipment-list',
  templateUrl: './equipment-list.component.html',
  styleUrls: ['./equipment-list.component.scss'],
  animations: [
    trigger('search', [
      state('void', style({opacity: 0, height: '0'})),
      state('*', style({opacity: 1, height: '*'})),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class EquipmentListComponent implements OnInit, AfterViewInit, OnDestroy, AfterContentChecked {
  @Select(EquipmentState.equipmentFilterFields)
  equipmentListFilterFields$: Observable<FilterFieldsModel[]>;
  equipmentListFilterFields: FilterFieldsModel[];

  @Select(EquipmentState.equipmentListSearch)
  equipmentList$: Observable<EquipmentModel[]>;

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentListPaging)
  equipmentListPaging$: Observable<PagingModel>;

  @Select(EquipmentState.equipmentLocation)
  equipmentLocation$: Observable<any>;

  @Select(EquipmentState.equipmentLocationLoading)
  equipmentLocationLoading$: Observable<boolean>;

  @Select(AppState.history)
  history$: Observable<any>;

  @Select(SettingsState.basic)
  basic$: Observable<SettingsResponse>;

  equipmentList: EquipmentModel[];
  imageSizeEnum = ImageSizeEnum;
  PermissionEnum = PermissionEnum;
  private customerNumber: string;
  page = 1;
  // tslint:disable: no-inferrable-types
  hideWorkingHoursOlderThanDays: number = 30;
  equipmentListLoading: boolean;
  relatedLocationField: EquipmentLocationModel;
  paging: PagingModel;
  eCommercePermission: Observable<boolean>;

  @ViewChild('list') listElement: ElementRef;
  @ViewChild('search') input: ElementRef;
  form: any;

  @ViewChild('filterModal')
  protected filterModalComponent: FilterModalComponent;

  @ViewChild('t') tooltip: NgbTooltip;
  searchText = '';

  equipmentFilters: FilterItemModel[] = [];
  hasFiltered = false;
  source: string;
  sourceRoot: string;
  equipmentLocations = []
  ladyBirdIcon = environment.assets + '/pse-boom360.svg';
  error: boolean;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly logger: LogService,
    private readonly fb: FormBuilder,
    private readonly settingsService: SettingsService,
    private readonly ref: ChangeDetectorRef,
    private readonly hasPermissionService: HasPermissionsService
  ) { }

  ngOnInit() {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetEquipmentLocationAction())
    this.equipmentLocation$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((data) => {
      if(data){
        this.equipmentLocations = data.equipmentLocations
      }
    })

    this.store.dispatch(new PageOpenedAction(PagesEnum.equipmentListPage));
    this.form = this.fb.group({
      search: [null, Validators.minLength(3)],
    });

    this.source = this.route.snapshot.queryParams?.source;
    this.sourceRoot = this.route.snapshot.queryParams?.sourceRoot;

    this.basic$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((data) => {
      if(data) {
        this.hideWorkingHoursOlderThanDays = Number(data.hideWorkingHoursOlderThanDays)
      }
    })
    // Has Service Indicator Filter - Notification
    this.buildQueryFilters();

    if (!this.store.selectSnapshot(EquipmentState.equipmentListSearch)) {
      this.loadEquipments();
    }

    // ###  history store
    this.router.events
      .pipe(takeUntil(this.subscriptions$))
      .pipe(filter(e => e instanceof NavigationStart))
      .subscribe(() => {
        // console.log('events', val);
        this.storeHistory();
      });

    this.history$
      .pipe(takeUntil(this.subscriptions$), take(1))
      // tslint:disable-next-line:no-shadowed-variable
      .subscribe((state: any) => {
        if (state) {
          // restore paging and search
          this.page = state.page;
          this.searchText = state.searchText;
          this.form.get('search').setValue(this.searchText);
          // this.equipmentFilters = state.filter;
          this.setEquipmentsFilter(state.filter);
          this.equipmentListFilterFields = state.filterFields;
          // restore scroll
          setTimeout(() => {
            document.documentElement.scrollTop = state.scrollPosition;
            this.storeHistory();
          });
        }
      });
    // #history

    this.equipmentListLoading$.subscribe(i => (this.equipmentListLoading = i));
    this.equipmentListPaging$
      .subscribe(paging => {
        if (paging) {
          console.log('paging', paging);
          this.paging = paging;
          this.page = this.paging?.pageNumber;
          setTimeout(() => {
            if (this.listElement && this.listElement?.nativeElement.clientHeight && window.innerHeight > this.listElement?.nativeElement.clientHeight) {
              // console.log('nextpage', [window.innerHeight, this.listElement?.nativeElement.clientHeight]);
              this.nextPage();
            }
          });
          this.equipmentList$
            .pipe(takeUntil(this.subscriptions$))
            .subscribe(e => {
              this.equipmentList = e;
            });
        }
      });


    this.loadEquipmentListFields();

    this.eCommercePermission = this.hasPermissionService.hasPermission(PermissionEnum.Ecommerce);
  }

  ngAfterViewInit() {
    fromEvent(this.input.nativeElement, 'keyup')
      .pipe(
        filter(Boolean),
        tap(() => {
          if (this.form.controls.search.hasError('minlength')) {
            this.tooltip.open();
          }
        }),
        debounceTime(400),
        distinctUntilChanged(),
        filter(
          () =>
            this.input.nativeElement.value.length >= 3 ||
            this.input.nativeElement.value.length === 0,
        ),
        tap(() => {
          this.tooltip.close();
        }),
      )
      .subscribe(() => {
        this.page = 1;
        this.searchText = this.form.get('search').value || '';
        this.hasFiltered = false;
        this.loadEquipments();
      });
  }

  loadEquipments() {
    // console.log('::::LOAD equipments');
    if (this.hasFiltered) {
      this.searchText = '';
      this.form.patchValue({ search: '' });
    }
    if (this.searchText) {
      const searchFilter = [
        {
          fieldName: 'search',
          fieldValue: this.searchText
        }
      ];
      this.hasFiltered = false;
      this.filterModalComponent?.clearFilter();
      this.logger
        .action('EQUIPMENT', 'SEARCH', {
          searchParameter: this.searchText,
          filterFields: this.equipmentFilters
        })
        .subscribe();
      this.store.dispatch(new GetEquipmentNewSearchAction(this.page, searchFilter));
    } else {
      if (this.equipmentFilters) {
        this.store.dispatch(new GetEquipmentNewSearchAction(this.page, this.equipmentFilters));
      } else {
        this.store.dispatch(new GetEquipmentNewSearchAction(this.page));
      }
    }
  }

  loadEquipmentListFields() {
    this.store.dispatch(new GetEquipmentListFilterAction());
    this.equipmentListFilterFields$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(fields => {
        if (fields?.length) {
          this.equipmentListFilterFields = fields;
        }
      });
  }

  onClickItem(equipment: EquipmentModel) {
    if (!equipment.model) { // abort if no model name
      return;
    }
    if(this.hasPermissionService.checkPermission(PermissionEnum.EquipmentDetail)){
      this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'equipment-detail',
          equipment.equipmentNumber,
        ],
        { relativeTo: this.route, queryParams: { source: this.source, sourceRoot: this.sourceRoot } },
      )
      .then();
    } else {
      this.error = true;
    }

  }

  onClickLocation(equipmentNumber: string, serialNumber: string) {
    this.logger
      .action(LogSectionEnum.EQUIPMENT, 'MAP_CLICK', {
        equipment: equipmentNumber,
        serialNumber
      })
      .subscribe();
    this.router.navigate(
      [
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'equipment-map',
        equipmentNumber,
      ],
      { relativeTo: this.route },
    ).then();
  }

  @HostListener('window:scroll', ['$event'])
  onScrollEvent() {
    const pos =
      (document.documentElement.scrollTop || document.body.scrollTop) +
      document.documentElement.clientHeight;
    const max = document.documentElement.scrollHeight;

    if (pos + 100 >= max && !this.equipmentListLoading) {
      this.nextPage();
      if (this.equipmentList?.length && this.equipmentListLoading) {
        this.logger
          .action('EQUIPMENT', 'SCROLL_PAGING', {
            page: this.page,
            equipmentFilters: this.equipmentFilters,
            searchText: this.searchText
          })
          .subscribe();
      }
    }
  }

  ngAfterContentChecked(): void {
    this.ref.detectChanges();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  private storeHistory() {
    history.scrollRestoration = 'auto';
    const historyState = {
      page: this.page,
      searchText: this.searchText,
      scrollPosition: document.documentElement.scrollTop,
      filter: this.equipmentFilters,
      filterFields: this.equipmentListFilterFields
    };
    console.log('store history', historyState);
    replaceHistoryState(historyState);
  }


  toCreateNewEquipment() {
    this.clearFilters();
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'equipment-create',
      ])
      .then();
  }

  setEquipmentsFilter(e: FilterItemModel[]) {
    if (JSON.stringify(this.equipmentFilters) !== JSON.stringify(e)) {
      // if (this.searchText) {
      //   this.searchText = '';
      //   this.form.patchValue({ search: '' });
      // }
      this.page = 1;
      this.equipmentFilters = e;
      this.loadEquipments();
    }
    this.hasFiltered = this.equipmentFilters?.length > 0;
  }

  clearFilters() {
    this.page = 1;
    this.hasFiltered = false;
    this.searchText = '';
    this.form.patchValue({ search: this.searchText });
    this.filterModalComponent?.clearFilter();

    this.loadEquipments();
    this.logger
      .action('EQUIPMENT', 'CLEAR_FILTERS_CLICK')
      .subscribe();
  }

  private nextPage() {
    if (this.paging?.pageNumber * this.paging?.pageSize < this.paging?.totalCount) {
      this.page++;
      this.loadEquipments();
    }
  }

  iconClockVisible(workingDate, workingHours) {
    if (!workingDate || workingHours === 0) {
      return false;
    }
    const daysAgo = (new Date(Date.now()).getTime() - new Date(workingDate.split('T')[0]).getTime())
      / (1000 * 3600 * 24);

    return daysAgo < this.hideWorkingHoursOlderThanDays;
  }

  protected buildFilter(filters) {
    return Object.entries(filters).map(([key, value]) => ({ fieldName: key, fieldValue: value }));
  }

  protected buildQueryFilters() {
    const { isServiceIndicator, IsProductLinked, SerialNumber, isPse } = this.route.snapshot.queryParams;
    const urlFilters: any = {};
    if (IsProductLinked) {
      urlFilters.isProductLink = true;
    }
    if (isServiceIndicator) {
      urlFilters.HasRevisionIndicator = true;
    }
    if (SerialNumber) {
      urlFilters.SerialNumber = SerialNumber;
    }
    if (isPse) {
      urlFilters.isPse = true;
    }
    if (!Object.values(urlFilters).length) {
      return;
    }
    this.setEquipmentsFilter(this.buildFilter(urlFilters));
    clearUrlParamsOnHistory(['isServiceIndicator', 'IsProductLinked', 'serialNumber', 'isPse']);

  }

  accessEquipmentLocation(serialNumber) {
    const relatedLocationField = this.equipmentLocations?.find(l => l.serialNumber === serialNumber)
    this.relatedLocationField = relatedLocationField

    if(this.equipmentLocations?.length > 0 && relatedLocationField?.longitude && relatedLocationField?.latitude ){
      return true;
    }

    return false;
  }
}
