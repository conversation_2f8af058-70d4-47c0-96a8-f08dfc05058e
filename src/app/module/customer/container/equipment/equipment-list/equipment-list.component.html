<cat-checked-in-equipments></cat-checked-in-equipments>
<div #list class="px-3 py-4">
  <div class="mb-2">
    <div class="d-flex flex-row">
      <div class="w-100 search-area position-relative" [formGroup]="form" [hidden]="equipmentList?.length === 0 && !hasFiltered
           && !(equipmentListLoading$ | async) && !searchText">
        <input #search [placeholder]="'_search' | translate" class="form-control search-input" type="text" formControlName="search"
          placement="bottom" [ngbTooltip]="'_search_min_length_3' | translate" triggers="none" #t="ngbTooltip" />
        <i class="icon icon-search"></i>
      </div>
      <!-- Equipment Filter -->
      <ng-container *ngIf="(equipmentListFilterFields?.length && equipmentList?.length) || hasFiltered || searchText">
        <cat-filter-modal
          #filterModal
          [filter]="equipmentFilters"
          [filterFields]="equipmentListFilterFields"
          [specialHiddenFieldsList]="['search']"
          [(filtered)]="hasFiltered"
          (applyFilter)="setEquipmentsFilter($event)"
          modalZIndexUpLoading="true"
        ></cat-filter-modal>
      </ng-container>
      <ng-container *ngIf="equipmentList">
        <div class="equipment-count d-flex justify-content-center align-items-center ml-1" *ngIf="equipmentList?.length || hasFiltered">
          <i class="icon icon-equipment"></i>
          <span class="ml-1"> {{ paging.totalCount }}</span>
        </div>
      </ng-container>

      <div *ngIf="equipmentList?.length || searchText" [hasPermission]="PermissionEnum.RequestsCreateEquipment"  (click)="toCreateNewEquipment()"
        class="add-equipment d-flex justify-content-center align-items-center ml-2">
        <i class="icon icon-plus font-weight-bold"></i>
      </div>

    </div>
  </div>

  <ng-container *ngIf="equipmentList?.length || searchText || hasFiltered; else emptyList">
    <ng-container *ngIf="equipmentList?.length; else emptySearchList">
      <div (click)="onClickItem(equipment)" *ngFor="let equipment of equipmentList" class="equipment d-flex mb-4">
        <div class="align-self-center equipment-image mr-3">
          <cat-image-preview [title]="equipment.brand + ' ' + equipment.model" [model]="equipment.model"
            [imageSize]="imageSizeEnum.mobilethumbnailsize" [id]="equipment.productHierarchy"></cat-image-preview>
        </div>
        <div class="flex-fill">
          <div class="font-weight-bold text-secondary">
            {{ equipment.brand }} {{ equipment.model }}
          </div>
          <div class="font-size-13px text-secondary mb-2">
            {{ equipment.serialNumber | serialFormat}}
            <div [hasPermission]="PermissionEnum.EquipmentDetailCampaign" class="d-inline-block ml-2" *ngIf="equipment?.equipmentRevisionCampaign">
              <div [ngSwitch]="equipment?.equipmentRevisionCampaign?.type"
                class="d-flex justify-content-center text-success font-size-16px fw-bolder">
                <i *ngSwitchCase="'_campaign'" class="icon icon-campaign"></i>
                <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon"></i>
                <i *ngSwitchCase="'_discount'" class="icon {{ equipment?.equipmentRevisionCampaign?.currency | currenyIcon }}"></i>
                <span *ngSwitchCase="'_percent_discount'">%</span>
                <i *ngSwitchDefault class="icon icon-campaign"></i>
              </div>
            </div>
            <div *ngIf="
                  equipment?.revisionIndicator === 'yellow' ||
                  equipment?.revisionIndicator === 'orange' ||
                  equipment?.revisionIndicator === 'red'
                " class="repair-icon d-inline-block ml-2" [ngClass]="{
                  'text-yellow': equipment?.revisionIndicator === 'yellow',
                  'text-warning': equipment?.revisionIndicator === 'orange',
                  'text-danger': equipment?.revisionIndicator === 'red'
                }">
              <div class="d-flex justify-content-center">
                <i class="icon icon-repair"></i>
              </div>
            </div>
            <div *ngIf="equipment.isProductLink" class="product-link d-inline-block ml-2">
              <div class="d-flex justify-content-center" [ngbTooltip]="'_connected_equipment' | translate" tooltipClass="wifi-icon"
                placement="left">
                <i class="icon icon-wifi" [style.color]="equipment?.productLinkColor"></i>
              </div>
            </div>
            <div [hasPermission]="PermissionEnum.EquipmentDetailPse" *ngIf="equipment.isPse && (eCommercePermission | async)" class="pse-equipment d-inline-block ml-2">
              <img class="ladyBirdIcon" [src]="ladyBirdIcon">
            </div>
          </div>
          <div class="max-width-180px d-flex justify-content-between flex-wrap">
            <div class="font-size-13px text-secondary text-nowrap"
              *ngIf="iconClockVisible(equipment?.workingHourDate,equipment?.workingHours)">
              <i class="icon icon-clock mr-1"></i>
              {{ equipment?.workingHours }}
              {{ "_hour" | translate }}
            </div>
            <div
              *ngIf="
                accessEquipmentLocation(equipment?.serialNumber) ||
                (equipment?.location &&
                equipment?.location.latitude &&
                equipment?.location.longtitude)"
              class="font-size-13px text-secondary"
              (click)="onClickLocation(equipment?.equipmentNumber, equipment?.serialNumber)"
            >
              <i class="icon icon-location mr-1"></i>
              {{ (relatedLocationField?.country || equipment?.location?.locationName) }}
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>
  <cat-loader [show]="page == 1 && equipmentListLoading || (equipmentLocationLoading$ | async)"></cat-loader>
  <div class="mini-loading-area" *ngIf="page > 1 && equipmentListLoading">
    <cat-loader [show]="true" bgColor="transparent" [overlay]="false"></cat-loader>
  </div>
</div>
<ng-template #emptyList>
  <div *ngIf="!(equipmentListLoading$ | async)">
    <cat-empty-content [message]="'_equipment_list_empty'" [iconName]="'equipment'">
      <a class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm" (click)="toCreateNewEquipment()" [hasPermission]="PermissionEnum.RequestsCreateEquipment">
        {{ "_add_equipment" | translate }}
      </a>
    </cat-empty-content>
  </div>
</ng-template>

<ng-template #emptySearchList>
  <div *ngIf="!(equipmentListLoading$ | async)">
    <cat-empty-content [message]="'_no_equipment_find'" [extraMessage]="'_update_criteria_and_try_again'" [iconName]="'equipment'">
      <!--      <p class="text-center font-size-10px">-->
      <!--      Ekipmanınızı göremiyorsanız, bilgileri güncellemek için tıklayınız-->
      <!--      </p>-->
      <a *ngIf="hasFiltered || searchText" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="clearFilters()">
        {{ "_clear_filters" | translate }}
      </a>
      <a class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm" (click)="toCreateNewEquipment()">
        {{ "_add_equipment" | translate }}
      </a>

    </cat-empty-content>
  </div>
</ng-template>

<cat-error-modal [(status)]="error" [message]="'_has_no_permisson' | translate "></cat-error-modal>
