<div class="my-equipment-agenda-notes px-4 py-4">
  <!-- <div *ngIf="!formStatus && !noteStatus && isShowNotes && !(equipmentAgendaLoading$ | async)">
    <div class="h5 mb-3">{{ "_notes" | translate }}</div>
    <div class="note-list">
      <div *ngFor="let item of groupedNotes | keyvalue">
        <div class="h5">{{item.key}}</div>
        <div *ngFor="let note of item.value" class="equipment-note-container">
          <div (click)="openNote(note)" class="note-group">
            <div class="note px-4 py-3">
              <div class="h5 m-0 note-title">{{ note.title }}</div>
              <p class="note-info font-size-13px m-0">
                {{ note.body }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div >
    </div>
  </div> -->
  <!-- <button
    *ngIf="!noteStatus && !formStatus"
    (click)="showEquipmentAgendaNoteForm()"
    class="add-note-btn btn btn-info"
  >
    <i class="icon icon-contract"></i>
  </button> -->
  <div>
    <form class="note-form" (submit)="sendForm()" [formGroup]="form">
      <div class="form-group d-flex">
        <div class="h5">
          {{ '_remind_me' | translate }}
        </div>
        <div class="round">
          <input
            type="checkbox"
            id="checkbox"
            formControlName="HasReminder"
            name="HasReminder"
          />
          <label for="checkbox">
            <i *ngIf="form.value.HasReminder" class="icon icon-success"></i>
          </label>
        </div>
      </div>
      <div class="pb-3 border-bottom" *ngIf="form.value.HasReminder">
        <div class="form-group">
          <div class="h6">
            {{ '_date' | translate }}
          </div>
          <div class="date-input">
            <input
              type="date"
              class="form-control"
              name="Date"
              (change)="onChangeDate()"
              id="Date"
              formControlName="Date"
              class="form-control"
            />
            <i class="icon icon-calendar"></i>
          </div>
          <div *ngIf="!validDate" [ngClass]="{'d-block': !validDate}" class="invalid-feedback pl-3">
            {{ '_unvalid_equipment_agenda_note_date' | translate }}
          </div>
          <div
            [ngClass]="{'d-block':!form.value?.Date && form.value?.HasReminder && form.get('Date').touched}"
            class="invalid-feedback pl-3"
          >
            {{ '_required' | translate }}
          </div>
        </div>
        <div class="form-group">
          <div class="h6">
            {{ '_hour' | translate }}
          </div>
          <div class="date-input">
            <input
              type="time"
              class="form-control"
              name="Time"
              id="Time"
              (change)="onChangeTime()"
              formControlName="Time"
              class="form-control"
            />
            <i class="icon icon-clock"></i>
          </div>
          <div *ngIf="!validTime" [ngClass]="{'d-block': !validTime}" class="invalid-feedback pl-3">
            {{ '_unvalid_equipment_agenda_note_time' | translate }}
          </div>
          <div
            [ngClass]="{'d-block':!form.value?.Time && form.value?.HasReminder && form.get('Time').touched}"
            class="invalid-feedback pl-3"
          >
            {{ '_required' | translate }}
          </div>
        </div>
      </div>
      <div class="form-group mt-3">
        <input
          [name]="'Title'"
          catInputLength
          [placeholder]="'_title' | translate"
          class="form-control title-input"
          formControlName="Title"
          type="text"
          maxlength="100"
        />
        <div
          [ngClass]="{
            'd-block':
              !form.get('Title').valid && form.get('Title').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Title) | translate }}
        </div>
      </div>
      <div class="form-group">
        <textarea
          catInputLength
          [placeholder]="'_note' | translate"
          [name]="'Description'"
          [rows]="10"
          formControlName="Description"
          class="form-control note-input"
          minlength="3"
          maxlength="500"
          style="resize: none;"
        ></textarea>
        <div
          [ngClass]="{
            'd-block':
              !form.get('Description').valid && form.get('Description').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Description) | translate }}
        </div>
      </div>
      <div class="d-flex align-items-center w-100">
        <!-- <button type="button" (click)="closeForm()" class="btn btn-sm btn-info text-white flex-grow-1 mr-2">
          {{ '_cancel' | translate }}
        </button> -->
        <button type="submit" class="btn btn-sm flex-grow-1 btn-warning text-white ">
          {{'_save' | translate}}
        </button>
      </div>
    </form>
  </div>
  <div class="note-content" *ngIf="noteStatus">
    <div class="text-container">
      <div class="h4 text-break">
        {{ noteValues.title }}
      </div>
      <p class="note-text">
        {{noteValues.body}}
      </p>
    </div>
    <button (click)="openEquipmentAgendaNote()" class="btn btn-warning btn-gradient btn-sm btn-block text-white mt-2">
      {{ '_go_back' | translate  }}
    </button>
  </div>
</div>

<div *ngIf="formSendStatus" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ '_success_added_note' | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="closeModal()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>

<cat-loader [show]="loading || (equipmentAgendaLoading$ | async)"></cat-loader>
<!-- <div class="empty-note-content" *ngIf="!isShowNotes && !formStatus && !(equipmentAgendaLoading$ | async)">
  <i class="icon icon-offer"></i>
  <div class="h5 mt-3 text-center">
    {{ '_empty_note_message' | translate }}
  </div>
  <button catUserClick [section]="'CREATE_NEW_NOTE'" (click)="showEquipmentAgendaNoteForm()"
    class="btn btn-gradient btn-warning btn-block text-white rounded-lg btn-sm">
    {{ "_add_note" | translate }}
  </button>
  <button (click)="navigateToBack()" class="btn btn-gradient btn-info btn-block text-white rounded-lg btn-sm">
    {{ '_go_back' | translate }}
  </button>
</div> -->
