.my-equipment-agenda-notes{
    height: 100%;
    position: relative;

    .note-list{

        .note-group{
            .note{
                height: 70px;
                border-radius: .5rem;
                box-shadow: 0px 10px 20px 0px #00000014;
                background-color: #ffffff;
                margin-bottom: 2rem;
                display: flex;
                flex-direction: column;
    
                &-info{
                    color: #cccccc;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                &-title{
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }

    .add-note-btn{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        position: fixed;
        bottom: 1.5rem;
        right: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon{
            &:before{
                color: #ffffff !important;
            }
        }
    }

    .note-content{
        
        .text-container{
            height: 375px;
            background-color: #EEF2F4;
            border-radius: .5rem;
            padding: 2rem;
            overflow: auto;
        }

        .note-text{
            word-wrap: break-word;
            color:#505050;
        }
    }

    .note-form{
        input{
            background-color: #EEF2F4 !important;
        }

        .date-input{
          position: relative;
        }

        .icon-calendar, .icon-clock{
          position: absolute;
          top: 16px;
          right: 20px;
          
          &::before{
            font-size: 17px;
            color: #000;
          }
        }

        .title-input{
            background-color: white;
        }

        .note-input{
            height: 275px;
        }
    }
}

.form-preview, .after-form-send {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #FAFAFA;
  }
  
  .after-form-send-content {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100vw;
    transform: translateY(-50%);
  }
  .success-message{
    font-size: 26px;
    font-weight: 700;
  }
  .icon-message-success {
    font-size: 60px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .empty-note-content{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 4rem;

    .icon-offer{
        color: #FFA300;
        font-size: 36px;
    }
  }

    .reminder-checkbox{
        position: relative;
    }

    // .reminder-checkbox label {
    //     background-color: #fff;
    //     border: 1px solid #ccc;
    //     border-radius: 50%;
    //     cursor: pointer;
    //     height: 28px;
    //     left: 0;
    //     position: absolute;
    //     top: 0;
    //     width: 28px;
    // }

    // .reminder-checkbox label:after {
    //   border: 2px solid #fff;
    //   border-top: none;
    //   border-right: none;
    //   content: "";
    //   height: 6px;
    //   left: 7px;
    //   opacity: 0;
    //   position: absolute;
    //   top: 8px;
    //   transform: rotate(-45deg);
    //   width: 12px;
    // }
    
    // .reminder-checkbox input[type="checkbox"] {
    //   visibility: hidden;
    // }
    
    // .reminder-checkbox input[type="checkbox"]:checked + label {
    //   background-color: #66bb6a;
    //   border-color: #66bb6a;
    // }
    
    // .reminder-checkbox input[type="checkbox"]:checked + label:after {
    //   opacity: 1;
    // }
    .round {
        position: relative;
        margin-left: .5rem;
      }
      
      .round label {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 0px;
        cursor: pointer;
        height: 20px;
        left: 0;
        position: absolute;
        top: 0;
        width: 20px;
      }

      
      .round input[type="checkbox"] {
        visibility: hidden;
      }
      
      .round input[type="checkbox"]:checked + label {
        background-color: #FFF;
      }
      
      .round input[type="checkbox"]:checked + label:after {
      }

      .icon-success{
        position: absolute;
        inset: 1px;
        color: #FFA300;
      }