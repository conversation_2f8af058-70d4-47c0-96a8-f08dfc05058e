import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { EquipmentState } from '../../../state/equipment.state';
import { Form, Validators, FormControl, FormGroup, AbstractControl, ValidationErrors } from '@angular/forms';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { Observable } from 'rxjs';
import {
  GetEquipmentAgendaHistory} from '../../../action/equipment.action';
import { EquipmentService } from '../../../service/equipment.service';
import { LogService } from '../../../service/log.service';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';

@Component({
  selector: 'cat-my-equipment-agenda-notes',
  templateUrl: './my-equipment-agenda-notes.component.html',
  styleUrls: ['./my-equipment-agenda-notes.component.scss'],
})
export class MyEquipmentAgendaNotesComponent implements OnInit {
  @Select(EquipmentState.equipmentAgendaHistory)
  equipmentAgendaHistory$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaDetails)
  equipmentAgendaDetails$: Observable<any>;
  @Select(EquipmentState.equipmentAgendaLoading)
  equipmentAgendaLoading$: Observable<boolean>;

  form: FormGroup = new FormGroup({
    HasReminder: new FormControl(false),
    Date: new FormControl(false),
    Time: new FormControl(false),
    Title: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, [Validators.required]),
  });
  getFormErrorMessage = getFormErrorMessage;
  formStatus = false
  noteStatus = false
  equipmentAgendaHistory: any;
  equipmentAgendaDetails: any;
  loading = false;
  formSendStatus = false;
  noteValues: any = {};
  groupedNotes: any;
  isShowNotes = false;
  validDate = true;
  validTime = true;
  remindMeControl = false;

  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly equipmentService: EquipmentService,
    private readonly log: LogService
  ) {}

  ngOnInit(): void {

    this.equipmentAgendaDetails$.subscribe((data) => {
      this.equipmentAgendaDetails = data;
    });

    // //this.store.dispatch(new GetEquipmentAgendaHistory(this.equipment.serialNumber));
    // this.equipmentAgendaHistory$.subscribe((data) => {
    //   // const notes = data?.notes
    //   // if(notes){
    //   //   const groupedNotes = data.notes.reduce(function(a, e) {
    //   //     const year = new Date(e.noteDate).getFullYear();
    //   //     (a[year] ? a[year] : (a[year] = null || [])).push(e);
    //   //     return a;
    //   //   }, {});
    //   //   this.isShowNotes = data?.notes?.length > 0
    //   //   this.groupedNotes = groupedNotes
    //   // }
    // });
  }

  get equipment() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  openNote(note) {
    this.openEquipmentAgendaNote()
    this.noteValues.title = note.title;
    this.noteValues.body = note.body;
  }

  showEquipmentAgendaNoteForm() {
    this.form.reset();
    this.formStatus = !this.formStatus
  }

  openEquipmentAgendaNote() {
    this.noteStatus = !this.noteStatus
  }

  getOnlyDate(date){
    return new Date(date.getFullYear(), date.getMonth(), date.getDate())
  }

  onChangeDate(){
    const noteDate = new Date(this.form.value?.Date)
    const today = new Date()
    this.onChangeTime()

    if(new Date(this.getOnlyDate(noteDate)) < new Date(this.getOnlyDate(today))){
      this.onChangeTime()
      return this.validDate = false
    }
    
    return this.validDate = true
  }

  onChangeTime(){
    const today = new Date()
    const noteDate = new Date(this.form.value?.Date)
    
    if(new Date(this.getOnlyDate(noteDate)) > new Date(this.getOnlyDate(today)) || !this.form?.value?.Date){
      return this.validTime = true
    }
    if(this.form?.value?.Time){
      const [hours, minutes] = this.form?.value?.Time?.split(':').map(Number);
      today.setHours(hours, minutes, 0, 0);
  
      if(today.getTime() < new Date().getTime()){
        return this.validTime = false
      }
    }

    return this.validTime = true
  }

  sendForm() {    
    const { value } = this.form;
    const date = value?.Date
    const time = value?.Time
    const dateWithTime = new Date(date + 'T' + time)
    const reminderDate = value?.HasReminder && date && time

    const formBody = {
      EquipmentAgendaId: this.equipmentAgendaDetails?.equipmentAgendaId,
      SerialNumber: this.equipment?.serialNumber,
      Title: value?.Title,
      Body: value?.Description,
      HasReminder:  reminderDate ? true : false ,
      ReminderDate: reminderDate ? dateWithTime : new Date()
    };
    
    if(value?.HasReminder && (!value?.Time || !value?.Date)){
      return validateAllFormFields(this.form);
    }
    
    if (this.form.valid && this.validDate && this.validTime) {
      this.loading = true;
      this.equipmentService.equipmentAgendaCreateNote(formBody).subscribe(
        () => {
          this.formSendStatus = true;
          this.form.reset();
          this.loading = false;
          this.formStatus = false
          this.store.dispatch(new GetEquipmentAgendaHistory(this.equipment?.serialNumber, this.equipment?.equipmentNumber, new Date()));
          this.log.action(LogSectionEnum.EQUIPMENT, 'ADD_EQUIPMENT_AGENDA_NOTE', formBody).subscribe();
        },
        () => {
          this.formSendStatus = false;
          this.loading = false;
        }
      );
    } else {
      validateAllFormFields(this.form);
    }
  }

  closeModal() {
    this.formSendStatus = false;
  }

  closeForm() {
    //this.formStatus = false
    window.history.back()
  }

  navigateToBack(){
    window.history.back()
  }
}
