<div class="py-2 px-3 mb-0 d-flex align-items-center">
  <div class="h4 nav-back">
    <i class="icon icon-back mr-4 float-left" (click)="navigateToBack()"></i>
  </div>
  <div class="search-select-area px-0">
<!--    <button class="btn filter-button" (click)="onClickFilter()">-->
<!--      <i class="icon icon-filter"></i>-->
<!--    </button>-->
    <cat-machine-serial-dropdown
      [form]="form"
      [fieldName]="'EquipmentSerialNumber'"
      [apiSearch]="false"
      [externalData]="true"
      [equipmentList]="equipments"
      [addTagDisable]="true"
      (selectEquipment)="onSelect($event)">
      >
    </cat-machine-serial-dropdown>
  </div>
</div>

<div class="map-content" *ngIf="apiLoaded | async">
  <google-map [center]="center" [zoom]="zoom" (mapClick)="onClickMarker(null)">
    <map-marker
      #marker="mapMarker"
      *ngFor="let marker of this.locations"
      [position]="marker"
      (mapClick)="onClickMarker(marker)"
      [options]="marker"
    ></map-marker>
  </google-map>
</div>

<div class="location-info-content pb-5"
     *ngIf="selected && (selected?.brand || selected?.model || selected?.serialNumber)">
  <div *ngIf="equipmentDetailLoading$ | async else isDetailLoading">
    <div class="small-spin">
      <ngx-loading
        [show]="true"
        [config]="{
          fullScreenBackdrop: false,
          backdropBackgroundColour: '#eef2f4'
         }"
      ></ngx-loading>
    </div>
  </div>
  <ng-template #isDetailLoading>
    <div class="equipment d-flex align-items-center">
      <div class="equipment-image mr-3">
        <cat-image-preview
          [title]="selected.brand + ' ' + selected.model"
          [model]="selected.model"
          [imageSize]="imageSizeEnum.mobilethumbnailsize"
          [id]="selected.productHierarchy"
          [maxHeight]="'80px'"
        ></cat-image-preview>
      </div>
      <div class="flex-fill">
        <div class="font-weight-bold text-secondary">
          {{ selected.brand }} {{ selected.model }}
        </div>
        <div class="font-size-13px text-secondary mb-2">
          {{ selected.serialNumber | serialFormat }}
        </div>
        <div class="font-size-13px text-secondary text-nowrap"
             *ngIf="iconClockVisible(selected?.workingHourDate,selected?.workingHours)">
          <i class="icon icon-clock mr-1"></i>
          {{ selected?.workingHours }}
          {{ "_hour" | translate }}
        </div>
      </div>
    </div>
    <div class="row px-1 m-0">
      <div class="col-7">
        <small class="text-secondary font-size-11px float-left">
          {{ selectedDetail?.lastProductLinkDetail?.date ?
          ("_equipment_disclaimer_date" | translate:{
            date: (selectedDetail?.lastProductLinkDetail?.date | date:"dd.MM.yyyy")
          }) :
          ("_equipment_disclaimer" | translate)}}
        </small>
      </div>
      <div class="col pl-0" [hasPermission]="PermissionEnum.RequestsUpdateEquipment">
        <small class="text-secondary font-size-11px float-right text-right">
          {{ "_is_false_equipment_location" | translate }}
          <b class="text-primary" (click)="onClickEditLocation()">
            {{ "_request_an_edit" | translate }}!
          </b>
        </small>
      </div>
    </div>
  </ng-template>
</div>

<cat-basic-modal
  *ngIf="isShowFilterModal"
  [(status)]="isShowFilterModal"
  [headerText]="'_filter' | translate"
>
  <div class="mb-2">
    <ng-select
      appendTo=".modal"
      #locationSelect
      class="search-select"
      [placeholder]="'_select_country' | translate"
      [searchable]="true"
      [multiple]="false"
      [dropdownPosition]="'bottom'"
      [(ngModel)]="selectedCountry"
      (ngModelChange)="onSelectCountry($event)"
      (close)="locationSelect.blur()"
    >
      <ng-option *ngFor="let item of countries$ | async" [value]="item.name">
        {{ item.name }}
      </ng-option>
    </ng-select>
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button
      (click)="onClearFilter()"
      class="modal-btn btn btn-secondary btn-gradient text-white shadow"
    >
      {{ "_clear" | translate }}
    </button>

    <button
      (click)="onApplyFilter()"
      class="modal-btn btn btn-warning btn-gradient text-white shadow"
    >
      {{ "_apply" | translate }}
    </button>
  </div>
</cat-basic-modal>
