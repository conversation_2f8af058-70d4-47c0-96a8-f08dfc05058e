.search-select-area {
  background: #ffffff;
  border: 1px solid #d7e5ea;
  border-radius: 6px;
  flex: 1;
  position: relative;
  .filter-button {
    position: absolute;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  //ng-select {
  //  margin-left: 20px;
  //}
  .search-select ::ng-deep.ng-select-container {
    background-color: transparent;
    border: none;
    width: 100%;
  }
}

.map-content {
  height: calc(100vh - (70px));
  width: 100vw;
  position: relative;
}

::ng-deep google-map .map-container {
  height: 100% !important;
  width: 100vw !important;
  .gmnoprint {
    display: none;
  }
}

.location-info-content {
  position: absolute;
  z-index: 1;
  bottom: 0;
  width: 100%;
  min-height: 150px;
  background-color: white;
}
