import { HttpClient } from '@angular/common/http';
import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import { catchError, map, takeUntil } from 'rxjs/operators';
import { ImageSizeEnum } from 'src/app/export/media/enum/image-size.enum';
import { Country } from 'src/app/module/definition/model/country.model';
import { DefinitionState } from 'src/app/module/definition/state/definition.state';
import { SettingsService } from 'src/app/module/shared/service/settings.service';
import { environment } from 'src/environments/environment';
import { EquipmentModel } from '../../../model/equipment.model';
import { EquipmentState } from '../../../state/equipment.state';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { GetEquipmentDetailAction, GetEquipmentListAllAction, GetEquipmentLocationAction } from '../../../action/equipment.action';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SettingsResponse } from 'src/app/module/shared/model/settings.model';

@Component({
  selector: 'cat-equipment-map',
  templateUrl: './equipment-map.component.html',
  styleUrls: ['./equipment-map.component.scss'],
})
export class EquipmentMapComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.equipmentsListAll)
  equipmentsListAll$: Observable<EquipmentModel[]>;

  @Select(EquipmentState.equipmentDetailLoading)
  equipmentDetailLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentLocation)
  equipmentLocation$: Observable<any>

  @Select(SettingsState.basic)
  basic$: Observable<SettingsResponse>;

  form: FormGroup = new FormGroup({
    EquipmentSerialNumber: new FormControl(null, [Validators.required]),
  });

  apiLoaded: Observable<boolean>;
  // center: google.maps.LatLngLiteral = { lat: 39.725452, lng: 36.995745 };
  center = { lat: 39.725452, lng: 36.995745 };
  zoom = 8;
  equipments: Array<EquipmentModel> = [];
  selected: EquipmentModel;
  selectedDetail: EquipmentModel;
  locations: Array<any> = [];
  imageSizeEnum = ImageSizeEnum;
  hideWorkingHoursOlderThanDays = 30;
  equipmentLocations = []
  PermissionEnum = PermissionEnum;

  @Select(DefinitionState.countryList)
  countries$: Observable<Country[]>;
  selectedCountry: string;
  isShowFilterModal = false;

  equipmentIcon = environment.assets + '/map_pin-02_px45-40.svg';
  grayEquipmentIcon = environment.assets + '/map_pin-02_px45-40-gray.svg';

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly settingsService: SettingsService,
    httpClient: HttpClient,
  ) {
    this.apiLoaded = httpClient
      .jsonp(
        'https://maps.googleapis.com/maps/api/js?key=AIzaSyBAIRBC-ckhE8xKezngRiaPMD3R42EvVC8',
        'callback',
      )
      .pipe(
        takeUntil(this.subscriptions$),
        map(() => {
          return true;
        }),
        catchError(() => of(false)),
      );
  }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    const { id } = this.route.snapshot.params;

    const equipmentList = this.store.selectSnapshot(EquipmentState.equipmentListSearch);
    if (equipmentList) {
      this.setEquipments(id, equipmentList);
    }
    this.store.dispatch(new GetEquipmentListAllAction(1, [], 1000));

    this.basic$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((data) => {
      if(data) {
        this.hideWorkingHoursOlderThanDays = Number(data.hideWorkingHoursOlderThanDays)
      }
    })
      this.equipmentLocation$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if(data){
          this.equipmentLocations = data.equipmentLocations
        }
      })

      this.equipmentsListAll$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((list) => {
          if (list?.length) {
            const equipmentsWithNewLocations = list.map(equipmentItem => {
              const newEquipmentLocationItem = this.equipmentLocations?.find(l => l.serialNumber === equipmentItem.serialNumber)
              const newLatitude = this.equipmentLocations?.length && newEquipmentLocationItem?.longitude ? this.replaceLocationInfo(newEquipmentLocationItem?.latitude) : equipmentItem?.location?.latitude
              const newLongitude =  this.equipmentLocations?.length && newEquipmentLocationItem?.latitude ? this.replaceLocationInfo(newEquipmentLocationItem?.longitude) : equipmentItem?.location?.longtitude

              return {
                ...equipmentItem,
                newLocationInfo: { latitude: newLatitude, longitude: newLongitude }
              }
            })
            this.setEquipments(id, equipmentsWithNewLocations);
          }
        });
  }

  replaceLocationInfo(val: string){
    return parseFloat(val?.replace(",", "."))
  }

  onSelectCountry(countryCode) {
    this.selectedCountry = countryCode;
  }

  setEquipments(id: string, list: EquipmentModel[]) {
    this.equipments = list?.filter(
      (e) => (e?.location && e?.newLocationInfo?.latitude && e?.newLocationInfo?.longitude) ||
        (e.lastProductLinkDetail && e?.lastProductLinkDetail.latitude && e?.lastProductLinkDetail.longitude),
    );
    this.findAndSelectEquipment(id);
    this.setLocations();
  }

  setLocations() {
    this.locations = this.equipments?.map((e) => (
      {
      equipmentNumber: e.equipmentNumber,
      lat: e.newLocationInfo.latitude,
      lng: e.newLocationInfo.longitude,
      country: e.location.locationName,
      draggable: false,
      zIndex: e?.equipmentNumber === this.selected?.equipmentNumber ? 99999 : 90000,
      icon: {
        url: e.equipmentNumber === this.selected?.equipmentNumber ? this.equipmentIcon : this.grayEquipmentIcon,
        // scaledSize: new google.maps.Size(45, 40),
      },
    }));
    this.equipments?.map((e) => {
      if (!e?.lastProductLinkDetail && !e.lastProductLinkDetail?.longitude) {
        return;
      }
    });
  }

  onFilterOffices() {
    this.equipments = this.store.selectSnapshot(EquipmentState.equipmentsListAll);
    if (this.selectedCountry) {
      this.equipments = this.equipments
        .filter(e => e.location.locationName.toLowerCase() === this.selectedCountry.toLowerCase());
    }
    this.selected = null;
    this.setLocations();
    this.locations = this.locations.filter(
      (l) =>
        !this.selectedCountry ||
        l.country.toLowerCase() === this.selectedCountry.toLowerCase(),
    );
  }

  findAndSelectEquipment(equipmentNumber) {
    const equipment = this.equipments.find(
      (c) => c.equipmentNumber === equipmentNumber,
    );
    this.onSelect(equipment);
  }

  onSelect(equipment?: EquipmentModel) {
    this.selected = equipment;
    this.setLocations();
    if (this.selected?.equipmentNumber) {
      this.store.dispatch(new GetEquipmentDetailAction(this.selected.equipmentNumber))
        .subscribe(() => {
          this.selectedDetail = this.store.selectSnapshot(EquipmentState.equipmentDetail);
        });
    }
    if (this.selected) {
      this.center = {
        lat: this.selected.newLocationInfo.latitude,
        lng: this.selected.newLocationInfo.longitude,
      };
      this.form.get('EquipmentSerialNumber').setValue(this.selected?.serialNumber);
    }
  }

  onClickMarker(marker: EquipmentModel | any) {
    if (!marker) {
      this.selected = null;
      return;
    }
    this.findAndSelectEquipment(marker.equipmentNumber);
    this.setLocations();
  }


  navigateToBack() {
    window.history.back();
  }

  onClickFilter() {
    this.isShowFilterModal = true;
  }

  onApplyFilter() {
    this.isShowFilterModal = false;
    this.onFilterOffices();
    if (this.locations.length > 0 && this.selectedCountry) {
      const { lat, lng } = this.locations[0];
      this.center = { lat, lng };
    }
  }

  onClearFilter() {
    this.selectedCountry = null;
  }

  onClickEditLocation() {
    if (this.selected?.equipmentNumber || this.selectedDetail?.equipmentNumber) {
      this.router
        .navigate([
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'equipment-update',
          this.selected?.equipmentNumber || this.selectedDetail?.equipmentNumber
        ])
        .then();
    }
  }

  iconClockVisible(workingDate, workingHours) {
    if (!workingDate || workingHours === 0) {
      return false;
    }
    const daysAgo =
      (new Date(Date.now()).getTime() -
        new Date(workingDate.split('T')[0]).getTime()) /
      (1000 * 3600 * 24);
    return daysAgo < this.hideWorkingHoursOlderThanDays;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
