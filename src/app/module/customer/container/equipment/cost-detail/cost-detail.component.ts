import { ChangeDetector<PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { EquipmentState } from '../../../state/equipment.state';
import { Observable, Subject } from 'rxjs';
import { CostCategoryListModel, CostListModel } from '../../../model/equipment.model';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { EquipmentCostCategoryListAction, GetEquipmentCostList, ResetEquipmentCostListAction } from '../../../action/equipment.action';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { ActivatedRoute } from '@angular/router';
import { EquipmentService } from '../../../service/equipment.service';
import { environment } from 'src/environments/environment';
import { LogService } from '../../../service/log.service';
import { CustomerModel } from '../../../model/customer.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';

@Component({
  selector: 'cat-cost-detail',
  templateUrl: './cost-detail.component.html',
  styleUrls: ['./cost-detail.component.scss']
})
export class CostDetailComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.equipmentCostCategoryList)
  equipmentCostCategoryList$: Observable<CostCategoryListModel[]>;
  @Select(EquipmentState.equipmentCostCategoryListLoading)
  equipmentCostCategoryListLoading$: Observable<boolean>;
  @Select(EquipmentState.equipmentCostList)
  equipmentCostList$: Observable<CostListModel[]>;
  @Select(EquipmentState.equipmentCostListLoading)
  equipmentCostListLoading$: Observable<boolean>;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  costDetailForm: FormGroup = new FormGroup({
    Cost: new FormControl(null, [Validators.required, Validators.max(9999999999), Validators.pattern('^[0-9]*$'), Validators.min(1)]),
    CostCategory: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, [Validators.pattern('^(?!\\s+$)[a-zA-Z0-9çğıiöşüÇĞIİÖŞÜ/-\\s.,]*$')]),
  });
  searchForm: FormGroup = new FormGroup({
    SearchText: new FormControl(null, [Validators.required]),
  });
  warningIcon = `${environment.assets}/warning.svg`;
  showAverageCostDetailForm = false;
  equipmentCostCategoryList: CostCategoryListModel[];
  equipmentCostList: CostListModel[];
  serialNumber: string;
  successSendForm = false;
  successDeleteCost = false;
  loading = false;
  deleteModalStatus = false;
  addCostModalStatus = false;
  costId: string;
  sorting = false;
  maxLength = 47;
  openedList: any[] = [];
  customer: CustomerModel;

  private subscriptions$: Subject<boolean> = new Subject();
  private searchInputSubject = new Subject<string>();

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly equipmentService: EquipmentService,
    private readonly ref: ChangeDetectorRef,
    private readonly logger: LogService,
  ) { }

  ngOnInit() {
    const { id } = this.route.snapshot.params;
    this.serialNumber = id;
    this.customer = this.store.selectSnapshot(LoginState.customer);
    this.store.dispatch(new EquipmentCostCategoryListAction());
    this.equipmentCostCategoryList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data){
          this.equipmentCostCategoryList = data;
        }
      });

    this.store.dispatch(new GetEquipmentCostList(this.serialNumber, ''));
    this.equipmentCostList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.equipmentCostList = data;
        }
      });

    this.searchInputSubject
      .pipe(debounceTime(500), takeUntil(this.subscriptions$))
      .subscribe((searchValue: string) => {
        this.store.dispatch(new GetEquipmentCostList(this.serialNumber, searchValue));
      });

    this.costDetailForm.get('CostCategory').valueChanges.subscribe(val => {
      if (val === 'Other') {
        this.costDetailForm.controls['Description'].setValidators([Validators.required]);
      } else {
        this.costDetailForm.controls['Description'].clearValidators();
      }
      this.costDetailForm.controls['Description'].updateValueAndValidity();
    });
  }

  onSubmitCostDetailForm(){
    if (!this.costDetailForm.valid){
      return validateAllFormFields(this.costDetailForm);
    }

    const { value } = this.costDetailForm;

    const formBody = {
      serialNumber: this.serialNumber,
      cost: value.Cost,
      description: value.Description,
      category: value.CostCategory,
      currency: 'EUR'
    };

    this.loading = true;

    this.equipmentService.addEquipmentAverageCost(formBody).subscribe(
      () => {
        this.logger
        .action('COST_DETAIL', 'ADD', {
          serialNumber: this.serialNumber
        })
        .subscribe();
        this.loading = false;
        this.addCostModalStatus = false;
        this.successSendForm = true;
        this.store.dispatch(new GetEquipmentCostList(this.serialNumber, ''));
        this.costDetailForm.reset();
      },
      () => {
        this.loading = false;
      }
    );
  }

  averageCostDetailFormStatus(){
    this.costDetailForm.reset();
    this.showAverageCostDetailForm = !this.showAverageCostDetailForm;
    this.successSendForm = false;

  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  navigateToBack() {
    window.history.back();
  }

  setDeleteModalStatus(costId?: string){
    this.deleteModalStatus = !this.deleteModalStatus;
    this.costId = costId;
  }

  deleteCost(){
    this.loading = true;

    this.equipmentService.deleteEquipmentAverageCost(this.costId).subscribe(
      () => {
        this.logger
        .action('COST_DETAIL', 'DELETE', {
          serialNumber: this.serialNumber
        })
        .subscribe();
        this.loading = false;
        this.successDeleteCost = true;
        this.store.dispatch(new GetEquipmentCostList(this.serialNumber, ''));
        this.deleteModalStatus = false;
      },
      () => {
        this.loading = false;
      }
    );
  }

  onSearchInputChange(e: any){
    const searchValue = e.target.value;
    this.searchInputSubject.next(searchValue);
  }

  sortCostData(){
    if (this.equipmentCostList.length){
      this.sorting = !this.sorting;
      const reversableCostList = [...this.equipmentCostList];
      this.equipmentCostList = reversableCostList.reverse();
    }
  }

  setAddCostModalStatus(){
    if (!this.costDetailForm.valid){
      return validateAllFormFields(this.costDetailForm);
    }

    this.addCostModalStatus = !this.addCostModalStatus;
  }

  setSuccessDeleteCostStatus(){
    this.successDeleteCost = !this.successDeleteCost;
  }

  openReadMore(cost: CostListModel) {
    if (!this.isOpened(cost?.id)) {
      this.openedList.push(cost?.id);
    } else {
      this.openedList = this.openedList.filter(data => data !== cost?.id);
    }
  }

  isOpened(id: string) {
    return !!this.openedList.filter(data => data === id)?.length;
  }

  truncatedDescription(cost: CostListModel){
    if(this.isOpened(cost?.id)){
      return cost?.description;
    }
    return cost?.description?.substring(0, this.maxLength);
  }


  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.store.dispatch(new ResetEquipmentCostListAction());
  }
}
