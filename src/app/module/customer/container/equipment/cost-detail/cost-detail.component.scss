.cost-detail-container {
  padding: 0 22px;
  background-color: #fafafa;

  .cost-detail-header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    .icon-back {
      font-size: 20px;
    }
  }

  .average-cost-container {
    .average-cost-header {
      .search-area {

        .search-input {
          padding-left: 2.5rem;
        }

        .icon-search {
          position: absolute;
          top: 50%;
          left: 1.5rem;
          transform: translate(-50%, -50%);
          color: #CFCFCF;
        }
      }

      .btn-sort {
        .icon-sort {
          font-size: 25px;
        }
      }

      .sorting {
        color: #4A8EB0;
      }
    }

  }

  .btn-plus {
    position: fixed;
    bottom: 2.5rem;
    right: 1.5rem;
    width: 65px;
    height: 65px;
    border-radius: 50% !important;
    display: flex;
    justify-content: center;
    align-items: center;

    &:focus {
      box-shadow: 0 !important;
    }

    .icon-plus {
      font-size: 24px;
      color: #fff;
    }
  }

  // &:has(.search-input:focus){
  //     .btn-plus{
  //         display: none;
  //     }
  // }

  .cost-list-container {
    .cost-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      height: 78vh;
      overflow: auto;
      padding-bottom: 7rem;

      &::-webkit-scrollbar {
        display: none;
      }

      .cost-info {
        border-radius: .25rem;
        background-color: #fff;
        color: #505050;
        box-shadow: 0px 4px 4px 0px #0000000F;

        .cost-description-content {
          .cost-category {
            max-width: 140px;
            word-break: break-all;
            font-weight: 400;
            color: #505050;
          }
        }

        // .description{

        //     .description-text{
        //         word-break: break-all;
        //     }
        // }

        .cost-price-content {
          display: flex;
          align-items: flex-start;
          gap: .5rem;

          .price {
            color: #8C8C8C;
            font-weight: 500;
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
          }

          .trash-btn {
            .icon-delete {
              font-size: 18px;
              color: #6F6F6F;
            }
          }
        }
      }
    }
  }

  .add-average-cost-form {
    .ng-select ::ng-deep .ng-select-container {
      border-radius: 6px;
      border: 1px solid #D7E5EA;
    }

    .cost-category-form-group:has(.ng-dropdown-panel-items) {
      .ng-select ::ng-deep .ng-select-container {
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
      }

    }

    .cost-input-container {
      position: relative;

      .euro-icon {
        position: absolute;
        top: 50%;
        right: 1rem;
        transform: translate(-50%, -50%);
        font-weight: bold;
        color: #B0B0B0;
        font-size: 18px;
      }
    }
  }
}

.btn {
  &:focus {
    box-shadow: unset !important;
  }
}

.after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: #FAFAFA;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.success-message {
  font-size: 26px;
  font-weight: 700;
}

.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mt-8 {
  margin-top: 8rem;
}

.ucnokta {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
