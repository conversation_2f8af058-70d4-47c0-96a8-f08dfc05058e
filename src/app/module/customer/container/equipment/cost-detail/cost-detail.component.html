<div class="cost-detail-container">
  <ng-container *ngIf="!showAverageCostDetailForm">
    <div class="h4 pt-4 mb-0 text-nowrap  font-weight-bold cost-detail-header">
      <i (click)="navigateToBack()" class="icon mr-4 icon-back"></i>
      {{ "_equipment_cost_detail" | translate }}
    </div>

    <div class="average-cost-container">
      <div class="average-cost-header mt-2">
        <form [formGroup]='searchForm' class="w-100">
          <div class="search-area position-relative my-3 d-flex align-items-center">
            <input
              #search
              [placeholder]="'_search' | translate"
              class="form-control search-input"
              type="text"
              formControlName="SearchText"
              (input)="onSearchInputChange($event)"
              [dir]="'ltr'"
            />
            <span class="d-flex justify-content-center">
              <i class="icon icon-search"></i>
            </span>
          </div>
        </form>
        <button
          class="btn btn-sort p-0 d-flex align-items-center pl-2 mb-2 font-weight-bold"
          [ngClass]="{'sorting': sorting}"
          (click)="sortCostData()"
        >
          {{ '_sort_by_date' | translate }}
          <i class="icon icon-sort ml-2" [ngClass]="{'sorting': sorting}"></i>
        </button>
      </div>
    </div>

    <div class="cost-list-container" *ngIf="equipmentCostList?.length">
      <div class="cost-list">
        <div class="cost-info p-3" *ngFor="let cost of equipmentCostList" [ngClass]="{ 'expanded': isOpened(cost?.id), 'collapsed': !isOpened(cost?.id) }">
          <div class="d-flex align-items-center justify-content-between">
            <div class="cost-description-content font-weight-normal d-flex flex-column">
              <span class="cost-category">
                {{ cost?.category }}
              </span>
              <span class="font-size-13px">
                {{ cost?.createdDate | date: "dd.MM.yyyy"}}
              </span>
            </div>
            <div class="cost-price-content">
              <span class="price text-nowrap">
                {{ cost?.cost | number }}
                <i class="icon icon-euro"></i>
              </span>
              <button (click)="setDeleteModalStatus(cost?.id)" class="trash-btn btn p-0" *ngIf="cost?.isDeletable">
                <i class="icon icon-delete"></i>
              </button>
          </div>
          </div>
          <div class="description mt-3" *ngIf="cost?.description">
            <div class="font-size-14px font-weight-bold">
              {{ "_description" | translate }}
            </div>
              <div class="mb-0 font-size-13px text-break ucnokta" (click)="openReadMore(cost)" [ngStyle]="{
                'white-space': isOpened(cost?.id) ? 'unset' : 'nowrap'
              }">
                {{ cost.description }}
                  <div class="text-info" *ngIf="cost?.description?.length > maxLength">
                   {{ (isOpened(cost?.id) ? '_show_less' : '_show_more') | translate }}
                  </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="!showAverageCostDetailForm">
    <div class="d-flex flex-column align-items-center mt-8"  *ngIf="!equipmentCostList.length && (!(equipmentCostCategoryListLoading$ | async) || !(equipmentCostListLoading$ | async))">
      <img [src]="warningIcon" alt="warning-icon" width="40" height="40">
      <span class="font-weight-bold pt-2">{{ '_not_found_equipment_cost_list' | translate }}</span>
    </div>

    <button class="btn btn-plus btn-warning text-white rounded-lg" (click)="averageCostDetailFormStatus()">
      <i class="icon icon-plus"></i>
    </button>
  </ng-container>


  <div class="add-average-cost-form" *ngIf="showAverageCostDetailForm && !successSendForm">
    <div class="h4 py-4 mb-0 font-weight-bold text-nowrap cost-detail-header">
      <div>
        <i (click)="averageCostDetailFormStatus()" class="icon mr-4 icon-back"></i>
      </div>
      {{ '_add_average_cost' | translate }}
    </div>
    <form [formGroup]="costDetailForm">
      <div class="form-group cost-category-form-group">
        <ng-select
          [searchable]="false"
          [placeholder]="'_cost_category' | translate"
          [clearable]="false"
          [dropdownPosition]="'bottom'"
          formControlName="CostCategory"
        >
          <ng-option
            *ngFor="let costCategory of equipmentCostCategoryList"
            [value]="costCategory?.categoryTag"
          >
            {{ costCategory?.localizedCategoryName }}
          </ng-option>
        </ng-select>
        <div
          [ngClass]="{ 'd-block': isShowError(costDetailForm.controls.CostCategory) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(costDetailForm.controls.CostCategory) | translate }}
        </div>
      </div>

      <div class="form-group">
        <div class="cost-input-container">
          <input
            catInputLength
            [name]="'Cost'"
            [placeholder]="'_cost' | translate"
            class="form-control form-control pr-5"
            formControlName="Cost"
            type="tel"
            (input)="onInputPhone($event)"
            inputmode="numeric"
          />
          <span class="euro-icon">
            <i class="icon icon-euro"></i>
          </span>
        </div>
        <div
          [ngClass]="{ 'd-block': isShowError(costDetailForm.controls.Cost) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(costDetailForm.controls.Cost) | translate }}
        </div>
      </div>

      <div class="form-group">
        <textarea
          catInputLength
          [name]="'Description'"
          [placeholder]="'_description' | translate"
          [rows]="4"
          class="form-control textarea-form-element"
          formControlName="Description"
          minlength="3"
          maxlength="30"
          style="resize: none;"
        ></textarea>
        <div
          [ngClass]="{ 'd-block': isShowError(costDetailForm.controls.Description) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(costDetailForm.controls.Description) | translate }}
        </div>
      </div>
      <button
        class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
        type="button"
        (click)="setAddCostModalStatus()"
      >
        {{ '_add' | translate }}
      </button>
    </form>
  </div>
</div>
<cat-loader [show]="(equipmentCostCategoryListLoading$ | async) || (equipmentCostListLoading$ | async) || loading"></cat-loader>
<div *ngIf="successSendForm" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_success_add_cost_message" | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="averageCostDetailFormStatus()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>

<cat-basic-modal [(status)]="deleteModalStatus">
  <div class="d-flex flex-column px-4 py-3">
    <p class="text-center">
      {{ '_delete_cost_text' | translate }}
    </p>
    <div class="action-btns row m-0">
      <button (click)="setDeleteModalStatus()" class="col btn btn-sm btn-secondary text-white mr-3">
        {{ '_cancel' | translate }}
      </button>
      <button (click)="deleteCost()"  class="col btn btn-sm btn-danger text-white">
        {{ '_confirm' | translate }}
      </button>
    </div>
  </div>
</cat-basic-modal>
<cat-basic-modal [(status)]="addCostModalStatus">
  <div class="d-flex flex-column px-4 py-3">
    <p class="text-center">
      {{ '_add_cost_text' | translate }}
    </p>
    <div class="action-btns row m-0">
      <button (click)="setAddCostModalStatus()" class="col btn btn-sm btn-secondary text-white mr-3">
        {{ '_cancel' | translate }}
      </button>
      <button (click)="onSubmitCostDetailForm()"  class="col btn btn-sm btn-warning text-white">
        {{ '_confirm' | translate }}
      </button>
    </div>
  </div>
</cat-basic-modal>
<!-- <cat-success-modal [(status)]="successDeleteCost" [message]="'_success_delete_cost_message'"></cat-success-modal> -->
<div *ngIf="successDeleteCost" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_success_delete_cost_message" | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="setSuccessDeleteCostStatus()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
