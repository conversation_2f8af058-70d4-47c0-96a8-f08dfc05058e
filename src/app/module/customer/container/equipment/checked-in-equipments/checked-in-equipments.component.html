<div class="px-3 pt-4" *ngIf="checkedInEquipments.length">
  <div class="equipment-card" *ngFor="let equipment of checkedInEquipments">
    <div class="equipment-card-top d-flex align-items-center justify-content-between">
      <div>
        <div class="h6">
          {{ equipment?.brand}} {{ equipment?.model}}
        </div>
        <span class="font-size-13px">
          {{ equipment?.serialNumber }}
        </span>
      </div>
      <i class="icon icon-boom-logo"></i>
    </div>
    <div class="equipment-card-bottom border-top">
      <div class="content row">
        <div class="col working-hours d-flex align-items-center pr-3 border-right font-size-14px">
          <i class="icon icon-clock mr-2"></i>
          {{ getWorkingHours(equipment?.checkInDate)}}  {{ '_hour' | translate }}
        </div>
        <button (click)="openCheckoutModal()" class="col btn p-0 d-flex align-items-center pl-4 font-size-14px">
          <i class="icon icon-contract mr-2"></i>
          {{ '_check_out' | translate }}
        </button>
      </div>
    </div>
    <cat-basic-modal [(status)]="showModal">
      <div class="after-form-send d-flex flex-column px-4 py-3">
        <p class="text-center">
          {{ user?.id === equipment?.checkinUserId 
            ? ("_self_check_out_info" | translate) 
            : replaceCheckInText(("_check_out_info" | translate), equipment?.shortName)
          }}
        </p>
        <div class="action-btns row m-0">
          <button (click)="closeCheckoutModal()" class="col btn btn-sm btn-secondary text-white mr-3">
            {{ '_cancel' | translate }}
          </button>
          <button (click)="checkoutEquipment(equipment)" class="col btn btn-sm btn-warning text-white">
            {{ '_confirm' | translate }}
          </button>
        </div>
      </div>
    </cat-basic-modal>
  </div>
</div>
<cat-loader [show]="loading"></cat-loader>
<div *ngIf="sendStatus" class="send-info">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ '_success_check_out_message' | translate }}
    </div>
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="closeCheckoutModal()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>