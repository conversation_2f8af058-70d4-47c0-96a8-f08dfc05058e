.equipment-card{
    border-radius: 6px;
    box-shadow: 0px 6px 4px 0px #00000008;
    background-color: #fff;
    border: 1px solid #eeeeee;

    .equipment-card-top{
        padding: 12px 16px;

        .icon-boom-logo{
            font-size: 30px;
        }
    }

    .equipment-card-bottom{
        border-top: 1px solid #eeeeee;

        .content{
            padding: 12px 16px;


            .icon-contract, .icon-clock{
                font-size: 18px;
            }
        }
    }
}

.send-info {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: #FAFAFA;
    z-index: 1041;
  }
  
  .after-form-send-content {
    position: absolute;
    left: 0;
    top: 50%;
    width: 100vw;
    transform: translateY(-50%);
  }
  .success-message{
    font-size: 26px;
    font-weight: 700;
  }
  .icon-message-success {
    font-size: 60px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }