import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { EquipmentState } from '../../../state/equipment.state';
import { takeUntil } from 'rxjs/operators';
import { GetCheckedInEquipmentsAction } from '../../../action/equipment.action';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { LogService } from '../../../service/log.service';
import { EquipmentService } from '../../../service/equipment.service';

@Component({
  selector: 'cat-checked-in-equipments',
  templateUrl: './checked-in-equipments.component.html',
  styleUrls: ['./checked-in-equipments.component.scss']
})
export class CheckedInEquipmentsComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.checkedInEquipments)
  checkedInEquipments$: Observable<any[]>;

  checkedInEquipments: any[] = [];
  user: UserModel;
  showModal = false;
  loading = false;
  sendStatus = false;

  constructor(
    private readonly store: Store,
    private readonly log: LogService,
    private readonly equipmentService: EquipmentService
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {

    this.store.dispatch(new GetCheckedInEquipmentsAction());
    this.checkedInEquipments$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (!data) {
          return;
        }
        this.checkedInEquipments = data;
      });

    this.user = this.store.selectSnapshot(LoginState.user);
  }


  checkoutEquipment(equipment) {
    this.showModal = true;
    this.loading = true;

    const formBody = {
      EquipmentAgendaId: equipment.equipmentAgendaId,
      SerialNumber: equipment.serialNumber,
      FirstName: this.user?.firstName,
      LastName: this.user?.lastName,
      UseEndDate: new Date(),
      Description: '',
    };

    this.log
      .action(
        LogSectionEnum.EQUIPMENT,
        'ADD_EQUIPMENT_AGENDA_CHECK_OUT',
        formBody
      ).subscribe();

    this.equipmentService.equipmentAgendaCreateCheckOut(formBody).subscribe(
      () => {
        this.sendStatus = true;
        this.loading = false;
        this.showModal = false;
        this.store.dispatch(new GetCheckedInEquipmentsAction());
      },
      () => {
        this.sendStatus = false;
        this.loading = false;
        this.showModal = false;
      }
    );
  }

  replaceCheckInText(text, name) {
    return text.replace('X', `${name}`);
  }

  getWorkingHours(date) {
    const now = new Date();
    const checkedInDate = new Date(date);
    const ms = now.getTime() - checkedInDate.getTime();

    return Math.round(ms / 3600000);
  }

  openCheckoutModal() {
    this.showModal = true;
  }

  closeCheckoutModal() {
    this.showModal = false;
    this.sendStatus = false;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
