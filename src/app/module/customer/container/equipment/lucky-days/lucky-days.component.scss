@import "variable/bootstrap-variable";
.PSECampaign {
  font-style: normal;
  line-height: normal;
  height: calc(96vh - 206px);
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  .header {
    i {
      margin: 0 auto;
    }
    &-text {
      margin: 10px 0px 10px 0px;
      font-size: 19px;
      font-weight: 700;
      line-height: 18px;
      letter-spacing: 0px;
      text-align: center;
    }
  }
  .content{
    .products-container{
      overflow-y: auto;
      border-bottom: 1px solid #d7d7d7;
      height: calc(100vh - 288px);
    }
    margin: 0 auto;
    width: 100%;
    &-text {
      font-size: 12px;
      font-weight: 700;
      line-height: 21px;
      letter-spacing: 0px;
      text-align: center;
    }
  }
  .products {
    width: 98%;
    margin-top: 15px;
    flex-direction: column;
    &-body {
      .products-body-container{
        width: 89%;
      }
      border-radius: 8px;
      background: #f3f3f38a;
      width: 100%;
      height: 105px;
      display: flex;
      justify-content: space-between;
      // box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
      .products-image {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        background: white;
        width: 114px;
        height: 104px;
        position: relative;
        overflow: hidden;
        img {
          object-fit: contain;
          width: 100%;
          height: 100%;
        }
      }
      .buy-button{
        width: 36px;
        background: #FFB025;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        height: auto;
        img {
          height: 30px;
          width: 30px;
        }
      }

    }
    &-price{
      width: 92%;
      .prices{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 2px;
        height: 41px;
        .price{
          width: 100%;
          display: flex;
          flex-direction: column-reverse;
        }
        .discount-rate {
          color: #FFF;
          font-size: 14.111px;
          font-weight: 600;
          padding: 5px;
          border-radius: 4px;
          background: #FFA300;
          height: max-content;
          margin-right: 10px;
        }
      }
      .old-price{
        display: flex;
        justify-content: space-between;
        s {
          color: #7A7A7A;
          font-size: 15px;

          font-weight: 300;
        }
      }
      .new-price{
        color: #2C2C2C;
        font-size: 17px;
        font-weight: 700;
      }
    }
    &-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 6px;
      .name {
        padding-top: 6px;
        color: #505050;
        font-size: 14px;
        font-weight: 400;
        width: 100%;
        white-space: break-spaces;
        overflow: hidden;
        /* text-overflow: ellipsis; */
        height: 53px;
        .pse-skeleton-loader {
          width: 90%;
        }
      }
    }
  }
  .footer{
    margin: 0 auto;
    position: absolute;
    bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    .buy-button{
      text-align: center;
      .toMyBasketText{
        color: #4A8EB0;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
      }
      .myBasketText{
        color: #2C2C2C;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
      }
    }
  }
  .my-basket {
    max-height: 477px !important;
    overflow-y: auto !important;
    height: 571px !important;
  }
}

::ng-deep .PSECampaign .full-screen {
  position: absolute;
}

.pse-input-area{
  display: flex;
  button {
    height: 34px;
    width: 25px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 900;
  }
  .product-quantity {
    width: 30%;
    height: 34px;
    background: white;
    border: 1px solid #4A8EB0;
    border-radius: 1px;
    color: #4A8EB0;
    font-size: 1rem;
    font-weight: 800;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .remove-product{
    border-radius: 4px;
    border: 1px solid #BABABA;
    width: 47%;
    margin-left: 9px;
  }
  .increment-button {
    margin-right: -1px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .decrement-button {
    margin-left: -1px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
.total-price{
  margin-top: 10px;
  .total{
    display: flex;
    justify-content: space-between;
    width: 88%;
    margin: 0 auto;
    &-text{
      color: #979797;
      font-size: 14px;
      font-weight: 500;
    }
    &-value{
      color: #303030;
      font-size: 14px;
      font-weight: 700;
    }
  }
}

.inCart {
  background: #36974b !important;
  pointer-events: none;
}

.pointer-events-none {
  pointer-events: none;
}

.price-not-found{
  color: #2C2C2C;
  font-size: 14px;
  font-weight: 500;
}

.search-input:focus {
  box-shadow: none;
}

.search-input{
  width: 97%;
}

.categories{
  overflow-x: auto;
  overscroll-behavior-x: contain;
  width: 90%;
}
.categories-container{
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  align-items: center;
  text-align: center;
  width: max-content;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
  border: none;
}
.categories-content {
  width: auto;
  display: flex;
  justify-content: center;
}
.categories-container .categories-content:last-child::after {
  content: '';
  position: absolute;
  width: 2rem;
  right: -2rem;
  opacity: 0;
  z-index: 9999;
}
 .text-container {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  color: grey;
  opacity: 0.5;
  display: flex;
  justify-content: center;
}
.activeCategory{
  color: #4A8EB0 !important;
  opacity: 1;
}
.categories-content  span {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: #4A8EB0;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  z-index: 9999;
}
.search-area {
  max-width: 100%;
  input {
    padding-left: 47px;
    // margin-left: 5px;
    width: 100%;
  }
  .icon-search {
    position: absolute;
    left: 20px;
    //top: 50%;
    top: 20px;
    font-size: 18px;
    line-height: 18px;
    height: 18px;
    margin-top: -9px;
    width: 30px;
  }
  .icon-filter {
    color: #4A8EB0 !important;
  }
  .icon-filter-area {
    width: 30px;
    height: auto;
  }
}
@media (min-width: 560px) {
  .search-area {
    width: 100%;
    input {
      padding-left: 47px;
      margin-left: 5px;
      width: 95%;
    }
  }
  .categories{
    overflow-x: hidden;
    overscroll-behavior-x: contain;
    width: 90%;
  }
  // .categories-container {
  //   justify-content: center;
  // }
}

::ng-deep .products-container .search-area input{
  height: 39px;
}
.emptyList {
  .content-text {
    font-size: 12px;
  }
}
