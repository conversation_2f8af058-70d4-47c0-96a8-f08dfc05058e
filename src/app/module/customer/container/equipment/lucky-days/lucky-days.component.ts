import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { environment } from 'src/environments/environment';
import { EquipmentState } from '../../../state/equipment.state';
import { interval, Observable, Subject, Subscription, } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import {
  ClearEquipmentPSEAction,
  EquipmentPSESearchAction,
  GetEquipmentHasPSEAction,
  PostEquipmentPSEAction
} from '../../../action/equipment.action';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { LogSectionEnum } from '../../../../definition/enum/log-section.enum';
import { LogService } from '../../../service/log.service';

@Component({
  selector: 'cat-lucky-days',
  templateUrl: './lucky-days.component.html',
  styleUrls: ['./lucky-days.component.scss']
})
export class LuckyDaysComponent implements OnInit, OnDestroy {

  constructor(
    private store: Store,
    private frameService: MessageFrameService,
    private readonly ref: ChangeDetectorRef,
    private readonly logger: LogService,
  ) { }

  loading: boolean = false;

  @Select(EquipmentState.equipmentPSE)
  equipmentPSE$: Observable<any>;

  @Select(EquipmentState.equipmentPSELoading)
  equipmentPSELoading$: Observable<boolean>;

  @Input()
  requestId: string;

  pollingStopped: boolean;
  @ViewChild('list') listElement: ElementRef;

  PermissionEnum = PermissionEnum;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  buyButton = environment.assets + '/add_to_basket.svg';
  toBasket = environment.assets + '/to_basket.svg';
  myBasket = environment.assets + '/my_basket.svg';
  greenPlus = environment.assets + '/green-buy-button-plus.svg';
  orangePlus = environment.assets + '/orange-buy-button-plus.svg';
  sparePartDummy = environment.assets + '/spare-part-dummy.png';
  inCartButton = environment.assets + '/inCartButton.svg';
  noProductFound = environment.assets + '/out-of-stock.png';

  error: boolean;
  IsAlreadyInCart: boolean;
  private pollingInterval = 2000;
  private pollingSubscription: Subscription | undefined;
  equipmentPSE: any;
  equipmentPSELoading: boolean;
  currentCustomer: any;
  pse: any;
  AllProducts: any[] = [];
  selectedProduct: any[] = [];
  isToBasket: boolean;
  basketItems: any[] = [];
  subTotal: number = 0;
  total: number = 0;
  paging: any = {
    pageSize: 10,
    pageNumber: 1,
    pageIndex: 0,
  };
  index = 0;
  moreData = 0;
  successFullyAdded: boolean;
  addedList = {};
  campaignProducts: any[] = [];
  searchValue: any[] = [];
  enableSearchArea: boolean;
  filterValue: string = '_all';
  categoryTypes: any[];

  @ViewChild('tabContainer') tabContainer: ElementRef;
  tabContainerWidth: number;

  form = new FormGroup({
    productCode: new FormControl('', [Validators.minLength(4)]),
  });

  groupedFilters: any = {
    'All': [],
  };
  filteredProducts: any[];
  @Output()
  errorPse: EventEmitter<boolean> = new EventEmitter<boolean>();
  // stopIt: EventEmitter<boolean> = new EventEmitter<boolean>();
  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.startPolling(this.requestId);
    this.currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);
    this.equipmentPSELoading$.subscribe(i => (this.equipmentPSELoading = i));
    this.loading = true;
    this.equipmentPSE$.subscribe((equipmentPSE) => {
      this.AllProducts = equipmentPSE?.campaignProducts;
      if (this.AllProducts?.length) {
        this.groupFilters();
        this.loading = false;
      }
    });
  }

  groupFilters() {
    this.groupedFilters = this.AllProducts.reduce((acc, product) => {
      const categoryKey = product.categoryName;
      if (!acc[categoryKey]) {
        acc[categoryKey] = [];
      }
      acc[categoryKey].push(product);
      return acc;
    }, { _all: this.AllProducts });
    this.setFilter('_all');
  }

  setFilter(filter: string) {
    this.filteredProducts = this.groupedFilters[filter];
    this.filterValue = filter;
    this.searchValue = [];

    this.logger
      .action(LogSectionEnum.PSE_CAMPAIGN, 'PSE_CATEGORY_SELECTED', {
        categoryTag: this.filteredProducts?.find(Boolean)?.categoryTag,
        categoryName: filter,
        requestId: this.requestId,
      })
      .subscribe();
  }

  getCategoryKeys(): string[] {
    return Object.keys(this.groupedFilters);
  }

  groupedFiltersClear() {
    this.groupedFilters = {};
  }

  startPolling(requestId): void {
    let i = 0;
    this.pollingSubscription = interval(this.pollingInterval)
      .pipe(startWith(0))
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        i += 1;
        this.store.dispatch(new GetEquipmentHasPSEAction(requestId));
      });

    this.equipmentPSE$.subscribe((equipmentPSE) => {
      this.equipmentPSE = equipmentPSE;
      if (this.equipmentPSE?.status === 1 || this.equipmentPSE?.status === 2) {
        this.stopPolling();
        this.pollingStopped = true;
        this.filterAllData();
      }
      if ((this.equipmentPSE?.status === 0) && i === 30) {
        this.stopPolling();
        this.pollingStopped = true;
        this.filterAllData();
      }
    });
  }

  stopPolling(): void {
    this.pollingSubscription.unsubscribe();
    this.tabContainerWidth = this.tabContainer?.nativeElement?.scrollWidth;
    this.ref.detectChanges();
  }

  filterAllData() {
    this.AllProducts = this.equipmentPSE?.campaignProducts?.filter(item => item?.discountedPrice !== null);
    if (!this.AllProducts?.length) {
      this.loading = false;
      this.error = true;
    }
  }

  isAvailable(product) {
    if (product?.amountInCart === product?.cartLimit) {
      this.alreadyInCart(product);
      return;
    } else if (this.addedList[product.productCode] || product.amountInCart > 0) {
      this.alreadyInCart(product);
      return;
    } else {
      this.addToCart(product);
    }
  }

  errorPseFunc() {
    this.errorPse.emit(true);
  }

  selectProduct(product) {
    this.selectedProduct.push(product);
    const groupedProducts = this.selectedProduct.reduce((result, product) => {
      const { ProductCode } = product;
      if (result[ProductCode]) {
        // Grup zaten varsa, ürün sayısını artır
        result[ProductCode].quantity++;
      } else {
        // Yoksa yeni bir grup oluştur
        result[ProductCode] = {
          ...product,
          quantity: 1
        };
      }
      return result;
    }, {});
    this.basketItems = Object.values(groupedProducts);
    this.calculateTotal();
  }

  openSparePartOrderDetail() {
    this.stopPolling();
    this.frameService.sendMessage(FrameMessageEnum.openStore, {
      url: 'borusancat360://open_module?moduleCode=sparePart&query=page%3Dcart'
    });
    return;

  }

  alreadyInCart(product) {
    this.IsAlreadyInCart = true;
  }

  toBasketFunc() {
    this.isToBasket = false;
  }

  myBasketFunc() {
    this.isToBasket = true;
  }

  incrementOffer(product) {
    const currentPiece = product.quantity;
    if (currentPiece < product.CardLimit) {
      this.selectedProduct.push(product);
      const newPiece = currentPiece + 1;
      product.quantity = newPiece;
      this.calculateTotal();
    }
  }

  decrementOffer(product) {
    const currentPiece = product.quantity;
    const newPiece = currentPiece - 1 < 0 ? 0 : currentPiece - 1;
    product.quantity = newPiece;
    if (newPiece === 0) {
      this.removeProduct(product);
    } else {
      // Öğenin bulunduğu dizini findIndex ile bulup çıkarma işlemi
      const index = this.selectedProduct.findIndex((item) => item === product);
      if (index !== -1) {
        this.selectedProduct.splice(index, 1);
      }
    }

    this.calculateTotal();
  }

  removeProduct(product) {
    this.basketItems = this.basketItems.filter(item => item.ProductCode !== product.ProductCode);
    this.selectedProduct = this.selectedProduct.filter(item => item.ProductCode !== product.ProductCode);
    this.calculateTotal();
  }

  isOnBasket(product) {
    return this.basketItems.find(item => item.ProductCode === product.ProductCode);
  }

  calculateTotal() {
    this.total = 0;
    this.subTotal = 0;
    this.selectedProduct.forEach(item => {
      this.subTotal += item.BasePrice;
      this.total += item.DiscountedPrice;
    });
  }

  addToCart(product) {
    this.store.dispatch(new PostEquipmentPSEAction(product.productCode, '1', this.requestId))
      .pipe(map(() => this.store.selectSnapshot(EquipmentState.equipmentPSEAddToCartResponse)))
      .subscribe((state: any) => {
        this.successFullyAdded = state;
        this.addedList[state.productCode] = true;
      });
    // this.selectProduct(product);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.stopPolling();
    this.store.dispatch(new ClearEquipmentPSEAction());
    this.groupedFiltersClear();
  }

  onSearchInputChange() {
    if (!this.searchValue) {
      this.campaignProducts = [];
      this.filterValue = '_all';
    } else if (this.searchValue.length > 3) {
      this.loading = true;
      this.filterValue = null;
      this.PSESearch(this.searchValue);
    }
  }
  PSESearch(productCode) {
    this.loading = true;
    this.store.dispatch(new EquipmentPSESearchAction(productCode, this.requestId))
      .pipe(map(() => this.store.selectSnapshot(EquipmentState.PSESearchResult)))
      .subscribe((state: any) => {
          if (state.status == 1) {
            this.loading = false;
            this.campaignProducts = state.campaignProducts;
          }
        },
        error => {
          console.log(error);
          this.loading = false;
          this.error = true;
        },
      );
    // this.selectProduct(product);
  }

}
