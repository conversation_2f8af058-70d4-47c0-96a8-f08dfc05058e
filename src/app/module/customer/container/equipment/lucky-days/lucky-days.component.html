<div class="PSECampaign">
  <div class="PSECampaign-container">
    <!-- <div class="header d-flex justify-content-center flex-column" *ngIf="!isToBasket"> -->
    <!-- <i class="icon icon-ladybird text-danger" style="font-size: 40px;"></i> -->
    <!-- <div class="header-text">{{ '_pse_title' | translate }}</div> -->
    <!-- </div> -->
    <div class="content" *ngIf="AllProducts">
      <ng-container *ngIf="!isToBasket">
        <div class="products-container">
          <div class="content-text" *ngIf="!isToBasket">
            {{ '_pse_description' | translate }}
          </div>
          <form [formGroup]='form'>
            <div class="search-area position-relative my-3 d-flex align-items-center">
              <input #search [placeholder]="'_search' | translate" class="form-control search-input" type="text"
                formControlName="productCode" placement="bottom" [ngbTooltip]="'_search_min_length_3' | translate"
                triggers="none" #t="ngbTooltip" [(ngModel)]="searchValue" (input)="onSearchInputChange()"
                [dir]="'ltr'"/>
              <span class="d-flex justify-content-center">
                <i class="icon icon-search"></i>
              </span>
            </div>
            <div
            [ngClass]="{ 'd-block': isShowError(form.controls.productCode) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.productCode) | translate }}
          </div>
          </form>

          <div class="col-12 categories mx-auto mt-3 p-2" *ngIf="groupedFilters?._all?.length">
            <div #tabContainer class="categories-container d-flex align-items-center justify-content-around">
              <ng-container *ngFor="let category of getCategoryKeys()">
                <div class="categories-content px-3">
                  <div class="text-container" *ngIf="groupedFilters[category]?.length"
                    [ngClass]="{ activeCategory: category === filterValue }" (click)="setFilter(category)">
                    {{ category | translate }}
                  </div>
                  <span *ngIf="category === filterValue" style="margin-top: 28.2px"></span>
                </div>
              </ng-container>
            </div>
            <hr style="min-width: 100%" [style.width.px]="tabContainerWidth" />
          </div>

          <ng-container
            *ngIf="AllProducts.length > 0 && !campaignProducts.length  && searchValue.length <= 3 && !loading && this.filterValue === 'All'">
            <ng-container *ngTemplateOutlet="products; context: {products: AllProducts}">
            </ng-container>
          </ng-container>

          <ng-container *ngIf="campaignProducts?.length && !loading && !filterValue && searchValue?.length > 3">
            <ng-container *ngTemplateOutlet="products; context: {products: campaignProducts}">
            </ng-container>
          </ng-container>

          <ng-container
            *ngIf="!campaignProducts?.length && !loading && this.filterValue && searchValue.length <= 3">
            <ng-container *ngTemplateOutlet="products; context: {products: filteredProducts}">
            </ng-container>
          </ng-container>

          <ng-container
            *ngIf="!loading && (!campaignProducts?.length || !AllProducts?.length) && !error && !filterValue">
            <ng-container *ngTemplateOutlet="emptyList">
            </ng-container>
          </ng-container>

          <ng-template #products let-products="products">
            <ng-container *ngIf="!loading">
              <div class="products" *ngFor="let product of products;">
                <div v class="products-body my-2" #list>
                  <div class="d-flex products-body-container">
                    <div class="products-image">
                      <img *ngIf="!product?.imageUrl" [src]="sparePartDummy" alt="product-image"
                        style="filter: contrast(0.5); width: 87%;">
                      <img *ngIf="product?.imageUrl" [src]="product?.imageUrl" alt="product-image" height="100%"
                        width="100%">
                    </div>
                    <div class="products-price ml-2">
                      <div class="products-info">
                        <div class="name">
                          <div class="d-flex w-100">
                            <div>
                              <span style="width: max-content" *ngIf="product?.productCode">
                                {{product?.productCode}}
                              </span>
                              <div class="pse-skeleton-loader" style="width: 112px;" *ngIf="!product?.productCode">
                                <cat-skeleton-loader></cat-skeleton-loader>
                              </div>
                            </div>

                            <div>
                              <span style="width: max-content;" *ngIf="product?.productName">
                                {{product?.productName}}
                              </span>
                              <div class="pse-skeleton-loader" style="width: 112px;" *ngIf="!product?.productName">
                                <cat-skeleton-loader></cat-skeleton-loader>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="prices">
                        <div class="price">
                          <div class="pse-skeleton-loader" style="width: 94%;"
                            *ngIf="!product?.discountedPrice && !pollingStopped">
                            <cat-skeleton-loader></cat-skeleton-loader>
                          </div>
                          <div class="new-price" *ngIf="product?.discountedPrice">
                            {{product?.discountedPrice}} <span *ngIf="product?.currency">{{product?.currency}}</span>
                          </div>
                          <div class="price-not-found" *ngIf="!product?.discountedPrice && pollingStopped">
                            {{'_price_not_found' | translate}}
                          </div>
                          <div class="pse-skeleton-loader" style="width: 94%;" *ngIf="!product?.basePrice">
                            <cat-skeleton-loader></cat-skeleton-loader>
                          </div>
                          <div class="old-price" *ngIf="product?.basePrice && product?.discountPercentage">
                            <s>{{product?.basePrice}} <span *ngIf="product?.currency">{{product?.currency}}</span></s>
                          </div>
                        </div>
                        <div class="discount-rate" *ngIf="product?.discountPercentage">
                          {{product?.discountPercentage}}%
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="buy-button" (click)="isOnBasket(product)?.quantity ? isOnBasket(product).quantity < product.CardLimit ? selectProduct(product) : null : selectProduct(product)"> -->
                    <!-- <img [src]="buyButton" alt="buy-button" height="100%" width="100%"> -->
                  <!-- </div> -->
                  <div class="buy-button" [ngClass]="{
                    'inCart': addedList[product.productCode] || product.amountInCart > 0,
                    'pointer-events-none': !product?.discountedPrice
                  }" catUserClick [section]="'PSE_CAMPAIGN'" [subsection]="'ADD_TO_CART'" (click)="isAvailable(product)">
                    <div [id]="product?.productCode" alt="buy-button" height="100%" width="100%"  >
                      <img [src]="addedList[product.productCode] ? inCartButton : product.amountInCart > 0 ? inCartButton : orangePlus" alt="">
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-template>

          <ng-template #emptyList>
            <div class="emptyList">
              <div class="products h-100 d-flex align-items-center justify-content-center">
                <img [src]="noProductFound" width="75" [alt]="('_no_products' | translate)">
                <span class="content-text">{{'_no_products' | translate}}</span>
              </div>
            </div>
          </ng-template>

        </div>
      </ng-container>

      <!-- ! sepetteki ürünler, bunlar ileride lazım olabilir -->
      <!-- <div class="selectedProducts" *ngIf="isToBasket"> -->
        <!-- <div class="products-container my-basket"> -->
          <!-- <div class="products" *ngFor="let product of basketItems"> -->
            <!-- <div class="products-body my-2"> -->
              <!-- <div class="d-flex products-body-container"> -->
                <!-- <div class="products-image"> -->
                  <!-- <img [src]="product.ImageUrl" alt="buy-button"> -->
                <!-- </div> -->
                <!-- <div class="products-price ml-2"> -->
                  <!-- <div class="prices"> -->
                    <!-- <div class="price"> -->
                      <!-- <div class="new-price"> -->
                        <!-- {{product.DiscountedPrice}} ({{product.Currency}}) -->
                      <!-- </div> -->
                      <!-- <div class="old-price"> -->
                        <!-- <s>€{{product.BasePrice}} ({{product.Currency}})</s> -->
                      <!-- </div> -->
                    <!-- </div> -->
                    <!-- <div class="discount-rate"> -->
                      <!-- {{product.DiscountPercentage}} -->
                    <!-- </div> -->
                  <!-- </div> -->
                  <!-- <div class="products-info"> -->
                    <!-- {{product.ProductCode}}: {{product.ProductName}} -->
                  <!-- </div> -->
                <!-- </div> -->
              <!-- </div> -->
            <!-- </div> -->
            <!-- <div class="pse-input-area"> -->
              <!-- <button type="button" class="btn text-light bg-info increment-button" (click)="incrementOffer(product)">+</button> -->
            <!-- <div class="product-quantity"> -->
              <!-- {{(product.quantity | number) + ' ' + 'Adet'}} -->
            <!-- </div> -->
              <!-- <button type="button" class="btn text-light bg-info decrement-button" (click)="decrementOffer(product)">-</button> -->
            <!-- <div class="product-quantity remove-product" (click)="removeProduct(product)"> -->
              <!-- Kaldır -->
            <!-- </div> -->
            <!-- </div> -->
          <!-- </div> -->
          <!-- <div class="btn btn-gradient btn-block text-white shadow btn-warning mt-3" (click)="toBasketFunc()" style="border-radius: 6px;">{{ 'Alışverişe Devam Et' | translate }}</div> -->
        <!-- </div> -->
        <!-- <div class="total-price"> -->
          <!-- <div class="total"> -->
            <!-- <div class="total-text"> -->
              <!-- {{ 'Ara Toplam' | translate }}: -->
            <!-- </div> -->
            <!-- <div class="total-value"> -->
              <!-- {{ subTotal | number: '1.2-2' }} -->
            <!-- </div> -->
          <!-- </div> -->
          <!-- <hr style="border: 0; -->
          <!-- border-bottom: 1px dashed #ccc; -->
          <!-- width: 93%;"> -->
          <!-- <div class="total"> -->
            <!-- <div class="total-text"> -->
              <!-- {{ 'Toplam' | translate }}: -->
            <!-- </div> -->
            <!-- <div class="total-value"> -->
              <!-- {{ total | number: '1.2-2' }} -->
            <!-- </div> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
    </div>
  </div>
  <div class="footer"
       catUserClick [section]="'PSE_CAMPAIGN'" [subsection]="'GO_TO_CART'"
       (click)="openSparePartOrderDetail()">
    <div class="buy-button">
      <img [src]="myBasket" alt="buy-button" height="22px" width="25px">
      <div class="myBasketText"> {{'_go_to_my_cart' | translate }} </div>
      <!-- <div class="toMyBasketText"> {{'Sepetime Git' | translate }} </div> -->
    </div>
  </div>
  <cat-loader [show]="loading || equipmentPSELoading"></cat-loader>
  <!-- <div *ngIf="loading && this.pollingStopped">asdas</div> -->
</div>

<cat-error-modal [backdropClose]="false" [(status)]="error" (statusChange)="errorPseFunc()" [message]="'_pse_items_not_found' | translate "></cat-error-modal>
<!-- <cat-basic-modal [(status)]="error" (statusChange)="resetError()"> -->
  <!-- <div> -->
    <!-- <div class="text-center"> -->
      <!-- <img [src]="warningIcon" style=" -->
      <!-- width: 60px; -->
      <!-- height: 60px; -->
      <!-- margin-bottom: 1rem;"/> -->
    <!-- </div> -->
    <!-- <div class=""> -->
      <!-- <span class="successFullyFeedback"  [innerHTML]="( wrongCode ? '_customer_day_wrong_code': '_error') | translate  | safe: 'html'"></span> -->
    <!-- </div> -->
  <!-- </div> -->
<!-- </cat-basic-modal> -->
