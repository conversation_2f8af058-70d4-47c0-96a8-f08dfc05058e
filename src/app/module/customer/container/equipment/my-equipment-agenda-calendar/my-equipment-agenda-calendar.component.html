<div class="equipment-agenda-calendar px-4 pb-4 pt-2">
  <div class="calendar-content">
    <div class="date-inputs">
      <button (click)="goToToday()" class="btn btn-sm btn-info p-0 px-3 py-1">
        {{ "_today" | translate }}
      </button>
      <div class="calendar-btn">
        <button
          class="icon-left btn btn-arrow-action p-0"
          (click)="setDate(-1)"
        >
          <i class="icon icon-chevron-right"></i>
        </button>
        <div class="datepicker-toggle">
          <span class="datepicker-toggle-button">
            {{ selectedMonth | date : "dd.MM.yyyy" }}
          </span>
          <input
            type="date"
            class="datepicker-input"
            name="Date"
            id="Date"
            [value]="selectedMonth | date : 'yyyy-MM-dd'"
            (change)="changeDate($event)"
            required
          />
        </div>
        <button class="btn btn-arrow-action p-0" (click)="setDate(+1)">
          <i class="icon icon-chevron-right"></i>
        </button>
      </div>
    </div>
    <div class="mb-3">
      <ng-select
        dropdownPosition="bottom"
        [clearable]="true"
        [searchable]="false"
        [(ngModel)]="selectedEquipmentHistory"
        (ngModelChange)="onChangeEquipmentHistoryFilter($event)"
        class="equipment-history-select ml-auto my-3"
        [placeholder]="'_all' | translate"
      >
      <ng-option [value]="0">{{ '_all' | translate }}</ng-option>
        <div *ngFor="let category of categories; trackBy:categoryTrackBy">
          <!--    *ngIf="(hasPermission(PermissionEnum[category.permission]) | async)"-->
          <ng-option
            *ngIf="category.permission | async"
            [value]="category.value">{{ category.category | translate }}
          </ng-option>
        </div>
      </ng-select>
    </div>
    <div #container class="date-list d-flex align-items-center overflow-auto">
      <div *ngFor="let day of currentMonthDays; let i = index">
        <div
          id="{{ day | date : 'dd' }}"
          (click)="selectDate(day)"
          class="d-flex flex-column align-items-center date"
          [ngClass]="{'selected-date': (day | date : 'yyyy-MM-dd') === (selectedDate | date : 'yyyy-MM-dd')}"
        >
          <div class="h6 day-date">
            {{ day | date : "dd" }}
          </div>
          <div class="day">
            {{ getDayNameByLang(day) }}
          </div>
          <div class="current-date-indicator"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="content-by-date">
    <div class="date-content">
      <div class="left-bar"></div>
            <!-- Check-In or Check-out -->
            <div [hasPermission]="PermissionEnum.EquipmentAgendaCheckinout">
              <div class="content-card" *ngFor="let checkData of checkInsAndCheckOutsByDate(selectedEquipmentHistory)">
                <div class="status">
                  <div class="status-bar d-flex align-items-center justify-content-center" [style.backgroundColor]="'#5D8D1C'">
                    <div>
                      <i class="icon icon-success font-size-10px text-white"></i>
                    </div>
                  </div>
                </div>
                <div class="row pb-2 border-bottom">
                  <div class="col h5">
                    {{ (checkData?.activityType === 2 ? '_check_in' : '_check_out') | translate }}
                  </div>
                  <div class="col h5" [style.color]="'#5D8D1C'">
                    {{ '_completed' | translate }}
                  </div>
                </div>
                <div class="list-group my-3">
                  <div class="row">
                    <div class="h6 col">
                      {{ '_name' | translate }}
                    </div>
                    <span class="col customer-info">: {{checkData?.data?.FirstName}}</span>
                  </div>
                  <div class="row">
                    <div class="h6 col">
                      {{ '_surname' | translate }}
                    </div>
                    <span class="col customer-info">: {{checkData?.data?.LastName}}</span>
                  </div>
                  <div class="row">
                    <div class="h6 col">
                      {{ '_date' | translate }}
                    </div>
                    <span class="col customer-info">: {{checkData?.activityDate | date: 'yyyy-MM-dd'}}</span>
                  </div>
                  <div class="row">
                    <div class="h6 col">
                      {{ '_hour' | translate }}
                    </div>
                    <span class="col customer-info">: {{checkData?.activityDate | date: 'HH:mm'}}</span>
                  </div>
                </div>
                <div class="list-group mt-3">
                  <div class="h6 mb-3">
                    {{ '_description' | translate }}
                  </div>
                  <p>{{checkData?.data[checkData?.activityType === 2 ? 'CheckInDescription' : 'CheckOutDescription'] || '-'}}</p>
                </div>
              </div>
            </div>
      <!-- service-history -->
      <div [hasPermission]="PermissionEnum.DocumentsServiceHistory" *ngIf="filterContent(EquipmentAgendaEnum.Service)">
        <div class="content-card" *ngFor="let service of serviceHistory">
          <div class="mb-3 mt-2" style="display: grid; grid-template-columns: auto auto;">
            <div class="font-weight-bold">
              {{ "_service_request" | translate }}
            </div>
            <!-- <div class="font-weight-semi-bold text-right" [style.color]="service?.data?.Status === 'E0002' ? '#FFA300' : '#5D8D1C' ">
              {{ getStatusLabel(service?.data?.Status) | translate }}
            </div> -->
            <div class="status" *ngIf="service?.data?.Status">
              <div class="status-bar d-flex align-items-center justify-content-center" [style.backgroundColor]="service?.data?.Status === 'E0002' ? '#FFA300' : '#5D8D1C' ">
                <div *ngIf="service?.data?.Status !== 'E0002'">
                  <i class="icon icon-success font-size-10px text-white"></i>
                </div>
                <div *ngIf="service?.data?.Status === 'E0002'">
                  <i class="icon icon-dot3 font-size-10px text-white"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-3 font-weight-semi-bold">
              {{ "_category" | translate }}
            </div>
            <div class="col-4 pl-0">
              :
              {{ service?.data?.ServiceCategory }}
            </div>
          </div>
          <div class="description mb-2 font-size-14px text-secondary">
            <div class="font-weight-semi-bold">
              {{ "_description" | translate }}
            </div>
            {{ service?.data?.Description }}
          </div>
        </div>
      </div>
      <!-- Inspection -->
      <div [hasPermission]="PermissionEnum.DocumentsCatInspections" *ngIf="filterContent(EquipmentAgendaEnum.CatInspection)">
        <div class="content-card" *ngFor="let inspection of equipmentInspectionResults">
        <div class="z-index-1">
          <div
            class="mb-3 mt-2 row mx-0"
            style="display: grid; grid-template-columns: auto auto"
          >
            <div class="font-weight-bold align-self-center px-0">
              {{ "_inspection_results" | translate }}
            </div>
            <div class="font-weight-bold text-right" [ngStyle]="{ 'color': inspection?.data?.StatusColor }">
              {{ inspection?.data?.StatusCode === '01' ? ('_completed' | translate) :  ('__continues' | translate) }}
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-4 font-weight-semi-bold">
              {{ "_end_date" | translate }}
            </div>
            <div class="col-4 pl-0">
              :
              {{inspection?.data?.FinishedDate | date : "dd.MM.yyyy"}}
            </div>
            <div class="col-4"></div>
            <div class="status" *ngIf="inspection?.data?.Status">
              <div class="status-bar d-flex align-items-center justify-content-center" [ngStyle]="{ 'background-color': inspection?.data?.StatusColor }">
                <div *ngIf="inspection?.data?.StatusCode === '01'">
                  <i class="icon icon-success font-size-10px text-white"></i>
                </div>
                <div *ngIf="inspection?.data?.StatusCode !== '01'">
                  <i class="icon icon-dot3 font-size-10px text-white"></i>
                </div>
              </div>
            </div>
              <div class="col-4 font-weight-semi-bold">
                {{ "_form_name" | translate }}
              </div>
              <div class="col-8 pl-0">
                : {{ inspection?.data?.FormName }}
              </div>
              <div class="col-4 font-weight-semi-bold">
                {{ "_approved_status" | translate }}
              </div>
              <div class="col-4 pl-0">
                : {{ inspection?.data?.OverallResponse }}
              </div>
          </div>
        </div>
        </div>
      </div>
      <!-- SOS ANALYZES -->
      <div [hasPermission]="PermissionEnum.DocumentsSosAnalyzes" *ngIf="filterContent(EquipmentAgendaEnum.Sos)">
        <div class="content-card" *ngFor="let sosAnalyzes of equipmentSosAnalyzes">
          <div
            class="mb-3 mt-2 mx-0"
            style="display: grid; grid-template-columns: auto auto"
          >
            <div class="font-weight-bold align-self-center text-left px-0">
              {{ "_equipment_sos_analyzes" | translate }}
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-5 font-weight-semi-bold">
              {{ "_equipment_analyzes_date" | translate }}
            </div>
            <div class="col-5 pl-0">
              :
              {{ sosAnalyzes?.data?.AnalyzeDate | date : "dd.MM.yyyy" }}
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-5 font-weight-semi-bold">
              {{ "_analyzes_document_number" | translate }}
            </div>
            <div class="col-5 pl-0">
              :
              {{ sosAnalyzes?.data?.AnalyzeDocumentNumber }}
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-4 font-weight-semi-bold">
              {{ "_compartment" | translate }}
            </div>
            <div class="col-8 pl-0">
              :
              {{ sosAnalyzes?.data?.CompDescription }}
            </div>
          </div>
          <!-- sosAnalyzes pdf download start -->
          <div class="row no-gutters d-flex align-items-center py-2" *ngIf="equipmentSosAnalyzes">
            <div
              [id]="sosAnalyzes?.data?.AnalyzeDocumentNumber"
              catUserClick
              [section]="'SOS_ANALYZES'"
              [subsection]="'PDF_DOWNLOAD'"
              [data]="{analyzeDocumentNumber: sosAnalyzes?.data?.AnalyzeDocumentNumber}"
              catDownloadFile
              [downloadType]="DownloadTypeEnum.sosAnalyzePdf"
              [haveDownload]="haveDownload"
              [downloadParams]="{
                serialNumber: equipmentDetail?.serialNumber,
                analyzeDocumentNumber: sosAnalyzes?.data?.AnalyzeDocumentNumber
              }"
              (downloadLoading)="downloading($event)"
              class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
              [class.spinner]="isDownloading(sosAnalyzes?.data?.AnalyzeDocumentNumber)"
            >
              <i class="icon icon-spinner8" *ngIf="isDownloading(sosAnalyzes?.data?.AnalyzeDocumentNumber)"></i>
              <i class="icon icon-download" *ngIf="!isDownloading(sosAnalyzes?.data?.AnalyzeDocumentNumber)"></i>
              <a class="d-none" [download]="sosAnalyzes?.data?.AnalyzeDocumentNumber"></a>
            </div>
            <div>
              <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                {{ "_sos_analyze_pdf_download" | translate }}
              </div>
            </div>
            <div
              class="ml-auto"
              *ngIf="isDownloading(sosAnalyzes?.data?.AnalyzeDocumentNumber) && cancelDonwloadVersion"
              (click)="cancelDownloadFile(sosAnalyzes?.data?.AnalyzeDocumentNumber)"
            >
              <i class="icon icon-x"></i>
            </div>
          </div>
          <!-- sosAnalyzes pdf download end -->
        </div>
      </div>
      <!-- pm Planner -->
      <div [hasPermission]="PermissionEnum.DocumentsMdaPlan" *ngIf="filterContent(EquipmentAgendaEnum.PMPlanner)">  <!--filterContent(EquipmentAgendaEnum.PMPlanner) -->
        <div class="content-card" *ngFor="let equipmentPlanner of equipmentMDAPmPlanner">
          <div class="service-history-item-color">
          </div>
          <div class="mb-3 mt-2 mx-0" style="display: grid; grid-template-columns: auto auto;">
            <div class="font-weight-bold col px-0">
              {{ "_equipment_mda_pm_planner" | translate }}
            </div>
            <div class="row font-size-14px text-secondary mb-2">
              <div class="col pl-0 mr-2 text-right font-weight-bold" [style.color]="
              equipmentPlanner?.data?.WorkOrderStatus === 'closed' ? '#5D8D1C' :
              equipmentPlanner?.data?.WorkOrderStatus === 'estimate' ? '#FFA300' :
              equipmentPlanner?.data?.WorkOrderStatus === 'cancel' ? '#DA3A3C' : '#6c757d'">
                {{ equipmentPlanner?.data?.ContractStatusText }}
              </div>
            </div>
            <div class="status">
              <div class="status-bar d-flex align-items-center justify-content-center" [style.backgroundColor]="
              equipmentPlanner?.data?.WorkOrderStatus === 'closed' ? '#5D8D1C' :
              equipmentPlanner?.data?.WorkOrderStatus === 'estimate' ? '#FFA300' :
              equipmentPlanner?.data?.WorkOrderStatus === 'cancel' ? '#DA3A3C' : '#6c757d'">

              <div *ngIf="equipmentPlanner?.data?.WorkOrderStatus === 'closed'">
                <i class="icon icon-success font-size-10px text-white"></i>
              </div>
              <div *ngIf="equipmentPlanner?.data?.WorkOrderStatus === 'estimate'">
                <i class="icon icon-dot3 font-size-10px text-white"></i>
              </div>
              <div *ngIf="equipmentPlanner?.data?.WorkOrderStatus === 'cancel'">
                <i class="icon icon-x font-size-10px text-white"></i>
              </div>
            </div>
          </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-6 font-weight-semi-bold">
              {{ "_contract_number" | translate }}
            </div>
            <div class="col-6 pl-0">
              :
              {{ equipmentPlanner?.data?.ContractNumber }}
            </div>
          </div>
          <div *ngIf="equipmentPlanner?.data?.WorkOrderStartDate !== '0000-00-00'" class="row font-size-14px text-secondary mb-2">
            <div class="col-6 font-weight-semi-bold">
              {{ "_start_date" | translate }}
            </div>
            <div class="col-6 pl-0">
              :
              {{ equipmentPlanner?.data?.WorkOrderStartDate | date: "dd.MM.yyyy" }}
            </div>
          </div>
          <div *ngIf="equipmentPlanner?.data?.WorkOrderPlanDate !== '0000-00-00'" class="row font-size-14px text-secondary mb-2">
            <div class="col-6 font-weight-semi-bold">
              {{ "_end_date" | translate }}
            </div>
            <div class="col-6 pl-0">
              :
              {{ equipmentPlanner?.data?.WorkOrderPlanDate | date: "dd.MM.yyyy" }}
            </div>
          </div>
        </div>
      </div>
      <!-- Diag -->
      <div [hasPermission]="PermissionEnum.EquipmentDetailWarnings" *ngIf="filterContent(EquipmentAgendaEnum.Diagnostic)">
        <div class="content-card note-card d-flex flex-column" *ngFor="let diag of diags">
          <div class="answer fit-body" style="width: 100%">
            <div class="d-flex flex-row mb-2">
              <div class="col-12"><b>{{diag?.data?.Mid}}-{{diag?.data?.Fmi}}-{{diag?.data?.CId}}</b></div>
              <!-- <i class="icon icon-dot3 text-primary"></i> -->
            </div>
            <div class="d-flex flex-row mb-2">
              <div class="col-12">
                <div>{{diag?.data?.Description}}</div>
              </div>
            </div>
            <div class="d-flex flex-row mb-2" *ngIf="diag?.data?.Level!==''">
              <div class="col-5">{{ "_priority_level" | translate }}:</div>
              <div [ngClass]="{
                  'text-success': diag?.data?.Level == '1',
                  'text-warning': diag?.data?.Level == '2',
                  'text-danger': diag?.data?.Level == '3'
                }">
                {{diag?.data?.Level==='1'? ('_low' | translate):diag?.data?.Level==='2'?('_medium' | translate):diag?.data?.Level==='3'?('_high' | translate):""}}
              </div>

            </div>
            <div class="d-flex flex-row mb-2">
              <div class="col-5">{{ "_date" | translate }}:</div>
              <div> {{ diag?.data?.Date | date: "dd.MM.yyyy" }}</div>
            </div>
            <div class="row m-0">
              <button [hasPermission]="PermissionEnum.RequestsService" class="col btn p-1 btn-sm btn-warning text-white mr-2" (click)="requestService()">
                {{ "_create_service_request" | translate }}
              </button>
              <button [hasPermission]="PermissionEnum.Ecommerce" class="col btn btn-sm p-1 btn-warning text-white" (click)="openSparePartOrderDetail()">
                {{ "_boom_shop" | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <div [hasPermission]="PermissionEnum.EquipmentAgendaAddNote" *ngIf="filterContent(EquipmentAgendaEnum.Note)">
        <div class="content-card note-card d-flex flex-column" *ngFor="let note of notes">
          <div class="h5 note-card-title">
            {{note?.data?.Title}}
          </div>
          <p #content class="note-card-body font-size-13px m-0" [ngClass]="{'show-full-note': showFullNote}">
            {{note?.data?.Body}}
          </p>
          <button *ngIf="content.scrollHeight > 40" class="btn p-0 show-more-btn font-size-12px" (click)="fullNoteStatus()">
            {{ showFullNote ? ('_show_less' | translate) : ('_show_more' | translate)}}
            <i class="icon icon-chevron-down font-size-12px" [ngClass]="{'icon-chevron-up': showFullNote}"></i>
          </button>
        </div>
      </div>
      <!-- Normal Inspections -->
      <div [hasPermission]="PermissionEnum.DocumentsInspectionHistory" *ngIf="filterContent(EquipmentAgendaEnum.Inspection)">
        <div class="content-card" *ngFor="let inspection of inspections">
          <div class="status">
            <div class="status-bar d-flex align-items-center justify-content-center" [style.backgroundColor]="'#5D8D1C'">
              <div>
                <i class="icon icon-success font-size-10px text-white"></i>
              </div>
            </div>
          </div>
          <div class="mb-3 mt-2 mx-0">
            <div class="row pb-2 border-bottom">
              <div class="col h5">
                {{ "_inspection" | translate }}
              </div>
              <div class="col h5" [style.color]="'#5D8D1C'">
                {{ '_completed' | translate }}
              </div>
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-5 font-weight-semi-bold">
              {{ "_inspection_date" | translate }}
            </div>
            <div class="col-5 pl-0">
              :
              {{ inspection?.data?.CompletedDate | date : "dd.MM.yyyy" }}
            </div>
          </div>
          <div class="row font-size-14px text-secondary mb-2">
            <div class="col-5 font-weight-semi-bold">
              {{ "_working_hours" | translate }}
            </div>
            <div class="col-5 pl-0">
              :
              {{ inspection?.data?.WorkingHours }}
            </div>
          </div>
          <!-- sosAnalyzes pdf download start -->
          <div class="inspection-download-btn" [class.spinner]="isDownloading(inspection?.data?.PortalUserInspectionFormId)">
            <div
              catDownloadFile
              [downloadType]="DownloadTypeEnum.inspectionHistory"
              [downloadParams]="{
                formId: inspection?.data?.FormId,
                portalUserInspectionFormId:
                inspection?.data?.PortalUserInspectionFormId,
                language: language,
                isMasked: 'true'
              }"
              (downloadLoading)="downloading($event)"
              class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
              [class.spinner]="
                isDownloading(inspection?.data?.PortalUserInspectionFormId)"
              [id]="'downloadButton_' + inspection?.data?.PortalUserInspectionFormId"
            >
              <i
                class="icon icon-spinner8"
                *ngIf="isDownloading(inspection?.data?.PortalUserInspectionFormId)">
              </i>
              <i
                class="icon icon-download"
                *ngIf="!isDownloading(inspection?.data?.PortalUserInspectionFormId)">
              </i>
              <a
                class="d-none"
                [download]="inspection?.data?.PortalUserInspectionFormId">
              </a>
            </div>
          </div>
          <!-- inspection pdf download end -->
        </div>
      </div>
    </div>
    <!-- <div
      class="d-flex align-items-center flex-column justify-content-center empty-equipment-agenda"
      *ngIf="showEmptyInfo || !equipmentAgendaHistory && !(equipmentAgendaLoading$ | async)"
    >
      <i class="icon icon-boom-logo"></i>
      {{ '_empty_equipment_agenda' | translate }}
    </div> -->
  </div>
</div>
<cat-loader [show]="equipmentAgendaLoading$ | async"></cat-loader>

<!--
          <div class="download-btn">
          <div [class.spinner]="isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
            <div
              catDownloadFile
              [downloadType]="DownloadTypeEnum.inspectionHistory"
              [downloadParams]="{
                formId: equipmentInspectionHistory?.formId,
                portalUserInspectionFormId:
                equipmentInspectionHistory?.portalUserInspectionFormId,
                language: language,
                isMasked: 'true'
              }"
              (downloadLoading)="downloading($event)"
              class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
              [class.spinner]="
                isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)"
              [id]="'downloadButton_' + equipmentInspectionHistory?.portalUserInspectionFormId"
            >
              <i
                class="icon icon-spinner8"
                *ngIf="isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
              </i>
              <i
                class="icon icon-download"
                *ngIf="!isDownloading(equipmentInspectionHistory?.portalUserInspectionFormId)">
              </i>
              <a
                class="d-none"
                [download]="equipmentInspectionHistory?.portalUserInspectionFormId">
              </a>
            </div>
          </div>
        </div>
 -->
