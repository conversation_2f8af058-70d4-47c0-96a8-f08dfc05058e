.equipment-agenda-calendar{

    .calendar-content{

      ::ng-deep ng-select ng-dropdown-panel {
        .ng-dropdown-panel-items{
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;    
        }
      }

        .date-inputs{
            display: flex;
            align-items: center;
            justify-content: space-between;

            .calendar-btn{
                display: flex;
                align-items: center;
                gap: 1.5rem;
                height: 30px;

                .btn-arrow-action{
                  width: 20px;
                  height: 20px;
                }

                .icon-chevron-right, .icon-chevron-left{

                    &::before{
                        font-size: 13px;
                    }
                }

                .icon-left{
                    transform: rotate(-180deg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 24px;
                    margin-top: 6px;
                }
            }
        }

        .date-list{
          margin-top: 30px;
          margin-bottom: 25px;
          gap: 1.5rem;

          &::-webkit-scrollbar{
            display: none;
          }

          .day{
            color: #A7A7A7;
          }

          .date{
            min-width: 30px;
            max-width: 30px;
            min-height: 60px;
            max-height: 60px;
          }

          .selected-date{

            .day-date, .day{
              color: #FFA300;
            }

            .current-date-indicator{
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #FFA300;
            }
          }

        }
    }
}

  .datepicker-toggle {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 19px;
  }

  .datepicker-toggle-button {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .datepicker-input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    box-sizing: border-box;
  }

  .datepicker-input::-webkit-calendar-picker-indicator {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    cursor: pointer;
  }

  .equipment-history-select{
    width: 200px;
  }

  .ng-select ::ng-deep {

    .ng-select-container{
      //box-shadow: 0px 0px 5px 0px #00000040;
      background-color: #ffffff;
      border: 1px solid #ddd;
      border-radius: 20px;
      height: 40px;
      min-height: 40px;
    }

    .ng-select-opened{
      box-shadow: 0px 0px 5px 0px #00000040;
    }

    .ng-dropdown-panel{
      border: 1px solid #ddd;
      border-top: 0;
      background-color: #ffffff;

      .ng-dropdown-panel-items{
        height: max-content;
        max-height: 160px;
      }
    }

    .ng-option{
      background-color: #ffffff;
    }

    .ng-option-marked{
      background-color: #f5faff !important;
    }
  }

  .content-by-date{

    .date-content{
      position: relative;

      .left-bar{
        position: absolute;
        width: 1px;
        background-color: #E1E1E1;
        top: 0;
        bottom: 0;
        left: 0;
      }

      .content-card{
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0px 10px 20px 0px #0000001A;
        margin-bottom: 10px;
        margin-left: 20px;
        position: relative;

        .status{
          padding-top: 10px;
          padding-bottom: 10px;
          background-color: #fff;
          position: absolute;
          top: 50%;
          left: -19px;
          transform: translate(-50%, -50%);

          .status-bar{
            width: 17px;
            height: 17px;
            border-radius: 50%;
            background-color: #FFA300;
          }
        }

        .download-file{
          width: 50px;
          height: 50px;
        }

        .description{
          word-break: break-word;
        }
      }

      .note-card{

        &-body{
          color: #555555;
          overflow: hidden;
          text-overflow: ellipsis;
          //white-space: nowrap;
          word-break: break-all;
          overflow: hidden;
          height: 40px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .show-more-btn{
          color: #FFA300;
          text-decoration: underline;
          width: max-content;
          align-self: center;
        }

        &-title{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .show-full-note{
          height: max-content;
          overflow: unset;
          -webkit-line-clamp: unset;
        }
      }
    }
  }

  .customer-info{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .empty-equipment-agenda{
    height: 200px;

    .icon-boom-logo{
      margin-bottom: 16px;

      &::before{
        font-size: 48px;
      }
    }
  }

  .inspection-download-btn {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    font-size: 18px;
  }
