import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { EquipmentState } from '../../../state/equipment.state';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { StartDownloadAction } from 'src/app/module/shared/state/common/common.actions';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { GetEquipmentAgendaHistory } from '../../../action/equipment.action';
import { takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { EquipmentAgendaEnum } from '../../../model/equipment.model';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import * as moment from 'moment';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { TranslateService } from '@ngx-translate/core';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';

@Component({
  selector: 'cat-my-equipment-agenda-calendar',
  templateUrl: './my-equipment-agenda-calendar.component.html',
  styleUrls: ['./my-equipment-agenda-calendar.component.scss']
})
export class MyEquipmentAgendaCalendarComponent implements OnInit, AfterViewInit {
  @Select(EquipmentState.equipmentAgendaHistory)
  equipmentAgendaHistory$: Observable<any>;

  @Select(EquipmentState.equipmentAgendaLoading)
  equipmentAgendaLoading$: Observable<boolean>;

  private subscriptions$: Subject<boolean> = new Subject();

  EquipmentAgendaEnum = EquipmentAgendaEnum;
  PermissionEnum = PermissionEnum;
  date = new Date();
  selectedDate: any = new Date();
  selectedMonth: any = new Date();
  lang = 'tr-TR';
  currentMonthDays: any = [];
  selectedEquipmentHistory: any = 0;
  currentCustomer: CustomerRelationModel;
  equipmentAgendaHistory: any = false;
  equipmentInspectionResults = [];
  equipmentSosAnalyzes = [];
  equipmentMDAPmPlanner = [];
  serviceHistory = [];
  checkIns = [];
  checkOuts = [];
  notes = [];
  diags = [];
  inspections = [];
  language: string;
  haveDownload: boolean;
  showEmptyInfo: boolean;
  analayzesHistoryStatus: boolean;
  loadingAttachment: any[] = [];
  DownloadTypeEnum = DownloadTypeEnum;
  cancelDonwloadVersion = false;
  showFullNote = false;
  categories = [
    // { value: 0, category: '_all'},
    { value: 1, category: '_equipment_sos_analyzes', permission: this.hasPermission(PermissionEnum.DocumentsSosAnalyzes) },
    { value: 3, category: '_equipment_agenda_checks', permission: this.hasPermission(PermissionEnum.EquipmentAgendaCheckinout) },
    { value: 4, category: '_notes', permission: this.hasPermission(PermissionEnum.EquipmentAgendaAddNote) },
    { value: 5, category: '_inspection_results', permission: this.hasPermission(PermissionEnum.DocumentsCatInspections) },
    { value: 6, category: 'Diag', permission: this.hasPermission(PermissionEnum.EquipmentDetailWarnings) },
    { value: 7, category: '_equipment_mda_pm_planner', permission: this.hasPermission(PermissionEnum.DocumentsMdaPlan) },
    { value: 8, category: '_service_detail', permission: this.hasPermission(PermissionEnum.DocumentsServiceRequests) },
    { value: 9, category: '_inspection', permission: this.hasPermission(PermissionEnum.DocumentsInspectionHistory) }
  ];


  constructor(
    private readonly translateService: TranslateService,
    private readonly el: ElementRef,
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly permissionService: HasPermissionsService
  ) {}

  ngOnInit() {
    this.language = this.store.selectSnapshot(LoginState.language);
    this.getDaysInMonth(this.date.getFullYear(), this.date.getMonth());
    this.currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);
    this.lang = this.store.selectSnapshot(LoginState.language) || this.translateService.currentLang ||
      this.translateService.defaultLang;

    this.cancelDonwloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');

    this.equipmentAgendaHistory$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {

        if (data?.responses?.length) {
          this.equipmentAgendaHistory = data;
        }
        this.equipmentSosAnalyzes = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.Sos);
        this.checkIns = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.CheckIn);
        this.checkOuts = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.CheckOut);
        this.notes = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.Note);
        this.equipmentInspectionResults = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.CatInspection);
        this.diags = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.Diagnostic);
        this.equipmentMDAPmPlanner = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.PMPlanner);
        this.serviceHistory = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.Service);
        this.inspections = data?.responses?.filter(item => item.activityType === EquipmentAgendaEnum.Inspection);
      });
  }

  ngAfterViewInit() {
    if (this.currentMonthDays.length > 0) {
      this.scrollIntoDate(this.date.getDate());
    }

    window.scroll({ top: 0 });
  }

  get equipmentDetail() {
    return this.store.selectSnapshot(EquipmentState.equipmentDetail);
  }

  hasPermission(permission: string): Observable<boolean> {
    return this.permissionService.hasPermission(permission)
      .pipe(takeUntil(this.subscriptions$));
  }

  selectDate(day) {
    const time = new Date(day).setHours(3);
    this.selectedMonth = day;
    this.selectedDate = new Date(day).toDateString();
    this.changeEquipmentAgendaHistoryByDate(time);
  }

  changeDate(e: Event) {
    const date = e.target as HTMLInputElement;

    if (!date.value) {
      return this.goToToday();
    }

    const changeDateValue = new Date(date.value);

    this.scrollIntoDate(changeDateValue.getDate());
    this.selectedDate = changeDateValue.toDateString();
    this.selectedMonth = changeDateValue;
    this.getDaysInMonth(changeDateValue.getFullYear(), changeDateValue.getMonth());
    this.changeEquipmentAgendaHistoryByDate(date.value);
  }

  changeEquipmentAgendaHistoryByDate(date) {
    this.store.dispatch(new GetEquipmentAgendaHistory(this.equipmentDetail?.serialNumber, this.equipmentDetail.equipmentNumber, new Date(date)));
    this.selectedEquipmentHistory = 0;
    //this.checkActivityDate(0)
  }

  scrollIntoDate(value) {
    setTimeout(() => {
      document.getElementById(value < 10 ? '0' + value.toString() : value.toString())?.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
        block: 'nearest'
      });
    }, 0);
  }

  setDate(value) {
    const currentMonth = new Date(this.selectedMonth);
    this.selectedMonth = currentMonth.setMonth(currentMonth.getMonth() + value);
    this.selectedDate = currentMonth.toDateString();
    this.getDaysInMonth(currentMonth.getFullYear(), currentMonth.getMonth());
    this.scrollIntoDate(currentMonth.getDate());
    const time = currentMonth.setHours(3);
    this.changeEquipmentAgendaHistoryByDate(time);
  }

  goToToday() {
    this.getDaysInMonth(this.date.getFullYear(), this.date.getMonth());
    this.selectedMonth = this.date;
    this.selectedDate = this.date.toDateString();
    this.scrollIntoDate(this.date.getDate());
    this.changeEquipmentAgendaHistoryByDate(this.date);
  }

  getDaysInMonth(year, month) {
    const daysInMonth = [];
    const date = new Date(year, month, 1);

    while (date.getMonth() === month) {
      daysInMonth.push(new Date(date));
      date.setDate(date.getDate() + 1);
    }
    this.currentMonthDays = daysInMonth;

  }

  onChangeEquipmentHistoryFilter(value) {
    //this.checkActivityDate(value)
    this.selectedEquipmentHistory = value;
  }

  filterContent(value) {
    return this.selectedEquipmentHistory === EquipmentAgendaEnum.All || this.selectedEquipmentHistory === value;
  }

  downloading(attachment) {
    this.haveDownload = true;
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
    if (this.loadingAttachment.length > 1) {
      this.cancelDownloadFile(attachment);
    } else if (this.loadingAttachment.length === 0) {
      this.haveDownload = false;
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  getStatusLabel(status: any) {
    if (status === 'E0002') {
      return '_continues';
    }

    return '_completed';
  }

  fullNoteStatus() {
    this.showFullNote = !this.showFullNote;
  }

  requestService() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-service',
        this.equipmentDetail?.equipmentNumber,
      ]).then();
  }

  openSparePartOrderDetail() {
    this.frameService.sendMessage(FrameMessageEnum.openSparePart, {
      user: this.currentCustomer,
    });
    return;
  }

  getDayNameByLang(day) {
    const date = moment(day);

    return date.locale(this.lang).format('ddd');
  }

  checkInsAndCheckOutsByDate(selectedEquipmentHistoryType) {
    if ([EquipmentAgendaEnum.All, EquipmentAgendaEnum.CheckIn, EquipmentAgendaEnum.CheckOut].includes(selectedEquipmentHistoryType)) {
      if (this.checkIns && this.checkOuts) {
        const data = [...this.checkIns, ...this.checkOuts];
        const sortedDate = data.sort((a, b) => {
          const dateA = new Date(a?.activityDate);
          const dateB = new Date(b?.activityDate);

          if (dateA < dateB) {
            return -1;
          }
          if (dateA > dateB) {
            return 1;
          }
          return 0;
        });
        return sortedDate.reverse();
      }
    }

    return [];
  }

  categoryTrackBy(index, value) {
    return value.category;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  // checkActivityDate(value){
  //   if(value === 0){
  //     return this.showEmptyInfo = false
  //   }

  //   if(this.equipmentAgendaHistory?.responses.length){
  //     const checkActivityDate = this.equipmentAgendaHistory?.responses.some(data => data?.activityType === value)
  //     return this.showEmptyInfo = !checkActivityDate
  //   }
  // }
}
