<div class="repair-icon ml-1 mr-1 bg-white main" *ngIf="showFuelInfo && FuelLevel && !loading">
  <div>
    <div class="d-flex justify-content-center">
      <i class="icon icon-fuel" style="--color:{{ gradientColor }}"></i>
    </div>
    <div class="d-flex justify-content-center">
      <span class="font-size-11px" style="width:auto; max-width: auto;" [ngStyle]="{
        'color': FuelLevel > 85 ? '#5cb85c' : FuelLevel < 20 ? '#d9534f' : '#f0ad4e'
      }">
      {{ FuelLevel }}%
    </span>
    </div>
  </div>
</div>
<div class="repair-icon ml-1 mr-1 bg-white main" *ngIf="showFuelInfo && loading">
  <!-- <cat-loader [show]="true" bgColor="transparent" [overlay]="false"></cat-loader> -->
  <cat-dot-loader></cat-dot-loader>
</div>
