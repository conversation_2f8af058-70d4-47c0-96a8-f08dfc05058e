.repair-icon {
  box-sizing: border-box;
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1), 0px 6px 4px rgba(0, 0, 0, 0.03);
  border-radius: 6px;
  // width: 50px;
  // height: 40px;
  padding: 5px;
  z-index: 2;
  i {
    font-size: 18px;
    margin-top: 2px;
  }
}
.fuel-area {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-size: 15px;
  font-weight: 500;
}

.icon-fuel::before {
  background: var(--color);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

// ::ng-deep .fuel-loader .minimal-app-loader {
//   left: -3px !important;
//   top: -4px !important;
//   margin-bottom: 0px !important;
//   border: 6px solid #7f7e7e;
// }


.main {
  max-width: 30px;
  height: 44px;
}
