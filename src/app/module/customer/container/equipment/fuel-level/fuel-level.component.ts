import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { EquipmentService } from '../../../service/equipment.service';

@Component({
  selector: 'cat-fuel-level',
  templateUrl: './fuel-level.component.html',
  styleUrls: ['./fuel-level.component.scss']
})
export class FuelLevelComponent implements OnInit  {

  constructor(
    private equipmentService: EquipmentService
  ) { }

  @Input()
  equipmentNumber: string;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  FuelLevel: any;
  @Output()
  FuelLevelLoading: EventEmitter<boolean> = new EventEmitter<boolean>();
  loading = false;
  gradientColor: string;
  showFuelInfo = false;

  ngOnInit() {
    if (this.equipmentNumber) {
      this.loading = true;
      this.FuelLevelLoading.emit(true);
      this.equipmentService.equipmentFuel(this.equipmentNumber).subscribe((res: any) => {
        if (res && res?.fuelLevel !== null) {
          this.FuelLevel = parseInt(res.fuelValue, 10);
        }
        if (this.FuelLevel >= 0) {
          this.loading = false;
          this.FuelLevelLoading.emit(false);
          this.setGradientColor(this.FuelLevel);
         } else {
          this.loading = false;
          this.FuelLevelLoading.emit(false);
        }
      },
        () => {
          this.loading = false;
          this.FuelLevelLoading.emit(true);
        });
    }

    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.showFuelInfo = systemFeature('fuel_info', features, false);
      }
    });

 }
  setGradientColor(percent: number) {
    if (percent > 85) {
      this.gradientColor = `linear-gradient(0deg, #5cb85c ${percent}%, rgb(151, 146, 146) 4%)`;
    } else if (percent < 20) {
      this.gradientColor = `linear-gradient(0deg, #d9534f ${percent}%, rgb(151, 146, 146) 4%)`;
    } else {
      this.gradientColor = `linear-gradient(0deg, #f0ad4e ${percent}%, rgb(151, 146, 146) 4%)`;
    }
  }

}
