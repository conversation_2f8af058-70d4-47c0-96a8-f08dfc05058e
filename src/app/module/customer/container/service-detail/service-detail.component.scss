.service-detail{
    // padding: 20px;
    // padding-bottom: 45px;
    // height: 100vh;
    // background-color:  #FAFAFAFA;

    // .service-information-content{
    //     flex: 1;

    //     .service-request-card{
    //         background-color: #fff;
    //         margin-top: 1.5rem;
    //         border-radius: .5rem;
    //     }

    //     .two-dots {
    //         padding-left: 25px;

    //         &:before {
    //             display: inline-block;
    //             content: ":";
    //             margin-left: -25px;
    //             padding-left: 10px;
    //             padding-right: 10px;
    //         }
    //     }

    //     .work-order-attachments{
    //         padding: 22px;
    //         padding-bottom: 100px;
    //     }
    // }

    .btn-info{
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        bottom: 40px;
        left: 20px;
        right: 20px;

        .icon-edit{
            font-size: 18px;

            &::before{
                color: #fff;
            }
        }
    }
}

.header{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

.ask-to-service{

    .chat-content{
        height: calc(100vh - 105px);

        .chat-content-top{
            padding: 20px;
            padding-bottom: 0;
        }

        .chat-container{
            height: calc(100% - 145px);
            overflow: auto;
            scroll-behavior: smooth;
        }

        .chat{
            padding-inline: 20px;

            .chat-date{
                color: #B9B9B9;
                font-size: 13px;
                display: flex;
                align-items: center;
                justify-content: center;

                .date{
                    padding-inline: 2rem;
                }

                &::before{
                    content: '';
                    height: 1px;
                    width: 100%;
                    background-color: #B9B9B9;
                }

                &::after{
                    content: '';
                    height: 1px;
                    width: 100%;
                    background-color: #B9B9B9;
                }

            }

            .messages{

                .person-message-content{
                    display: flex;
                    flex-direction: column;
                    gap: .5rem;
                    margin-bottom: 15px;

                    .message{
                        background-color: #F5F1F1;
                        color: #4A8EB0;
                        padding: 18px;
                        border-radius: 10px;
                        border-bottom-right-radius: 0;
                        font-size: 14px;

                        &.borusan-user{
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 10px;
                            background-color: #4A8EB0;
                            color: #F5F1F1;
                            position: relative;

                            .borusan-slug{
                                position: absolute;
                                bottom: 10px;
                                right: 20px;
                            }
                        }
                    }

                    .message-date{
                        align-self: flex-end;
                        color: #B9B9B9;
                        font-size: 13px;

                        &.borusan-message-date{
                            align-self: flex-start;
                        }
                    }
                }
            }
        }
    }

    .message-content{
        height: 105px;
        border-top-left-radius: 27px;
        border-top-right-radius: 27px;
        background-color: #F5F4F4;
        box-shadow: 0px -2px 2px 0px #0000001F;
        padding: 30px 24px;

        .message-input{
            background-color: #fff;
            border: 1px solid #ddd;
        }

        .send-message-btn{
            width: 50px;
            height: 50px;

            .icon{
                font-size: 20px;
                &::before{
                    color: #fff;
                }
            }
        }
    }
}

.download-file{
    width: 40px;
}

.work-order-attachments{
}


.message-form{
    width: 100%;
    background-color: #fff;
    border-radius: 6px;

    .form-group{
        position: relative;

        .message-input{
            background-color: transparent;
            border: none;
            box-shadow: none;
            border-radius: 0;

            &:focus{
                border: none;
                box-shadow: none;
                border-right: none;
            }
        }

        .invalid-feedback{
            position: absolute;
            top: 100%;
            bottom: 0;
        }
    }
}

.empty-message{
    height: calc(100% - 145px);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: .5rem;
}
