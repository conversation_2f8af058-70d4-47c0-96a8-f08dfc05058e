import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { ServiceDetailedRequestModel } from '../../model/service-request.model';
import { Observable, Subject } from 'rxjs';
import { ServiceState } from '../../state/service.state';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { environment } from 'src/environments/environment';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';

@Component({
  selector: 'cat-service-detail',
  templateUrl: './service-detail.component.html',
  styleUrls: ['./service-detail.component.scss']
})
export class ServiceDetailComponent implements OnInit {
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  protected subscriptions$: Subject<boolean> = new Subject();

  mapLocation = `${environment.assets}/map_location.png`;
  warningIcon = `${environment.assets}/warning.svg`;
  getFormErrorMessage = getFormErrorMessage;
  serviceDetail: ServiceDetailedRequestModel;
  loadMore: any = {};
  loadingAttachment: any[] = [];
  DownloadTypeEnum = DownloadTypeEnum;
  serviceLocationShow = true;
  featureSoundDiagnostics = false;
  expertiseShow: boolean;
  cancelDownloadVersion = false;
  loading = false;
  id: string;
  PermissionEnum = PermissionEnum;

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
  ) { }

  ngOnInit() {
    const { id } = this.route.snapshot.params;
    this.id = id;
    this.cancelDownloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.serviceDetail = this.store.selectSnapshot(ServiceState.serviceDetail);
  }

  askToService() {
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'work-order',
          'chat',
          this.serviceDetail?.serviceNumber,
        ],
      )
      .then();
  }


  navigateToBack() {
    window.history.back();
  }
}
