<ng-container *ngIf="!loading">
  <div class="overflow-hidden map-container">
    <div class="d-flex justify-content-between align-items-center cursor-pointer h4 nav-back mb-0">
      <div class="pl-3 h4 nav-back">
        <i class="icon icon-back mr-4 float-left" (click)="navigateToBack()"></i>
      </div>
      <div class="p-2 mr-4">
        {{'_service_location_tracking' |translate}}
      </div>
      <div class="p-2"></div>
    </div>
    <div class="d-inline-block map-content" *ngIf="apiLoaded | async">
      <google-map [center]="center" [zoom]="zoom" (mapClick)="onClickMap()" (zoomChanged)="onClickMap()">
        <map-marker #marker="mapMarker" *ngFor="let marker of this.locations" [position]="marker"
          [options]="marker.options" (mapClick)="onClickMarker(marker)"></map-marker>
      </google-map>
    </div>
  </div>
  <div *ngIf="serviceData" class="service-area radius-6px p-0">
    <div class="mb-3 service-request-item overflow-hidden">
      <div [style.background-color]="serviceData?.serviceStatusColor" class="service-request-item-color"></div>
      <div class="p-4">
        <div>
          <div class="d-flex justify-content-between mb-2 overflow-hidden">
            <div class="font-weight-semi-bold h6">
              {{ serviceData?.model || ("_component" | translate) }}
            </div>
            <div [style.color]="serviceData?.serviceStatusColor" class="font-weight-semi-bold">
              {{ (this.getStatusLabel(serviceData)) | translate }}
            </div>
          </div>
          <div class="row no-gutters service-area-info">
            <div class="col-5 service-area-info-label">
              {{ "_service_number" | translate }}
            </div>
            <div class="col-7 two-dots service-area-info-title">
              {{ serviceData?.serviceNumber || "-" }}
            </div>
          </div>
          <div class="row no-gutters service-area-info">
            <div class="col-5 service-area-info-label">
              {{ "_machine_serial_number" | translate }}
            </div>
            <div class="col-7 two-dots service-area-info-title">
              {{ (serviceData?.equipmentSerialNumber | serialFormat) || "-" }}
            </div>
          </div>
          <div [ngSwitch]="serviceData?.workOrderStatus?.serviceApplication" class="row no-gutters service-area-info">
            <div *ngSwitchCase="'Cdoms'" class="col-5">{{ "_acceptance_date" | translate }}</div>
            <div *ngSwitchCase="'Weking'" class="col-5">{{ "_start_date" | translate }}</div>
            <div *ngSwitchCase="'Sim'" class="col-5">{{ "_request_date" | translate }}</div>
            <div *ngSwitchDefault class="col-5">{{ "_acceptance_date" | translate }}</div>
            <div class="col-7 two-dots service-area-info-title">
              {{ serviceData?.startDate | date: "dd.MM.yyyy" || "-" }}
            </div>
          </div>
        </div>

        <div *ngIf="serviceData?.workOrderStatus?.currentStatusName">
          <div class="row no-gutters service-area-info" *ngIf="serviceData?.workOrderStatus?.currentStatusName">
            <div class="col-5 service-area-info-label">{{ "_status" | translate }}</div>
            <div class="col-7 two-dots service-area-info-title">
              {{ serviceData?.workOrderStatus?.currentStatusName }}
            </div>
          </div>
        </div>

        <div *ngIf="serviceData?.workOrderStatus">
          <div class="row no-gutters service-area-info" *ngIf="serviceData?.workOrderStatus?.servicePlateNumber">
            <div class="col-5 service-area-info-label">{{ "_service_car_plate" | translate }}</div>
            <div class="col-7 two-dots service-area-info-title">
              <!-- Araç Plakası Alanı -->
              {{ serviceData?.workOrderStatus?.servicePlateNumber || '-' }}
            </div>
          </div>
        </div>

        <div class="row no-gutters service-area-info">
          <div class="col-5 service-area-info-label">{{ "_description" | translate }}</div>
          <div class="col-7 two-dots service-area-info-title" style="word-break: break-all;">
            {{ serviceData?.description || "-" }}
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
<cat-loader [show]="loading && !nanServiceLocation"></cat-loader>
<cat-basic-modal
*ngIf="nanServiceLocation"
[(status)]="nanServiceLocation"
[headerText]="'_nan_service_location' | translate"
>
<div class="mb-3">
  <div>{{ "_nan_service_location_text" | translate }}</div>

  <div class="mx-auto text-center mt-4">
    <button
        catUserClick
        [section]="'SERVICE'"
        [subsection]="'SERVICE_MAP'"
        [data]="{
          serviceNumber: serviceData?.serviceNumber,
          plateNumber: serviceData?.workOrderStatus?.servicePlateNumber,
          equipmentNumber: serviceData?.equipmentSerialNumber | serialFormat
        }"
        class="
          modal-btn
          btn-sm btn btn-warning btn-gradient
          text-white
          shadow
        "
        (click)="serviceCarLocationModalClose()"
      >
        {{ "_close" | translate }}
      </button>
  </div>
</div>
</cat-basic-modal>
