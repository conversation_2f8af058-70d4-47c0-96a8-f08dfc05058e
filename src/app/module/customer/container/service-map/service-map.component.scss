::ng-deep body {
  overflow: hidden;
}
.map{
  &-container{
    height: 100vh;
  }
  &-content {
  height: calc(100vh - (37px));
  width: 100vw;
  position: relative;
  overflow: hidden;
}
}

.service-request {
  &-item {
    background: #ffffff;
    mix-blend-mode: normal;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;
    }

    .two-dots {
      padding-left: 25px;

      &:before {
        display: inline-block;
        content: ":";
        margin-left: -25px;
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }
}
.service-area {
  position: absolute;
  z-index: 1;
  bottom: 0;
  width: 100%;
  // background-color: white;
  overflow-y: hidden;
  height: 100%;
  max-height: 265px;
  &-info{
    margin-bottom: 5px;
    line-height: 1.1rem;
    &-label{
      line-height: 1.1rem;
      word-wrap: break-word;
    }
    &-title{
      line-height: 1.1rem;
      align-self: center;
      word-wrap: break-word;
    }
  }
}
.radius-6px {
  border-radius: 6px;
}
::ng-deep google-map .map-container {
  height: 100% !important;
  width: 100vw !important;
  .gmnoprint {
    display: none;
  }
}
