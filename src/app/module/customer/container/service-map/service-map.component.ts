import { animate, state, style, transition, trigger } from '@angular/animations';
import { HttpClient } from '@angular/common/http';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { interval, Observable, of, Subject } from 'rxjs';
import { catchError, map, takeUntil } from 'rxjs/operators';
import snq from 'snq';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { IncomingMessageEnum } from 'src/app/module/shared/enum/incoming-message.enum';
import { SettingsResponse } from 'src/app/module/shared/model/settings.model';
import { IncomingMessageService } from 'src/app/module/shared/service/incoming-message.service';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { ShowPermissionErrorAction } from 'src/app/module/shared/state/common/common.actions';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { environment } from 'src/environments/environment';
import { EquipmentModel } from '../../model/equipment.model';
import { LocationModel } from '../../model/locations.model';
import { ServiceDetailedRequestModel } from '../../model/service-request.model';
import { CurrentLocationModel } from '../../../shared/model/company.model';
import { GetEquipmentDetailAction } from '../../action/equipment.action';
import { EquipmentState } from '../../state/equipment.state';
import { ServiceService } from '../../service/service.service';
import { GetServiceDetailAction } from '../../action/service.action';
import { ServiceState } from '../../state/service.state';

@Component({
  selector: 'cat-service-map',
  templateUrl: './service-map.component.html',
  styleUrls: ['./service-map.component.scss'],
  animations: [
    trigger('loadMore', [
      state('void', style({ height: 0, opacity: 0 })),
      state('*', style({ height: '*', opacity: 1 })),
      transition('* => *', [animate('.2s')]),
    ]),
  ],
})
export class ServiceMapComponent implements OnInit, OnDestroy {
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(SettingsState.basic)
  settings$: Observable<SettingsResponse>;

  @Select(CommonState.currentLocation)
  currentLocation$: Observable<CurrentLocationModel>;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;
  private permissionOkSub: any;
  customerLocationPermission = false;
  errorModal: any;

  equipmentDetail: EquipmentModel;
  serviceData: ServiceDetailedRequestModel;
  selected: ServiceDetailedRequestModel;

  apiLoaded: Observable<boolean>;
  center: google.maps.LatLngLiteral;
  zoom = 10;
  id: string;
  locations: LocationModel[] = [];

  equipmentLocationData: LocationModel;
  customerLocationData: LocationModel;
  serviceLocationData: LocationModel;

  customerNumber: string;
  loading = true;
  nanServiceLocation: boolean;
  serviceLocationModal: boolean;
  centerControl: boolean;
  serviceLocationRefreshTime: number = 10;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly serviceService: ServiceService,
    protected readonly messageFrameService: MessageFrameService,
    protected readonly incomingMessageService: IncomingMessageService,
    protected readonly translateService: TranslateService,
    protected readonly modalService: ModalService,
    httpClient: HttpClient,
  ) {
    this.apiLoaded = httpClient
      .jsonp(
        'https://maps.googleapis.com/maps/api/js?key=AIzaSyBAIRBC-ckhE8xKezngRiaPMD3R42EvVC8',
        'callback',
      )
      .pipe(
        map(() => {
          return true;
        }),
        catchError(() => of(false)),
      );

  }

  ngOnInit() {
    // APP Location Permission
    this.store.dispatch(new ShowPermissionErrorAction(null));
    this.checkPermission();
    this.listenPermissionError();
    this.listenPermissionOK();

    this.centerControl = true;
    this.serviceLocationData = {
      name: 'Service',
      lat: 0,
      lng: 0,
      options: {
        icon: {
          draggable: false,
          url: environment.assets + '/service-car-px40-40.svg'
        }
      }
    };
    this.equipmentLocationData = {
      name: 'Equipment',
      lat: 0,
      lng: 0,
      options: {
        icon: {
          draggable: false,
          url: environment.assets + '/map_pin-02_px45-40.svg'
        }
      }
    };
    this.customerLocationData = {
      name: 'Customer',
      lat: null,
      lng: null,
    };
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    this.customerNumber = loginResponse.customer?.customerNumber;
    this.route.params.subscribe((params) => {
      this.id = params.id;
      if (this.id && this.customerNumber) {
        this.store.dispatch(new GetServiceDetailAction(this.id, this.customerNumber))
          .pipe(map(stateModel => this.store.selectSnapshot(ServiceState.serviceDetail)))
          .subscribe(serviceDetail => {
            this.serviceData = serviceDetail;
            // this.serviceData.workOrderStatus.hasArventoNode = true;
            // this.serviceData.workOrderStatus.servicePlateNumber = '34BOL550';
            if (this.serviceData?.equipmentNumber) {
              this.store.dispatch(new GetEquipmentDetailAction(this.serviceData?.equipmentNumber));
            }
          });
      }
    });

    // Setting
    this.store.dispatch(new GetBasisSettingsAction());
    this.settings$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(settings => {
        if (settings?.serviceLocationRefreshTime) {
          this.serviceLocationRefreshTime = Number(settings.serviceLocationRefreshTime);
        }
      });
    this.setEquipmentLocation();
    this.setCustomerLocation();

    // ilk yuklemede pirpir engellemek icin
    this.apiLoaded.subscribe((stat)=>{
      this.setLocations();
    });
  }

  onClickMap() {
    this.centerControl = false;
  }

  navigateToBack() {
    window.history.back();
  }

  setServiceLocation() {
    if (this.serviceData?.workOrderStatus?.servicePlateNumber) {
      // this.customerService.requestServiceLocation('34BOL550')
      this.serviceService.requestServiceLocation(this.serviceData.workOrderStatus?.servicePlateNumber)
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(data => {
          if (!data) {
            return;
          }
          if (data?.latitude && data?.longitude) {
            this.serviceLocationData.lat = Number(data.latitude);
            this.serviceLocationData.lng = Number(data.longitude);
            this.setLocations();
          }
        });
    } else if (this.serviceLocationModal !== false) {
      this.nanServiceLocation = true;
    }
  }

  setCustomerLocation() {
    this.currentLocation$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(currentLocation => {
        console.log('Map currentLocation: ', currentLocation);

        if (currentLocation) {
          this.customerLocationData = {
            name: 'Customer',
            lat: Number(currentLocation?.latitude),
            lng: Number(currentLocation?.longitude),
          };
          this.setLocations();
        }
      });

  }

  setEquipmentLocation() {
    this.equipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((e) => {
        if (e) {
          this.center = {
            lat: e?.location?.latitude,
            lng: e?.location?.longtitude,
          };
          this.equipmentLocationData.name = e?.brand + ' ' + e?.model;
          this.equipmentLocationData.lat = e?.location?.latitude;
          this.equipmentLocationData.lng = e?.location?.longtitude;
          if (this.serviceData) {
            this.setServiceLocation();
          }
        }
      });

    // this.setServiceLocation();
    interval(this.serviceLocationRefreshTime * 1000)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(t => {
        if (this.customerLocationPermission) {
          this.getCurrentLocation();
        }
        this.setServiceLocation();
      });
  }

  getStatusLabel(serviceRequest: ServiceDetailedRequestModel) {
    switch (serviceRequest?.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
  }

  setLocations() {
    if (this.serviceLocationData.lat && this.equipmentLocationData.lat && this.customerLocationData.lat) {
      this.locations = [
        { ...this.equipmentLocationData },
        { ...this.serviceLocationData },
        { ...this.customerLocationData }
      ];
    } else if (this.serviceLocationData.lat && this.equipmentLocationData.lat) {
      this.locations = [
        { ...this.equipmentLocationData },
        { ...this.serviceLocationData }];
    } else if (this.serviceLocationData.lat) {
      this.locations = [{ ...this.serviceLocationData }];
    }

    if (this.centerControl) {
      this.getCenterPosition();
    }
  }

  onClickMarker(marker) {
    if (!marker) {
      return;
    }
    console.log('Click Marker: ', marker);
    this.onSelect(marker);
  }

  onSelect(marker) {
    this.selected = this.serviceData;
  }

  serviceCarLocationModalClose() {
    this.nanServiceLocation = false;
    this.serviceLocationModal = false;
    this.navigateToBack();
  }

  listenPermissionError() {
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          // this.checkPermission();
          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.goToSettings(data.action)
          });
        });
  }

  checkPermission() {
    this.messageFrameService.sendMessage(FrameMessageEnum.checkPermissions, [
      PermissionEnum.Location,
    ]);
  }

  listenPermissionOK() {
    if (this.permissionOkSub) {
      return;
    }
    this.permissionOkSub = this.incomingMessageService
      .subscribe(IncomingMessageEnum.permissionOk, data => {
        console.log('received data', data);
        this.customerLocationPermission = true;
        this.centerControl = true;
        this.setLocations();
        this.permissionOkSub?.unsubscribe();
        this.permissionOkSub = null;
      });
  }

  goToSettings(action) {
    this.messageFrameService.sendMessage(action);
  }

  getCenterPosition() {
    const latitudearray = [];
    const longitudearray = [];
    let location: any;
    for (location of this.locations) {
      latitudearray.push(location.lat);
      longitudearray.push(location.lng);
    }
    // tslint:disable-next-line: only-arrow-functions
    latitudearray.sort(function(a, b) { return a - b; });
    // tslint:disable-next-line: only-arrow-functions
    longitudearray.sort(function(a, b) { return a - b; });
    const latdifferenece = latitudearray[latitudearray.length - 1] - latitudearray[0];
    let temp = (latdifferenece / 2).toFixed(4);
    const latitudeMid = parseFloat(latitudearray[0]) + parseFloat(temp);
    const longidifferenece = longitudearray[longitudearray.length - 1] - longitudearray[0];
    temp = (longidifferenece / 2).toFixed(4);
    const longitudeMid = parseFloat(longitudearray[0]) + parseFloat(temp);
    const maxdifference = (latdifferenece > longidifferenece) ? latdifferenece : longidifferenece;
    let zoomvalue: number;
    if (maxdifference >= 0 && maxdifference <= 0.0037) {
      zoomvalue = 17;
    } else if (maxdifference > 0.0037 && maxdifference <= 0.0070) {
      zoomvalue = 16;
    } else if (maxdifference > 0.0070 && maxdifference <= 0.0130) {
      zoomvalue = 15;
    } else if (maxdifference > 0.0130 && maxdifference <= 0.0290) {
      zoomvalue = 14;
    } else if (maxdifference > 0.0290 && maxdifference <= 0.0550) {
      zoomvalue = 13;
    } else if (maxdifference > 0.0550 && maxdifference <= 0.1200) {
      zoomvalue = 12;
    } else if (maxdifference > 0.1200 && maxdifference <= 0.4640) {
      zoomvalue = 10;
    } else if (maxdifference > 0.4640 && maxdifference <= 1.8580) {
      zoomvalue = 8;
    } else if (maxdifference > 1.8580 && maxdifference <= 3.5310) {
      zoomvalue = 7;
    } else if (maxdifference > 3.5310 && maxdifference <= 7.3367) {
      zoomvalue = 6;
    } else if (maxdifference > 7.3367 && maxdifference <= 14.222) {
      zoomvalue = 5;
    } else if (maxdifference > 14.222 && maxdifference <= 28.000) {
      zoomvalue = 4;
    } else if (maxdifference > 28.000 && maxdifference <= 58.000) {
      zoomvalue = 3;
    } else {
      zoomvalue = 1;
    }
    if (zoomvalue > 8) {
      this.center = {
        lat: latitudeMid,
        lng: longitudeMid
      };
    } else {
      this.center = {
        lat: latitudeMid - 1,
        lng: longitudeMid
      };
    }
    this.zoom = zoomvalue;
    this.loading = false;
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  private getCurrentLocation() {
    this.messageFrameService.sendMessage(FrameMessageEnum.getLocation);
  }
}
