import { Component } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CustomerModel } from '../../model/customer.model';
import { CustomerState } from '../../state/customer.state';

@Component({
  selector: 'cat-address-list',
  templateUrl: './address-list.component.html',
  styleUrls: ['./address-list.component.scss'],
})
export class AddressListComponent {
  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;
}
