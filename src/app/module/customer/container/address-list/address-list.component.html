<div class="px-3 py-4">
  <ng-container *ngIf="customer$ | async as customer">
    <ng-container *ngIf="customer?.details?.addresses.length; else emptyList">
      <div
        *ngFor="let address of customer?.details?.addresses; last as last"
        [class.border-bottom]="!last"
        class="card bg-transparent mb-3">
        <div class="card-body">
          <cat-address-item [address]="address"></cat-address-item>
        </div>
      </div>
    </ng-container>
  </ng-container>
</div>
<ng-template #emptyList>
  <div class="text-center">
    {{'_address_list_empty' | translate}}
  </div>
</ng-template>
