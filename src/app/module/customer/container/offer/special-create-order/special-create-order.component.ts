import { Component, OnInit } from '@angular/core';
import { CreateOrderModel, CreateOrderModelData } from '../../../model/offer.model';
import { Select, Store } from '@ngxs/store';
import { OfferState } from '../../../state/offer.state';
import { Observable } from 'rxjs';
import { CreateOrderQuotationAction } from '../../../action/offer.actions';
import { ActivatedRoute } from '@angular/router';
import { environment } from '../../../../../../environments/environment';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { UserModel } from '../../../../authentication/model/user.model';

declare const halyk: any;

@Component({
  selector: 'cat-special-create-order',
  template: `
    <cat-empty-content *ngIf="showPaymentErrorMessage" [message]="'_extra_close_message'"
                       [iconName]="'close-circle'" [hasBackButton]="false"></cat-empty-content>
    <cat-loader [show]="createOrderLoading$ | async"></cat-loader>
  `,
  styles: [``]
})
export class SpecialCreateOrderComponent implements OnInit {
  @Select(OfferState.createOrder)
  createOrder$: Observable<CreateOrderModel>;

  @Select(OfferState.createOrderLoading)
  createOrderLoading$: Observable<boolean>;

  paymentObject: CreateOrderModelData;
  quotationNumber: string;
  type: any;
  user: UserModel;
  userNotFound = false;

  showPaymentNotFound = false;
  showPaymentErrorMessage = false;

  scriptsTest = [
    'https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js',
    'https://test-epay.homebank.kz/payform/payment-api.js'
  ];

  scriptProd = [
    'https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js',
    'https://epay.homebank.kz/payform/payment-api.js'
  ];

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
  ) {
    this.route.queryParams.subscribe(params => {
      this.quotationNumber = params?.quotationNumber;
      this.type = params?.type || 3;
    });
  }

  ngOnInit(): void {
    this.user = this.store.selectSnapshot(LoginState.user);
    if (this.user) {
      if (this.quotationNumber) {
        this.loadScripts();
        this.loadData();
        const timer = setTimeout(() => {
          this.showPaymentErrorMessage = true;
          clearTimeout(timer);
        }, 5000);
      } else {
        this.showPaymentNotFound = true;
      }
    } else {
      this.userNotFound = true;
    }
  }

  loadData() {
    this.store.dispatch(new CreateOrderQuotationAction(this.quotationNumber, this.type));
    this.createOrder$.subscribe(data => {
      if (data?.data) {
        this.paymentObject = data.data;
        this.startCreateOrder();
      }
    });
  }

  startCreateOrder() {
    halyk.showPaymentWidget(this.paymentObject, this.paymentCallback);
  }

  paymentCallback(data) {
    console.log('Callback:::: ', data);
    if (data.success) {
      console.log('Success: ', data);
    } else {
      // this.showPaymentErrorMessage = true;
      console.log('Failure: ', data);
    }
  }

  loadScripts() {
    if (['local', 'development', 'stage'].indexOf(environment.envName) !== -1) {
      this.scriptsTest.map((script) => {
        this.injectScript(script);
      });
    } else {
      this.scriptProd.map((script) => {
        this.injectScript(script);
      });
    }
  }

  injectScript(src) {
    const scriptInjector = document.createElement('script');
    scriptInjector.type = 'text/javascript';
    scriptInjector.src = src;
    document.head.appendChild(scriptInjector);
  }
}
