import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { MediaService } from 'src/app/export/media/service/media/media.service';

@Component({
  selector: 'cat-offer-pdf',
  templateUrl: './offer-pdf.component.html',
  styleUrls: [ './offer-pdf.component.scss' ]
})
export class OfferPdfComponent implements OnInit {
  loading = true;

  constructor(private readonly route: ActivatedRoute, private readonly mediaService: MediaService, private sanitizer: DomSanitizer) {}

  ngOnInit(): void {}

  getPdf() {
    const { id } = this.route.snapshot.params;

    const base = 'https://prod.borusancat.com/lgnz/api/quotation/getPdf?quotationNumber=0061159417';
    // const base = `https://prod.borusancat.com${this.mediaService.offerPdfUrl(id)}`;

    const url = 'http://docs.google.com/gview?url=' + base + '&embedded=true';
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  pageRendered($event: CustomEvent<any>) {
    this.loading = false;
  }
}
