<div class="offer-create-order" *ngIf="offer && !approvalFormSendStatus; else notFindOffer">
  <mat-horizontal-stepper linear
                          (selectionChange)="onSelectionChange($event)"
  >
    <mat-step>
      <ng-template matStepLabel>{{ "_offer_detail" | translate }}</ng-template>
      <!-- TODO Adres seçeneği eklenecek -->
      <!-- <ng-template matStepLabel>{{ '_address' | translate }}</ng-template> -->
      <!-- TODO Offer Detail - kaldırılacak -->
      <div class="pt-3" style="min-height: 150px">
        <div class="d-flex justify-content-between align-items-center w-100">
          <div class="offer-text">
            <div class="mr-2 pr-1 d-flex flex-fill">
              <div class="mb-1">{{ "_offer_no" | translate }}</div>
              <span class="mr-2 pr-1">:</span>{{ offer?.quotationNumber }}
            </div>
            <div *ngIf="offer?.quotationValidDate && offer?.processType !== OfferCategoriesEnum.CVA_OFFER"
                 class="mr-2 pr-1 d-flex flex-fill">
              <div class="mb-1">{{ "_validity_date" | translate }}</div>
              <span class="mr-2 pr-1">:</span>{{ offer?.quotationValidDate | date: "dd.MM.yyyy" }}
            </div>
            <div class="mr-2 pr-1 d-flex flex-fill">
              <div class="mb-1">{{ "_total_amount" | translate }}</div>
              <span class="mr-2 pr-1">:</span>{{ offer?.totalAmount | number: '':locale }} {{ offer?.currency }}
            </div>
          </div>
          <div class="d-flex justify-content-center align-items-center ml-2">
            <div catDownloadFile [downloadType]="DownloadTypeEnum.offer"
                 [downloadParams]="{ quotationNumber: offer.quotationNumber, guid: offer.guid }"
                 (downloadLoading)="downloading($event)"
                 class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                 [class.spinner]="isDownloading(offer?.quotationNumber)">
              <i class="icon icon-spinner8" *ngIf="isDownloading(offer?.quotationNumber)"></i>
              <i class="icon icon-download" *ngIf="!isDownloading(offer?.quotationNumber)"></i>
              <a class="d-none" [download]="offer?.quotationNumber"></a>
            </div>
          </div>
        </div>
        <div class="mr-2 pr-1 d-flex flex-fill w-100" *ngIf="offerDetail?.address?.address">
          <div class="mb-1">{{ "_address" | translate }}</div>
          <span
            class="mr-2 pr-1">:</span>{{ offerDetail?.address?.address }} {{ offerDetail?.address?.district }} {{ offerDetail?.address?.city }}
        </div>
        <div *ngIf="offerDetail && offerDetail?.items?.length" class="d-flex flex-column mt-4">
          <label for="table"><b>{{ '_product_detail' | translate }}</b></label>
          <div class="table-container" style="overflow-y: auto; overflow-x: hidden;">
            <table
              *ngIf="selectedStep !== 1"
              id="table" class="table table-striped table-sm operation-history-table my-2">
              <thead>
              <tr class="table-header" [class.table-header-ge]="language=='ka'">
                <th scope="col">
                  {{ "_description" | translate }}
                </th>
                <th scope="col">
                  {{ "_quotatity" | translate }}
                </th>
                <th scope="col">
                  {{ "_gross_weight" | translate }}
                </th>
                <th scope="col">
                  {{ "_part_no" | translate }}
                </th>
                <th scope="col">
                  {{ "_amount" | translate }}
                </th>
              </tr>
              </thead>
              <tbody class="table-header" [class.table-header-ge]="language=='ka'">
              <tr *ngFor="let items of offerDetail?.items; index as i">
                <td>{{ items.description }}</td>
                <td>{{ parseInt(items.quotatity) }}</td>
                <td>{{ items.grossWeight }} {{ items.weightUnit }}</td>
                <td>{{ items.originalPart }}</td>
                <td>{{ items.grossValue | number }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="bottom-buttons">
        <div class="d-flex flex-row flex-nowrap justify-content-between px-3 mb-2">
          <button class="btn btn-sm btn-secondary text-white px-2 mr-auto d-flex" (click)="returnOfferList()">
            <div style="transform: rotateY(180deg)">
              <i class="icon icon-chevron-right"></i>
            </div>
          </button>
          <button *ngIf="offer?.actions?.indexOf('CreateOrder') && offer?.status !== OfferStatusEnum.IsProcessing"
                  class="btn btn-sm btn-warning text-white px-2 ml-auto" matStepperNext>
            {{ "_offer_create_order" | translate }}
            <i class="icon icon-chevron-right"></i>
          </button>
        </div>
      </div>
    </mat-step>
    <mat-step *ngIf="offer?.actions?.indexOf('CreateOrder') && offer?.status !== OfferStatusEnum.IsProcessing">
      <ng-template matStepLabel>{{ "_payments" | translate }}</ng-template>
      <div class="px-2 pb-5 pt-3" style="min-height: 150px">
        <ul>
          <li *ngFor="let method of paymentMethods; index as currentIndex">
            <div>
              <input name="paymentMethod" type="radio" [id]="'payment' + method.type"
                     [checked]="method.isDefault
                        || paymentMethods?.length === 1
                        || selectedPaymentMethod?.type === method.type"
                     (click)="selectPaymentMethod(method, false)"/>
              <label [for]="'payment' + method.type" class="d-flex">
                <span class="text-wrap">
                  {{ method.name }}
                </span>
                <span
                  *ngIf="method.type === 3 && offerDetail?.cc_TotalAmount && offerDetail?.cc_TotalAmount < offerDetail?.totalAmount && offerDetail?.cc_TotalAmount > 0 && offerDetail?.totalAmount"
                  class="ml-2"
                >
                  / {{ offerDetail?.cc_TotalAmount | number : "" : locale }}
                  {{ offerDetail?.currency }}</span
                >
                <span
                  *ngIf="(method.type === 2 && offerDetail?.cc_TotalAmount && offerDetail?.cc_TotalAmount < offerDetail?.totalAmount) || (method.type === 3 && !offerDetail?.cc_TotalAmount && offerDetail?.cc_TotalAmount < offerDetail?.totalAmount)"
                  class="ml-2"
                >
                  / {{ offerDetail?.totalAmount | number : "" : locale }}
                  {{ offerDetail?.currency }}</span>
              </label>
            </div>
          </li>
          <li *ngIf="offer?.status === null || offer.status === 'Revised'">
            <input name="paymentMethod" type="radio" [id]="'payment' + 'Revised'"
                   [checked]="finishWithoutPayment"
                   (click)="selectPaymentMethod('Revised', true)"/>
            <label [for]="'payment' + 'Revised'" class="d-flex flex-column">
              <span class="text-wrap">
                 {{ '_pay_later' | translate }}
              </span>
            </label>
          </li>
          <div class="pt-5"
               *ngIf="this.selectedPaymentMethod && !this.finishWithoutPayment && agreements?.length"
          >
            <!-- Agreement Checkboxes -->
            <cat-order-agreement-list
              [form]="orderAgreementForm"
              [params]="getOrderAgreementsData()"
              [agreements]="agreements"
            >
            </cat-order-agreement-list>
          </div>
        </ul>
      </div>
      <div class="mt-auto d-flex flex-row flex-nowrap justify-content-between">
        <button class="btn btn-sm btn-secondary text-white px-2" style="transform: rotateY(180deg);" matStepperPrevious>
          <!-- {{ "_prev" | translate }}-->
          <i class="icon icon-chevron-right"></i>
        </button>
        <!-- <button class="btn btn-sm btn-warning text-white px-4 ml-auto" matStepperNext>{{ '_next' | translate }}</button> -->
        <button class="btn btn-sm btn-warning text-white px-3"
                [disabled]="!selectedPaymentMethod
                || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.CreateOrder) >= 0)
                || !allAgreementsChecked && (selectedPaymentMethod.type === 2 || selectedPaymentMethod.type === 3)"

                (click)="showHideConfirmModal()">
          {{ getPaymentButtonText() | translate }}
        </button>
      </div>
    </mat-step>
  </mat-horizontal-stepper>
</div>
<ng-template #notFindOffer>
  <div class="text-center" *ngIf="!offerListLoading">
    <cat-empty-content [iconName]="'offer'" [message]="'_offer_list_empty'">
      <button catUserClick [section]="'OFFER_CREATE_ORDER'" [subsection]="'NOT_FOUND_RETURN_BACK'"
              (click)="returnOfferList()"
              class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm">
        {{ "_return_offer_list" | translate }}
      </button>
    </cat-empty-content>
  </div>
</ng-template>

<div *ngIf="openPaymentModal && !offerListLoading" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <div class="text-center px-4 py-5">
      <div class="success-message mb-5">
        <ng-container *ngIf="paymentStatus else otherPayment">
          <div class="d-flex flex-column" [ngSwitch]="paymentStatus?.paymentTransactionStatusText">
            <ng-container *ngSwitchCase="'Error'">
              <i class="icon message-class icon-close-circle text-danger d-inline-block mb-4"></i>
              <div>{{ "_payment_error_message" | translate }}</div>
            </ng-container>
            <ng-container *ngSwitchCase="'Warning'">
              <i class="icon message-class icon-close-circle text-warning d-inline-block mb-4"></i>
              <div>{{ "_payment_warning_message" | translate }}</div>
            </ng-container>
            <ng-container *ngSwitchCase="'Success'">
              <i class="icon icon-message-success d-inline-block mb-4"></i>
              <div>{{ "_payment_processed_message" | translate }}</div>
            </ng-container>
            <ng-container *ngSwitchCase="'New'">
              <i class="icon icon-message-success d-inline-block mb-4"></i>
              <div>{{ "_payment_new_message" | translate }}</div>
            </ng-container>
          </div>
          <small class="font-size-12px" *ngIf="
              paymentStatus?.paymentTransactionStatusText !== 'Success' &&
              paymentStatus?.paymentTransactionStatusText !== 'New'
            ">
            <ng-container *ngIf="paymentStatus?.errorMessage">
              {{ paymentStatus.errorMessage }}
            </ng-container>
          </small>
        </ng-container>
        <ng-template #otherPayment>
          <i class="icon icon-message-success d-inline-block mb-4"></i>
          <div>{{ "_payment_processed_message" | translate }}</div>
        </ng-template>
      </div>
      <div class="btn btn-warning btn-gradient btn-block text-white shadow" (click)="returnOfferList()">
        {{ "_close" | translate }}
      </div>
    </div>
  </div>
</div>


<div *ngIf="approvalFormSendStatus" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <div class="text-center px-4 py-5">
      <i class="icon icon-message-success d-inline-block mb-4"></i>
      <div class="success-message mb-5">
        {{ "_offer_successfully_approval_send_form" | translate }}
      </div>
      <div class="btn btn-warning btn-gradient btn-block text-white shadow" (click)="returnOfferList()">
        {{ "_close" | translate }}
      </div>
    </div>
  </div>
</div>

<cat-loader
  [show]="offerListLoading || (createOrderLoading$ | async) || (offerDetailLoading$ | async ) || loading"></cat-loader>
<cat-basic-modal *ngIf="offerApprovalModal && offer" [(status)]="offerApprovalModal"
                 [headerText]="'_offer_approval' | translate">
  <div class="mb-3">
    <div class="d-flex justify-content-between align-items-center card m-2 p-2">
      <div class="offer-text">
        <div class="mr-2 pr-1 d-flex flex-fill text-wrap">
          <div class="mb-1 text-nowrap">{{ "_offer_no" | translate }}</div>
          <span class="mr-2 pr-1">:</span>{{ offer?.quotationNumber }}
        </div>
        <div *ngIf="offer?.quotationValidDate && offer?.processType !== OfferCategoriesEnum.CVA_OFFER">
          <div class="mr-2 pr-1 d-flex flex-fill">
            <div class="mb-1">{{ "_validity_date" | translate }}</div>
            <span class="mr-2 pr-1">:</span>{{ offer?.quotationValidDate | date: "dd.MM.yyyy" }}
          </div>
        </div>
      </div>
    </div>
    <div class="text-center">{{ "_offer_approval_text" | translate }}</div>
    <div class="mx-auto text-center mt-4">
      <button class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow col-8 mr-3" catUserClick
              [section]="'OFFER'"
              [subsection]="'OFFER_APPROVED_CLICK'" [data]="{ quotationNumber: offer?.quotationNumber }"
              (click)="finishWithoutPaying()">
        {{ "_offer_approval" | translate }}
      </button>
    </div>
  </div>
</cat-basic-modal>

<cat-basic-modal *ngIf="confirmModal" [(status)]="confirmModal" [headerText]="'_complete' | translate">
  <div class="mb-3">
    <div class="d-flex justify-content-between align-items-center  m-2 p-2">
      <div class="offer-text">
        <div class="mr-2 pr-1 d-flex flex-fill text-wrap">
          {{ confirmModalText }}
        </div>
        <div>
        </div>
      </div>
    </div>

    <div class="mx-auto text-center mt-4 d-flex">
      <button (click)="goToPayment()"
              class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow col-6 mr-1">
        {{ "_yes" | translate }}
      </button>
      <button (click)="showHideConfirmModal()"
              class="modal-btn btn-sm btn btn-secondary btn-gradient text-white shadow col-6 ml-1">
        {{ "_no" | translate }}
      </button>
    </div>
  </div>
</cat-basic-modal>
