import { AfterViewInit, ChangeDetectorRef, Component, forwardRef, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslatePipe, TranslateService, } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import snq from 'snq';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { environment } from 'src/environments/environment';
import { CustomerDetailAction } from '../../../action/customer.actions';
import { LogService } from '../../../service/log.service';
import {
  ClearCreateOrderAction,
  ClearOfferDetailAction,
  ClearQuotationAction,
  CreateOrderQuotationAction,
  CreateOrderQuotationRemoveAction,
  GetOfferDetailAction,
  GetOfferListAction,
  GetPaymentStatusAction
} from '../../../action/offer.actions';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import {
  CreateOrderModel,
  OfferDetailModel,
  OfferModel,
  PaymentMethodModel,
  PaymentStatusModel
} from '../../../model/offer.model';
import { OfferState } from '../../../state/offer.state';
import { PaymentFinishedRemoveAction } from 'src/app/module/shared/state/common/common.actions';
import { CustomerService } from '../../../service/customer.service';
import { IncomingMessageEnum } from '../../../../shared/enum/incoming-message.enum';
import { IncomingMessageService } from '../../../../shared/service/incoming-message.service';
import { OfferCategoriesEnum, OfferStatusEnum } from '../../../../definition/enum/offerList.enum';
import { OfferService } from '../../../service/offer.service';
import { FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { DownloadTypeEnum } from 'src/app/export/media/enum/download-type.enum';
import { Lang2LocaleEnum } from '../../../../shared/enum/locale.enum';
import { LogSectionEnum } from '../../../../definition/enum/log-section.enum';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { OrderAgreementModel, QuotationAgreementRequest } from '../../../../definition/model/agreement.model';

@Component({
  selector: 'cat-offer-create-order',
  templateUrl: './offer-create-order.component.html',
  styleUrls: ['./offer-create-order.component.scss'],
  providers: [
    TranslatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => OfferCreateOrderComponent),
      multi: true,
    },
  ],
})
export class OfferCreateOrderComponent implements OnInit, OnDestroy, AfterViewInit {
  quotationNumber: string;
  quotationGuid: string;
  OfferCategoriesEnum = OfferCategoriesEnum;

  customerNumber: string;
  offer: OfferModel;
  paymentMethods: PaymentMethodModel[];
  selectedPaymentMethod: PaymentMethodModel;

  confirmModal = false;
  confirmModalText: any;
  DownloadTypeEnum = DownloadTypeEnum;
  loadingAttachment: any[] = [];

  @Select(OfferState.offerList)
  offerList$: Observable<OfferModel[]>;

  @Select(OfferState.offerDetail)
  offerDetail$: Observable<OfferDetailModel[]>;
  @Select(OfferState.offerDetailLoading)
  offerDetailLoading$: Observable<boolean>;
  offerDetail: OfferDetailModel;
  finishWithoutPayment: boolean;

  @Select(OfferState.createOrderLoading)
  createOrderLoading$: Observable<boolean>;
  @Select(OfferState.offerListLoading)
  offerListLoading$: Observable<boolean>;
  offerListLoading: boolean;

  @Select(OfferState.createOrder)
  createOrder$: Observable<CreateOrderModel>;
  createOrder: CreateOrderModel;

  loading = false;

  @Select(CommonState.paymentFinished)
  paymentFinished$: Observable<any>;
  paymentFinished: {
    quotationNumber: string;
    isPaymentClosedFromUser: boolean;
    isPaymentFinish: boolean;
    paymentWithError: boolean;
  };

  @Select(OfferState.getPaymentStatus)
  paymentStatus$: Observable<PaymentStatusModel>;
  paymentStatus: PaymentStatusModel;

  @Select(OfferState.offerApprove)
  offerApprove$: Observable<boolean>;
  offerApprove: boolean;

  openPaymentModal = false;
  private errorModal: any;
  private goToPay = false;
  private subscriptions$: Subject<boolean> = new Subject();
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  approvalFormSendStatus = false;
  private showApproveSuccess = false;
  offerApprovalModal: boolean;
  PermissionEnum = PermissionEnum;

  locale: string;

  OfferStatusEnum = OfferStatusEnum;
  private paymentStartStatus: boolean;
  selectedStep: number;
  language: string;

  agreements: OrderAgreementModel[];
  allAgreementsChecked = false;

  orderAgreementForm = new FormGroup({});

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly store: Store,
    private readonly cd: ChangeDetectorRef,
    private readonly frameMessage: MessageFrameService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly logger: LogService,
    private readonly customerService: CustomerService,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly offerService: OfferService,
    protected readonly translatePipe: TranslatePipe,
  ) {
    const { id } = this.route.snapshot.params;
    id ? this.quotationNumber = id : this.returnOfferList();

    this.route.queryParams.subscribe(params => {
      this.quotationGuid = params['guid'];
    });
  }

  ngOnInit() {
    this.store.dispatch(new GetOfferDetailAction(this.quotationNumber, this.quotationGuid));
    this.offerDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data: any) => {
        if (!data) {
          return;
        }
        this.offerDetail = data;
        this.getAgreements();
      });
    const currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);
    if (currentCustomer?.customer) {
      setTimeout(() => {
        this.store.dispatch(
          new CustomerDetailAction(currentCustomer.customer?.customerNumber)
        );
      }, 0);
      this.customerNumber = currentCustomer.customer?.customerNumber;
    }
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    if (loginResponse) {
      this.store.dispatch(
        new GetOfferListAction(snq(() => loginResponse.customer.customerNumber), null)
      );
    }
    this.language = this.translateService.getDefaultLang();

    this.offerList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.offer = data.find(f => f.quotationNumber === this.quotationNumber);
          this.paymentMethods = this.offer?.paymentMethods;
          this.selectedPaymentMethod = this.selectedPaymentMethod
            || this.paymentMethods?.find(p => p.isDefault === true);
          if (this.paymentMethods?.length === 1) {
            this.selectPaymentMethod(this.paymentMethods[0], false);
          }
        }
        this.locale = Lang2LocaleEnum[this.language] ? Lang2LocaleEnum[this.language] : 'tr-TR';
      });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    this.offerListLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => this.offerListLoading = x);

    this.createOrder$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.createOrder = data;

          if (data?.errorMessage) {
            this.openPaymentModal = false;
            this.errorModal = this.modalService.errorModal({
              message: this.translateService.instant('_general_error_message'),
              button: this.translateService.instant('_close'),
              buttonClick: (modal) => this.returnOfferList(modal)
            });
            this.store.dispatch(new ClearCreateOrderAction());
            return;
          }
          this.paymentStartStatus = false;
          if (this.offer.totalAmount === 0 || (this.selectedPaymentMethod?.type === 4 && !data?.errorMessage)) {
            this.openPaymentModal = true;
            this.store.dispatch(new ClearCreateOrderAction());
          } else if (this.selectedPaymentMethod?.type === 3 && data?.url) {
            this.openPaymentFrame(this.createOrder.url, this.quotationNumber);
          } else if (this.selectedPaymentMethod?.type === 3 && data.paymentProviderType === 2) {
            this.navigateSpecialPayment();
          } else if (this.selectedPaymentMethod?.type === 2 && !data?.errorMessage) {
            this.openPaymentModal = true;
            this.store.dispatch(new ClearCreateOrderAction());
          }
        }
      });
    this.listenAgreementChanges();

  }

  downloading(attachment) {
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  showHideConfirmModal() {
    this.confirmModal = !this.confirmModal;
  }

  selectPaymentMethod(method, val) {
    console.log('method: ', method);
    this.selectedPaymentMethod = method;
    this.finishWithoutPayment = val;
    if (this.finishWithoutPayment) {
      this.confirmModalText = this.translatePipe.transform('_want_to_confirm');
    } else {
      this.confirmModalText = this.translatePipe.transform('_selected_payment_text')
        .replace('{PaymentMethod}', this.selectedPaymentMethod.name);
    }

  }

  goToPayment() {
    if (this.finishWithoutPayment) {
      this.finishWithoutPaying();
      return;
    }
    this.goToPay = true;
    if (this.offer?.status === OfferStatusEnum.Revised
      || this.offer?.status === null) {
      this.confirmModal = false;
      this.quotationApprove();
    } else {
      this.paymentStart();
    }
  }

  paymentStart() {
    if (this.paymentStartStatus) {
      return;
    }
    // this.paymentStartStatus = true;
    this.store.dispatch(new CreateOrderQuotationAction(this.quotationNumber, this.selectedPaymentMethod.type, this.allAgreementsChecked)).pipe(
      takeUntil(this.subscriptions$)
    ).subscribe(
      () => {
        this.confirmModal = false;
      }
    );
    this.logger.action('OFFER', 'OFFER_CREATE_GO_PAYMENT', {
      quotationNumber: this.quotationNumber,
      paymentMethod: this.selectedPaymentMethod?.name
    }).subscribe();
    if (this.selectedPaymentMethod?.type === 3) {
      this.paymentIframeEventSubscriptions();
    }
  }

  quotationApprove() {
    if (this.offer?.status === null || this.offer?.status === OfferStatusEnum.Revised) {
      this.loading = true;
      this.offerService.approveQuotation(this.quotationNumber)
        .subscribe(x => {
          if (x) {
            this.offerApprove = x;
            this.logger.action(LogSectionEnum.OFFER, 'OFFER_APPROVE_CLICK', {
              QuotationNumber: this.offer?.quotationNumber
            }).subscribe();
            this.store.dispatch(new GetOfferListAction(this.customerNumber))
              .subscribe(() => {
                this.loading = false;
                if (this.showApproveSuccess) {
                  this.approvalFormSendStatus = true;
                } else if (this.goToPay) {
                  this.paymentStart();
                }
              }, () => {
                this.loading = false;
              });
          }
        }, () => {
          this.loading = false;
        });
    } else if (!this.showApproveSuccess) {
      this.paymentStart();
    }
  }

  paymentIframeEventSubscriptions() {
    this.paymentFinished$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(payment => {
        if (payment && this.createOrder?.traceId && payment?.isPaymentFinish) {
          this.offerListLoading = true;
          this.paymentFinished = payment;
          console.log('createOrder?.traceId:: ', this.createOrder?.traceId);
          setTimeout(() => {
            this.store.dispatch(new GetPaymentStatusAction(this.createOrder?.traceId));
          }, 2000);
          this.paymentStatus$
            .pipe(takeUntil(this.subscriptions$))
            .subscribe(status => {
              if (status && this.paymentFinished?.isPaymentFinish) {
                console.log('paymentStatus: ', status);
                this.paymentStatus = status;
                this.logger.action('OFFER', 'OFFER_CREATE_ORDER_PAYMENT', {
                  quotationNumber: this.quotationNumber,
                  paymentMethod: this.selectedPaymentMethod?.name,
                  paymentType: this.selectedPaymentMethod?.type,
                  paymentStatus: this.paymentStatus?.paymentTransactionStatus,
                  paymentStatusText: this.paymentStatus?.paymentTransactionStatusText,
                  orderId: this.paymentStatus?.orderId
                }).subscribe();
              }
            });
          this.openPaymentModal = true;
          this.paymentStartStatus = false;
        }
      });
  }

  navigateSpecialPayment() {
    this.customerService.getOTT().subscribe(ott => {
      if (ott) {
        this.frameMessage.sendMessage(FrameMessageEnum.openModule, {
          url: window.location.origin + '/' + environment.rootUrl
            + '/customer/offer/special-order?ott=' + ott.oneTimeToken
            + '&quotationNumber=' + this.quotationNumber
            + '&type=' + this.selectedPaymentMethod?.type,
          title: this.translateService.instant('_payment_page_header'),
          closeButton: true
        });
        this.incomingMessageService
          .subscribe(IncomingMessageEnum.webviewReopened, () => {
            this.returnOfferList();
          });
      }
    });
  }

  openPaymentFrame(url, quotationNumber) {
    this.frameMessage.sendMessage(FrameMessageEnum.openModule, {
      url, quotationNumber,
      title: this.translateService.instant('_payment_page_header'),
      isPayment: true
    });
  }

  returnOfferList = (modal = null) => {
    modal?.close();
    this.logger.action('OFFER', 'PAYMENT_RETURN_OFFER', {
      quotationNumber: this.quotationNumber,
      paymentMethod: this.selectedPaymentMethod?.name,
      status: this.paymentStatus
    }).subscribe();
    this.store.dispatch(new PaymentFinishedRemoveAction());
    this.store.dispatch(new CreateOrderQuotationRemoveAction());
    this.store.dispatch(new ClearOfferDetailAction());
    this.router
      .navigate([
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'offer-list',
        ],
        { relativeTo: this.route, replaceUrl: true })
      .then();
  };

  ngAfterViewInit() {
    this.cd.detectChanges();
  }

  ngOnDestroy() {
    this.store.dispatch([
      new ClearOfferDetailAction(),
      new ClearQuotationAction()
    ]);
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  finishWithoutPaying() {
    this.offerApprovalModal = false;
    this.confirmModal = false;
    this.showApproveSuccess = true;
    this.quotationApprove();
  }

  // approveWithoutPayment() {
  //   this.offerApprovalModal = true;
  //   this.confirmModal = false;
  // }

  parseInt(value) {
    return parseInt(value, 10);
  }

  getPaymentButtonText() {
    if (this.offer?.isApproved) {
      return this.offer.paymentMethods?.length === 1 &&
      this.offer.paymentMethods[0]?.type === 2
        ? '_offer_open_account_payment'
        : this.offer.paymentMethods?.length === 1 &&
        this.offer.paymentMethods[0]?.type === 3
          ? '_go_to_payment_page'
          : '_complete';

    }
    return '_complete';
  }

  onSelectionChange($event: StepperSelectionEvent) {
    this.selectedStep = $event.selectedIndex;
  }

  getOrderAgreementsData(): QuotationAgreementRequest {
    return {
      quotationNumber: this.quotationNumber,
      paymentMethod: this.selectedPaymentMethod?.type
    };
  }

  checkAgreements() {
    const agreements = this.orderAgreementForm.get('agreements').value;
    return !Object.values(agreements || {}).some(value => !value);
  }

  private listenAgreementChanges() {
    this.orderAgreementForm?.valueChanges
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(agreements => {
        this.allAgreementsChecked = this.checkAgreements();
        console.log('All agreements checked:', this.allAgreementsChecked);
      });
  }

  private getAgreements() {
    this.agreements = [];
    if (this.offerDetail.isShowPreInformAgreement) {
      this.agreements.push({
        name: 'pre_information',
        method: 'getOrderPreAgreement',
        description: this.translateService.instant('_pre_information_agreement')
      });
    }
    if (this.offerDetail.isShowDistanceSaleAgreement) {
      this.agreements.push({
        name: 'distance_sales',
        method: 'getOrderDistanceAgreement',
        description: this.translateService.instant('_distance_sales_agreement')
      });
    }
    this.allAgreementsChecked = !this.agreements?.length;
  }
}
