@import "variable/bootstrap-variable";

.offer-create-order {
  background-color: $body-bg;

  //full fill
  display: flex;
  flex-flow: column;
  min-height: 100vh;
  padding: 5px 0;

  ::ng-deep .mat-step-icon {
    display: none;
  }

  // ? Stepper Header Click Disable
  // ::ng-deep .mat-horizontal-stepper-header-container,
  // ::ng-deep .mat-step-header {
  //   pointer-events: none !important;
  // }

  ::ng-deep .mat-stepper-horizontal {
    height: calc(100vh - 11px);
    overflow: hidden;
  }

  ::ng-deep .mat-horizontal-stepper-header-container {
    justify-content: space-between;
  }

  ::ng-deep .mat-stepper-horizontal-line {
    min-width: 15px;
    border-color: #ffa300;
  }

  ::ng-deep .mat-step-label {
    white-space: break-spaces;
    text-align: center;
    background-color: #fff;
    padding: 0.57rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 8px;
    color: #ffa300;
    box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%)
  }

  ::ng-deep .mat-step-label-active.mat-step-label-selected {
    background-color: #ffa300;
    color: #fff;
  }

  ::ng-deep .mat-step-header,
  ::ng-deep .mat-stepper-horizontal-line {
    padding-bottom: 10px;
  }


  ::ng-deep .mat-horizontal-content-container {
    height: 100%;
  }

  ul {
    list-style: none;
    margin: 0 9px;
    padding: 0;

    // max-height: 58vh;
    li {
      margin: 10px 0;
      //height: 30px;

      &:first-child {
        margin-top: 0;
      }

      label {
        white-space: nowrap;
        //overflow: hidden !important;
        text-overflow: ellipsis;
        max-width: 90%;
      }
    }
  }

  [type="radio"]:checked,
  [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
  }

  [type="radio"]:checked+label,
  [type="radio"]:not(:checked)+label {
    position: relative;
    padding-left: 36px;
    cursor: pointer;
    display: inline-block;
    color: #666;
    font-size: 16px;
    line-height: 24px;
  }

  [type="radio"]:checked+label:before,
  [type="radio"]:not(:checked)+label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5%;
    width: 22px;
    height: 22px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
  }

  [type="radio"]:checked+label:before {
    border-color: #ffa300;
  }

  [type="radio"]:checked+label:after,
  [type="radio"]:not(:checked)+label:after {
    content: "";
    width: 16px;
    height: 16px;
    background: #ffa300;
    position: absolute;
    top: calc(5% + 3px);
    left: 3px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }

  [type="radio"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  [type="radio"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}


.after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.success-message {
  font-size: 26px;
  font-weight: 700;
  line-height: 1;
}

.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.message-class {
  font-size: 60px;
  padding-top: 1px;
}

.z-index-over-loader {
  z-index: 10001;
}

.table-header {
  font-size: 12px;
  text-align: center;
  &-break {
    font-size: 12px;
    text-align: center;
    word-break: break-all;
  }
}

.table-header-ge {
  font-size: 11px;
  &-break {
    font-size: 11px;
  }
}

.operation-history-table {
  // margin-left: 15px !important;
    // margin-right: 15px !important;
  width: calc(100vw - 45px) !important;
  margin: 0 auto;
}

.table-container thead th {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #f8f9fa;
}

.download-file {
  width: 54px;
  height: 54px;
  min-width: 54px;
  min-height: 54px;
  background-color: #f5f5f5;
  font-size: 18px;
}

::ng-deep .card {
  border: unset !important;
}
.bottom-buttons {
    position: absolute;
    right: 0;
    left: 0;
    bottom: 0;
}
