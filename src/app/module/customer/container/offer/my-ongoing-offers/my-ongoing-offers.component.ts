import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { OfferState } from '../../../state/offer.state';
import { Observable, Subject } from 'rxjs';
import { GetMyOngoingQuotationsAction } from '../../../action/offer.actions';
import { takeUntil } from 'rxjs/operators';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { CustomerModel } from '../../../model/customer.model';
import { CustomerState } from '../../../state/customer.state';

@Component({
  selector: 'cat-my-ongoing-offers',
  templateUrl: './my-ongoing-offers.component.html',
  styleUrls: ['./my-ongoing-offers.component.scss'],
})
export class MyOngoingOffersComponent implements OnInit {
  @Select(OfferState.ongoingQuotations)
  ongoingQuotations$: Observable<any>;

  @Select(OfferState.ongoingQuotationsLoading)
  ongoingQuotationsLoading$: Observable<boolean>;

  @Select(CustomerState.isShowCallService)
  isShowCallService$: Observable<boolean>;

  ongoingQuotations = [];

  isShowCallService = false;

  openedCardIndex: number | null = null;

  contactModal = false;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(private readonly store: Store) {}

  ngOnInit() {
    this.store.dispatch(new GetMyOngoingQuotationsAction());

    this.ongoingQuotations$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((quotations) => {
        this.ongoingQuotations = quotations;
      });

    this.isShowCallService$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((isShowCallService) => {
        this.isShowCallService = isShowCallService;
      });
  }

  handleToggleCard(index: number) {
    if (this.openedCardIndex === index) {
      this.openedCardIndex = null;
    } else {
      this.openedCardIndex = index;
    }
  }

  openContactModal() {
    this.contactModal = !this.contactModal;
    document.documentElement.style.overflow = 'hidden';
  }

  handleCloseRepresentativeModal() {
    this.openContactModal();
  }

  navigateToBack() {
    window.history.back();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
