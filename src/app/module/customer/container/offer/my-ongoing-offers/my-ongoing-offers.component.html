<div class="overflow-auto" style="height: 100vh; background-color: #fafafa">
  <div style="padding: 1.5rem">
    <div class="d-flex align-items-center">
      <button class="btn btn-sm" (click)="navigateToBack()">
        <i class="icon icon-back" style="font-size: 20px;"></i>
      </button>
      <button
        *ngIf="isShowCallService"
        class="btn btn-warning btn-sm d-flex align-items-center text-white ml-0 m-auto"
        (click)="openContactModal()"
      >
        <i class="icon icon-headset mr-3"></i>
        {{ "_sales_support_manager" | translate }}
      </button>
    </div>
    <div>
      <ng-container *ngFor="let quotation of ongoingQuotations; let i = index">
        <cat-offer-list-card
          [order]="quotation"
          [cardIndex]="i"
          [isOpen]="openedCardIndex === i"
          (toggleCard)="handleToggleCard(i)"
        ></cat-offer-list-card>
      </ng-container>
      <h4 *ngIf="!ongoingQuotations?.length && !(ongoingQuotationsLoading$ | async)" class="text-center mt-2">
        {{ "_ongoing_offers_not_found" | translate }}
      </h4>
    </div>
  </div>
</div>
<cat-loader [show]="ongoingQuotationsLoading$ | async"></cat-loader>

<cat-contact-your-representative-modal
  [contactModal]="contactModal"
  (handleClose)="handleCloseRepresentativeModal()"
></cat-contact-your-representative-modal>
