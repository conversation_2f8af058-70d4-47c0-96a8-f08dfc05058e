import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, Input, OnInit, Output } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ImageSizeEnum } from 'src/app/export/media/enum/image-size.enum';
import { GetMyOngoingQuotationsDetailsAction } from 'src/app/module/customer/action/offer.actions';
import { OfferState } from 'src/app/module/customer/state/offer.state';
import { MyOngoinOffersEnum } from '../../enum/my-ongoing-offers.enum';
import { EventEmitter } from '@angular/core';
@Component({
  selector: 'cat-offer-list-card',
  templateUrl: './offer-list-card.component.html',
  styleUrls: ['./offer-list-card.component.scss'],
  animations: [
    trigger('rotate', [
      state(
        'default',
        style({
          transform: 'rotate(0deg)',
        })
      ),
      state(
        'rotated',
        style({
          transform: 'rotate(180deg)',
        })
      ),
      transition('default <=> rotated', [animate('0.5s ease-in-out')]),
    ]),
  ],
})
export class OfferListCardComponent implements OnInit {
  @Input()
  order: any;

  @Input()
  cardIndex: number;

  @Input() isOpen: boolean;

  @Select(OfferState.ongoingQuotationsOrdersDetail)
  ongoingQuotationsOrdersDetail$: Observable<any>;

  @Select(OfferState.ongoingQuotationsOrdersDetailLoading)
  ongoingQuotationsOrdersDetailLoading$: Observable<boolean>;

  imageSizeEnum = ImageSizeEnum;

  ongoingQuotationsOrdersDetail = [];

  private subscriptions$: Subject<boolean> = new Subject();

  @Output() toggleCard = new EventEmitter<void>();

  statusKeys = [
    {
      translate: '_in_assembly',
      statusList: [
        MyOngoinOffersEnum.ASSEMBLY_STATUS_A2,
        MyOngoinOffersEnum.DELIVERY_STATUS_A1,
        MyOngoinOffersEnum.DELIVERY_STATUS_T1,
        MyOngoinOffersEnum.ASSEMBLY_STATUS_T2,
      ],
    },
    {
      translate: '_assembly_completed',
      statusList: [
        MyOngoinOffersEnum.DELIVERY_STATUS_A1,
        MyOngoinOffersEnum.DELIVERY_STATUS_T1,
        MyOngoinOffersEnum.ASSEMBLY_STATUS_T2,
      ],
    },
    {
      translate: '_in_transit',
      statusList: [
        MyOngoinOffersEnum.DELIVERY_STATUS_T1,
        MyOngoinOffersEnum.DELIVERY_STATUS_A1,
      ],
    },
    {
      translate: '_delivered',
      statusList: [MyOngoinOffersEnum.DELIVERY_STATUS_T1],
    },
  ];

  constructor(private readonly store: Store) {}

  ngOnInit() {}

  handleCardStyle() {

    this.toggleCard.emit();
    if (!this.isOpen) {
      if(!this.order?.items.length){
        return;
      }
      const orderItemsSerialNumber = this.order.items.map(
        (item) => item.serialNumber
      );
      this.store.dispatch(
        new GetMyOngoingQuotationsDetailsAction(orderItemsSerialNumber)
      );

      this.ongoingQuotationsOrdersDetail$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((detail) => {
          const ordersDetails = detail?.items.map((item) => {
            const findedItems = this.order?.items.find(
              (item) => item.serialNumber === item?.serialNumber
            );
            return {
              ...item,
              model: findedItems.model,
            };
          });
          this.ongoingQuotationsOrdersDetail = ordersDetails;
        });
    }
  }

  getStatusColor(status: string) {
    switch (status) {
      case 'I1006':
        return '#5d8d1c';
      case 'I1005':
        return '#5d8d1c';
      default:
        return '#FFA300';
    }
  }

  getOfferStatusColor(
    assemblyStatus: string,
    deliveryStatus: string,
    statusList: any[]
  ) {
    const inAssemblyStatus = statusList.includes(assemblyStatus);
    const inDeliveryStatus = statusList.includes(deliveryStatus);

    if (inAssemblyStatus || inDeliveryStatus) {
      return '#ffa300';
    } else {
      return '#d9d9d9';
    }
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}

// if Assembly T2 -> sona kadar turuncu
// else if Delivery T1 -> Teslim edildiye kadar
// else if Delivery A1 -> Araca yüklendiye kadar
// else if Assembly A2 -> Sadece montaj aşamasında
// else hepsi gri
