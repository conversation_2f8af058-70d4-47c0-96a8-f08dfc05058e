.btn {
  &:focus {
    box-shadow: none !important;
  }
}

.chevron-up {
  transition: 300ms ease-in-out all;
  transform: rotate(180deg);
}

.card-container {
  height: 150px;
  overflow: hidden;
}

.expanded{
  height: min-content;
  overflow: unset;
}


.status-thin {
  &::before {
    content: "";
    position: absolute;
    top: 12%;
    left: 58%;
    width: 100%;
    height: 0;
    border-top: 2px dotted #d9d9d9;
  }
}

.status-thin-active {
  &::before {
    content: "";
    position: absolute;
    top: 12%;
    left: 58%;
    width: 100%;
    height: 0;
    border-top: 2px dotted #ffa300;
  }
}
