<div class="bg-white p-3 mt-3" style="border-radius: 1rem">
  <div class="card-container" [ngClass]="{ 'expanded': isOpen }">
    <div class="d-flex align-items-center justify-content-end">
      <div
        class="ml-auto"
        style="border-radius: 10px; height: 1.5rem; padding-inline: 1rem"
        [ngStyle]="{ 'background-color': getStatusColor(order?.statusCode) }"
      >
        <span class="text-white font-size-14px">{{ order?.status }}</span>
      </div>
      <button
        class="btn btn-sm"
        (click)="handleCardStyle()"
        [@rotate]="isOpen ? 'rotated' : 'default'"
      >
        <i class="icon icon-chevron-down"></i>
      </button>
    </div>
    <div class="d-flex align-items-center justify-content-end">
      <div class="position-relative">
        <div
          class="position-absolute top-0 left-0 d-flex align-items-center justify-content-center"
          style="
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #d9d9d9;
            z-index: 2;
          "
        >
          <span style="color: #555555; font-weight: bold">
            {{ order?.totalItemCount || '0' }}
          </span>
        </div>
        <cat-image-preview
          [model]="order?.items[0]?.model || 'none'"
          [title]="order?.items[0]?.model"
          [maxHeight]="'80px'"
          [minWidth]="'80px'"
        >
        </cat-image-preview>
      </div>
      <div class="d-flex flex-column ml-2">
        <div
          style="
            display: grid;
            grid-template-columns: 1fr 1fr;
            font-size: 12px;
            white-space: nowrap;
          "
        >
          <span style="min-width: 110px; white-space: pre-line">
            {{ "_order_date" | translate }}
          </span>
          <span style="white-space: pre-line;">:&nbsp;{{ order?.orderDate | date : "dd.MM.yyyy" }}</span>
        </div>
        <div
          style="
            display: grid;
            grid-template-columns: 1fr 1fr;
            font-size: 12px;
            white-space: nowrap;
          "
        >
          <span style="min-width: 110px; white-space: pre-line"> {{ "_order_no" | translate }}</span>
          <span style="white-space: pre-line;">:&nbsp;{{ order?.orderNumber || "-" }}</span>
        </div>
        <div
          style="
            display: grid;
            grid-template-columns: 1fr 1fr;
            font-size: 12px;
            white-space: nowrap;
          "
        >
          <span style="min-width: 110px; white-space: pre-line">
            {{ "_quotation_no" | translate }}
          </span>
          <span style="white-space: pre-line;">:&nbsp;{{ order?.quotationNumber || "-" }}</span>
        </div>
        <div
          style="
            display: grid;
            grid-template-columns: 1fr 1fr;
            font-size: 12px;
            white-space: nowrap;
          "
        >
          <span style="min-width: 110px; white-space: pre-line">
            {{ "_total_value" | translate }}
          </span>
          <span
             style="white-space: pre-line;">:&nbsp;{{ order?.totalValue | number }} {{ order?.currency }}</span
          >
        </div>
        <div
          style="
            display: grid;
            grid-template-columns: 1fr 1fr;
            font-size: 12px;
            white-space: nowrap;
          "
        >
          <span style="min-width: 110px; white-space: pre-line">
            {{ "_invoice_status" | translate }}
          </span>
          <span style="white-space: pre-line;">:&nbsp;{{ order?.invoiceProgress || "-" }}</span>
        </div>
      </div>
    </div>
    <ng-container *ngIf="isOpen">
      <div
        *ngFor="let item of ongoingQuotationsOrdersDetail"
        class="p-3 mt-2"
        style="background-color: #fafafa; border-radius: 0.5rem"
      >
        <div class="d-flex align-items-center">
          <cat-image-preview
            [model]="item?.model || 'none'"
            [title]="item?.model"
            [maxHeight]="'80px'"
          >
          </cat-image-preview>
          <div class="d-flex flex-column ml-4">
            <span class="font-weight-bold font-size-14px">{{
              item?.model
            }}</span>
            <span class="font-size-12px">{{ item?.serialNumber }}</span>
          </div>
        </div>
        <div
          class="status-bar mt-4"
          style="display: grid; grid-template-columns: repeat(4, 1fr)"
        >
          <div
            *ngFor="let status of statusKeys; let i = index"
            class="d-flex flex-column align-items-center position-relative"
            [ngClass]="{ 'status-thin': i % 2 == 0 || i === 1 }"
          >
            <div
              style="width: 14px; height: 14px; border-radius: 50%; z-index: 1"
              [ngStyle]="{
                'background-color': getOfferStatusColor(
                  item?.assemblyStatusCode,
                  item?.deliveryStatusCode,
                  status.statusList
                )
              }"
            ></div>
            <span
              class="font-size-12px text-center mt-1"
              [ngStyle]="{
                color: getOfferStatusColor(
                  item?.assemblyStatusCode,
                  item?.deliveryStatusCode,
                  status.statusList
                )
              }"
            >
              {{ status.translate | translate }}
            </span>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
<cat-loader [show]="ongoingQuotationsOrdersDetailLoading$ | async"></cat-loader>
