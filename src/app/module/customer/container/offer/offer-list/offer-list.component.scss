@import "variable/bootstrap-variable";

::ng-deep .pull-to-refresh {
  .content {
    background: #f9f9f9;
  }
}
.offer {
  &-title {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
  }

  &-text {
    width: 271px;
    font-style: normal;
    font-weight: normal;
    font-size: 13px;
    line-height: 24px;
  }

  &-textrevision {
    width: 210px;
    font-style: normal;
    font-weight: normal;
    font-size: 15px;
    line-height: 24px;
  }

  &-textreject {
    width: 210px;
    font-style: normal;
    font-weight: normal;
    font-size: 15px;
    line-height: 24px;
  }

  &-textapproval {
    width: 210px;
    font-style: normal;
    font-weight: normal;
    font-size: 15px;
    line-height: 24px;
  }

  &-dropdown {
    .dropdown-item {
      padding: 0.5rem 1.5rem;
    }

    .dropdown-item.active {
      background-color: var(--warning);
      border-radius: 16px;
    }

    .dropdown-toggle {
      box-shadow: none;
      color: #4A8EB0;
    }
    .text-warning .dropdown-toggle{
      color: var(--warning);
    }

    .dropdown-toggle::after {
      display: none !important;
    }

    .icon-message-success {
      font-size: 20px;
      background: var(--success);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .icon-edit:before {
      font-size: 20px;
      color: #ffa300;
    }

    .icon-close-circle {
      font-size: 20px;
      color: #DA3A3C;
    }
  }

  &-menu {
    font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    //padding: 0;
    border-radius: 4px;
    box-shadow: 2px 3px 10px 0px #00000040;
    top: 6px !important;

    @media (max-width: 768px){
      right: 0;
      left: 0;
    }

    &-element {
      display: flex;
      align-items: center;
      gap: .5rem;
      font-size: 14px;

      &.approve-btn{
        color: $success;
      }

      &.revision-btn{
        color: $warning;
      }

      &.reject-btn{
        color: $danger;
      }
    }

    &-button {
      display: flex;
      align-items: center;
      gap: .5rem;
      border-radius: 9px !important;
      border-color: $info !important;
      color: $info;
    }
  }
}

.download-file {
  width: 33px;
  height: 33px;
  min-width: 33px;
  min-height: 33px;
  background-color: #f5f5f5;
  font-size: 18px;
}

.offer-animation {
  animation: offerSplash 5s 1;
}

.offer_approve {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  background-color: #f5f5f5;
  font-size: 18px;
  color: #ffa300;
  border: 1px solid #ffa300;
}

@keyframes offerSplash {
  50% {
    box-shadow: 0 0 30px black;
  }
}

.equipmnet-part-btn {
  width: 50%;
  padding-top: 9px;
  padding-bottom: 9px;
  border-radius: 6px;
}

.phone-btn {
  width: calc(50% - 65px);
  //padding-top: 9px;
  //padding-bottom: 9px;
  border-radius: 6px;
  background-color: #ffa300;
}

.phone-button-text {
  line-height: 30px;
  color: white;
  text-align: center;
  //border: 1px solid green;
  padding: 0;

  p {
    margin: 0;
    //font-size: 11px;
    font-size: min(max(2vw, 11px), 14px);
    overflow-wrap: anywhere;
    line-height: 1em;
    display: inline-block;
    vertical-align: middle;
  }

  i {
    font-size: 18px;
  }
}

.action-button {
  padding: 0.3rem 1rem;
}

.pssr-title {
  font-weight: normal;
  font-size: 11px;
}

.btn-primary {
  background-color: #4A8EB0;
  border-color: #4A8EB0;
}

.approval-modal-phone-btn {
  border-radius: 6px;
  background-color: #ffa300;
  height: 30px;
  color: white;
  text-align: center;
  padding: 0;
  font-size: 14px;
}

.after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
  z-index: 10;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.success-message {
  font-size: 26px;
  font-weight: 700;
  line-height: 1;
}

.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.message-class {
  font-size: 60px;
  padding-top: 1px;
}

.z-index-over-loader {
  z-index: 10001;
}

.spare-order-detail-text-bottom {
  position: fixed;
  bottom: 0;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
  padding-bottom: 15px;
  padding-top: 17px;
  //height: 55px;
  max-height: 100px;
  background: #4A8EB0;
  box-shadow: 1px -3px 8px 0px rgba(0, 0, 0, 0.10);
  color: #FFF;
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 153.5%;
  border-radius: 17px 17px 0px 0px;
}
// .yellow-bar{
//   position: absolute;
//   left: 0;
//   top: 0;
//   width: 9px;
//   height: 55px;
//   background: #FFA300;
// }

.categories{
  overflow-x: auto;
  overscroll-behavior-x: contain;
  width: 90%;
}
.categories-container{
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  align-items: center;
  text-align: center;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
  border: none;
}
.categories-content {
  width: auto;
}
.categories-container .categories-content:last-child::after {
  content: '';
  position: absolute;
  width: 2rem;
  right: -2rem;
  opacity: 0;
}
 .text-container {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  color: grey;
  opacity: 0.2;
  display: flex;
  justify-content: center;
}
.activeCategory{
  color: #4A8EB0 !important;
  opacity: 1;
}
.categories-content  span {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: #4A8EB0;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
}
.search-area {
  max-width: 100%;
  input {
    padding-left: 45px;
    //margin-left: 5px;
    //width: 85%;
  }
  .icon-search {
    position: absolute;
    left: 15px;
    //top: 50%;
    top: 26px;
    font-size: 18px;
    line-height: 18px;
    height: 18px;
    margin-top: -9px;
    //width: 30px;
  }
  .icon-filter {
    color: #4A8EB0 !important;
  }
  .icon-filter-area {
    width: 30px;
    height: auto;
  }
}
@media (min-width: 560px) {
  .search-area {
    width: 100%;
    input {
      padding-left: 47px;
      margin-left: 5px;
      width: 95%;
    }
  }
  .categories{
    overflow-x: hidden;
    overscroll-behavior-x: contain;
    width: 90%;
  }
  .categories-container {
    justify-content: center;
  }
}
.service-menu-button{
  box-shadow: none;
  line-height: 1.5em;
  font-size: 20px;
}
.dropdown-after-none::after{
  display: none;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}
.btn-col {
  flex: 0 0 60%;
  max-width: 60%;
}

.filter-search-modal{
  gap: 1rem;
}

.active-icon-filter{
  color: #4A8EB0 !important;
}

.date-text{
  top: 25%;
  left: 24px;
  color: #8E8E8E;
}
.equipment-menu{
  max-width: 350px;

  button{
    white-space: pre-line !important;
    overflow-wrap: break-word !important;
    max-width: 300px;
    min-width: 200px;
  }
}
