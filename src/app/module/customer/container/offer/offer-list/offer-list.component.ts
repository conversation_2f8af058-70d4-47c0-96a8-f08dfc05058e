import { AfterContentChecked, AfterView<PERSON>nit, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { fromEvent, Observable, Subject } from 'rxjs';
import snq from 'snq';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { CustomerDetailAction } from '../../../action/customer.actions';
import { OfferModel } from '../../../model/offer.model';
import { CustomerState } from '../../../state/customer.state';
import { environment } from '../../../../../../environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageFrameService } from '../../../../shared/service/message-frame.service';
import { debounceTime, distinctUntilChanged, filter, takeUntil, tap } from 'rxjs/operators';
import { LogService } from '../../../service/log.service';
import { DownloadTypeEnum } from '../../../../../export/media/enum/download-type.enum';
import { ViewportScroller } from '@angular/common';
import { CustomerModel, PssrList } from '../../../model/customer.model';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { PagesEnum } from 'src/app/module/shared/enum/pages.enum';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { PageOpenedAction, StartDownloadAction, } from 'src/app/module/shared/state/common/common.actions';
import { SystemFeatureAction } from 'src/app/module/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import {
  ApproveQuotationAction,
  ClearQuotationAction,
  GetAvailableQuotationTypesAction,
  GetOfferListAction,
  RejectQuotationAction,
  RevisionQuotationAction
} from '../../../action/offer.actions';
import { OfferState } from '../../../state/offer.state';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { LogSectionEnum } from 'src/app/module/definition/enum/log-section.enum';
import { BorusanBlockedActionsEnum } from 'src/app/module/definition/enum/borusan-blocked-actions.enum';
import { CustomerService } from '../../../service/customer.service';
import { IncomingMessageService } from '../../../../shared/service/incoming-message.service';
import { IncomingMessageEnum } from '../../../../shared/enum/incoming-message.enum';
import { OfferActionsEnum, OfferCategoriesEnum, OfferStatusEnum } from 'src/app/module/definition/enum/offerList.enum';
import { Lang2LocaleEnum } from '../../../../shared/enum/locale.enum';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { HasPermissionsService } from 'src/app/export/permissions/service/has-permissions.service';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'cat-offer-list',
  templateUrl: './offer-list.component.html',
  styleUrls: ['./offer-list.component.scss'],
  providers: [DatePipe]
})
export class OfferListComponent implements OnInit, OnDestroy, AfterViewInit, AfterContentChecked {
  @Select(OfferState.offerList)
  offerList$: Observable<OfferModel[]>;

  @Select(OfferState.offerListLoading)
  offerListLoading$: Observable<boolean>;
  offerListLoading: boolean;

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  @Select(OfferState.availableQuotationTypes)
  availableQuotationTypes$: Observable<any>;

  PermissionEnum = PermissionEnum;
  OfferCategoriesEnum = OfferCategoriesEnum;
  offerCategoriesProcessType: any[];
  filterValue = 'All';
  @ViewChild('tabContainer') tabContainer: ElementRef;
  tabContainerWight: number;
  date = new Date();

  form = new FormGroup({
    search: new FormControl(null, [Validators.minLength(3)]),
    date: new FormControl(null, []),
    amount: new FormControl(null, [Validators.min(1), Validators.max(10000000000000001)]),
    operator: new FormControl('>', []),
  });
  enableSearchArea: boolean ;
  @ViewChild('searchTooltip') tooltip: NgbTooltip;
  @ViewChild('search') searchInput: ElementRef;
  searchText: string;
  hasFiltered = false;
  filterOperators = ['<', '>', '='];
  offerFilterActive = false;

  groupedFilters = {
    All: [],
    Y101: [],
    ZKRQ: [],
    ZMDA: [],
    ZSRT: [],
    ZCVQ: [],
    Y201: [],
  };

  rentalCatalogPayload = {
    title: 'Rental',
    webUrl: 'https://borusancat.com/tr/equipment/rent?view=boom',
    id: '8ceffe33-1b6a-477d-94f6-934075046186',
    tags: 'appRental',
  };

  newMachine = {
    title: 'Yeni',
    webUrl: 'https://www.borusancat.com/en/power-system/new-machines?view=boom',
    id: '1bae1b73-e8ed-4c28-b211-56145aed4c2f',
    tags: 'formPowerSystem'
  };

  OfferActionsEnum = OfferActionsEnum;

  selectedAgreement = null;
  customerNumber: string;
  pssrList: PssrList[] | any = [];
  loadingAttachment: any[] = [];
  offerList: OfferModel[];
  agreementModal = false;
  DownloadTypeEnum = DownloadTypeEnum;
  paramQuotationNumber: any;
  contactModal = false;
  cancelDownloadVersion = false;
  availableQuotationTypes: any[] = [];

  offerRevisionModal = false;
  offerRejectModal = false;
  selectedOffer: OfferModel;
  featureOfferApproval = false;
  featureOfferReject = false;
  featureOfferRevision = false;
  featureOfferCreateOrder = false;
  featureMyOngoingOffers = false;

  @Select(OfferState.offerApprove)
  offerApprove$: Observable<boolean>;
  @Select(OfferState.offerReject)
  offerReject$: Observable<boolean>;

  @Select(OfferState.offerRevision)
  offerRevision$: Observable<boolean>;

  revisionForm: FormGroup = new FormGroup({
    Description: new FormControl(null, [
      Validators.required,
      Validators.pattern('^(?!\\s+$)[a-zA-Z0-9çğıiöşüÇĞIİÖŞÜ\\s.,]*$')
    ]),
  });
  revisionFormSendStatus: boolean;
  approvalFormSendStatus: boolean;
  rejectFormSendStatus: boolean;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  isSparePartModule: Observable<boolean>;
  currentCustomer: CustomerRelationModel;

  isShowDigitalBanko = false;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  private errorModal: any;
  private subscriptions$: Subject<boolean> = new Subject();
  OfferStatusEnum = OfferStatusEnum;
  locale: string;
  offerApprovalModal = false;

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly ref: ChangeDetectorRef,
    private readonly frameService: MessageFrameService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly logger: LogService,
    private viewportScroller: ViewportScroller,
    private readonly customerService: CustomerService,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly hasPermissionService: HasPermissionsService,
    private readonly datePipe: DatePipe
  ) { }

  ngOnInit() {
    this.store.dispatch(new PageOpenedAction(PagesEnum.quotationListPage));
    this.cancelDownloadVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.currentCustomer = this.store.selectSnapshot(CommonState.currentCustomer);
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);

    if (this.currentCustomer?.customer || loginResponse.customer) {
      this.customerNumber = this.currentCustomer?.customer?.customerNumber || loginResponse.customer?.customerNumber;

      setTimeout(() => {
        this.store.dispatch(
          new CustomerDetailAction(this.customerNumber)
        );
      }, 0);
      this.isSparePartModule = this.hasPermissionService.hasPermission(PermissionEnum.Ecommerce);
    }
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.frameService.sendMessage(data.action)
          });
        });
    this.customer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customer => {
        if (customer) {
          this.pssrList = customer.details?.pssrList.filter(pssr => {
            if (pssr.titles.find(data => data === 'PSSR')) {
              return pssr.mailList.length || pssr.telephoneList.length;
            }
            return false;
          });
        }
      });
    this.store.dispatch(new GetAvailableQuotationTypesAction());
    this.availableQuotationTypes$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(quotationTypes => {
        if (quotationTypes) {
          this.availableQuotationTypes = quotationTypes?.[this.currentCustomer?.countryCode] || quotationTypes?.['OTHERS'];
        }
      });
    const refresh = null;
    this.fetchOffers(refresh);

    this.offerList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.filterOffers();
      });

    this.offerListLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => {
        this.offerListLoading = x;
      });

    const allParams = this.route.snapshot.queryParams;
    if (allParams.quotationNumber) {
      this.paramQuotationNumber = allParams.quotationNumber;
      console.log('Selected Quotation: ', this.paramQuotationNumber);
      this.scrollOffer(allParams.quotationNumber);
    }

    this.store.dispatch(new SystemFeatureAction());
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.featureOfferApproval = systemFeature('approve_quotation', features, true);
        this.featureOfferReject = systemFeature('quotation_reject', features, false);
        this.featureOfferRevision = systemFeature('quotation_revision', features, false);
        this.featureOfferCreateOrder = systemFeature('quotation_create_order', features, false);
        this.isShowDigitalBanko = systemFeature('digital_banko_videocall', features, true);
        this.featureMyOngoingOffers = systemFeature('my_ongoing_offers', features, true);
        const isBorusanUser: any = this.store.selectSnapshot(LoginState.user);

        if (isBorusanUser?.isLdapLogin) {
          this.featureOfferApproval = systemFeature('approve_quotation_borusan_user', features, false) &&
            !(this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ApproveQuotation) >= 0);
          this.featureOfferReject = systemFeature('quotation_reject_borusan_user', features, false) &&
            !(this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.RejectQuotation) >= 0);
          this.featureOfferRevision = systemFeature('quotation_revision_borusan_user', features, false) &&
            !(this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ReviseQuotation) >= 0);
          this.featureOfferCreateOrder = systemFeature('quotation_create_order_borusan_user', features, false) &&
            !(this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.CreateOrder) >= 0);
        }
      }
    });

    this.reOpenedSubs();
    this.offerStatusSubs();
  }

  doRefresh() {
    const refresh = true;
    this.fetchOffers(refresh);
  }

  fetchOffers(refresh: any) {
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    // const company = this.store.selectSnapshot(LoginState.company);
    if (loginResponse) {
      this.store.dispatch(
        new GetOfferListAction(snq(() => loginResponse.customer.customerNumber), refresh)
      );
    }
  }

  setFilter(val: string) {
    this.filterValue = val;
  }

  // onClickItem(offer: OfferModel) {
  //   this.router
  //     .navigate(
  //       [
  //         '/',
  //         ...environment.rootUrl.split('/'),
  //         'customer',
  //         'offer-pdf',
  //         offer.quotationNumber,
  //       ],
  //       { relativeTo: this.route }
  //     )
  //     .then();
  // }

  groupedFiltersClear() {
    this.groupedFilters = {
      All: [],
      Y101: [],
      ZKRQ: [],
      ZMDA: [],
      ZSRT: [],
      ZCVQ: [],
      Y201: [],
    };
  }

  scrollOffer(quotationNumber) {
    this.offerListLoading$.subscribe(() => {
      this.viewportScroller.scrollToAnchor(quotationNumber);
      const position = this.viewportScroller.getScrollPosition();
      this.viewportScroller.scrollToPosition([position[0], position[1] - 250]);
    });
  }

  animationClick(id: string) {
    if (id === this.paramQuotationNumber) {
      this.paramQuotationNumber = '';
    }
  }

  createOffer(filterVal) {
    const relativeUrls = {
      [OfferCategoriesEnum.ALL]: 'request-equipment-part',
      [OfferCategoriesEnum.SPARE_PART_OFFER]: 'request-equipment-part',
      [OfferCategoriesEnum.MDA_OFFER]: 'request-mda',
      [OfferCategoriesEnum.CVA_OFFER]: 'request-mda',
      [OfferCategoriesEnum.SERVICE_OFFER]: 'request-service',
    };

    switch (filterVal) {
      case OfferCategoriesEnum.RENTAL:
        this.frameService.sendMessage(FrameMessageEnum.openCatalog, this.rentalCatalogPayload);
        break;
      case OfferCategoriesEnum.NEW_MACHINE:
        this.goToCatalogNew();
        break;
      default:
        if (relativeUrls[filterVal]) {
          this.router
            .navigate([
              '/',
              ...environment.rootUrl.split('/'),
              'form',
              relativeUrls[filterVal],
            ])
            .then();
        }
        break;
    }

  }


  getFilterValue(filterValue: string) {
    switch (filterValue) {
      case OfferCategoriesEnum.SPARE_PART_OFFER:
        return '_order_parts_btn';
      case OfferCategoriesEnum.SERVICE_OFFER:
        return '_create_service_request';
      case OfferCategoriesEnum.MDA_OFFER:
        return '_create_mda';
      case OfferCategoriesEnum.CVA_OFFER:
        return '_create_mda';
      case OfferCategoriesEnum.RENTAL:
        return '_create_rental_request';
      case OfferCategoriesEnum.NEW_MACHINE:
        return '_open_to_catalog_page';
    }
  }

  onCall(phone: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }

  selectAgreement(offer: any) {
    this.agreementModal = true;
    this.selectedAgreement = offer;
  }

  downloading(attachment) {
    this.applyDisabledClassToNonDownloadButtons();
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
    if (!this.isDownloadingAny()) {
      this.enableNonDownloadButtons();
    }
  }

  applyDisabledClassToNonDownloadButtons() {
    const downloadButtonIds = this.groupedFilters.All
      .map((history) => history.quotationNumber)
      .filter((id) => id !== null);

    const allButtons = document.getElementsByClassName('download-file');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const id = button.getAttribute('id');
      if (id && !downloadButtonIds.includes(+id.split('_')[1])) {
        button.classList.add('disabled');
      }
    }
  }

  isDownloading(id) {
    return !!this.loadingAttachment.filter(data => data.id === id).length;
  }

  isDownloadingAny() {
    return this.loadingAttachment.length > 0;
  }

  enableNonDownloadButtons() {
    const allButtons = document.getElementsByClassName('download-file');
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      button.classList.remove('disabled');
    }
  }

  offerApprovalBtnClick(offer: OfferModel) {
    this.selectedOffer = offer;
    if (this.selectedOffer?.actions?.indexOf('CreateOrder') >= 0) {
      this.offerApprovedClick();
    } else {
      this.offerApprovalModal = true;
    }
  }

  offerRevisionBtnClick(offer: OfferModel) {
    this.selectedOffer = offer;
    this.offerRevisionModal = true;
  }

  offerRevisionModalStatus() {
    this.revisionForm.reset();
  }

  offerRejectBtnClick(offer: OfferModel) {
    this.selectedOffer = offer;
    this.offerRejectModal = true;
  }

  onInputOffer(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  offerApprovedClick() {
    if (!this.selectedOffer) { return; }
    if (this.selectedOffer?.actions?.indexOf('CreateOrder') >= 0) {
      this.offerCreateOrder(this.selectedOffer);
    } else {
      this.store.dispatch(new ApproveQuotationAction(this.selectedOffer?.quotationNumber));
      this.offerApprove$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(x => {
          if (x) {
            this.logger.action(LogSectionEnum.OFFER, 'OFFER_APPROVE_CLICK', {
              QuotationNumber: this.selectedOffer?.quotationNumber
            }).subscribe();
            this.offerApprovalModal = false;
            this.approvalFormSendStatus = true;
          }
        });
    }
  }

  offerRevisionSend() {
    const { value } = this.revisionForm;

    if (!this.revisionForm.valid) {
      this.revisionForm.markAllAsTouched();
      return;
    }
    if (!value.Description) {
      this.revisionForm.controls.Description.markAllAsTouched();
      return;
    }
    this.store.dispatch(new RevisionQuotationAction(this.selectedOffer.quotationNumber, value.Description));
    this.offerRevisionModal = false;
  }

  offerRejectedClick() {
    this.store.dispatch(new RejectQuotationAction(this.selectedOffer.quotationNumber));
    this.offerRejectModal = false;
  }

  offerStatusSubs() {
    this.offerRevision$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          if (data) {
            this.logger.action(LogSectionEnum.OFFER, 'OFFER_REVISION', {
              QuotationNumber: this.selectedOffer?.quotationNumber,
              Description: this.revisionForm.value?.Description
            }).subscribe();
            this.revisionFormSendStatus = true;
            this.revisionForm.reset();
          }
        }
      );
    this.offerReject$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          if (data) {
            this.rejectFormSendStatus = true;
          }
        }
      );
  }

  offerModalClose(modalName) {
    if (modalName === 'revision') {
      this.revisionFormSendStatus = false;
      this.offerRevisionModal = false;
    } else if (modalName === 'reject') {
      this.rejectFormSendStatus = false;
      this.offerRejectModal = false;
    } else if (modalName === 'approval') {
      this.approvalFormSendStatus = false;
      this.offerApprovalModal = false;
    }
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    if (loginResponse) {
      this.store.dispatch(
        new GetOfferListAction(snq(() => loginResponse.customer.customerNumber), null)
      );
    }
    this.store.dispatch(new ClearQuotationAction());
  }

  offerCreateOrder(offer: OfferModel) {
    this.router
      .navigate([
          '/',
          ...environment.rootUrl.split('/'),
          'customer',
          'offer',
          'create-order',
          offer.quotationNumber,
        ],
        {
          relativeTo: this.route,
          queryParams: { guid: offer.guid }
        }
      )
      .then();
  }

  onOpenModal() {
    this.contactModal = !this.contactModal;
    document.documentElement.style.overflow = 'hidden';
  }

  handleCloseRepresentativeModal(){
    this.onOpenModal();
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.frameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  openSparePartOrderDetail() {
    this.frameService.sendMessage(FrameMessageEnum.openSparePart, {
      query: 'page=order-list'
    });
  }

  goToCatalogNew() {
    this.frameService.sendMessage(FrameMessageEnum.redirectMainPage,
      { urlPath: '/redirect-catalog/b192cd83-**************-6a9cc98de78f' });
  }

  reOpenedSubs() {
    this.incomingMessageService
      .subscribe(IncomingMessageEnum.webviewReopened, () => {
        this.store.dispatch(new GetOfferListAction(this.customerNumber, null));
      });
  }

  startDigitalBankoVideocall() {
    this.customerService.getOTT().subscribe(ott => {
      if (ott) {
        this.frameService.sendMessage(FrameMessageEnum.openAssistBox, {
          url: window.location.origin + '/' + environment.rootUrl + '/digital-banko?ott=' + ott.oneTimeToken
        });
      }
    });
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  offerActionCheck(offer: OfferModel, action: OfferActionsEnum) {
    const general = (
      !offer.isApproved &&
      offer?.actions?.indexOf(action) > -1 &&
      !(
        offer?.status === OfferStatusEnum.OrderCreated ||
        offer?.status === OfferStatusEnum.OrderStart
      )
    );
    switch (action) {
      case OfferActionsEnum.Approve:
        return this.featureOfferApproval && general;
      case OfferActionsEnum.CreateOrder:
        return this.featureOfferCreateOrder &&
          offer?.actions?.indexOf(action) > -1 &&
          (offer?.isApproved || offer?.status === 'OrderStart');
      case OfferActionsEnum.Reject:
        return this.featureOfferReject && general;
      case OfferActionsEnum.Revision:
        return this.featureOfferRevision && general;
    }
  }

  showAvailableQuotation(value: string) {
    return this.availableQuotationTypes?.includes(value);
  }

  navigateToOngoingOffers() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'ongoing-offers',
      ])
      .then();
  }

  ngAfterViewInit(): void {
    this.tabContainerWight = this.tabContainer?.nativeElement?.scrollWidth;
    this.ref.detectChanges();
    //this.listenSearchChanges();
  }

  ngAfterContentChecked(): void {
    this.ref.detectChanges();
  }

  // protected listenSearchChanges() {
  //   fromEvent(this.searchInput.nativeElement, 'keyup')
  //     .pipe(
  //       // filter(Boolean),
  //       debounceTime(200),
  //       tap(() => {
  //         if (this.form.controls.search.hasError('minlength')) {
  //           this.tooltip.open();
  //         }
  //       }),
  //       distinctUntilChanged(),
  //       filter(
  //         () =>
  //           this.searchInput.nativeElement.value.length >= 3 ||
  //           this.searchInput.nativeElement.value.length === 0,
  //       ),
  //       tap(() => {
  //         this.tooltip.close();
  //       }),
  //     )
  //     .subscribe(() => {
  //       this.searchText = this.form.get('search').value || '';
  //       this.hasFiltered = false;
  //       this.filterOffers();
  //     });
  // }

  protected filterOffers() {
    const data = this.store.selectSnapshot(OfferState.offerList);

    this.offerList = this.offerFilter(data);

    this.setFilter('All');

    if (data) {
      const language = this.translateService.getDefaultLang();
      this.locale = Lang2LocaleEnum[language] ? Lang2LocaleEnum[language] : 'tr-TR';
      this.groupedFiltersClear();
      const groups = Object.values(OfferCategoriesEnum);
      this.groupedFilters.All = this.offerList;

      this.offerList.forEach(item => {
        if (Object.values(groups).includes(item.processType)) {
          const processList = this.offerList.map(({
              processType,
              processTypeDescription
            }) => {
              return {
                key: processType,
                value: processTypeDescription
              };
            }
          );
          this.offerCategoriesProcessType = processList.filter((value, index, self) =>
              index === self.findIndex((t) => (
                t.key === value.key && t.value === value.value
              ))
          );
          this.groupedFilters[item.processType].push(item);
        }
      });
    }
  }

  private offerFilter(data: OfferModel[]) {
    return data?.filter(item => {
      let isMatch = true;

      if (this.form.value.search) {
        isMatch = isMatch && item.quotationNumber?.includes(this.form.value.search);
      }

      if (this.form.value.date) {
        isMatch = isMatch && this.datePipe.transform(item?.postingDate, 'yyyy-MM-dd') === this.form.value.date;
      }

      if (this.form.value.amount || this.form.value.amount === 0) {
        isMatch = isMatch && this.evaluateAmountCondition(item.totalAmount, this.form.value.amount, this.form.value.operator);
      }

      return isMatch;
    });
  }

  private evaluateAmountCondition(amount: number, filterAmount: number, operator: string): boolean {
    switch (operator) {
      case '>':
        return amount > filterAmount;
      case '<':
        return amount < filterAmount;
      case '=':
        return amount === filterAmount;
      default:
        return true;
    }
  }

  onFilterSubmit(){
    const activeFilterStatus = this.form.value.search || this.form.value.date || this.form.value.amount || this.form.value.amount === 0;
    this.filterOffers()
    this.offerFilterActive = activeFilterStatus ? true : false;
    this.enableSearchArea = false;
  }

  onClearFilter(){
    this.form.reset();
    this.form.controls['operator'].setValue('>')
    this.offerFilterActive = false;
    this.filterOffers()
  }


  clearFilters() {
    this.hasFiltered = false;
    this.searchText = '';
    this.form.patchValue({ search: this.searchText });
    this.filterOffers();

    this.logger
      .action('QUOTATIONS', 'CLEAR_FILTERS_CLICK')
      .subscribe();
  }

  changeDate(event: any) {
    console.log(event.target.value);
  }

}
