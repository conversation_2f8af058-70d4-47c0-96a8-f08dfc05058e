import { Component, EventEmitter, OnInit, Output, Input, OnChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { PostDonateFeedbackAction } from '../../../action/donate.actions';
import { DonateState, DonateStateModel } from '../../../state/donate.state';
import { map } from 'rxjs/operators';
import { getFormErrorMessage } from '../../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../../util/is-show-form-error.util';
import { SimpleChanges } from '@angular/core';
import { CustomValidator } from 'src/app/util/custom-validator';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';

@Component({
  selector: 'cat-rate-us',
  templateUrl: './rate-us.component.html',
  styleUrls: ['./rate-us.component.scss']
})
export class RateUsComponent implements OnInit, OnChanges {
  @Output() togglePopup: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() successSent: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input()  resetForm = false;
  customerInfo: any;
  rating: number = 0;
  stars: number[] = [1, 2, 3, 4, 5];
  loading: boolean;
  user: UserModel;
  starIcon = `${environment.assets}/Star.svg`;
  starFilledIcon = `${environment.assets}/Star-filled.svg`;
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  successSend: boolean;
  error: boolean;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, []),
    Surname: new FormControl(null, []),
    Email: new FormControl(null, [
      CustomValidator.mailFormat,
    ]),
    Rating: new FormControl(null, [Validators.required]),
    Comment: new FormControl(null, [Validators.maxLength(100)]),
  });

  constructor(
    private readonly store: Store,

  ) { }

  ngOnInit() {
    document.documentElement.style.overflow = 'auto';
    this.user = this.store.selectSnapshot(LoginState.user);
    if (this.user) {
      this.form.controls['Name'].setValue(this.user.firstName);
      this.form.controls['Surname'].setValue(this.user.lastName);
      this.form.controls['Email'].setValue(this.user.email);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.resetForm) {
      this.resetValues();
      this.resetForm = false;
    }
  }

  resetValues() {
    this.form.reset();
    this.form.controls['Rating'].setValue(0);
    this.rating = 0;
    this.form.controls['Comment'].setValue(null);
    this.form.controls['Name'].setValue(null);
    this.form.controls['Surname'].setValue(null);
    this.form.controls['Email'].setValue(null);
    this.resetForm = false;
  }

  rate(value: number): void {
    this.rating = value;
    this.form.controls['Rating'].setValue(value);
  }

  onSubmit() {
    const value = this.form.value;

    if (this.form.valid) {
      if(value.Rating >= 1) {
        this.save(value);
      }
    } else {
      this.form.markAllAsTouched();
    }
  }

  save(payload: Partial<DonateStateModel>) {
    this.loading = true;
    this.store.dispatch(new PostDonateFeedbackAction(payload))
      .pipe(map(() => this.store.selectSnapshot(DonateState.getState)))
      .subscribe(state => {
        this.customerInfo = { ...state };
        this.resetValues();
        this.togglePopup.emit(false);
        this.loading = false;
        this.successSent.emit(true);
        this.successSend = true;
      },
      error => {
        this.loading = false;
        console.error(error);
        this.error = true;
        this.successSend = false;
      },
      );
  }



}
