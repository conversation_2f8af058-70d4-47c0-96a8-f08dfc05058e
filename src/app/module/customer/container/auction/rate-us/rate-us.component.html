<div class="main">
  <div class="main-header">
    <div class="main-header-title">
      {{ '_customer_day_evaluation' | translate }}
    </div>
    <div class="main-header-desc mt-2">
      {{ '_customer_day_popup_text' | translate }}
    </div>
  </div>
    <div class="stars-area">
      <div class="stars mt-4" [formGroup]="form">
        <span *ngFor="let star of stars" (click)="rate(star)" [ngClass]="{ 'filled': star <= rating }">
          <img [src]="star <= rating ? starFilledIcon : starIcon" alt="">
        </span>
      </div>
      <div
      [ngClass]="{ 'd-block': isShowError(form.controls.Rating) }"
      class="invalid-feedback mt-2 mr-5">
      {{ getFormErrorMessage(form.controls.Rating) | translate }}
    </div>
    </div>


  <div class="suggestion mt-3" [formGroup]="form">
    <div class="suggestion-form form-group">
      <div class="pt-2" >
        <div class="form-group">
          <input
            catInputLength
            [name]="'Name'"
            [placeholder]="'_name' | translate"
            class="form-control form-control"
            formControlName="Name"
            type="text"
            minlength="3"
          />
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.Name) | translate }}
          </div>
        </div>
        <div class="form-group">
          <input
            catInputLength
            [name]="'Surname'"
            [placeholder]="'_surname' | translate"
            class="form-control form-control"
            formControlName="Surname"
            type="text"
            minlength="2"
          />
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.Surname) | translate }}
          </div>
        </div>
        <div class="form-group">
          <input
            catInputLength
            [name]="'Email'"
            [placeholder]="'_email' | translate"
            class="form-control form-control"
            formControlName="Email"
            type="email"
          />
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.Email) | translate }}
          </div>
        </div>
        <div class="mx-n4"></div>
      </div>
      <div class="form-group">
      <div class="suggestion-header">{{ '_customer_day_suggestions' | translate }}</div>
      <textarea catInputLength [name]="'Surname'" class="form-control" id="exampleFormControlTextarea1" rows="5" formControlName="Comment" minlength="3" maxlength="100" style="resize: none;"></textarea>
      <div
      [ngClass]="{ 'd-block': isShowError(form.controls.Comment) }"
      class="invalid-feedback pl-3"
    >
      {{ getFormErrorMessage(form.controls.Comment) | translate }}
    </div>
      </div>

    </div>
    <div class="submit mt-3">
      <button type="submit" class="btn btn-warning"  (click)="onSubmit()">{{ '_customer_day_confirm_button' | translate }}</button>
    </div>
  </div>
</div>
<cat-loader [show]="loading"></cat-loader>
<cat-error-modal [(status)]="error" [message]="'_error' | translate "></cat-error-modal>
<cat-basic-modal [(status)]="successSend">
  <div>
    <div class="text-center">
      <i class="icon icon-message-success d-inline-block mb-3"></i>
    </div>
    <div class="">
      <span class="successFullyFeedback"  [innerHTML]="('_general_successfully_send_form' | translate) | safe: 'html'"></span>
    </div>
  </div>
</cat-basic-modal>
