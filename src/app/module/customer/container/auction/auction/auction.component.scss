.auction {
  height: calc(100vh - 0px);
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  color: black;
  font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
}
.auction-header-background-pic  {
  background-image: url('../../../../../../assets/auction-header-background-pic.svg');
  background-repeat: no-repeat;
  width: 100%;
  height: 662px;
  background-size: cover;
}

.content {
  width: 100%;
  padding-top: 13%;
  &-header {
    padding-left: 44px;
    &-welcome {
      font-weight: 700;
      font-size: 17px;
      line-height: 172%;
      display: flex;
      align-items: center;
      color: #17223C;
      padding-bottom: 25px;
      &-desc {
        width: 282px;
        height: 38px;
        font-weight: 400;
        font-size: 13px;
        line-height: 172%;
        display: flex;
        align-items: center;
        color: #17223C;
      }
    }
  }
  &-info-area-location {
    display: flex;
    width: 100%;
    width: max-content;
    align-items: center;
    justify-content: flex-start;
    margin-left: -6px;
    padding-left: 44px;
    .icon-calendar {
      margin-left: -2px;
      margin-right: 2px;
    }
    .icon i {
      width: 15px;
      height: 17px;
    }
    .adress {
      font-weight: 500;
      font-size: 13px;
      line-height: 172%;
      display: flex;
      align-items: center;
      color: #17223C;
    }
  }
  &-offer-area {
    display: flex;
    width: 100%;
    margin-top: 29px;
    padding-left: 32px;
    &-agree {
      width: 148px;
      height: 39px;
      background: linear-gradient(257.17deg, rgba(35, 43, 63, 0.69) 2.91%, #17223C 56.49%, rgba(34, 44, 68, 0.86) 105.78%);
      box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);
      border-radius: 4px;
      display: flex;
      justify-content: center;
    }
    &-disagree {
      width: 148px;
      height: 39px;
      background: linear-gradient(257.17deg, rgba(146, 152, 167, 0.69) 2.91%, #AFB6C7 56.49%, rgba(169, 176, 193, 0.86) 105.78%);
      box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);
      border-radius: 4px;
      display: flex;
      justify-content: center;
    }
    .text {
      width: max-content;
      height: 40px;
      font-weight: 700;
      font-size: 14px;
      line-height: 243.5%;
      display: flex;
      align-items: center;
      color: #FFFFFF;
    }
  }
  &-checkIn-area {
    width: 300px;
    margin: 0 auto;
    padding-top: 106px;
    &-header {
      .title {
        width: 202px;
        height: 63px;
        font-weight: 700;
        font-size: 28.4444px;
        line-height: 222.5%;
        display: flex;
        align-items: center;
        color: #16213B;
        margin: 10px 0px 19px 0px;
      }
      .desc {
        width: 287px;
        height: 23px;
        font-weight: 400;
        font-size: 13px;
        line-height: 172%;
        display: flex;
        align-items: center;
        color: #16213B;
      }
    }
    &-form {
      margin-top: 15px;
      .form-group {
        display: flex;
        width: 81%;
        margin: 0 auto;
        padding-top: 30px;
      }
      form .form-group input  {
        padding: 16px !important;
      }
      .buton {
        margin-top: 45px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        button {
          font-weight: 700;
          font-size: 14px;
          line-height: 21px;
          width: 94%;
          color: white;
        }
      }
    }
  }

}

.hr {
  width: 339px;
  height: 0px;
  margin-top: 17%;
  border: 1px solid #C1CDD1;
}
 ::ng-deep .btn {
  border-radius: 6px;
 }
 ::ng-deep .ng-select .ng-select-container {
  border-radius: 6px;
 }

 .content-checkIn-area-form .buton  button {
  border-radius: 6px;
 }

 ::ng-deep  cat-info-box > div > div.mt-2.content {
  font-weight: 400;
  font-size: 13px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  display: flex;
  justify-content: center;
 }

::ng-deep cat-info-box > div {
  margin: 15px;
}

.auction-image {
  width: 100%;
  min-height: 85px;
  border: 1px solid #f1f1f1;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  border-radius: 8px;
  margin-top: 5px;
  padding: 5px;
}

.rate {
  width: 100% !important;
  font-weight: 400 !important;
  font-size: 15px;
  line-height: 172% !important;
  display: flex;
  align-items: center;
  color: #16213B !important;
}

.pointerEventsNone {
  pointer-events: none;
}

.successFullyFeedback {
  width: 100%;
  font-weight: 600;
  font-size: 21px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  justify-content: center;
  height: max-content;
  padding-bottom: 15px;
  text-align: center;
}

@media (max-width: 389px) {
  .content-offer-area {
    padding-left: 0px;
    display: flex;
    justify-content: center;
  }

  .keyboard-open {
    margin-top: 0px;
    margin-bottom: 30px;
  }
}
