import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngxs/store';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { GetDonateCustomerAction, PostDonateCustomerAction } from '../../../action/donate.actions';
import { DonateState, DonateStateModel } from '../../../state/donate.state';
import { map } from 'rxjs/operators';
import { LoginResponse } from '../../../../authentication/response/login.response';
import { environment } from 'src/environments/environment';
import { LogService } from '../../../service/log.service';
import { LoggerService } from '../../../../shared/service/logger.service';

@Component({
  selector: 'cat-auction',
  templateUrl: './auction.component.html',
  styleUrls: ['./auction.component.scss']
})
export class AuctionComponent implements OnInit {

  @ViewChild('CodeFirst') codeFirstInput: ElementRef;
  @ViewChild('CodeSecond') codeSecondInput: ElementRef;
  @ViewChild('CodeThird') codeThirdInput: ElementRef;
  @ViewChild('CodeFourth') codeFourthInput: ElementRef;
  togglePopup: boolean;
  loginInfo: LoginResponse;
  firstSendCode: boolean;
  resetForm: boolean;
  successSent = false;
  temporaryDisabled = false;
  error: boolean;
  form: FormGroup = new FormGroup({
    CodeFirst: new FormControl(null, [
      Validators.required,
      Validators.maxLength(1),
      Validators.pattern('[0-9]')
    ]),
    CodeSecond: new FormControl(null, [
      Validators.required,
      Validators.maxLength(1),
      Validators.pattern('[0-9]')
    ]),
    CodeThird: new FormControl(null, [
      Validators.required,
      Validators.maxLength(1),
      Validators.pattern('[0-9]')
    ]),
    CodeFourth: new FormControl(null, [
      Validators.required,
      Validators.maxLength(1),
      Validators.pattern('[0-9]')
    ])
  });

  customerInfo: Partial<DonateStateModel> = {};
  auctionSelectionSuccessIcon = `${environment.assets}/auction-selection-success-icon.svg`;
  auctionTicket = `${environment.assets}/auction-ticket.png`;
  correctCode = '9723';
  wrongCode: boolean;
  loading: boolean;
  warningIcon = `${environment.assets}/warning.svg`;

  constructor(
    private readonly store: Store,
    private readonly logService: LogService,
    private readonly logger: LoggerService
  ) { }

  ngOnInit() {
    document.documentElement.style.overflow = 'auto';
    this.loading = true;

    this.loginInfo = this.store.selectSnapshot(LoginState.loginResponse);
    this.logService
      .action('CUSTOMER_DAY', 'INIT', { userId: this.loginInfo?.user?.id, userEmail: this.loginInfo?.user?.email })
      .subscribe();

    this.store.dispatch(new GetDonateCustomerAction(this.loginInfo?.user?.id))
      .pipe(map(() => this.store.selectSnapshot(DonateState.getState)))
      .subscribe(state => {
          this.loading = false;
          this.customerInfo = { ...state };
          this.logService
            .action('CUSTOMER_DAY', 'API_RESP', this.customerInfo)
            .subscribe();
        },
        error => {
          console.error(error);
          this.loading = false;
          this.error = true;
          this.logger.jsErrorLog({
            message: error.message,
            name: JSON.stringify({ total: error.error.total, timeStamp: error.error.timeStamp,  }),
            url: window.location.href,
          }).subscribe();

        },
        () => {
          this.loading = false;
        }
      );

  }

  changeDefaultValue() {
    this.successSent = false;
  }

  onInputCode(event: any, nextInput: ElementRef, prevInput: ElementRef) {
    event.target.value = event.target.value.replace(/\D/g, '');
    const input = event.target as HTMLInputElement;
    const code = input.value;

    if (code.length === 1) {
      nextInput?.nativeElement.focus();
    }
    if (code.length === 0) {
      prevInput?.nativeElement.focus();
    }
  }

  selectAgreement(answer: string) {
    this.save({
      Status: answer,
    });
  }

  toggler() {
    this.togglePopup = !this.togglePopup;
  }

  onTogglePopup(event: boolean): void {
    this.togglePopup = event;
  }

  success(event: boolean): void {
    this.successSent = event;
  }

  checkInSubmit() {
    const { CodeFirst, CodeSecond, CodeThird, CodeFourth } = this.form.value;
    const code = `${CodeFirst}${CodeSecond}${CodeThird}${CodeFourth}`;
    if (code !== this.correctCode) {
      this.wrongCode = true;
      this.form.reset();
    }
    if (this.form.valid && code === this.correctCode) {
      this.wrongCode = false;
      this.save({
        ...this.customerInfo,
        CheckinCode: code,
      });
    } else {
      console.log('form invalid', this.form.value);
    }

  }

  resetError() {
    this.wrongCode = false;
    this.error = false;
    this.firstSendCode = false;
    this.codeFirstInput?.nativeElement?.focus();
  }


  save(payload: Partial<DonateStateModel>) {
    this.loading = true;
    if (payload.CheckinCode) {
      this.firstSendCode = true;
    }
    this.store.dispatch(new PostDonateCustomerAction(payload))
      .pipe(
        map(() => this.store.selectSnapshot(DonateState.getState))
      )
      .subscribe(
        state => {
          this.customerInfo = { ...state };
          this.loading = false;
          this.error = false;
        },
        error => {
          console.error(error);
          this.loading = false;
          this.error = true;
        }
      );
  }

}
