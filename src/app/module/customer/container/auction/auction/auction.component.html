<div class="auction">
  <div class="auction-header-background-pic">
    <div class="content">
      <div class="content-header">
        <div class="content-header-welcome">{{ '_hello' | translate }} {{ loginInfo?.user?.firstName }},</div>
        <div class="content-header-welcome-desc">{{ '_customer_day_header_welcome_desc' | translate }}
        </div>
      </div>
      <div class="content-info-area pt-5">
        <div class="content-info-area-location">
          <div class="icon mx-1">
            <i class="icon icon-calendar"></i>
          </div>
          <div class="">
            |
          </div>
          <div class="adress ml-2">
            {{ '_customer_day_header_date' | translate }}
          </div>
        </div>
        <div class="content-info-area-location">
          <div class="icon mx-1">
            <i class="icon icon-location"></i>
          </div>
          <div class="mx-1">
            |
          </div>
          <div class="adress mx-1">
            {{ '_customer_day_header_adress' | translate }}
          </div>
        </div>
        <div class="content-offer-area">
            <div [ngClass]="{
              'content-offer-area-agree pointerEventsNone' : customerInfo?.Status === 'true',
              'content-offer-area-disagree' : customerInfo?.Status !== 'true'
              }" class=" mx-2"><span class="text" (click)="selectAgreement('true')">{{ '_i_join' | translate }}</span></div>
              <div [ngClass]="{
              'content-offer-area-agree pointerEventsNone' : customerInfo?.Status === 'false',
              'content-offer-area-disagree' : customerInfo?.Status !== 'false'
              }" class="mx-2"><span class="text" (click)="selectAgreement('false')">{{ '_i_dont_join' | translate }}</span></div>
        </div>
      </div>
      <div class="content-checkIn-area" *ngIf="customerInfo.Status === 'true' && !temporaryDisabled" >
        <div class="content-checkIn-area-header"  *ngIf="(!customerInfo.CheckinCode) || (customerInfo.CheckinCode !== correctCode) && !firstSendCode">
          <span class="title">{{ '_welcome' | translate }}</span>
          <span
            class="desc">{{ '_customer_day_came' | translate }}</span>
        </div>
        <div class="content-checkIn-area-header"  *ngIf="firstSendCode">
          <span class="title">{{ '_welcome' | translate }}</span>
          <span
            class="desc"> {{ '_customer_day_checkin_completed' | translate }}</span>
        </div>
        <div class="content-checkIn-area-form" *ngIf="(!customerInfo.CheckinCode) || (customerInfo.CheckinCode !== correctCode) ">
          <form [formGroup]="form" (submit)="checkInSubmit()">
            <div class="form-group">
              <input
                catInputLength
                [name]="'CodeFirst'"
                class="form-control mx-2"
                [formControlName]="'CodeFirst'"
                type="tel"
                maxlength="1"
                (input)="onInputCode($event, codeSecondInput, null)"
                #CodeFirst
              />
              <input
                catInputLength
                [name]="'CodeSecond'"
                class="form-control mx-2"
                [formControlName]="'CodeSecond'"
                type="tel"
                maxlength="1"
                (input)="onInputCode($event, codeThirdInput, codeFirstInput)"
                #CodeSecond
                pattern="[0-9]*"
              />
              <input
                catInputLength
                [name]="'CodeThird'"
                class="form-control mx-2"
                [formControlName]="'CodeThird'"
                type="tel"
                maxlength="1"
                (input)="onInputCode($event, codeFourthInput, codeSecondInput)"
                #CodeThird
              />
              <input
                catInputLength
                [name]="'CodeFourth'"
                class="form-control mx-2"
                [formControlName]="'CodeFourth'"
                type="tel"
                maxlength="1"
                (input)="onInputCode($event, null, codeThirdInput)"
                #CodeFourth
              />
            </div>
            <div class="buton">
              <button type="submit" class="btn btn-warning">{{ '_customer_day_confirm_button' | translate }}</button>
            </div>
          </form>
          <div class="rate pt-5 pb-3" (click)="toggler()">
            <span [innerHTML]="('_customer_day_click_to_evaluate' | translate) | safe: 'html'"></span>
          </div>
        </div>
        <div class="content-checkIn-area-form" *ngIf="firstSendCode && !error">
          <div class="d-flex justify-content-center">
            <img [src]="auctionSelectionSuccessIcon" alt="" style="width: 195px; height: 202px;">
          </div>
        </div>
      </div>
    </div>
    <!-- <hr class="hr" *ngIf="firstSendCode"> -->
    <cat-auction-selection [customerInfo]="customerInfo" *ngIf="(customerInfo.CheckinCode) && (customerInfo.CheckinCode === correctCode) && customerInfo.Status === 'true'"></cat-auction-selection>

    <div class="ifStatusFalse" *ngIf="customerInfo.Status === 'false' || customerInfo.Status === null && !temporaryDisabled" style="margin: 5px;">
      <div class="content-checkIn-area-header" style="margin-top: 106px">
        <cat-info-box [title]="'_info' | translate">
          {{ "_customer_day_attend_the_event" | translate }}
        </cat-info-box>
        <img [src]="auctionTicket" class="auction-image">
      </div>
      <!-- <div class="rate pt-3 pb-5 pl-2" style="margin-left: 7px; font-size: 14px;" (click)="toggler()"> -->
        <!-- <span [innerHTML]="('_customer_day_click_to_evaluate' | translate) | safe: 'html'"></span> -->
      <!-- </div> -->
    </div>
    <!-- temporaryDisabled  sonradan silinecek-->
<div class="temporaryDisabled" *ngIf="temporaryDisabled && customerInfo.Status === ('true' || '')" style="margin: 5px; padding-top: 100px;">
  <div class="content-checkIn-area-header">
    <img [src]="auctionTicket" class="auction-image">
  </div>
</div>
  </div>

</div>
<cat-loader [show]="loading"></cat-loader>

<cat-basic-modal [(status)]="wrongCode || error" (statusChange)="resetError()">
  <div>
    <div class="text-center">
      <img [src]="warningIcon" style="
      width: 60px;
      height: 60px;
      margin-bottom: 1rem;"/>
    </div>
    <div class="">
      <span class="successFullyFeedback"  [innerHTML]="( wrongCode ? '_customer_day_wrong_code': '_error') | translate  | safe: 'html'"></span>
    </div>
  </div>
</cat-basic-modal>

<cat-big-modal [(status)]="togglePopup">
  <cat-rate-us (togglePopup)="onTogglePopup($event)" [resetForm]="this.togglePopup" (successSent)="success($event)"></cat-rate-us>
</cat-big-modal>

<cat-basic-modal [(status)]="successSent" (statusChange)="changeDefaultValue()">
  <div>
    <div class="text-center">
      <i class="icon icon-message-success d-inline-block mb-3"></i>
    </div>
    <div class="">
      <span class="successFullyFeedback"  [innerHTML]="('_general_successfully_send_form' | translate) | safe: 'html'"></span>
    </div>
  </div>
</cat-basic-modal>
