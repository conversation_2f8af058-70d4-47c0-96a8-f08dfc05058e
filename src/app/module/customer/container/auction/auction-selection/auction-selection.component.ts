import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CustomValidator } from 'src/app/util/custom-validator';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { environment } from 'src/environments/environment';
import { Store } from '@ngxs/store';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { DonateState, DonateStateModel } from '../../../state/donate.state';
import { PostDonateFeedbackAction } from '../../../action/donate.actions';
import { map } from 'rxjs/operators';


@Component({
  selector: 'cat-auction-selection',
  templateUrl: './auction-selection.component.html',
  styleUrls: ['./auction-selection.component.scss']
})
export class AuctionSelectionComponent implements OnInit {

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  user: UserModel;
  successDonated = false;
  successSent = false;
  temporaryDisabled = false;
  error: boolean;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    Amount: new FormControl(0, [Validators.required, Validators.min(1), Validators.max(10000000000000001)]),
    Rating: new FormControl(null, []),
    Comment: new FormControl(null, []),
    Agreement: new FormControl(false, []),
  });

  auctionSelectionIcon = `${environment.assets}/auction-selection-icon.svg`;
  agreementValue = false;
  togglePopup = false;
  correctCode = '9723';
  loading = false;
  @Input() customerInfo: any;


  constructor(
    private store: Store,
  ) { }

  ngOnInit() {
    this.getUser();
  }
  getUser(){
    document.documentElement.style.overflow = 'auto';
    this.user = this.store.selectSnapshot(LoginState.user);
    if (this.user) {
      this.form.controls['Name'].setValue(this.user.firstName);
      this.form.controls['Surname'].setValue(this.user.lastName);
      this.form.controls['Email'].setValue(this.user.email);
    }
  }

  success(event: boolean): void{
    this.successSent = event;
    console.log('successSent', this.successSent);

  }
  changeDefaultValue() {
    this.successSent = false;
  }

  onInputPhone(event: Event) {
    const input = event.target as HTMLInputElement;
    const code = input.value;
    const newValue = code.replace(/\D/g, '');
    this.form.controls['Amount'].setValue(newValue);
  }

  // incrementAmount() {
  //   const currentAmount = this.form.get('Amount').value;
  //   const newAmount = currentAmount + 5000;
  //   this.form.controls['Amount'].setValue(newAmount);
  // }

  // decrementAmount() {
  //   const currentAmount = this.form.get('Amount').value;
  //   const newAmount = currentAmount - 5000 < 0 ? 0 : currentAmount - 5000;
  //   this.form.controls['Amount'].setValue(newAmount);
  // }

  onSubmit() {
    const value = this.form.value;

    if (this.form.valid) {
      this.save(value); // save
    }
    if (this.form.invalid) {
      this.form.markAllAsTouched();
    }
  }
  save(payload: Partial<DonateStateModel>) {
    this.loading = true;
    this.store.dispatch(new PostDonateFeedbackAction(payload))
      .pipe(map(() => this.store.selectSnapshot(DonateState.getState)))
      .subscribe(state => {
        this.customerInfo = { ...state };
        this.loading = false;
        this.successDonated = true;
        this.form.reset();
        this.form.controls['Amount'].setValue(0);
        this.form.controls['Agreement'].setValue(false);
        this.agreementValue = false;
        if(this.user){
          this.getUser();
        }
      },
      error => {
        console.error(error);
        this.loading = false;
        this.error = true;
      },
      );
  }
  toggler() {
    this.togglePopup = !this.togglePopup;
    document.documentElement.style.overflow = 'auto';
  }
  onTogglePopup(event: boolean): void {
    this.togglePopup = event;
    document.documentElement.style.overflow = 'auto';
  }
  agreementChange() {
   this.agreementValue = !this.agreementValue;
  }
}
