.auction {
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  color: black;
  font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  font-style: normal;
}

.content {

  &-info-area {
    width: 90%;
    margin: 0 auto;
  }
  &-header {
    &-title {
      font-weight: 700;
      font-size: 23px;
      line-height: 222.5%;
      display: flex;
      align-items: center;
      color: #16213B;
      margin: 0 auto;
      width: 294px;
      height: 63px;
      line-height: 172%;

    }
    &-desc {
      margin: 0 auto;
      width: 294px;
      height: 63px;
      font-weight: 400;
      font-size: 13px;
      line-height: 172%;
      display: flex;
      align-items: center;
      color: #16213B;
    }
  }
}
.offerInput {
  display: flex;
  justify-content: center;
  button {
    background-color: #4A8EB0;
    width: 35px;
    height: 50px;
    border-radius: 3.06667px !important;
    display: flex;
    justify-content: center;

    &:first-child {
      margin-right: -8px;
      position: relative;
      z-index: 99;
    }
    &:last-child {
      margin-left: -8px;
      position: relative;
      z-index: 99;
    }
  }
}
.hr {
  width: 339px;
  height: 0px;
  margin-top: 17%;
  border: 1px solid #C1CDD1;
}

form .buttonSubmit {
  display: flex;
  justify-content: center;
  button {
    font-weight: 700;
    font-size: 16px;
    align-items: center;
    text-align: center;
    color: #FFFFFF !important;
  }
}

.offerInput {
  position: relative;
  width: 100%;
}

.offerInput::after {
  content: "AZN";
  position: absolute;
  top: 7px;
  right: 11px;
  padding: 8px;
  pointer-events: none;

  color: #8E8E8E;
  font-weight: 700;
  font-size: 16px;
  line-height: 243.5%;
}

.agreement {
  &-text {
    width: 100%;
    font-weight: 400;
    font-size: 13px;
    line-height: 172%;
    display: flex;
    align-items: center;
    color: #16213B;
    margin-left: 7px;
  }
  &-check {
    margin-left: 7px;
  }
}
[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
  margin-top: 2px;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
form .buttonSubmit  button  {
  border-radius: 6px !important;
}

.rate {
  width: 100%;
  font-weight: 400;
  font-size: 15px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  margin-left: 7px;
}

.customerDayDonationSuccess {
  width: 100%;
  font-weight: 400;
  font-size: 17px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  justify-content: center;
  height: 100px;
  padding-bottom: 15px;
}

.successFullyFeedback {
  width: 100%;
  font-weight: 600;
  font-size: 21px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  justify-content: center;
  height: max-content;
  padding-bottom: 15px;
  text-align: center;
}

.customerDayAgreementText {
  font-weight: 400;
  font-size: 13px;
  line-height: 172%;
  display: flex;
  align-items: center;
  color: #16213B;
  margin-left: 3px;
}

@media (min-width: 1023px) {
  .content-info-area {
    width: 30%;
  }
}
