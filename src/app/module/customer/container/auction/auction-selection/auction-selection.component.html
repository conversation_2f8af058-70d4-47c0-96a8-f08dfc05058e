
<div class="auction" *ngIf="!temporaryDisabled">
  <div class="content">
    <div class="content-header">
      <div class="content-header-title">
        {{ '_customer_day_donate' | translate }}
      </div>
      <div class="content-header-desc">
        {{ '_customer_day_donation_text' | translate }}
      </div>
      <div class="d-flex justify-content-center">
        <img [src]="auctionSelectionIcon" alt="" style="width: 305px; height: 242px;">
      </div>
    </div>
    <div class="content-info-area pt-5" >
      <!-- <div style="color: #16213B; width: 150px; -->
      <!-- height: 19px; font-size: 14px;">Açık Arttırma Seç.</div> -->
      <form [formGroup]="form" (submit)="onSubmit()" >
        <div class="pt-2" *ngIf="!user">
          <div class="form-group">
            <input
              catInputLength
              [name]="'Name'"
              [placeholder]="'_name' | translate"
              class="form-control form-control"
              formControlName="Name"
              type="text"
              minlength="3"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Name) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              catInputLength
              [name]="'Surname'"
              [placeholder]="'_surname' | translate"
              class="form-control form-control"
              formControlName="Surname"
              type="text"
              minlength="2"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Surname) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              catInputLength
              [name]="'Email'"
              [placeholder]="'_email' | translate"
              class="form-control form-control"
              formControlName="Email"
              type="email"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Email) | translate }}
            </div>
          </div>
          <div class="mx-n4"></div>
        </div>
        <div style="color: #16213B; width: 150px;
        height: 19px; font-size: 14px; margin-left: 3px;">{{ '_customer_day_type_offer' | translate }}</div>
        <div class="form-group offerInput pt-2">
          <!-- <button type="button" class="btn text-light" (click)="incrementOffer()">+</button> -->
          <input
          catInputLength
          [name]="'Amount'"
          [value]="(form.controls['Amount'].value | number: '1.0-0')"
          style="width: 100%;"
          class="form-control offerInput"
          formControlName="Amount"
          type="tel"
          (input)="onInputPhone($event)"
          #Amount
        />
      <!-- <button type="button" class="btn text-light" (click)="decrementOffer()">-</button> -->
        </div>
        <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Amount) }"
        class="invalid-feedback pl-3" style="
          padding-left: 3px !important;
          width: 100%;
          margin-top: -8px !important;
          font-size: 80%;
          color: #DA3A3C;
          margin-bottom: 9px;
        "
      >
        {{ getFormErrorMessage(form.controls.Amount) | translate }}
      </div>
      <div class="agreement">

       <div class="agreement-text">
        {{ 'customer_day_welcome_text' | translate }}
       </div>
       <div class="agreement-check d-flex my-3">
        <input [name]="'Agreement'" id="Agreement" formControlName="Agreement" class="form-check-input" type="checkbox"
        [checked]="this.agreementValue" [value]="form.value.Agreement" (click)="agreementChange()">
        <label [for]="'Agreement'"></label>

        <div class="customerDayAgreementText" (click)="agreementChange()">
          {{ 'customer_day_agreement_text' | translate }}
        </div>

       </div>
      </div>
       <div class="buttonSubmit py-3">
        <button type="submit" class="btn btn-warning text-light w-75 d-flex justify-content-center" [disabled]="!agreementValue" >{{ '_donate' | translate }}</button>
       </div>
      </form>
      <div class="rate pt-4 mb-5" (click)="toggler()">
        <span [innerHTML]="('_customer_day_click_to_evaluate' | translate) | safe: 'html'"></span>
      </div>
    </div>
  </div>
</div>

<cat-basic-modal [(status)]="togglePopup">
  <cat-rate-us (togglePopup)="onTogglePopup($event)" [resetForm]="togglePopup" (successSent)="success($event)"></cat-rate-us>
</cat-basic-modal>

<cat-basic-modal [(status)]="successDonated">
<div>
  <div class="text-center">
    <img [src]="auctionSelectionIcon" alt="" style="width: 257px; height: 142px;">
  </div>
  <div class="text-center pt-3">
    <span class="customerDayDonationSuccess"  [innerHTML]="('_customer_day_donation_success' | translate) | safe: 'html'"></span>
  </div>
</div>
</cat-basic-modal>

<cat-loader [show]="loading"></cat-loader>

<cat-basic-modal [(status)]="successSent" (statusChange)="changeDefaultValue()">
  <div>
    <div class="text-center">
      <i class="icon icon-message-success d-inline-block mb-3"></i>
    </div>
    <div class="">
      <span class="successFullyFeedback"  [innerHTML]="('_general_successfully_send_form' | translate) | safe: 'html'"></span>
    </div>
  </div>
</cat-basic-modal>

<cat-error-modal [(status)]="error" [message]="'_error' | translate "></cat-error-modal>
