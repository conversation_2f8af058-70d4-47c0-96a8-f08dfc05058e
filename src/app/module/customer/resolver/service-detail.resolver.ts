import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { Store } from '@ngxs/store';
import { GetServiceDetailAction, SetServiceDetailAction } from '../action/service.action';
import { LoginState } from '../../authentication/state/login/login.state';
import { ServiceState } from '../state/service.state';

@Injectable()
export class ServiceDetailResolver implements Resolve<boolean> {
  constructor(
    protected readonly store: Store
  ) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const workOrderNumber = route.params.id;
    const serviceList = this.store.selectSnapshot(ServiceState.requestServiceDetailedList);
    if (serviceList?.length) {
      const serviceDetail = serviceList?.find(item => item?.serviceNumber === workOrderNumber);
      return this.store.dispatch(new SetServiceDetailAction(serviceDetail));
    }

    const customerNumber = this.store.selectSnapshot(LoginState.customerNumber);
    return this.store.dispatch(new GetServiceDetailAction(route.params.id, customerNumber));
  }
}
