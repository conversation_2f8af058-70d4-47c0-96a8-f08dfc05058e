import { FilterSearchModel } from '../model/filter.model';
import { ServiceDetailedRequestModel } from '../model/service-request.model';

export class GetWorkOrderFilterFieldsAction {
  public static readonly type = '[Customer] Get Work Order Search Fields';

  constructor(public customerId?: string, public language?: string) {
  }
}
export class GetServiceDetailedListAction {
  public static readonly type = '[Customer] Get Service Detailed List';

  constructor(public filterBody: FilterSearchModel) {
  }
}

export class GetServiceDetailAction {
  public static readonly type = '[Customer] Get Service Detail';

  constructor(public workorderNumber: string, public customerNumber: string) {
  }
}
export class SetServiceDetailAction {
  public static readonly type = '[Customer] Set Service Detail';
  constructor(public serviceDetail: ServiceDetailedRequestModel) {
  }
}

export class GetWorkOrderListAction {
  public static readonly type = '[Customer] Get Work Order List';

  constructor(public serviceNumber: string, public isCrc, public serviceOrganization = null) {
  }
}

export class GetServiceLocationAction {
  public static readonly type = '[Customer] Get Service Car Location';

  constructor(public plateNumber: string) {
  }
}

export class GetWorkOrderChatMessagesAction {
  public static readonly type = '[Customer] Get Work Order Messages';

  constructor(public serviceDetail: ServiceDetailedRequestModel, public isRefresh: boolean = false) {
  }
}


export class ResetWorkOrderChatMessagesAction {
  public static readonly type = '[Customer] Reset Work Order Messages';

  constructor() {
  }
}

export class GetWorkOrderFromQrAction {
  public static readonly type = '[Customer] Get Work Order From QR';

  constructor(public workorderNumber: string) {
  }
}
