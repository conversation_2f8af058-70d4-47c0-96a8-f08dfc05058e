export class GetEquipmentNewSearchAction {
  public static readonly type = '[Customer] Get Equipment List Search';

  constructor(public page: number = 1, public searchParameter = [], public top: number = 10) {
  }
}

export class GetEquipmentListFilterAction {
  public static readonly type = '[Customer] Get Equipment List Filter';

  constructor(public customerId?: string, public language?: string) {
  }
}

export class GetEquipmentAgendaHistory {
  public static readonly type = '[Customer] Get Equipment Agenda History'

  constructor(public SerialNumber: string, public EquipmentNumber: string, public SelectedDate?: any){}
}

export class GetEquipmentAgendaDetails {
  public static readonly type = '[Customer] Get Equipment Agenda Details'

  constructor(public SerialNumber: string, public Model: string, public EquipmentNumber: string){}
}

export class GetCheckedInEquipmentsAction {
  public static readonly type = '[Customer] Get Checked In Equipments'

  constructor(){}
}

export class ResetEquipmentAgendaHistoryAction {
  public static readonly type = '[Customer] Reset Equipment Agenda Details'

  constructor(){}
}

export class GetEquipmentDetailAction {
  public static readonly type = '[Customer] Get Equipment Detail';

  constructor(public equipmentNumber: string) {
  }
}

export class GetEquipmentSosAnalyzesAction {
  public static readonly type = '[Customer] Get Equipment Sos Analyzes';

  constructor(public serialNumber: string) {
  }
}

export class GetEquipmentMDAPmPlannerAction {
  public static readonly type = '[Customer] Get Equipment MDA Pm Planner';

  constructor(public serialNumber: string) {
  }
}

export class GetEquipmentInspectionsAction {
  public static readonly type = '[Customer] Get Equipment Inspections';

  constructor(public serialNumber: string) {
  }
}

export class GetEquipmentListAllAction {
  public static readonly type = '[Customer] Get Equipment List All';

  constructor(public page: number = 1, public searchParameter = [], public top: number = 1000) {
  }
}

export class GetEquipmentLocationAction {
  public static readonly type = ' [Customer] Get Equipment Location'

  constructor(){}
}

export class GetEquipmentDiagnosticDataPagingAction {
  public static readonly type = '[Customer] Get Equipment Diagnostic Data';

  constructor(public equipmentNumber: string, public serialNumber: string, public top: number, public page: number = 1) {
  }
}

export class GetEquipmentAllDiagnosticDataAction {
  public static readonly type = '[Customer] Get Equipment Diagnostic Data All';

  constructor(public equipmentNumber: string, public serialNumber: string, public top: number = 1000, public page: number = 1) {
  }
}

export class GetEquipmentInspectionHistoryAction {
  public static readonly type = '[Customer] Get Equipment Inspection History';

  constructor(public serialNumber: string) {
  }
}

export class GetEquipmentHasPSEAction {
  public static readonly type = '[Customer] Get Equipment PSE';

  constructor(public pseRequestId: string) {
  }
}

export class PostEquipmentPSEAction {
  public static readonly type = '[Customer] Post Equipment PSE';

  constructor(public ProductCode: string, public Quantity: string, public RequestId: string) {
  }
}

export class ClearEquipmentPSEAction {
  public static readonly type = '[Customer] Clear Equipment PSE';

  constructor() {
  }
}
export class AddedToCartEquipmentPSEAction {
  public static readonly type = '[Customer] AddedToCart Equipment PSE';

  constructor() {
  }
}

export class EquipmentPSESearchAction {
  public static readonly type = '[Customer] Equipment Search PSE';

  constructor(public productCode: string, public requestId: string) {

  }
}

export class EquipmentWorkOrderPlanAction {
  public static readonly type = '[Customer] Equipment Work Order Plan';

  constructor(public serialNumber: string) {

  }
}

export class EquipmentCostCategoryListAction {
  public static readonly type = '[Customer] Equipment Cost Category List Action';

  constructor(){}
}

export class GetEquipmentCostList {
  public static readonly type = '[Customer] Equipment Cost List Action';

  constructor(public serialNumber: string, public searchText: string){}
}

export class ResetEquipmentCostListAction {
  public static readonly type = '[Customer] Reset Equipment Cost List';

  constructor(){}
}

export class GetEquipmentQuotationsAction {
  public static readonly type = '[Customer] Equipment Quotation List';

  constructor(public serialNumber: string){}
}

export class GetEquipmentServiceHistoryAction {
  public static readonly type = '[Customer] Equipment Service History List';

  constructor(public equipmentNumber: string){}
}
