export class GetOfferListAction {
  public static readonly type = '[Customer] Get Offer List';

  constructor(public customerNumber: string, public refresh?: any) {
  }
}

export class GetOfferDetailAction {
  public static readonly type = '[Customer] Get Offer Detail';

  constructor(public quotationNumber: string, public guid?: string) {
  }
}

export class ClearOfferDetailAction {
  public static readonly type = '[Customer] Clear Offer Detail';
}

export class ApproveQuotationAction {
  public static readonly type = '[Customer] Quotation Approve';

  constructor(public quotationNumber: string) {
  }
}

export class RejectQuotationAction {
  public static readonly type = '[Customer] Quotation Reject';

  constructor(public quotationNumber: string) {
  }
}

export class RevisionQuotationAction {
  public static readonly type = '[Customer] Quotation Revision';

  constructor(public quotationNumber: string, public revisionReason: string) {
  }
}

export class CreateOrderQuotationAction {
  public static readonly type = '[Customer] Quotation Create Order';

  constructor(public quotationNumber: string, public paymentMethod: number, public approveAgreements  = false) {
  }
}

export class CreateOrderQuotationRemoveAction {
  public static readonly type = '[Customer] Quotation Create Order Remove';

  constructor() {
  }
}

export class GetPaymentStatusAction {
  public static readonly type = '[Customer] Quotation Payment Status';

  constructor(public traceId: string) {
  }
}

export class ClearCreateOrderAction {
  public static readonly type = '[Customer] Quotation Create Order Clear';

  constructor() {
  }
}
export class ClearQuotationAction {
  public static readonly type = '[Customer] Quotation Action Clear';

  constructor() {
  }
}

export class GetAvailableQuotationTypesAction {
  public static readonly type = '[Customer] Get Available Quotation Types'
  constructor(){}
}

export class GetMyOngoingQuotationsAction {
  public static readonly type = '[Customer] Get My Ongoing Quotations'
  constructor(){}
}

export class GetMyOngoingQuotationsDetailsAction {
  public static readonly type = '[Customer] Get My Ongoing Quotations Details'
  constructor(public serialNumbers: string[]){}
}
