import { MutabakatDetailRequestModel, MutabakatReplyRequestModel } from '../model/financial.model';
import { MutabakatReplyActionEnum } from '../../definition/enum/mutabakat-type.enum';

export class GetFinancialInformationAction {
  public static readonly type = '[Financial] Get Financial Information';

  constructor(public customerNumber: string) {
  }
}

export class GetMutabakatListAction {
  public static readonly type = '[Financial] Get Mutabakat List';

  constructor(public numberOfItems: number = 5) {
  }
}

export class GetMutabakatDetailAction {
  public static readonly type = '[Financial] Get Mutabakat Detail';

  constructor(public mutabakatDetailRequest: MutabakatDetailRequestModel) {
  }
}

export class GetMutabakatReplyReasonsAction {
  public static readonly type = '[Financial] Get Mutabakat Reply Reason';

  constructor(public action: MutabakatReplyActionEnum) {
  }
}

export class ClearMutabakatDetailAction {
  public static readonly type = '[Financial] Clear Mutabakat Detail';

  constructor() {
  }
}

export class ClearMutabakatReplyDetailAction {
  public static readonly type = '[Financial] Clear Mutabakat Reply Detail';
  constructor() {
  }
}


export class SendMutabakatReplyAction {
  public static readonly type = '[Financial] Send Mutabakat Reply';

  constructor(public mutabakatReply: MutabakatReplyRequestModel) {
  }
}

export class GetReconciliationStatementSendAction {
  public static readonly type = '[Financial] Send Reconciliation Statement';

  constructor(public reconciliationSend: {startDate: string, endDate: string}) {
  }
}
