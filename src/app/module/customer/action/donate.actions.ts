import { DonateState, DonateStateModel } from '../state/donate.state';

export class PostDonateCustomerAction {
  public static readonly type = '[Customer] Donate PostDetail';

  constructor(public payload: any ) {
  }
}

export class GetDonateCustomerAction {
  public static readonly type = '[Customer] Donate GetDetail';

  constructor(public PortalUserId: string ) {
  }
}

export class PostDonateFeedbackAction {
  public static readonly type = '[Customer] Donate PostFeedback';

  constructor(public payload: any ) {
  }
}
