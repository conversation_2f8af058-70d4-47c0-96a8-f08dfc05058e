import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { LoyalityAction } from '../../action/loyality.action';
import { LoyalityState } from '../../state/loyality.state';
import { filter, take } from 'rxjs/operators';
import { LoginState } from '../../../authentication/state/login/login.state';
import { CustomerModel } from '../../model/customer.model';
import { LogService } from '../../service/log.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'cat-loyality',
  templateUrl: './loyality.component.html',
  styleUrls: ['./loyality.component.scss'],
})
export class LoyalityComponent implements OnInit {
  @ViewChild('promotionForm') promotionForm: ElementRef;

  @Select(LoyalityState.getLoading)
  loading$: Observable<boolean>;

  @Select(LoyalityState.getRedirectParameters)
  redirectParameters$: Observable<any>;

  actionUrl = '';
  token = '';

  @Select(LoginState.customer)
  customer$: Observable<CustomerModel>;

  @Select(LoginState.loginLoading)
  loginLoading$: Observable<boolean>;
  protected path: string;

  constructor(
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly log: LogService,
    private readonly route: ActivatedRoute,
  ) {
    stopLoadingAnimation();
  }

  ngOnInit(): void {
    this.path = this.route.snapshot.queryParams?.path;

    const customer = this.store.selectSnapshot(LoginState.customer);
    const user = this.store.selectSnapshot(LoginState.user);
    if (customer) {
      this.loadData();
    } else if (user) {
      this.customer$
        .pipe(filter(Boolean))
        .pipe(take(1))
        .subscribe(() => {
          this.loadData();
        });
    }
  }

  loadData() {
    this.store.dispatch(new LoyalityAction());
    this.redirectParameters$
      .subscribe((response) => {
      if (!response || !response?.url || !response?.token) {
        return;
      }
      this.token = response?.token;
      this.actionUrl = response?.url;
      this.log.action('PROMOTION', 'PROMOTION_PORTAL_REDIRECT', {
        actionUrl: window.location.href,
      }).subscribe();
      this.start();
    });
  }

  start() {
    const form = document.createElement('form');
    form.action = this.actionUrl;
    if (this.path) {
      form.action += '?url=' + encodeURIComponent(this.path);
    }
    form.method = 'post';
    const hiddenField = document.createElement('input');
    hiddenField.type = 'hidden';
    hiddenField.name = 'token';
    hiddenField.value = this.token;
    form.appendChild(hiddenField);
    document.body.appendChild(form);
    form.submit();
  }
}
