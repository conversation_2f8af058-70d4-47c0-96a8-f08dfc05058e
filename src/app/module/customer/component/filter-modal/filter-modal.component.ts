import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { FilterFieldsModel, FilterItemModel } from '../../model/filter.model';

export enum FilterFieldModelEnum {
  equipment = 'equipment',
  workorder = 'workorder'
}

@Component({
  selector: 'cat-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.scss']
})
export class FilterModalComponent implements OnInit, OnDestroy {
  @Input()
  totalCount: number = null;

  // tslint:disable-next-line:variable-name
  private _filterFields: FilterFieldsModel[];

  @Input()
  set filterFields(filterFields: FilterFieldsModel[]) {

    this._filterFields = filterFields;
    this.applyFields();
  }

  get filterFields() {
    return this._filterFields;
  }

  @Input()
  specialHiddenFieldsList: string[] = [];

  @Input()
  filterFieldModel: FilterFieldModelEnum;

  @Output()
  applyFilter = new EventEmitter<FilterItemModel[]>();

  @Input()
  filter: FilterItemModel[];

  @Output()
  filteredChange = new EventEmitter<boolean>();

  @Input()
  filtered = false;

  @Input()
  clearOnlyForm = false;

  @Input()
  modalZIndexUpLoading = false;

  @Input()
  closeAfterSyncForm = true;

  isShowFilterModal = false;
  filterForm = new FormGroup({});
  isShowError = isShowFormError;

  constructor() { }

  ngOnInit() {
    this.applyFields();
    if (this.filter) {
      this.applyToForm();
    }

  }

  applyToForm() {
    this.filter?.map(f => {
      this.filterForm.get(f.fieldName)?.setValue(f.fieldValue);
    });
  }

  applyFields() {
    if (this._filterFields?.length) {
      this._filterFields.map(f => {
        if (!this.specialHiddenFieldsList.includes(f.fieldName)) {
          if (f.fieldValidations?.length) {
            this.setRegexValidator(f);
          } else {
            this.filterForm.addControl(f.fieldName, new FormControl());
          }
        }
      });
      this.applyToForm();
    }
  }

  filterSync() {
    this.filterForm = new FormGroup({});
    this.applyFields();
  }

  openFilterModal() {
    this.isShowFilterModal = true;
  }

  getFilterList() {
    if(!this.filterForm.valid) return
    this.filter = [];
    Object.entries(this.filterForm.value).map(([key, value]) => {
      let data = value;
      if (typeof value === 'object') {
        data = null;
      }
      if (value && data !== null) {
        this.filter.push({
          fieldName: key,
          fieldValue: data
        });
      }
    });
    this.filtered = !!this.filter?.length;
    this.filteredChange.emit(this.filtered);
    this.applyFilter.emit(this.filter);
    this.isShowFilterModal = false;
  }

  setRegexValidator(f: FilterFieldsModel) {
    switch (f.fieldValidations?.length) {
      case 1:
        this.filterForm.addControl(f.fieldName, new FormControl(null, [
          this.regexValidator(new RegExp(f.fieldValidations[0].regex), { error: true }),
        ]));
        break;
      case 2:
        this.filterForm.addControl(f.fieldName, new FormControl(null, [
          this.regexValidator(new RegExp(f.fieldValidations[0].regex), { min: true }),
          this.regexValidator(new RegExp(f.fieldValidations[1].regex), { max: true }),
        ]));
        break;
      default:
        this.filterForm.addControl(f.fieldName, new FormControl());
        break;
    }
  }

  regexValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      if (!control.value) {
        return null;
      }
      if (control.value.length === 2) {
        return null;
      }
      const valid = regex.test(control.value);
      return valid ? null : error;
    };
  }

  protected reset() {
    this.filtered = false;
    this.filterForm?.reset();
    this.filteredChange.emit(this.filtered);
  }

  public clearFilter(): void {
    this.reset();
    this.applyFilter.emit([]);
    this.isShowFilterModal = false;
  }

  hasClear() {
    return !!Object.values(this.filterForm.value).filter((item: any) => !['', false, null].includes(item))?.length;
  }

  modalStatusChange() {
    if (this.closeAfterSyncForm) {
      this.filterSync();
    }
  }

  clearFilterButton() {
    this.filtered = false;
    if (this.clearOnlyForm) {
      this.filterForm.reset();
    } else {
      this.filter = [];
      this.applyFilter.emit([]);
      this.reset();
    }
  }

  ngOnDestroy(): void {
    this.reset();
  }
}
