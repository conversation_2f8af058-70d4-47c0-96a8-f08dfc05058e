.filter-modal {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  color: #505050;
  min-width: 50px;

  .dropdown-menu {
    min-width: 16rem;
  }

  .is-sound-diag-enabled {
    height: 1.5rem !important;
    width: 15%;
  }

  .dropdown-item.active,
  .dropdown-item:active {
    color: #16181b;
  }

  input:focus {
    box-shadow: none;
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    background-color: transparent !important;
    border: 0;
    box-shadow: none !important;
  }

  .filter-select,
  .filter-select * {
    color: #8E8E8E;
    font-weight: 400;
  }

  .ng-select ::ng-deep .ng-select-container {
    border-radius: 6px;
    border: 1px solid #D7E5EA;
  }

  .ng-select ::ng-deep .ng-dropdown-panel.ng-select-bottom {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    text-align: left;
  }

  label {
    color: #8E8E8E;
    font-weight: 400;
  }
  .isFiltered{
    color: #FFA300;
  }
}

.modalOnLoading {
  z-index: 10001;
}


[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
