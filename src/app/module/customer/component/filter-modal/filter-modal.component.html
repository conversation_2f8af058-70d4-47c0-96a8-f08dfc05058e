<div *ngIf="filterFields?.length" class="d-flex justify-content-center h-100">
  <div class="filter-modal d-flex justify-content-center align-items-center ml-1">
    <div [ngClass]="{'isFiltered': filtered}" (click)="openFilterModal()">
      <i class="icon icon-filter"></i>
      <span *ngIf="totalCount !== null && totalCount >= 0">
        {{ totalCount }}
      </span>
    </div>
    <cat-basic-modal [ngClass]="{'modalOnLoading': modalZIndexUpLoading}"
                     *ngIf="isShowFilterModal" [(status)]="isShowFilterModal" (statusChange)="modalStatusChange()">
      <form [formGroup]="filterForm">
        <div class="my-3">
          <ng-container *ngFor="let filter of filterFields">
            <ng-container *ngIf="specialHiddenFieldsList.indexOf(filter.fieldName) < 0">
              <div [ngSwitch]="filter.fieldType">
                <!-- ? String -->
                <ng-container *ngSwitchCase="'String'">
                  <!-- ? String & Select -->
                  <ng-container *ngIf="filter.fieldValues?.length else showTextInput">
                    <ng-select dropdownPosition="bottom" [clearable]="true" [searchable]="true"
                               [formControlName]="filter.fieldName"
                               class="filter-select my-3" [placeholder]="filter.fieldLabel" [id]="filter.fieldName">
                      <ng-option [value]="null">{{ '_all' | translate }}</ng-option>
                      <ng-option *ngFor="let fieldValue of filter.fieldValues" [value]="fieldValue.value">
                        {{ fieldValue.label}}
                      </ng-option>
                    </ng-select>
                  </ng-container>
                  <!-- ? Only String -->
                  <ng-template #showTextInput>
                    <div class="form-group d-flex flex-column align-content-center">
                      <input class="form-control" [formControlName]="filter.fieldName" [placeholder]="filter.fieldLabel"
                             type="text"
                             [name]="filter.fieldName" [id]="filter.fieldLabel">
                      <div *ngIf="filterForm.controls[filter.fieldName]?.hasError('error')" class="small text-danger">
                        {{ filter.fieldValidations[0].description }}
                      </div>
                      <div *ngIf="filterForm.controls[filter.fieldName]?.hasError('min')" class="small text-danger">
                        {{ filter.fieldValidations[0].description }}
                      </div>
                      <div *ngIf="filterForm.controls[filter.fieldName]?.hasError('max')" class="small text-danger">
                        {{ filter.fieldValidations[1].description }}
                      </div>
                    </div>
                  </ng-template>
                </ng-container>
                <!-- ? Select Box -->
                <ng-container *ngSwitchCase="'Select'">
                  <ng-select
                    dropdownPosition="bottom"
                    class="filter-select my-3"
                    [clearable]="true"
                    [searchable]="true"
                    [formControlName]="filter.fieldName"
                    [placeholder]="filter.fieldLabel"
                    [id]="filter.fieldName"
                  >
                    <ng-option [value]="null">{{ '_all' | translate }}</ng-option>
                    <!-- Equipment Revision Indicator Dropdown -->
                    <!-- <ng-container *ngIf="filter.fieldName === 'RevisionIndicator'; else otherFilterSelect"> -->
                    <!-- <ng-option *ngFor="let fieldValue of filter.fieldValues" [value]="fieldValue.value"> -->
                    <!-- {{ -->
                    <!-- fieldValue.label === "EquipmentsSearchFieldValue.RevisionIndicator.orange" -->
                    <!-- ? ('_orange' | translate) -->
                    <!-- : fieldValue.label === "EquipmentsSearchFieldValue.RevisionIndicator.red" -->
                    <!-- ? ('_red' | translate) -->
                    <!-- : ('_other' | translate) -->
                    <!-- }} -->
                    <!-- </ng-option> -->
                    <!-- </ng-container> -->
                    <!-- Other Dropdown -->
                    <!-- <ng-template #otherFilterSelect> -->
                    <ng-option *ngFor="let fieldValue of filter.fieldValues" [value]="fieldValue.value">
                      {{ fieldValue.label }}
                    </ng-option>
                    <!-- </ng-template> -->
                  </ng-select>
                </ng-container>
                <!-- ? Checkbox -->
                <div *ngSwitchCase="'Bool'" class="form-group d-flex align-content-center">
                  <input class="form-control mr-2 is-sound-diag-enabled" type="checkbox"
                         [formControlName]="filter.fieldName"
                         [name]="filter.fieldName" [id]="filter.fieldName">
                  <label [for]="filter.fieldName">
                    <ng-container [ngSwitch]="filter.fieldName">
                      <i *ngSwitchCase="'IsProductLink'" class="icon icon-wifi pr-1"></i>
                      <i *ngSwitchCase="'HasRevisionIndicator'" class="icon icon-repair text-warning pr-1"></i>
                      <i *ngSwitchCase="'HasActiveCamera'" class="icon icon-camera pr-1"></i>
                      <!-- <i *ngSwitchCase="'HasServiceSurvey'" class="icon icon-camera pr-1"></i> -->
                    </ng-container>
                    {{ filter.fieldLabel }}
                  </label>
                </div>
              </div>
            </ng-container>
          </ng-container>

          <!-- Sesli Arıza Tespiti -->
          <!-- <div class="d-flex align-content-center">
          <input [placeholder]="'_search' | translate" type="checkbox" class="form-control mr-2 is-sound-diag-enabled"
            id="isSoundDiagEnabled" />
          <label for="isSoundDiagEnabled">{{ '_sound_diagnostic' | translate }}</label>
        </div> -->
        </div>
        <button (click)="getFilterList()"
                class="modal-btn btn btn-warning btn-gradient btn-block rounded-lg text-white shadow mb-3 mt-0 px-4">
          {{ '_filter' | translate }}
        </button>
        <button (click)="clearFilterButton()" [disabled]="!filtered && !hasClear()"
                class="modal-btn btn btn-secondary btn-gradient btn-block rounded-lg text-white shadow mb-3 mt-0 px-4">
          {{ '_clear' | translate }}
        </button>
      </form>
    </cat-basic-modal>
  </div>
</div>
