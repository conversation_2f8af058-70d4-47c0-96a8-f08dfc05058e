import { CdkAccordionItem } from '@angular/cdk/accordion';
import { Component, Input } from '@angular/core';
import { matExpansionAnimations, MatExpansionPanelState } from '@angular/material/expansion';

@Component({
  selector: 'cat-financial-accordion-item',
  templateUrl: './financial-accordion-item.component.html',
  styleUrls: ['./financial-accordion-item.component.scss'],
  animations: [matExpansionAnimations.bodyExpansion],
})
export class FinancialAccordionItemComponent extends CdkAccordionItem {
  @Input()
  title: string;

  getExpandedState(): MatExpansionPanelState {
    return this.expanded ? 'expanded' : 'collapsed';
  }
}
