<div *ngIf="title" (click)="toggle()" class="d-flex align-items-center justify-content-between">
  <div class="font-size-13px py-2">
    {{title | translate}}
  </div>
  <div class="px-0 font-size-12px">
    <i class="icon" [ngClass]="{'icon-chevron-up': getExpandedState() === 'expanded','icon-chevron-down': getExpandedState() === 'collapsed'}"></i>
  </div>
</div>
<div class="overflow-hidden" [@bodyExpansion]="getExpandedState()">
  <ng-content select=".accordion-content"></ng-content>
</div>

