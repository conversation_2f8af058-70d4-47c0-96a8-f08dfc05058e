import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { EquipmentModel } from '../../model/equipment.model';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { CustomerModel } from '../../model/customer.model';
import { LoginState } from '../../../authentication/state/login/login.state';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { LogService } from '../../service/log.service';
import { LogSectionEnum } from '../../../definition/enum/log-section.enum';
import { v4 as uuid } from 'uuid';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { takeUntil } from 'rxjs/operators';
import { PagingModel } from 'src/app/module/definition/model/paging.model';
import { GetEquipmentNewSearchAction } from '../../action/equipment.action';
import { EquipmentState } from '../../state/equipment.state';

@Component({
  selector: 'cat-equipment-select-modal',
  templateUrl: './equipment-select-modal.component.html',
  styleUrls: ['./equipment-select-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '30%', opacity: 0 })),
      state('*', style({ top: 0, opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: .1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class EquipmentSelectModalComponent implements OnInit, OnDestroy {

  @Output()
  closeModal: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output()
  equipmentListChanged: EventEmitter<EquipmentModel[]> = new EventEmitter<EquipmentModel[]>();

  // tslint:disable-next-line: variable-name
  private _status: boolean;
  // tslint:disable-next-line: no-inferrable-types
  equipmentCreate: boolean = true;
  protected subscriptions$: Subject<boolean> = new Subject();

  @Input()
  set status(val: boolean) {
    document.body.style.overflow = val ? 'hidden' : 'auto';
    this._status = val;
  }

  get status() {
    return this._status;
  }

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentListSearch)
  equipmentListSearch$: Observable<EquipmentModel[]>;

  @Select(EquipmentState.equipmentListPaging)
  equipmentListPaging$: Observable<PagingModel>;

  equipmentListLoading: boolean = false;
  equipmentList: EquipmentModel[];
  equipmentNotFound: boolean = false;
  loadingstr: string = '';

  selectedEquipment: any;
  private customer: CustomerModel;

  private page = 1;
  paging: PagingModel;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly frameService: MessageFrameService,
    private readonly loggerService: LogService,
  ) { }

  ngOnInit(): void {
    this.customer = this.store.selectSnapshot(LoginState.customer);
    this.equipmentListLoading$.pipe(takeUntil(this.subscriptions$)).subscribe(loading => this.equipmentListLoading = loading);
    this.loadEquipments();

    this.equipmentListPaging$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(page => {
        if (page) {
          this.paging = page;
          this.page = this.paging.pageNumber;
        }
      });

    this.equipmentListSearch$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((list) => {
        this.equipmentList = list;
        this.equipmentListChanged.emit(list);
        this.equipmentNotFound = !list?.length;
      });
  }

  onCloseModal() {
    this.closeModal.emit();
  }

  onClickBackdrop() {
    this.onCloseModal();
  }

  send() {
    if (!this.selectedEquipment) {
      return;
    }
    this.soundDiagnostic();
    this.onClickBackdrop();
  }


  loadEquipments() {
    const items = [
      {
        fieldName: 'isSoundDiagEnabled',
        fieldValue: true
      }
    ];
    // Equipment Search
    this.store.dispatch(new GetEquipmentNewSearchAction(this.page, items));
  }

  scrollToEndEvent(event: any) {
    this.nextPage();
  }

  nextPage() {
    // console.log('paging::: ', this.paging);
    if (this?.paging?.pageNumber * this.paging?.pageSize < this.paging?.totalCount) {
      this.page++;
      this.loadEquipments();
    }
  }

  soundDiagnostic() {

    const transactionId = uuid();

    const companyId = this.store.selectSnapshot(LoginState.company)?.id;
    this.loggerService
      .log(LogSectionEnum.SERVICE, 'SOUND_DIAGNOSTIC_BUTTON', {
        companyId,
        customerId: this.customer.id,
        source: 'Service',
        transactionId,
        equipmentNumber: this.selectedEquipment.equipmentNumber,
      })
      .subscribe();

    this.frameService.sendMessage(FrameMessageEnum.voice_record, {
      EquipmentNumber: this.selectedEquipment.equipmentNumber,
      SerialNumber: this.selectedEquipment.serialNumber,
      CustomerNumber: this.customer?.customerNumber,
      Source: 'Service',
      TransactionId: transactionId,
    });
  }

  createEquipment() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'equipment-create',
      ])
      .then();
  }

  ngOnDestroy() {

    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
