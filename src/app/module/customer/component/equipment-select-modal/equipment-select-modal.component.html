<div
  (click)="onClickBackdrop()"
  *ngIf="status"
  [@backdrop]
  class="modal-backdrop"
></div>

<div *ngIf="status" [@modal] class="modal d-block" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-body py-5 create_equipment_popup">
        <i
          (click)="equipmentCreate = false; onCloseModal()"
          class="icon icon-x cursor-pointer"
        ></i>
        <div
          class="d-flex flex-column align-items-center justify-content-center"
        >
          <h4 class="mb-2">{{ "_sound_diagnostic" | translate }}</h4>
          <div *ngIf="equipmentNotFound">
            <!-- <cat-error-box class="w-100" [title]="'_warning' | translate">
              <div class="text-danger">
                {{ "_sound_diag_equipment_empty" | translate }}
              </div>
            </cat-error-box> -->
            <div class="mt-3 text-danger text-center">
              {{ "_sound_diag_equipment_empty" | translate }}
            </div>
            <button catUserClick [section]="'EQUIPMENT'" [subsection]="'EQUIPMENT_ADD_BUTTON_CLICK'"
              (click)="equipmentCreate = true"
              [hidden]="equipmentCreate"
              class="modal-btn btn btn-warning btn-gradient btn-block text-white shadow my-3"
            >
              {{ "_add_equipment" | translate }}
            </button>
            <div class=" mt-3" *ngIf="equipmentCreate">
              <cat-equipment-create-request
                [headerVisible]="false"
              ></cat-equipment-create-request>
            </div>
          </div>

          <div class="col" *ngIf="!equipmentNotFound">
            <p class="mt-3 text-center font-size-14px">
              {{ "_please_select_equipment" | translate }}
            </p>

            <ng-select
              *ngIf="equipmentList"
              class="mb-3 equipment-drp"
              [searchable]="true"
              [placeholder]="'_equipment' | translate"
              [(ngModel)]="selectedEquipment"
              #equipmentSelect
              (close)="equipmentSelect.blur()"
              [dropdownPosition]="'bottom'"
              [loading]="this.equipmentListLoading"
              loadingText="{{'_loading' | translate}}"
              (scrollToEnd)="scrollToEndEvent($event)"
            >
              <ng-option
                *ngFor="let equipment of equipmentListSearch$ | async"
                [value]="equipment"
                >{{ equipment?.model }} /
                {{ equipment?.serialNumber | serialFormat }}</ng-option
              >
            </ng-select>
            <button
              (click)="send()"
              class="modal-btn btn btn-warning btn-gradient btn-block text-white shadow mb-3"
            >
              {{ "_send" | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
