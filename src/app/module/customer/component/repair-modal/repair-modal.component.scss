@import "variable/bootstrap-variable";
:host {
  p {
    font-size: 16px;
    line-height: 1.4;
    text-align: center;
    color: #505050;
  }

  .modal-backdrop {
    opacity: 0.1;
  }

  .modal {
    left: 50%;
    top: 8% !important;
    transform: translate(-50%);
    width: 90vw;
    height: auto;
    overflow: visible;

    .problem-description{
      
      .description{
        overflow: hidden;
        height: 45px;
        display: -webkit-box;
        -webkit-line-clamp: 2; 
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .showRevisionIndicatorDescription{
        height: max-content;
        overflow: unset;
        -webkit-line-clamp: unset;
      }

      .description-content{
        font-size: 15px;
      }
    }

    &-content {
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05),
        0 20px 50px rgba(0, 0, 0, 0.15);
      border-radius: 6px;
      border: none;
      max-height: 80vh;
      overflow-y: scroll;
    }

    &-header {
      padding: 1rem 1rem 0 0;
      font-size: 20px;
      font-weight: 600;
      border: 0;
      min-height: 40px;
    }

    &-body {
      padding: 0.2rem 1rem;
      overflow-x: hidden;
    }

    &-backdrop {
      opacity: 0.2;
      background-color: white;
    }

    &-btn {
      border-radius: 6px;
      min-height: 50px;
    }
    .icon-back {
      position: absolute;
      left: 20px;
      top: 20px;
      font-size: 19px;
      color: #2c2c2c;
    }

    .icon-x {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 19px;
      color: #2c2c2c;
    }
  }
}

.btn-warning {
  background-color: $warning;
  &:focus {
    background-color: $warning;
    border-color: $warning;
  }
}

.service-person {
  margin: 0;
  padding: 0;
  list-style: none;
  &-item {
    border-bottom: 1px solid #dbdbdb;
    padding: 1.25rem;
    &-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #505050;
    }
    &-detail {
      margin: 0;
      padding: 0;
      list-style: none;
      &-item {
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: #505050;
        margin-top: 1rem;
        .icon-area {
          margin-right: 1rem;
          border-radius: 50%;
          background-color: #ffa300;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0.625rem;
        }
      }
    }
    &:last-child {
      border-bottom: none;
    }
  }

}
.custom-card-disount {
  width: 100%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05),
  0 0 15px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  border: 1px solid #dee2e6;
}
.circle-discount-40{
  border: 2px solid #5E9731;
  color: #5E9731;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bolder;
  font-size: 17px;
}

.w-40px{
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
}
.fs-40px{
  font-size: 40px;
}

.edit-icon-white::before{
  color: #fff;
}

.rest-indicator-date{
  border: 1px solid #ffa300;
  border-radius: 6px;

  .rest-indicator-date-text{
    color: #ffa300;
  }
}