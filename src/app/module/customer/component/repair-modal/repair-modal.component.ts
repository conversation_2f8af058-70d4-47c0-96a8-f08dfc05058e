import { animate, state, style, transition, trigger, } from '@angular/animations';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, } from '@angular/core';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { EquipmentModel } from '../../model/equipment.model';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { LogSectionEnum } from '../../../definition/enum/log-section.enum';
import { LoginState } from '../../../authentication/state/login/login.state';
import { LogService } from '../../service/log.service';
import { FormService } from '../../../form/service/form/form.service';
import { Select, Store } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { SettingsState } from '../../../shared/state/settings/settings.state';
import { BorusanBlockedActionsEnum } from '../../../definition/enum/borusan-blocked-actions.enum';
import { AppState } from '../../../../state/app/app.state';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from 'src/app/module/shared/service/modal.service';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'cat-repair-modal',
  templateUrl: './repair-modal.component.html',
  styleUrls: ['./repair-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '20%', opacity: 0 })),
      state('*', style({ top: '14%', opacity: 1 })),
      transition('* => *', [animate('.05s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 0.1 })),
      transition('* => *', [animate('.05s')]),
    ]),
  ],
})
export class RepairModalComponent implements OnInit, OnDestroy {
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Output()
  closeModal: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output()
  requestService: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output()
  connectionModal: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input()
  repairStatus: boolean;

  @Input()
  equipment: EquipmentModel;

  @Input()
  source = 'Equipment';

  @Input()
  sourceRoot = 'Equipment';

  requestServiceDisabled = false;
  requestServiceCreated: boolean;
  showContact: boolean;
  descriptionLink: any;
  showRevisionIndicatorDescription = false
  currentLanguage = this.store.selectSnapshot(LoginState.language)
  featureRevisionCampaignBuffer = false;

  equipmentInspectionIconWhite = environment.assets + '/equipment_inspect_icon.svg';
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  PermissionEnum = PermissionEnum;
  constructor(
    private readonly frameService: MessageFrameService,
    private readonly logger: LogService,
    private readonly formService: FormService,
    private readonly store: Store,
    private readonly router: Router,
    private readonly translateService: TranslateService,
    private readonly modalService: ModalService
  ) {
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  ngOnInit() {
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.featureRevisionCampaignBuffer = systemFeature('revision_campaign_buffer ', features, false);
      }
    });
  }

  ngOnDestroy(): void {
  }

  onCloseModal() {
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);
    this.logger
      .action(LogSectionEnum.SERVICE_ALERT, 'SERVICE_REQUEST_CLOSE', {
        companyId: company.id,
        customerId: customer.id,
        userClicked: true,
        equipmentNumber: this.equipment?.equipmentNumber,
        serialNumber: this.equipment?.serialNumber,
        requestServiceCreated: !!this.requestServiceCreated,
      })
      .subscribe();
    this.closeModal.emit();
    this.showContact = false;
  }

  onClickBackdrop() {
    this.onCloseModal();
  }

  onClickRequestService() {
    if (this.equipment && !this.equipment?.isServiceCreateAvailable && this.featureRevisionCampaignBuffer){
      return this.modalService.errorModal({
        message: '_active_request_message',
        translate: true
      });
    }
    this.requestService.emit();
    this.requestServiceDisabled = true;
    this.createServiceRequest();
  }

  revisionIndicatorDescriptionStatus() {
    this.showRevisionIndicatorDescription = !this.showRevisionIndicatorDescription
  }

  startExpertise() {
    this.logger
      .log(LogSectionEnum.SERVICE_ALERT, 'EXPERTISE_CLICK', {
        userClicked: true,
        equipmentNumber: this.equipment?.equipmentNumber,
        requestServiceCreated: !!this.requestServiceCreated,
      })
      .subscribe();
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'expertise',
      'init',
      this.equipment.equipmentNumber
    ], {
      queryParams: { backButton: 1 }
    }).then();
  }

  toggleContact() {
    this.showContact = !this.showContact;
  }

  callNumber(phone: string) {
    this.frameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  createServiceRequest() {
    if (this.store.selectSnapshot(AppState.connectionModalStatus)) {
      this.connectionModal.emit(true);
      this.onCloseModal();
      return;
    }
    const user = this.store.selectSnapshot(LoginState.user);
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);
    this.logger
      .action(LogSectionEnum.SERVICE_ALERT, 'SERVICE_REQUEST_CREATED', {
        userClicked: true,
        serialNumber: this.equipment?.serialNumber,
        equipmentNumber: this.equipment?.equipmentNumber,
        source: this.source,
        sourceRoot: this.sourceRoot
      })
      .subscribe();

    const data = {
      Name: user.firstName,
      Surname: user.lastName,
      Email: user.email.toLowerCase(),
      CompanyPhone: user.mobile,
      CompanyName: customer.name,
      CompanyId: company?.id,

      EquipmentSerialNumber: this.equipment.serialNumber,
      ServiceCategory: 'Maintenance', // ServiceCategoryEnum.Maintenance,
      IsEquipmentWorking: true,
      CountryCode: company?.countryCode, // this.equipment.CountryCode,
      EquipmentCity: this.equipment?.city,
      EquipmentTown: this.equipment?.region,
      // EquipmentDistrict: this.equipment?.district,
      EquipmentRevisionCampaignTitle: this.equipment?.equipmentRevisionCampaign?.title,
      EquipmentRevisionCampaignDescription: this.equipment?.equipmentRevisionCampaign?.description,
      EquipmentRevisionCampaignAmount: this.equipment?.equipmentRevisionCampaign?.amount,
      EquipmentRevisionCampaignCurrency: this.equipment?.equipmentRevisionCampaign?.currency,
      EquipmentRevisionCampaignType: this.equipment?.equipmentRevisionCampaign?.type,
      EquipmentRevisionCampaignId: this.equipment?.equipmentRevisionCampaign?.id,
      RevisionIndicator: this.equipment?.revisionIndicator,
      // TODO translate
      Description:
        'BOOM uygulamasından gelen servis uyarısı sebebiyle oluşturulmuştur.',
      EquipmentType: 'Machine',
      AttachmentIdList: [],
      Source: this.source,
      SourceRoot: this.sourceRoot
    } as any;
    // if (value.EquipmentType) {
    //   data.EquipmentType = value.EquipmentType;
    // }
    this.formService.sendRequestService(data).subscribe(
      (x) => {
        if (x) {
          this.requestServiceCreated = true;
          this.requestServiceDisabled = true;
        }
      },
      () => {
        this.requestServiceCreated = false;
        this.requestServiceDisabled = false;
      }
    );
  }

  showCallButton(equipment: EquipmentModel) {
    return equipment?.pssrList?.find(i => i.telephoneList.length);
  }

  getDescription(desc) {
    if (desc.split('<u>').length > 0 && desc.search('<u>') !== -1) {
      const descriptionUrl = this.getTagText(desc, '<u>');
      this.descriptionLink = {
        text: this.getTagText(descriptionUrl, '<txt>'),
        url: this.getTagText(descriptionUrl, '<a>'),
        open: this.getTagText(descriptionUrl, '<oe>') === 'true',
        title: this.getTagText(descriptionUrl, '<t>')
      };
    }
    // else{
    //   return desc.replace(/<[^>]*>?/gm, ' ');
    // }
    return desc.split('<u>')[0];
  }

  getTagText(text, tag) {
    if (text.indexOf(tag) !== -1) {
      return text.substring(text.indexOf(tag) + tag.length, text.indexOf(tag.replace('<', '</')));
    }
    return null;
  }

  descriptionGoLink() {
    const url = this.descriptionLink?.url;
    if (!url) {
      return;
    }
    if (this.descriptionLink.open) {
      this.frameService.sendMessage(FrameMessageEnum.openStore, {
        url
      });
    } else {
      const title = this.descriptionLink.title;
      this.frameService.sendMessage(FrameMessageEnum.openModule, {
        url, title
      });
    }
  }

  getRestIndicatorCampaignText(text){
    return text.replace('X', this.equipment?.equipmentRevisionCampaign?.restOfRevisionIndicatorDate)
  }
}
