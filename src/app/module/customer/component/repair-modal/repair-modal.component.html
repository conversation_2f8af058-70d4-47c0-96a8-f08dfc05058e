<div (click)="onClickBackdrop()" *ngIf="repairStatus" [@backdrop] class="modal-backdrop"></div>

<div *ngIf="repairStatus" [@modal] class="modal d-block" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <!-- <i
          (click)="toggleContact()"
          *ngIf="showContact"
          class="icon icon-back cursor-pointer"
        ></i> -->
        <i (click)="onCloseModal()" class="icon icon-x cursor-pointer"></i>
      </div>
      <div class="modal-body">
        <div class="d-flex flex-column align-items-center justify-content-center pb-3" *ngIf="!showContact; else contactContent">
          <h4 *ngIf="equipment?.revisionIndicator" class="mb-2 px-4">{{ "_service_warning" | translate }}</h4>
          <p *ngIf="equipment?.revisionIndicator" class="mb-4 px-4 font-size-14px">
            {{ "_service_warning_description" | translate }}
          </p>
          <div *ngIf="equipment?.revisionIndicatorDescriptions" class="mb-2 problem-description">
            <div
              *ngIf="equipment?.revisionIndicatorDescriptions[currentLanguage] || equipment?.revisionIndicatorDescriptions['en']"
              class="d-flex align-items-center flex-column"
            >
              <div  class="mb-0 description" [ngClass]="{'showRevisionIndicatorDescription': showRevisionIndicatorDescription}">
                  <p #content class="mb-0 description-content">
                    {{ equipment?.revisionIndicatorDescriptions[currentLanguage] || equipment?.revisionIndicatorDescriptions['en'] }}
                  </p>
              </div>
              <a *ngIf="content.scrollHeight > 45" class="show-more-btn font-size-14px" (click)="revisionIndicatorDescriptionStatus()">
                {{ showRevisionIndicatorDescription ? ('_show_less' | translate) : ('_show_more' | translate)}}
                <i class="icon icon-chevron-down font-size-12px" [ngClass]="{'icon-chevron-up': showRevisionIndicatorDescription}"></i>
              </a>
            </div>
            <div *ngIf="equipment?.equipmentRevisionCampaign?.restOfRevisionIndicatorDate" class="rest-indicator-date py-2 px-3">
              <p 
                class="rest-indicator-date-text mb-0" 
                [innerHTML]="getRestIndicatorCampaignText('_rest_of_revision_indicator_date' | translate)"
              ></p>
            </div>
          </div>
          <cat-equipment-revision-campaign [equipmentDetail]="equipment"></cat-equipment-revision-campaign>
          <button [hasPermission]="PermissionEnum.RequestsService" (click)="onClickRequestService()"
            class="modal-btn btn btn-gradient btn-block text-white shadow mb-3 px-4"
            [ngClass]="requestServiceCreated ? 'btn-success' : 'btn-warning'"
            [disabled]="this.requestServiceDisabled || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitServiceRequestForm) >= 0)"
          >
            <i class="icon icon-edit edit-icon-white mr-1"></i>
            {{
            (requestServiceCreated
            ? "_request_service_campaign_created"
            : "_request_service_campaign_create"
            ) | translate
            }}
          </button>
          <!-- <button (click)="startExpertise()" class="modal-btn btn btn-warning btn-gradient btn-block text-white shadow mb-3 mt-0 px-4">
            <img [src]="equipmentInspectionIconWhite" height="24" width="24" alt="">
            {{ '_start_expertise' | translate }}
          </button> -->

          <button catUserClick [section]="'SERVICE_ALERT'" [subsection]="'CALL_REPRESENTATIVE_CLICK'"
            [data]="{ equipmentNumber: equipment?.equipmentNumber, requestServiceCreated: requestServiceCreated }"
            (click)="toggleContact()" class="modal-btn btn btn-warning btn-gradient btn-block text-white shadow mt-0 m-0 p-1 px-4"
            *ngIf="showCallButton(equipment)">
            <div>
              <i class="icon icon-phone-call"></i>
              {{ "_call_representative" | translate }}
            </div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #contactContent>
  <div class="d-flex justify-content-center">
    <h4 class="mb-2">{{ "_call_representative" | translate }}</h4>
  </div>
  <ul class="service-person">
    <ng-container *ngFor="let persons of equipment.pssrList">
      <li class="service-person-item" *ngIf="persons.telephoneList?.length">
        <div class="service-person-item-name">
          {{ persons.pssrName }}
        </div>

        <ul class="service-person-item-detail">
          <li class="service-person-item-detail-item" catUserClick [section]="'SERVICE_ALERT'" [subsection]="'CALL_REPRESENTATIVE'" [data]="{
            phone: phones.telephoneNumber, equipmentNumber: equipment?.equipmentNumber,  requestServiceCreated: requestServiceCreated
          }" *ngFor="let phones of persons.telephoneList" (click)="callNumber(phones.telephoneNumber)">
            <div class="icon-area">
              <i class="icon icon-phone-call"></i>
            </div>
            {{ phones.telephoneNumberShort }}
          </li>
        </ul>
      </li>
    </ng-container>
  </ul>
</ng-template>
