@keyframes pull-in1 {
  from {
    top: -50px;
    //background-color: #ffffff;
  }
  to {
    top: 20px;
    //background-color: #ffa300;
  }
}
@keyframes pull-in2 {
  from {
    top: -50px;
    background-color: #ffffff;
  }
  to {
    top: 55px;
    background-color: #ffa300;
  }
}
@keyframes pull-in3 {
  from {
    top: -50px;
    background-color: #ffffff;
  }
  to {
    top: 75px;
    background-color: #ffa300;
  }
}
@keyframes pull-in4 {
  from {
    top: -50px;
    border-color: #ffffff transparent transparent transparent;
  }
  to {
    top: 95px;
    border-color: #ffa300 transparent transparent transparent;
  }
}
.pullToRefreshDiv {
  >div {
    z-index: 10000;
    left: 0;
    right: 0;
    top: -200px;
    height: 12px;
    width: 20px;
    position: absolute;
    margin-left: auto;
    margin-right: auto;
    border-radius: 2px;
    animation-duration: 2s;
    animation-direction: alternate;
    animation-iteration-count: 2;
    animation-timing-function: linear;
  }
  .in1 {
    animation-name: pull-in1;
    height: 25px;
    width: 25px;
    transition: transform .3s;
    will-change: transform, opacity;
    background-color: #fff;
    border: 1px solid rgba(53, 53, 53, .3);
    border-radius: 100px;

    svg {
      height: 25px;
    }
  }
  .in2 {
    animation-name: pull-in2;
  }
  .in3 {
    animation-name: pull-in3;
  }

  .in4 {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 20px 20px 0 20px;
    border-color: #ffa300 transparent transparent transparent;
    animation-name: pull-in4;
  }

  .pull .spinner.spin-animation {
    -webkit-animation-name: spin;
    animation-name: spin;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
  }
}
