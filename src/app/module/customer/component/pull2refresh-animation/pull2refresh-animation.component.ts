import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'cat-pull2refresh-animation',
  template: `
    <div class="pullToRefreshDiv">
      <div class="in1 d-flex justify-content-center align-items-center">
        <div class="d-flex justify-content-center align-items-center spinner">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"
               preserveAspectRatio="xMidYMid">
            <g>
              <path d="M50 15A35 35 0 1 0 74.74873734152916 25.251262658470843" fill="none" stroke-width="10"
                    stroke="#ffa300"></path>
              <path d="M49 3L49 27L61 15L49 3" stroke-width="4" fill="#ffa300" stroke="#ffa300"></path>
            </g>
          </svg>
        </div>
      </div>
      <!--      <div class="in1"></div>-->
      <div class="in2"></div>
      <div class="in3"></div>
      <div class="in4"></div>
    </div>
  `,
  styleUrls: ['./pull2refresh-animation.component.scss']
})
export class Pull2refreshAnimationComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
  }

}
