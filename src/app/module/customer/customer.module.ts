import { CommonModule } from '@angular/common';
import { LOCALE_ID, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { MediaModule } from '../../export/media/media.module';
import { MouseEventModule } from '../../export/mouse-event/mouse-event.module';
import { SearchModule } from '../../export/search/search.module';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';
import { AddressItemComponent } from './component/address-item/address-item.component';
import { FinancialAccordionItemComponent } from './component/financial-accordion-item/financial-accordion-item.component';
import { AddressListComponent } from './container/address-list/address-list.component';
import { CustomerLayoutComponent } from './container/customer-layout/customer-layout.component';
import {
  FinancialInformationComponent
} from './container/financial-information/financial-information-list/financial-information.component';
import { OfferListComponent } from './container/offer/offer-list/offer-list.component';
import { CameraListComponent } from './container/service-list/camera-list/camera-list.component';
import { ServiceListComponent } from './container/service-list/service-list.component';
import { WorkOrderListComponent } from './container/work-order-list/work-order-list.component';
import { CustomerRoutingModule } from './customer-routing.module';
import { RepairModalComponent } from './component/repair-modal/repair-modal.component';
import { EquipmentSelectModalComponent } from './component/equipment-select-modal/equipment-select-modal.component';
import { SharedModule } from '../shared/shared.module';
import { BoomGuruModule } from '../boom-guru/boom-guru.module';
import { GoogleMapsModule } from '@angular/google-maps';
import { HttpClientJsonpModule, HttpClientModule } from '@angular/common/http';
import { NgbAccordionModule, NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { EquipmentCreateRequestComponent } from './container/equipment/equipment-create-request/equipment-create-request.component';
import { Store } from '@ngxs/store';
import { LoginState } from '../authentication/state/login/login.state';
import { EquipmentDetailComponent } from './container/equipment/equipment-detail/equipment-detail.component';
import { EquipmentListComponent } from './container/equipment/equipment-list/equipment-list.component';
import { EquipmentMapComponent } from './container/equipment/equipment-map/equipment-map.component';
import { EquipmentUpdateComponent } from './container/equipment/equipment-update/equipment-update.component';
import { OfferPdfComponent } from './container/offer/offer-pdf/offer-pdf.component';
import { ServiceMapComponent } from './container/service-map/service-map.component';
import { OfferCreateOrderComponent } from './container/offer/offer-create-order/offer-create-order.component';
import { MatStepperModule } from '@angular/material/stepper';
import { LoyalityComponent } from './component/loyality/loyality.component';
import { MachineSerial } from '../form/component/machine-serial.module';
import { FilterModalComponent } from './component/filter-modal/filter-modal.component';
import { ReconciliationComponent } from './container/financial-information/reconciliation/reconciliation.component';
import { FinancialExtractComponent } from './container/financial-information/financial-extract/financial-extract.component';
import { MatTabsModule } from '@angular/material/tabs';
import { FormModule } from '../form/form.module';
import { Pull2refreshAnimationComponent } from './component/pull2refresh-animation/pull2refresh-animation.component';
import { SpecialCreateOrderComponent } from './container/offer/special-create-order/special-create-order.component';
import { EnumToArrayModule } from 'src/app/export/enum-to-array/enum-to-array.module';
import { ReconciliationDetailComponent } from './container/financial-information/reconciliation-detail/reconciliation-detail.component';
import { ReconciliationReplyComponent } from './container/financial-information/reconciliation-reply/reconciliation-reply.component';
import { FuelLevelComponent } from './container/equipment/fuel-level/fuel-level.component';
import { DiagnosticDataComponent } from './container/equipment/diagnostic-data/diagnostic-data.component';
import { AuctionComponent } from './container/auction/auction/auction.component';
import { AuctionSelectionComponent } from './container/auction/auction-selection/auction-selection.component';
import { RateUsComponent } from './container/auction/rate-us/rate-us.component';
import { CompanyInformationsComponent } from './container/company-informations/company-informations.component';
import { InspectionHistoryComponent } from './container/equipment/inspection-history/inspection-history.component';
import { LuckyDaysComponent } from './container/equipment/lucky-days/lucky-days.component';
import { MyEquipmentAgendaNotesComponent } from './container/equipment/my-equipment-agenda-notes/my-equipment-agenda-notes.component';
import {
  MyEquipmentAgendaCheckInComponent
} from './container/equipment/my-equipment-agenda-check-in/my-equipment-agenda-check-in.component';
import {
  MyEquipmentAgendaCheckOutComponent
} from './container/equipment/my-equipment-agenda-check-out/my-equipment-agenda-check-out.component';
import { ServiceDetailComponent } from './container/service-detail/service-detail.component';
import { ServiceChatComponent } from './container/service-chat/service-chat.component';
import {
  ServiceDetailInformationComponent
} from './container/service-list/service-detail-information/service-detail-information.component';
import { QrDetailComponent } from './container/service-list/qr-detail/qr-detail.component';
import {
  MyEquipmentAgendaCalendarComponent
} from './container/equipment/my-equipment-agenda-calendar/my-equipment-agenda-calendar.component';
import { CheckedInEquipmentsComponent } from './container/equipment/checked-in-equipments/checked-in-equipments.component';
import { HasPermissionsModule } from 'src/app/export/permissions/has-permissions.module';
import { ServiceDetailResolver } from './resolver/service-detail.resolver';
import { CostDetailComponent } from './container/equipment/cost-detail/cost-detail.component';
import { CmPseComponent } from './container/equipment/cm-pse/cm-pse.component';
import { MyOngoingOffersComponent } from './container/offer/my-ongoing-offers/my-ongoing-offers.component';
import { OfferListCardComponent } from './container/offer/my-ongoing-offers/components/offer-list-card/offer-list-card.component';

@NgModule({
  declarations: [
    OfferListComponent,
    EquipmentListComponent,
    CustomerLayoutComponent,
    EquipmentDetailComponent,
    AddressListComponent,
    WorkOrderListComponent,
    AddressItemComponent,
    FinancialInformationComponent,
    ServiceListComponent,
    FinancialAccordionItemComponent,
    CameraListComponent,
    RepairModalComponent,
    OfferPdfComponent,
    EquipmentSelectModalComponent,
    EquipmentMapComponent,
    EquipmentCreateRequestComponent,
    EquipmentUpdateComponent,
    ServiceMapComponent,
    OfferCreateOrderComponent,
    LoyalityComponent,
    FilterModalComponent,
    Pull2refreshAnimationComponent,
    ReconciliationComponent,
    FinancialExtractComponent,
    SpecialCreateOrderComponent,
    ReconciliationDetailComponent,
    ReconciliationReplyComponent,
    FuelLevelComponent,
    DiagnosticDataComponent,
    AuctionComponent,
    AuctionSelectionComponent,
    RateUsComponent,
    CompanyInformationsComponent,
    InspectionHistoryComponent,
    LuckyDaysComponent,
    CmPseComponent,
    MyEquipmentAgendaNotesComponent,
    MyEquipmentAgendaCheckInComponent,
    MyEquipmentAgendaCheckOutComponent,
    ServiceDetailComponent,
    ServiceChatComponent,
    ServiceDetailInformationComponent,
    QrDetailComponent,
    MyEquipmentAgendaCalendarComponent,
    CheckedInEquipmentsComponent,
    CostDetailComponent,
    MyOngoingOffersComponent,
    OfferListCardComponent,
  ],
  imports: [
    CommonModule,
    CustomerRoutingModule,
    TranslateModule,
    NgxLoadingModule,
    MediaModule,
    FormsModule,
    SearchModule,
    MouseEventModule,
    UserEventLogModule,
    NgSelectModule,
    SharedModule,
    BoomGuruModule,
    GoogleMapsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    ReactiveFormsModule,
    NgbTooltipModule,
    NgbDropdownModule,
    MatStepperModule,
    NgbAccordionModule,
    MachineSerial,
    MatTabsModule,
    FormModule,
    EnumToArrayModule,
    HasPermissionsModule
  ],
  exports: [
    Pull2refreshAnimationComponent
  ],
  providers: [
    {
      provide: LOCALE_ID,
      deps: [Store],
      useFactory: (store: Store) => {
        console.log('locale lang', store.selectSnapshot(LoginState.language));
        switch (store.selectSnapshot(LoginState.language)) {
          case 'tr':
            return 'tr-TR';
          case 'en':
            return 'en-US';
        }
        return 'tr-TR';
      }
    },
    ServiceDetailResolver
  ]
})
export class CustomerModule {
  constructor() {
    stopLoadingAnimation();
  }
}
