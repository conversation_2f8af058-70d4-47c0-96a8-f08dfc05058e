import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';

@Injectable({
  providedIn: 'root',
})
export class PseService {

  constructor(
    private readonly http: HttpClient,
  ) {
  }

  cmPseSubmit(body: any): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/cmpse/submit`, body);
  }

}
