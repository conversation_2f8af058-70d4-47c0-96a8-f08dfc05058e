import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { CreateOrderModel, OfferDetailModel, OfferModel, PaymentStatusModel } from '../model/offer.model';

@Injectable({
  providedIn: 'root'
})
export class OfferService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  offerList(customerNumber: string, refresh?: any): Observable<OfferModel[]> {
    const params = { customerNumber };
    if (refresh) {
      // tslint:disable-next-line: no-string-literal
      params['refresh'] = refresh;
    }
    return this.http.get<HttpResponse<OfferModel[]>>(`${environment.api}/quotation/listV2`, {
      params
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return [];
      }),
    );
  }

  offerDetail(quotationNumber: string, guid?: string): Observable<OfferDetailModel> {
    return this.http.post<HttpResponse<OfferDetailModel>>(`${environment.api}/quotation/detail`, { quotationNumber, guid }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  approveQuotation(quotationNumber: string): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/quotation/approve`, { quotationNumber })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  rejectQuotation(quotationNumber: string): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/quotation/reject`, { quotationNumber })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  revisionQuotation(quotationNumber: string, revisionReason: string): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/quotation/revision`,
      { quotationNumber, revisionReason }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  createOrderQuotation(quotationNumber: string, paymentMethod: number, approveAgreements: boolean): Observable<CreateOrderModel> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/quotation/createorder`,
      { quotationNumber, paymentMethod, approveAgreements }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  paymentStatus(traceId: string): Observable<PaymentStatusModel> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/payment/status`,
      { traceId }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  availableQuotationTypes(): Observable<any> {
    return this.http.get<HttpResponse<any>>(`${environment.api}/settings/get?key=quotation_available_process_types`).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }
}
