import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ServiceDetailedRequestModel, ServiceDetailedRequestWorkOrderStatus } from '../model/service-request.model';
import { WorkOrderModel } from '../model/work-order.model';
import { ServiceLocationModel } from '../model/locations.model';
import { FilterFieldsModel, FilterSearchModel } from '../model/filter.model';

@Injectable({
  providedIn: 'root'
})
export class ServiceService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  workOrderFilterFields(customerId?: string, language?: string): Observable<FilterFieldsModel[]> {
    return this.http.post<HttpResponse<FilterFieldsModel[]>>(`${environment.api}/workorder/search/fields`, { customerId, language }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  serviceListDetailed(body: FilterSearchModel ): Observable<HttpResponse<ServiceDetailedRequestModel[]>> {
    return this.http.post<HttpResponse<ServiceDetailedRequestModel[]>>(`${environment.api}/workorder/search`, {
      ...body,
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val;
        }
        return null;
      }),
    );
  }

  serviceDetail(params: { workorderNumber: string, customerNumber: string }): Observable<ServiceDetailedRequestModel> {
    return this.http.post<HttpResponse<ServiceDetailedRequestModel>>(`${environment.api}/workorder/detail`, params).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  requestWorkOrder(request: any): Observable<WorkOrderModel> {
    return this.http.post<HttpResponse<WorkOrderModel>>(`${environment.api}/workorder/status`, { request }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  requestServiceLocation(plateNumber: string): Observable<ServiceLocationModel> {
    return this.http.post<HttpResponse<ServiceLocationModel>>(`${environment.api}/plate/getstatus`, { plateNumber })
    .pipe(
      map(val => {
        if (val.code === 0) {
          // const test = val.data;
          // test.latitude = (parseFloat(test.latitude) + parseFloat((Math.random() * (0.00001 - 0.00200) + 0.00200).toFixed(6))).toString();
          // test.longitude = (parseFloat(test.longitude) + parseFloat((Math.random() * (0.00001 - 0.00200) + 0.00200).toFixed(6))).toString();
          // console.log('Test Location: ', test);
          // return test;
          return val.data;
        }
        return null;
      })
    );
  }

  createNewWorkOrderChatMessage(body): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/workorderchat/sendMessage`, body)
  }

  workOrderChatMessages(body): Observable<any>{
    return this.http.post<HttpResponse<any>>(`${environment.api}/workorderchat/getmessages`, body
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  workOrderFromQr(workorderNumber: string): Observable<ServiceDetailedRequestWorkOrderStatus> {
    return this.http.post<HttpResponse<ServiceDetailedRequestWorkOrderStatus>>(`${environment.api}/workorder/crc/status` , { workorderNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

}
