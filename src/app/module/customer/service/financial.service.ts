import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import {
  FinancialModel,
  MutabakatDetailModal,
  MutabakatDetailRequestModel,
  MutabakatlarListModel,
  MutabakatListModel, MutabakatReplyReasonListModel, MutabakatReplyRequestModel
} from '../model/financial.model';
import { MutabakatReplyActionEnum } from '../../definition/enum/mutabakat-type.enum';

@Injectable({
  providedIn: 'root',
})
export class FinancialService {

  constructor(
    private readonly http: HttpClient,
  ) {
  }

  financialInformation(customerNumber: string): Observable<FinancialModel> {
    return this.http.post<HttpResponse<FinancialModel>>(`${environment.api}/financials/detailV2`, { customerNumber })
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  mutabakatList(numberOfItems: number = 5): Observable<MutabakatListModel[]> {
    return this.http.post<HttpResponse<MutabakatlarListModel>>(`${environment.api}/reconciliation/list`, {
      numberOfItems
    }).pipe(
      map(val => {
        if (val.code === 0 && val?.data) {
          return val?.data?.items;
        }
        return null;
      }),
    );
  }

  mutabakatDetail(mutabakatDetailRequest: MutabakatDetailRequestModel): Observable<MutabakatDetailModal> {
    return this.http.post<HttpResponse<MutabakatDetailModal>>(`${environment.api}/reconciliation/detail`, {
      formNumber: mutabakatDetailRequest.formNumber,
      guuid: mutabakatDetailRequest.guuid,
      partnerNumber: mutabakatDetailRequest.partnerNumber,
      sequenceNumber: mutabakatDetailRequest.sequenceNumber
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  mutabakatReply(mutabakatReplyRequest: MutabakatReplyRequestModel): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/reconciliation/reply`, {
      formNumber: mutabakatReplyRequest.formNumber,
      guuid: mutabakatReplyRequest.guuid,
      sequenceNumber: mutabakatReplyRequest.sequenceNumber,
      action: mutabakatReplyRequest.action,
      reason: mutabakatReplyRequest?.reason,
      partnerNumber: mutabakatReplyRequest.partnerNumber,
      description: mutabakatReplyRequest?.description
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val?.data;
        }
        return null;
      }),
    );
  }

  mutabakatReplyReasonList(action: MutabakatReplyActionEnum): Observable<MutabakatReplyReasonListModel[]> {
    return this.http.post<HttpResponse<MutabakatReplyReasonListModel[]>>(`${environment.api}/reconciliation/reply/reason/list`, {
      action
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  reconciliationStatementSend(body: { startDate: string, endDate: string }): Observable<boolean> {
    return this.http.post<HttpResponse<boolean>>(`${environment.api}/reconciliation/statement/send`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data; // True False
        }
        return null;
      }),
    );
  }

}
