import { HttpClient, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: "root"
})
export class LoyalityService {
    constructor(
        private readonly httpClient: HttpClient
    ){}

    getLoyalityJwtToken(){
        return this.httpClient.get<any>(`${environment.api}/loyality/get/jwttoken`);
    }
}