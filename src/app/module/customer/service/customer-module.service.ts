import { Injectable } from '@angular/core';
import { FrameMessageEnum } from '../../shared/enum/frame-message.enum';
import { environment } from '../../../../environments/environment';
import { LoginState } from '../../authentication/state/login/login.state';
import { CustomerService } from './customer.service';
import { MessageFrameService } from '../../shared/service/message-frame.service';
import { Store } from '@ngxs/store';

@Injectable({
  providedIn: 'root',
})
export class CustomerModuleService {

  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly messageFrameService: MessageFrameService,
  ) { }

  openDigitalBanko(isLogin: boolean) {
    return isLogin
      ? this.customerService.getOTT().subscribe(ott => {
      if (ott){
        this.messageFrameService.sendMessage(FrameMessageEnum.openAssistBox, {
          url: window.location.origin + '/' + environment.rootUrl + '/live-support/public-videocall?ott=' + ott.oneTimeToken
            + '&language=' + this.store.selectSnapshot(LoginState.language) || 'en'
        });
      }})
      : this.messageFrameService.sendMessage(FrameMessageEnum.openAssistBox, {
        url: window.location.origin + '/' + environment.rootUrl + '/live-support/public-videocall?'
          + 'language=' + this.store.selectSnapshot(LoginState.language) || 'en'
      });
  }

}
