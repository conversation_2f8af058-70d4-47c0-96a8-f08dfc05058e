import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/response/http.response';
import { environment } from 'src/environments/environment';
import { EquipmentModel, EquipmentSosAnalyzesModel, EquipmentInspectionModel, EquipmentRevisionCampaign, CostCategoryListModel, AddCostFormBodyModel, CostListModel, EquipmentServiceHistoryModel, EquipmentQuotationModel } from '../model/equipment.model';
import { FilterFieldsModel, FilterSearchModel } from '../model/filter.model';

@Injectable({
  providedIn: 'root'
})
export class EquipmentService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  equipmentListFilterFields(customerId?: string, language?: string): Observable<FilterFieldsModel[]> {
    return this.http.post<HttpResponse<FilterFieldsModel[]>>(`${environment.api}/equipment/equipments/search/fields`, { customerId, language }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentListSearch(params: { customerNumber: string, page?: any, top?: any, searchParameter?: string }): Observable<HttpResponse<EquipmentModel[]>> {
    return this.http.post<HttpResponse<EquipmentModel[]>>(`${environment.api}/equipment/equipments`, {
      params,
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val;
        }
        return null;
      }),
    );
  }

  equipmentNewListSearch(params: FilterSearchModel): Observable<HttpResponse<EquipmentModel[]>> {
    return this.http.post<HttpResponse<EquipmentModel[]>>(`${environment.api}/equipment/equipments/search`, params).pipe(
      map(val => {
        if (val.code === 0) {
          return val;
        }
        return null;
      }),
    );
  }

  equipmentLocationList(): Observable<any>{
    return this.http.get<HttpResponse<any>>(`${environment.api}/equipment/location`).pipe(
      map(val => {
        if(val.code === 0){
          return val
        }
        return null
      })
    )
  }

  equipmentDetail(equipmentNumber: string): Observable<EquipmentModel> {
    return this.http.post<HttpResponse<EquipmentModel>>(`${environment.api}/equipment/detail`,
      { equipmentNumber },
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentSosAnalyzes(serialNumber: string): Observable<EquipmentSosAnalyzesModel[]> {
    return this.http.post<HttpResponse<EquipmentSosAnalyzesModel[]>>(`${environment.api}/equipment/SosAnalyzes`, { serialNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return [];
      }),
    );
  }

  equipmentInspections(serialNumber: string) {
    return this.http.post<HttpResponse<EquipmentInspectionModel[]>>(`${environment.api}/equipment/equipmentInspections`, { serialNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentInspectionPdf(dcode: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/equipmentInspectionPdf`, { dcode }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentInspectionContent(dcode: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/equipmentInspectionContent`, { dcode }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentMDAPmaPlanner(serialNumber: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/workorder/mda/list`, {
      serialNumber,
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentRevisionCampaign(id: string): Observable<EquipmentRevisionCampaign> {
    return this.http.get<HttpResponse<EquipmentRevisionCampaign>>(`${environment.api}/equipment/equipmentrevisioncampaign`, {
      params: { id },
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentFuel(equipmentNumber: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/fuelinfo`, { equipmentNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  diagData(equipmentNumber: string, serialNumber: string, top?: any, page?: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/getdiagnosticdata`, { equipmentNumber, serialNumber, top, page }).pipe(
      map(val => {
        if (val.code === 0) {
          return val;
        }
        return null;
      }),
    );
  }
  inspectionHistory(serialNumber: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/inspectionForm/equipment/history/list`, {
       serialNumber,
    })
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
  equipmentHasPSE(requestid: string) {
    return this.http.get<HttpResponse<any>>(`${environment.api}/equipment/getcampaignsbyrequest`, {
      params: { requestid },
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentAddPSEToCart(body) {
    return this.http.post<HttpResponse<any[]>>(`${environment.api}/equipment/addtocart`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentAddUsageDetail(body){
    return this.http.post<HttpResponse<any[]>>(`${environment.api}/api`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentAdd(body){
    return this.http.post<HttpResponse<any[]>>(`${environment.api}/api`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentAgendaHistory(body){
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/gethistory`, body).pipe(
      map(val => {
        if(val.code === 0) {
          return val.data
        }
        return null
      })
    )
  }

  equipmentAgendaDetails(body){
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/getdetails`, body).pipe(
      map(val => {
        if(val.code === 0) {
          return val.data
        }
        return null
      })
    )
  }

  equipmentAgendaCreateNote(form: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/createnote`, form)
  }

  equipmentAgendaCreateCheckIn(form: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/checkin`, form)
  }

  equipmentAgendaCreateCheckOut(form: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/checkout`, form)
  }

  equipmentAgendaSetNewAlias(form: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipmentagenda/setalias`, form)
  }

  checkedInEquipments(){
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/checkedinequipments/search`, {}).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentSearchPSE(body) {
    return this.http.post<HttpResponse<any[]>>(`${environment.api}/equipment/searchproducts`, body).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  equipmentWorkOrderPlan(serialNumber: string) {
    return this.http.post<HttpResponse<any[]>>(`${environment.api}/equipment/dailywo`, { serialNumber })
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  getCostCategoryList(): Observable<CostCategoryListModel[]> {
    return this.http.get<HttpResponse<CostCategoryListModel[]>>(`${environment.api}/equipment/cost/category/list`)
    .pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  addEquipmentAverageCost(formBody: AddCostFormBodyModel): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/equipment/cost/add`, formBody);
  }

  getEquipmentCostList(formBody): Observable<CostListModel[]> {
    return this.http.post<HttpResponse<CostListModel[]>>(`${environment.api}/equipment/cost/list`, formBody).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  deleteEquipmentAverageCost(id: string): Observable<any> {
    return this.http.get<HttpResponse<any>>(`${environment.api}/equipment/cost/delete?id=${id}`);
  }

  getEquipmentServiceHistory(equipmentNumber: string){
    return this.http.post<HttpResponse<EquipmentServiceHistoryModel[]>>(`${environment.api}/equipment/detail/workorders`, { equipmentNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  getEquipmentQuotation(serialNumber: string){
    return this.http.post<HttpResponse<EquipmentQuotationModel[]>>(`${environment.api}/equipment/detail/quotations`, { serialNumber }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }
}
