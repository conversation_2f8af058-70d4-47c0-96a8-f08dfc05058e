import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpResponse } from '../../../response/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { LoggerModel } from '../../shared/model/logger.model';
import { MessageFrameService } from '../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../shared/enum/frame-message.enum';
import { Store } from '@ngxs/store';
import { LoginState } from '../../authentication/state/login/login.state';
import { CommonState } from '../../shared/state/common/common.state';

@Injectable({
  providedIn: 'root',
})
export class LogService {
  constructor(
    private readonly http: HttpClient,
    private readonly frameService: MessageFrameService,
    private readonly store: Store,

  ) {}
  uuid: string;
  public action(section: string, subsection: string, data: any = {}): Observable<LoggerModel> {
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);
    if (customer) {
      data.customerId = customer.id;
    }
    if (company) {
      data.companyId = company.id;
    }

    const trackerUser = this.store.selectSnapshot(CommonState.trackerUser);
    if (trackerUser && trackerUser.uuid) {
      this.uuid = trackerUser.uuid;
    }

    return this.log(section, subsection, data);
  }


  public log(
    section: string,
    subsection: string,
    data: object | any,
  ): Observable<LoggerModel> {
    const request: any = {
      section,
      subsection,
      logData: data,
      uuid: this.uuid,
    };

    if (data.trackerUserId) {
      request.uuid = data.trackerUserId;
      delete request.logData.trackerUserId;
    }
    if (data.source) {
      request.source = data.source;
      delete request.logData.source;
    }

    this.frameService.sendMessage(FrameMessageEnum.log, request);

    return this.http
      .post<HttpResponse<LoggerModel>>(`${environment.api}/log`, request)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

}
