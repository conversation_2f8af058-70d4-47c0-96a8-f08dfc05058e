import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import { CustomerModel } from '../model/customer.model';
import { FinancialModel } from '../model/financial.model';
import { OttResponse } from '../model/ott.response';

@Injectable({
  providedIn: 'root',
})
export class CustomerService {

  constructor(
    private readonly http: HttpClient,
  ) {
  }

  detail(customerNumber: string): Observable<CustomerModel> {
    return this.http.post<HttpResponse<CustomerModel>>(`${environment.api}/user/customer`, {
      customerNumber
    }).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  financialInformation(customerNumber: string): Observable<FinancialModel> {
    return this.http.post<HttpResponse<FinancialModel>>(`${environment.api}/financials/detailV2`, { customerNumber })
    .pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  getOTT(): Observable<OttResponse> {
    return this.http
      .get<HttpResponse<OttResponse>>(`${environment.api}/user/ott`)
      .pipe(
        map((val) => {
          console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  auctionCustomerInfo(payload: any): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.customerDayApi}/customer-info`, payload, {
      params: { code: 'M80hmHeyN6azstcDvyT7NWHZLuOr7Ft84ZiFPmpTQdo9AzFuIdPAhA==' },
      headers: {
        Authorization: 'mZvLQyczUoVelV3cQl3BMd3qkIRwhly4',
        TOKEN_FREE: 'true'
      }
    }).pipe(
      map(val => {
        if (val) {
          return val.data;
        }
        return null;
      }),
    );
  }

  getCustomerDayCustomer(PortalUserId: string): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.customerDayApi}/customer-info`, {
      params: { code: 'M80hmHeyN6azstcDvyT7NWHZLuOr7Ft84ZiFPmpTQdo9AzFuIdPAhA==', PortalUserId },
      headers: {
        Authorization: 'mZvLQyczUoVelV3cQl3BMd3qkIRwhly4',
        TOKEN_FREE: 'true'
      }
    }).pipe(
      map(val => {
        if (val) {
          return val.data[0];
        }
        return null;
      }),
    );
  }

  auctionDonationsFeedbacks(payload: any): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.customerDayApi}/donations-feedbacks`, payload, {
      params: { code: 'UF0-feY6x70ymYligejM1zLoHWeh35mn-1r5MPusZDFMAzFuS-O-OQ==' },
      headers: {
        Authorization: 'mZvLQyczUoVelV3cQl3BMd3qkIRwhly4',
        TOKEN_FREE: 'true'
      }
    }).pipe(
      map(val => {
        if (val) {
          return val.data;
        }
        return null;
      }),
    );
  }

  companyInformations(): Observable<OttResponse> {
    return this.http
      .get<HttpResponse<OttResponse>>(`${environment.api}/company/getCompanyInformation`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  ongoingQuotations(): Observable<any> {
    return this.http
      .get<HttpResponse<any>>(`${environment.api}/order/list`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  ongoingQuotationOrderDetail(serialNumbers: string[]): Observable<any> {
    return this.http
      .post<HttpResponse<any>>(`${environment.api}/order/detail`, { serialNumbers })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
