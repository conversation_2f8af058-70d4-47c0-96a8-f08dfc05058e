import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoyalityComponent } from './component/loyality/loyality.component';
import { AddressListComponent } from './container/address-list/address-list.component';
import { CustomerLayoutComponent } from './container/customer-layout/customer-layout.component';
import { EquipmentCreateRequestComponent } from './container/equipment/equipment-create-request/equipment-create-request.component';
import { EquipmentDetailComponent } from './container/equipment/equipment-detail/equipment-detail.component';
import { EquipmentListComponent } from './container/equipment/equipment-list/equipment-list.component';
import { EquipmentMapComponent } from './container/equipment/equipment-map/equipment-map.component';
import { EquipmentUpdateComponent } from './container/equipment/equipment-update/equipment-update.component';
import {
  FinancialInformationComponent
} from './container/financial-information/financial-information-list/financial-information.component';
import { OfferCreateOrderComponent } from './container/offer/offer-create-order/offer-create-order.component';
import { OfferListComponent } from './container/offer/offer-list/offer-list.component';
import { OfferPdfComponent } from './container/offer/offer-pdf/offer-pdf.component';
import { CameraListComponent } from './container/service-list/camera-list/camera-list.component';
import { ServiceListComponent } from './container/service-list/service-list.component';
import { ServiceMapComponent } from './container/service-map/service-map.component';
import { WorkOrderListComponent } from './container/work-order-list/work-order-list.component';
import { ReconciliationComponent } from './container/financial-information/reconciliation/reconciliation.component';
import { SpecialCreateOrderComponent } from './container/offer/special-create-order/special-create-order.component';
import { ReconciliationDetailComponent } from './container/financial-information/reconciliation-detail/reconciliation-detail.component';
import { DiagnosticDataComponent } from './container/equipment/diagnostic-data/diagnostic-data.component';
import { AuctionComponent } from './container/auction/auction/auction.component';
import { AuctionSelectionComponent } from './container/auction/auction-selection/auction-selection.component';
import { RateUsComponent } from './container/auction/rate-us/rate-us.component';
import { CompanyInformationsComponent } from './container/company-informations/company-informations.component';
import { InspectionHistoryComponent } from './container/equipment/inspection-history/inspection-history.component';
import { ServiceDetailComponent } from './container/service-detail/service-detail.component';
import { ServiceChatComponent } from './container/service-chat/service-chat.component';
import { QrDetailComponent } from './container/service-list/qr-detail/qr-detail.component';
import { ServiceDetailResolver } from './resolver/service-detail.resolver';
import { CostDetailComponent } from './container/equipment/cost-detail/cost-detail.component';
import { MyOngoingOffersComponent } from './container/offer/my-ongoing-offers/my-ongoing-offers.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'offer-list',
  },
  {
    path: '',
    component: CustomerLayoutComponent,
    children: [
      { path: 'offer-list', component: OfferListComponent },
      { path: 'offer-pdf/:id', component: OfferPdfComponent },
      { path: 'equipment-list', component: EquipmentListComponent },
      { path: 'auction', component: AuctionComponent },
      { path: 'auction-amount', component: AuctionSelectionComponent },
      { path: 'auction-rate-us', component: RateUsComponent },
      { path: 'equipment-detail/:id', component: EquipmentDetailComponent },
      { path: 'company-informations', component: CompanyInformationsComponent },
      { path: 'equipment-map/:id', component: EquipmentMapComponent },
      { path: 'equipment-create', component: EquipmentCreateRequestComponent },
      { path: 'equipment-update/:id', component: EquipmentUpdateComponent },
      { path: 'financial-information', component: FinancialInformationComponent },
      { path: 'mutabakatlar', component: ReconciliationComponent },
      { path: 'mutabakat-detail', component: ReconciliationDetailComponent },
      { path: 'diagnostic-data', component: DiagnosticDataComponent },
      { path: 'inspection-history', component: InspectionHistoryComponent },
      { path: 'cost-detail/:id', component: CostDetailComponent},
      {
        path: 'service-list',
        component: ServiceListComponent,
      },
      {
        path: 'service-list/map/:id',
        component: ServiceMapComponent,
      },
      {
        path: 'service-list/camera-list/:id',
        component: CameraListComponent,
      },
      {
        path: 'service/detail/:id',
        component: ServiceDetailComponent,
        resolve: [ServiceDetailResolver]
      },
      {
        path: 'work-order/chat/:id',
        component: ServiceChatComponent,
        resolve: [ServiceDetailResolver]
      },
      { path: 'work-order/:workOrderNumber', component: QrDetailComponent },
      // old
      { path: 'work-order-list', component: WorkOrderListComponent },
      { path: 'address-list', component: AddressListComponent },
      { path: 'offer/create-order/:id', component: OfferCreateOrderComponent },
      { path: 'offer/special-order', component: SpecialCreateOrderComponent },
      { path: 'loyality', component: LoyalityComponent },
      { path: 'ongoing-offers', component: MyOngoingOffersComponent }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomerRoutingModule {
}
