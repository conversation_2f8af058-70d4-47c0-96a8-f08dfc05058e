import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  FinancialModel,
  MutabakatDetailModal,
  MutabakatListModel,
  MutabakatReplyReasonListModel,
} from '../model/financial.model';
import { FinancialService } from '../service/financial.service';
import {
  ClearMutabakatDetailAction, ClearMutabakatReplyDetailAction,
  GetFinancialInformationAction,
  GetMutabakatDetailAction,
  GetMutabakatListAction, GetMutabakatReplyReasonsAction, SendMutabakatReplyAction
} from '../action/financial.actions';

export interface FinancialStateModel {
  financialInformation: FinancialModel;
  financialInformationLoading: boolean;
  mutabakatList: MutabakatListModel[];
  mutabakatDetail: MutabakatDetailModal;
  mutabakatLoading: boolean;
  mutabakatReplyReasonsList: MutabakatReplyReasonListModel[];
  mutabakatReplySendStatus: any;
  mutabakatReplyLoading: boolean;
}

@State<FinancialStateModel>({
  name: 'financial',
  defaults: {
    financialInformation: null,
    financialInformationLoading: false,
    mutabakatList: [],
    mutabakatDetail: null,
    mutabakatLoading: false,
    mutabakatReplyReasonsList: null,
    mutabakatReplySendStatus: null,
    mutabakatReplyLoading: false,
  },
})
@Injectable()
export class FinancialState {
  constructor(private readonly financialService: FinancialService) { }

  @Selector()
  public static getState(state: FinancialStateModel) {
    return state;
  }

  @Selector()
  public static financialInformation({
    financialInformation,
  }: FinancialStateModel): FinancialModel {
    return financialInformation;
  }

  @Selector()
  public static financialInformationLoading({
    financialInformationLoading,
  }: FinancialStateModel): boolean {
    return financialInformationLoading;
  }

  @Selector()
  public static mutabakatList({ mutabakatList }: FinancialStateModel): MutabakatListModel[] {
    return mutabakatList;
  }

  @Selector()
  public static mutabakatDetail({ mutabakatDetail }: FinancialStateModel): MutabakatDetailModal {
    return mutabakatDetail;
  }

  @Selector()
  public static mutabakatLoading({ mutabakatLoading }: FinancialStateModel): boolean {
    return mutabakatLoading;
  }

  @Selector()
  public static mutabakatReplyReasonsList({ mutabakatReplyReasonsList }: FinancialStateModel): MutabakatReplyReasonListModel[] {
    return mutabakatReplyReasonsList;
  }

  @Selector()
  public static mutabakatReplySendStatus({ mutabakatReplySendStatus }: FinancialStateModel): boolean {
    return mutabakatReplySendStatus;
  }

  @Selector()
  public static mutabakatReplyLoading({ mutabakatReplyLoading }: FinancialStateModel): boolean {
    return mutabakatReplyLoading;
  }

  @Action(GetFinancialInformationAction)
  getFinancialInformationAction(
    { patchState }: StateContext<FinancialStateModel>,
    { customerNumber }: GetFinancialInformationAction,
  ) {
    patchState({
      financialInformationLoading: true,
    });
    return this.financialService.financialInformation(customerNumber).pipe(
      tap((value) => {
        patchState({
          financialInformation: value,
          financialInformationLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          financialInformationLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetMutabakatListAction)
  getMutabakatListState(
    { patchState }: StateContext<FinancialStateModel>,
    { numberOfItems }: GetMutabakatListAction,
  ) {
    patchState({
      mutabakatLoading: true,
    });
    return this.financialService.mutabakatList(numberOfItems).pipe(
      tap((value) => {
        patchState({
          mutabakatLoading: false,
          mutabakatList: value,
        });
      }),
      catchError((err) => {
        patchState({
          mutabakatLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetMutabakatDetailAction)
  getMutabakatDetailState(
    { patchState }: StateContext<FinancialStateModel>,
    { mutabakatDetailRequest }: GetMutabakatDetailAction,
  ) {
    patchState({
      mutabakatLoading: true,
    });
    return this.financialService.mutabakatDetail(mutabakatDetailRequest).pipe(
      tap((value) => {
        patchState({
          mutabakatDetail: value,
          mutabakatLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          mutabakatLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetMutabakatReplyReasonsAction)
  getMutabakatReplyReasonsListState(
    { patchState }: StateContext<FinancialStateModel>,
    { action }: GetMutabakatReplyReasonsAction,
  ) {
    patchState({
      mutabakatLoading: true,
    });
    return this.financialService.mutabakatReplyReasonList(action).pipe(
      tap((value) => {
        patchState({
          mutabakatReplyReasonsList: value,
          mutabakatLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          mutabakatLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(SendMutabakatReplyAction)
  getMutabakatReplyReasonsState(
    { patchState }: StateContext<FinancialStateModel>,
    { mutabakatReply }: SendMutabakatReplyAction,
  ) {
    patchState({
      mutabakatReplyLoading: true,
    });
    return this.financialService.mutabakatReply(mutabakatReply).pipe(
      tap((value) => {
        patchState({
          mutabakatReplySendStatus: value,
          mutabakatReplyLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          mutabakatReplyLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ClearMutabakatDetailAction)
  clearMutabakatDetailState(
    { patchState }: StateContext<FinancialStateModel>
  ) {
    patchState({
      mutabakatDetail: null,
      mutabakatReplyReasonsList: null,
      mutabakatReplySendStatus: null,
    });
  }

  @Action(ClearMutabakatReplyDetailAction)
  clearMutabakatReplyDetailState(
    { patchState }: StateContext<FinancialStateModel>
  ) {
    patchState({
      mutabakatReplyReasonsList: null,
      mutabakatReplySendStatus: null,
    });
  }
}
