import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CustomerService } from '../service/customer.service';
import { GetDonateCustomerAction, PostDonateCustomerAction, PostDonateFeedbackAction } from '../action/donate.actions';
import { LoginState } from '../../authentication/state/login/login.state';

export interface DonateStateModel {
  id: string;
  PortalUserId: string;
  PortalUserEmail: string;
  PortalUserFirstName: string;
  PortalUserLastName: string;
  CustomerId: string;
  CustomerName: string;
  CustomerNumber: string;
  UserAgent: string;
  IpAddress: string;
  HeaderCompanies: string;
  RelatedPersonId: string;
  RelatedPersonFullName: string;
  AppSessionId: string;
  CheckinCode: string;
  Status: boolean | string;
  CreatedDate: string;
  UpdatedDate: string;
  loading: boolean;
}

@State<DonateStateModel>({
  name: 'donate',
  defaults: {
    id: null,
    PortalUserId: null,
    PortalUserEmail: null,
    PortalUserFirstName: null,
    PortalUserLastName: null,
    CustomerId: null,
    CustomerName: null,
    CustomerNumber: null,
    UserAgent: null,
    IpAddress: null,
    HeaderCompanies: null,
    RelatedPersonId: null,
    RelatedPersonFullName: null,
    AppSessionId: null,
    CheckinCode: null,
    Status: null,
    CreatedDate: null,
    UpdatedDate: null,
    loading: false,
  },
})
@Injectable()
export class DonateState {

  values: any;
  constructor(
    private readonly customerService: CustomerService,
    private readonly store: Store,
  ) { }

  @Selector()
  public static getState(state: DonateStateModel) {
    return state;
  }

  // @Selector()
  // public static customer({ customer }: DonateStateModel): CustomerModel {
  //   return customer;
  // }

  @Action(PostDonateCustomerAction)
  PostDonateCustomerAction(
    { patchState }: StateContext<DonateStateModel>,
    { payload }: PostDonateCustomerAction
  ) {
    patchState({
      loading: true
    });

    const userInfo = this.store.selectSnapshot(LoginState.loginResponse);
    const userData = {
      PortalUserId: userInfo?.user?.id,
      PortalUserEmail: userInfo?.user?.email,
      PortalUserFirstName: userInfo?.user?.firstName,
      PortalUserLastName: userInfo?.user?.lastName,
      CustomerId: userInfo?.customer?.id,
      CustomerName: userInfo?.customer?.name,
      CustomerNumber: userInfo?.customer?.customerNumber,
      HeaderCompanies: userInfo?.headerCompanies,
      RelatedPersonId: userInfo?.relatedPerson?.relatedPersonCrmId,
      RelatedPersonFullName: userInfo?.user?.firstName + ' ' + userInfo?.user?.lastName,
      AppSessionId: userInfo?.appSessionId,
      Status: payload?.Status === 'true',
      CheckinCode: payload?.CheckinCode,
    };

    const values = { ...userData };
    return this.customerService.auctionCustomerInfo(values).pipe(
      tap((value) => {
        patchState({
          ...value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetDonateCustomerAction)
  getCustomerDayCustomer(
    { patchState }: StateContext<DonateStateModel>,
    { PortalUserId }: GetDonateCustomerAction,
  ) {
    patchState({
      loading: true,
    });

    return this.customerService.getCustomerDayCustomer(PortalUserId).pipe(
      tap((value) => {
        patchState({
          ...value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(PostDonateFeedbackAction)
  PostDonateFeedbackAction(
    { patchState }: StateContext<DonateStateModel>,
    { payload }: PostDonateFeedbackAction
  ) {
    patchState({
      loading: true
    });
    const userInfo = this.store.selectSnapshot(LoginState.loginResponse);

    if(userInfo?.user?.id) {
      const user = {
        PortalUserId: userInfo?.user?.id,
        PortalUserEmail: userInfo?.user?.email,
        PortalUserFirstName: userInfo?.user?.firstName,
        PortalUserLastName: userInfo?.user?.lastName,
        CustomerId: userInfo?.customer?.id,
        CustomerName: userInfo?.customer?.name,
        CustomerNumber: userInfo?.customer?.customerNumber,
        HeaderCompanies: userInfo?.headerCompanies,
        RelatedPersonId: userInfo?.relatedPerson?.relatedPersonCrmId,
        RelatedPersonFullName: userInfo?.user?.firstName + ' ' + userInfo?.user?.lastName,
        AppSessionId: userInfo?.appSessionId,
        Amount: payload.Amount,
        Currency: 'AZN',
        Rating: payload.Rating,
        Comment: payload.Comment
      }
      this.values = user;
    } else {
      const notUser = {
        PortalUserEmail: payload.Email,
        PortalUserFirstName: payload.Name,
        PortalUserLastName: payload.Surname,
        Amount: payload.Amount,
        Currency: 'AZN',
        Rating: payload.Rating,
        Comment: payload.Comment
      };
      this.values = notUser;
    }
    if(this.values) {
      return this.customerService.auctionDonationsFeedbacks(this.values).pipe(
        tap((value) => {
          patchState({
            loading: false,
          });
        }),
        catchError((err) => {
          patchState({
            loading: false,
          });
          return throwError(err);
        }),
      );

    }
    }
}
