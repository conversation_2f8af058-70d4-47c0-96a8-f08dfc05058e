import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CompanyInformationsAction, CustomerDetailAction } from '../action/customer.actions';
import { CustomerModel } from '../model/customer.model';
import { CustomerService } from '../service/customer.service';

export interface CustomerStateModel {
  customer: CustomerModel;
  customerDetailLoading: boolean;
  companyInfo: any;
}

@State<CustomerStateModel>({
  name: 'customer',
  defaults: {
    customer: null,
    customerDetailLoading: false,
    companyInfo: null,
  },
})
@Injectable()
export class CustomerState {
  constructor(private readonly customerService: CustomerService) { }

  @Selector()
  public static getState(state: CustomerStateModel) {
    return state;
  }

  @Selector()
  public static customer({ customer }: CustomerStateModel): CustomerModel {
    return customer;
  }

  @Selector()
  public static getCompanyInformations(companyInfo: CustomerStateModel) {
    return companyInfo;
  }

  @Selector()
  public static customerDetailLoading({
    customerDetailLoading,
  }: CustomerStateModel): boolean {
    return customerDetailLoading;
  }

  @Selector()
  public static isShowCallService({ customer }: CustomerStateModel): boolean {
    return !!customer?.details?.pssrList
    ?.some(pssr => pssr.telephoneList.length > 0 || pssr.mailList.length > 0);
  }

  @Action(CustomerDetailAction)
  customerDetailAction(
    { patchState }: StateContext<CustomerStateModel>,
    { customerNumber }: CustomerDetailAction,
  ) {
    patchState({
      customerDetailLoading: true,
    });
    return this.customerService.detail(customerNumber).pipe(
      tap((value) => {
        patchState({
          customer: value,
          customerDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          customerDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(CompanyInformationsAction)
  CompanyInformationsAction(
    { patchState }: StateContext<CustomerStateModel>,
  ) {
    patchState({
      customerDetailLoading: true,
    });
    return this.customerService.companyInformations().pipe(
      tap((value) => {
        patchState({
          companyInfo: value,
          customerDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          customerDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }

}
