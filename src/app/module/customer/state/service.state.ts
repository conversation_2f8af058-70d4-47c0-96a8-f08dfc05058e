import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ServiceDetailedRequestModel, ServiceDetailedRequestWorkOrderStatus } from '../model/service-request.model';
import { WorkOrderModel } from '../model/work-order.model';
import { PagingModel } from '../../definition/model/paging.model';
import {
  GetServiceDetailAction,
  GetServiceDetailedListAction,
  GetServiceLocationAction,
  GetWorkOrderFilterFieldsAction,
  GetWorkOrderListAction,
  GetWorkOrderChatMessagesAction,
  ResetWorkOrderChatMessagesAction,
  GetWorkOrderFromQrAction, SetServiceDetailAction
} from '../action/service.action';
import { ServiceService } from '../service/service.service';
import { ServiceLocationModel } from '../model/locations.model';
import { FilterFieldsModel } from '../model/filter.model';

export interface ServiceStateModel {
  workOrderSearchFields: FilterFieldsModel[];
  requestServiceDetailedList: ServiceDetailedRequestModel[];
  serviceDetail: ServiceDetailedRequestModel;
  workOrder: WorkOrderModel;
  requestServiceListPaging: PagingModel;
  requestServiceListLoading: boolean;
  serviceDetailLoading: boolean;
  workOrderLoading: boolean;
  serviceLocation: ServiceLocationModel;
  workOrderChatMessages: any;
  workOrderChatMessagesLoading: boolean;
  serviceDetailFromQr: ServiceDetailedRequestWorkOrderStatus;
  serviceDetailFromQrLoading: boolean;
}

@State<ServiceStateModel>({
  name: 'service',
  defaults: {
    workOrderSearchFields: [],
    requestServiceDetailedList: [],
    serviceDetail: null,
    workOrder: null,
    requestServiceListPaging: {
      totalCount: 0,
      pageSize: 0,
      pageNumber: 0,
    },
    requestServiceListLoading: false,
    serviceDetailLoading: false,
    workOrderLoading: false,
    serviceLocation: null,
    workOrderChatMessages: [],
    workOrderChatMessagesLoading: false,
    serviceDetailFromQr: null,
    serviceDetailFromQrLoading: false
  },
})
@Injectable()
export class ServiceState {
  constructor(private readonly serviceService: ServiceService) { }

  @Selector()
  public static getWorkOrderSearchFields({
    workOrderSearchFields,
  }: ServiceStateModel): FilterFieldsModel[] {
    return workOrderSearchFields;
  }

  @Selector()
  public static requestServiceDetailedList({
    requestServiceDetailedList,
  }: ServiceStateModel): ServiceDetailedRequestModel[] {
    return requestServiceDetailedList;
  }

  @Selector()
  public static serviceDetail({
    serviceDetail,
  }: ServiceStateModel): ServiceDetailedRequestModel {
    return serviceDetail;
  }

  @Selector()
  public static workOrder({ workOrder }: ServiceStateModel): WorkOrderModel {
    return workOrder;
  }

  @Selector()
  public static requestServiceListLoading({
    requestServiceListLoading,
  }: ServiceStateModel): boolean {
    return requestServiceListLoading;
  }

  @Selector()
  public static serviceDetailLoading({
    serviceDetailLoading,
  }: ServiceStateModel): boolean {
    return serviceDetailLoading;
  }

  @Selector()
  public static requestServiceListPaging({
    requestServiceListPaging,
  }: ServiceStateModel): PagingModel {
    return requestServiceListPaging;
  }

  @Selector()
  public static workOrderLoading({
    workOrderLoading,
  }: ServiceStateModel): boolean {
    return workOrderLoading;
  }

  @Selector()
  public static serviceLocation({
    serviceLocation,
  }: ServiceStateModel): ServiceLocationModel {
    return serviceLocation;
  }

  @Selector()
  public static workOrderChatMessages({
    workOrderChatMessages,
  }: ServiceStateModel) {
    return workOrderChatMessages;
  }

  @Selector()
  public static workOrderChatMessagesLoading({
    workOrderChatMessagesLoading
  }: ServiceStateModel) {
    return workOrderChatMessagesLoading;
  }

  @Selector()
  public static serviceDetailFromQr({
    serviceDetailFromQr
  }: ServiceStateModel) {
    return serviceDetailFromQr;
  }

  @Selector()
  public static serviceDetailFromQrLoading({
    serviceDetailFromQrLoading
  }: ServiceStateModel) {
    return serviceDetailFromQrLoading;
  }

  @Action(GetWorkOrderFromQrAction)
  getWorkOrderFromQrAction(
    { getState, patchState }: StateContext<ServiceStateModel>,
    { workorderNumber }: GetWorkOrderFromQrAction,
  ) {
    patchState({
      serviceDetailFromQrLoading: true,
    });
    return this.serviceService.workOrderFromQr(workorderNumber).pipe(
      tap((res) => {
        patchState({
          serviceDetailFromQr: res,
          serviceDetailFromQrLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          serviceDetailFromQrLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetWorkOrderFilterFieldsAction)
  getWorkOrderFilterFieldsState(
    {  patchState }: StateContext<ServiceStateModel>,
    action: GetWorkOrderFilterFieldsAction,
  ) {
    return this.serviceService.workOrderFilterFields().pipe(
      tap((res) => {
        patchState({
          workOrderSearchFields: res,
        });
      }),
      catchError((err) => {
        return throwError(err);
      }),
    );
  }

  @Action(GetServiceDetailedListAction)
  getServiceDetailedListAction(
    { getState, patchState }: StateContext<ServiceStateModel>,
    { filterBody }: GetServiceDetailedListAction,
  ) {
    patchState({
      requestServiceListLoading: true,
    });
    return this.serviceService.serviceListDetailed(filterBody).pipe(
      tap((res) => {
        let list = filterBody.page === 1 ? [] : getState().requestServiceDetailedList;
        if (res.data) {
          list = list.concat(res.data);
        }

        // list[0].workOrderStatus.hasArventoNode = true;
        // list[0].workOrderStatus.servicePlateNumber = '34BOL550';
        patchState({
          requestServiceDetailedList: list,
          requestServiceListLoading: false,
          requestServiceListPaging: res.paging,
        });
      }),
      catchError((err) => {
        patchState({
          requestServiceListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetServiceDetailAction)
  getServiceDetailAction(
    { getState, patchState }: StateContext<ServiceStateModel>,
    { workorderNumber, customerNumber }: GetServiceDetailAction,
  ) {
    patchState({
      serviceDetailLoading: true,
    });
    return this.serviceService.serviceDetail({ workorderNumber, customerNumber }).pipe(
      tap((serviceDetail) => {
        patchState({
          serviceDetail,
          serviceDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          serviceDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(SetServiceDetailAction)
  setServiceDetailAction(
    { getState, patchState }: StateContext<ServiceStateModel>,
    { serviceDetail }: SetServiceDetailAction,
  ) {
    patchState({
      serviceDetail,
    });
  }

  @Action(GetWorkOrderListAction)
  getWorkOrderListAction(
    { patchState }: StateContext<ServiceStateModel>,
    action: GetWorkOrderListAction,
  ) {
    patchState({
      workOrderLoading: true,
    });

    const request: any = {
      workorderNumber: action.serviceNumber,
      serviceOrganization: action.serviceOrganization,
    };

    return this.serviceService.requestWorkOrder(request).pipe(
      tap((value) => {
        patchState({
          workOrder: value,
          workOrderLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          workOrderLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetServiceLocationAction)
  getServiceLocation(
    { patchState }: StateContext<ServiceStateModel>,
    { plateNumber }: any,
  ) {
    return this.serviceService.requestServiceLocation(plateNumber).pipe(
      tap((value) => {
        patchState({
          serviceLocation: value,
        });
      }),
      catchError((err) => {
        patchState({
          // ! Loading Eklenebilir
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetWorkOrderChatMessagesAction)
  getWorkOrderChatMessages(
    { patchState }: StateContext<ServiceStateModel>,
    action: GetWorkOrderChatMessagesAction,
  ) {
    patchState({
      workOrderChatMessagesLoading: action.isRefresh,
    });

    return this.serviceService.workOrderChatMessages({
      workOrderNumber: action.serviceDetail.serviceNumber,
      workorderGuid: action.serviceDetail.workOrderId,
    }).pipe(
      tap((value) => {
        patchState({
          workOrderChatMessages: value,
          workOrderChatMessagesLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          workOrderChatMessagesLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ResetWorkOrderChatMessagesAction)
  resetWorkOrderChatMessages(
    { patchState }: StateContext<ServiceStateModel>,
  ) {
    return patchState({
      workOrderChatMessages: [],
    });
  }

}
