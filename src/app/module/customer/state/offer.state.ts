import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CreateOrderModel, OfferDetailModel, OfferModel, PaymentStatusModel } from '../model/offer.model';
import {
  GetOfferListAction,
  ApproveQuotationAction,
  RevisionQuotationAction,
  RejectQuotationAction,
  CreateOrderQuotationAction,
  GetPaymentStatusAction,
  CreateOrderQuotationRemoveAction,
  ClearCreateOrderAction,
  GetOfferDetailAction,
  ClearOfferDetailAction, ClearQuotationAction, GetAvailableQuotationTypesAction,
  GetMyOngoingQuotationsAction,
  GetMyOngoingQuotationsDetailsAction
} from '../action/offer.actions';
import { OfferService } from '../service/offer.service';
import { CustomerService } from '../service/customer.service';

export interface OfferStateModel {
  offerList: OfferModel[];
  offerDetail: OfferDetailModel;
  offerListLoading: boolean;
  offerDetailLoading: boolean;
  offerApprove: boolean;
  offerReject: boolean;
  offerRevision: boolean;
  createOrder: CreateOrderModel;
  createOrderLoading: boolean;
  paymentStatus: PaymentStatusModel;
  availableQuotationTypes: any;
  ongoingQuotations: any;
  ongoingQuotationsLoading: boolean;
  ongoingQuotationsOrdersDetail: any;
  ongoingQuotationsOrdersDetailLoading: boolean;
}

@State<OfferStateModel>({
  name: 'offer',
  defaults: {
    offerList: [],
    offerDetail: null,
    offerListLoading: false,
    offerDetailLoading: false,
    offerApprove: false,
    offerReject: false,
    offerRevision: false,
    createOrder: null,
    createOrderLoading: false,
    paymentStatus: null,
    availableQuotationTypes: null,
    ongoingQuotations: null,
    ongoingQuotationsLoading: false,
    ongoingQuotationsOrdersDetail: null,
    ongoingQuotationsOrdersDetailLoading: false
  },
})
@Injectable()
export class OfferState {
  constructor(
    private readonly offerService: OfferService,
    private readonly customerService: CustomerService
  ) { }

  @Selector()
  public static offerList({ offerList }: OfferStateModel): OfferModel[] {
    return offerList;
  }

  @Selector()
  public static offerApprove({ offerApprove }: OfferStateModel): boolean {
    return offerApprove;
  }

  @Selector()
  public static offerReject({ offerReject }: OfferStateModel): boolean {
    return offerReject;
  }

  @Selector()
  public static offerRevision({ offerRevision }: OfferStateModel): boolean {
    return offerRevision;
  }

  @Selector()
  public static createOrder({ createOrder }: OfferStateModel): CreateOrderModel {
    return createOrder;
  }

  @Selector()
  public static createOrderLoading({ createOrderLoading }: OfferStateModel): boolean {
    return createOrderLoading;
  }

  @Selector()
  public static getPaymentStatus({ paymentStatus }: OfferStateModel): PaymentStatusModel {
    return paymentStatus;
  }

  @Selector()
  public static offerListLoading({ offerListLoading }: OfferStateModel): boolean {
    return offerListLoading;
  }

  @Selector()
  public static offerDetail({ offerDetail }: OfferStateModel): OfferDetailModel {
    return offerDetail;
  }

  @Selector()
  public static offerDetailLoading({ offerDetailLoading }: OfferStateModel): boolean {
    return offerDetailLoading;
  }

  @Selector()
  public static availableQuotationTypes({ availableQuotationTypes }: OfferStateModel) {
    return availableQuotationTypes
  }

  @Selector()
  public static ongoingQuotations({ ongoingQuotations }: OfferStateModel) {
    return ongoingQuotations
  }

  @Selector()
  public static ongoingQuotationsLoading({ ongoingQuotationsLoading }: OfferStateModel) {
    return ongoingQuotationsLoading
  }

  @Selector()
  public static ongoingQuotationsOrdersDetail({ ongoingQuotationsOrdersDetail }: OfferStateModel) {
    return ongoingQuotationsOrdersDetail
  }

  @Selector()
  public static ongoingQuotationsOrdersDetailLoading({ ongoingQuotationsOrdersDetailLoading }: OfferStateModel) {
    return ongoingQuotationsOrdersDetailLoading
  }

  @Action(GetOfferListAction)
  getOfferListAction(
    { patchState }: StateContext<OfferStateModel>,
    { customerNumber, refresh }: GetOfferListAction,
  ) {
    patchState({
      offerListLoading: true,
    });
    return this.offerService.offerList(customerNumber, refresh).pipe(
      tap((value) => {
        patchState({
          offerList: value,
          offerListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetOfferDetailAction)
  getOfferDetailAction(
    { patchState }: StateContext<OfferStateModel>,
    { quotationNumber, guid }: GetOfferDetailAction,
  ) {
    patchState({
      offerDetailLoading: true,
    });
    return this.offerService.offerDetail(quotationNumber, guid).pipe(
      tap((value) => {
        patchState({
          offerDetail: value,
          offerDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          offerDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ClearOfferDetailAction)
  clearOfferDetailAction(
    { patchState }: StateContext<OfferStateModel>
  ) {
    patchState({
      offerDetail: null,
    });
  }

  @Action(ApproveQuotationAction)
  approveQuotationState(
    { patchState }: StateContext<OfferStateModel>,
    { quotationNumber }: ApproveQuotationAction,
  ) {
    patchState({
      offerListLoading: true,
    });
    return this.offerService.approveQuotation(quotationNumber).pipe(
      tap((value) => {
        patchState({
          offerApprove: value,
          offerListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetAvailableQuotationTypesAction)
  getAvailableQuotationTypes(
    { patchState }: StateContext<OfferStateModel>
  ) {
    return this.offerService.availableQuotationTypes().subscribe(data => {
        patchState({ availableQuotationTypes: JSON.parse(data) })
    })
  }


  @Action(RejectQuotationAction)
  rejectQuotationState(
    { patchState }: StateContext<OfferStateModel>,
    { quotationNumber }: RejectQuotationAction,
  ) {
    patchState({
      offerListLoading: true,
    });
    return this.offerService.rejectQuotation(quotationNumber).pipe(
      tap((value) => {
        patchState({
          offerReject: value,
          offerListLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(RevisionQuotationAction)
  revisionQuotationState(
    { patchState }: StateContext<OfferStateModel>,
    { quotationNumber, revisionReason }: RevisionQuotationAction,
  ) {
    patchState({
      offerListLoading: true,
    });
    return this.offerService.revisionQuotation(quotationNumber, revisionReason).pipe(
      tap((value) => {
        patchState({
          offerRevision: value,
          offerListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(CreateOrderQuotationAction)
  createOrderQuotationState(
    { patchState }: StateContext<OfferStateModel>,
    { quotationNumber, paymentMethod, approveAgreements }: CreateOrderQuotationAction,
  ) {
    patchState({
      createOrderLoading: true,
      offerListLoading: true,
    });
    return this.offerService.createOrderQuotation(quotationNumber, paymentMethod, approveAgreements).pipe(
      tap((value) => {
        patchState({
          createOrderLoading: false,
          offerListLoading: false,
          createOrder: value
        });
      }),
      catchError((err) => {
        patchState({
          createOrderLoading: false,
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(CreateOrderQuotationRemoveAction)
  createOrderQuotationRemoveState(
    { patchState }: StateContext<OfferStateModel>,
  ) {
    patchState({
      createOrder: null
    });
  }

  @Action(GetPaymentStatusAction)
  getPaymentStatusState(
    { patchState }: StateContext<OfferStateModel>,
    { traceId }: GetPaymentStatusAction,
  ) {
    patchState({
      createOrderLoading: true,
      offerListLoading: true,
    });
    return this.offerService.paymentStatus(traceId).pipe(
      tap((value) => {
        patchState({
          createOrderLoading: false,
          offerListLoading: false,
          paymentStatus: value
        });
      }),
      catchError((err) => {
        patchState({
          createOrderLoading: false,
          offerListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ClearCreateOrderAction)
  clearCreateOrder(
    { patchState }: StateContext<OfferStateModel>,
  ) {
    patchState({
      createOrder: null,
      paymentStatus: null,
    });
  }

  @Action(ClearQuotationAction)
  clearQuotation(
    { patchState }: StateContext<OfferStateModel>,
  ) {
    patchState({
      offerApprove: false,
      offerRevision: false,
      offerReject: false
    });
  }

  @Action(GetMyOngoingQuotationsAction)
  myOngoingQuotationsAction(
    { patchState }: StateContext<OfferStateModel>,
  ) {
    patchState({
      ongoingQuotationsLoading: true
    });
    return this.customerService.ongoingQuotations().pipe(
      tap((data) => {
        patchState({
          ongoingQuotationsLoading: false,
          ongoingQuotations: data?.orders
        });
      }),
      catchError((err) => {
        patchState({
          ongoingQuotationsLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetMyOngoingQuotationsDetailsAction)
  myOngoingQuotationsDetailsAction(
    { patchState }: StateContext<OfferStateModel>,
    { serialNumbers }: GetMyOngoingQuotationsDetailsAction
  ) {
    patchState({
      ongoingQuotationsOrdersDetailLoading: true
    });
    return this.customerService.ongoingQuotationOrderDetail(serialNumbers).pipe(
      tap((data) => {
        patchState({
          ongoingQuotationsOrdersDetailLoading: false,
          ongoingQuotationsOrdersDetail: data
        });
      }),
      catchError((err) => {
        patchState({
          ongoingQuotationsOrdersDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }
}
