import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { EquipmentInspectionModel, EquipmentMDAPmPlannerModel, EquipmentModel, EquipmentSosAnalyzesModel, DiagnosticData, workOrderPlanModel, CostCategoryListModel, CostListModel, EquipmentQuotationModel, EquipmentServiceHistoryModel } from '../model/equipment.model';
import { PagingModel } from '../../definition/model/paging.model';
import {
  GetEquipmentListAllAction,
  GetEquipmentDetailAction,
  GetEquipmentInspectionsAction,
  GetEquipmentListFilterAction,
  GetEquipmentMDAPmPlannerAction,
  GetEquipmentNewSearchAction,
  GetEquipmentSosAnalyzesAction,
  GetEquipmentAllDiagnosticDataAction,
  GetEquipmentDiagnosticDataPagingAction,
  GetEquipmentInspectionHistoryAction,
  GetEquipmentHasPSEAction,
  PostEquipmentPSEAction,
  ClearEquipmentPSEAction,
  GetEquipmentLocationAction,
  GetEquipmentAgendaHistory,
  GetEquipmentAgendaDetails,
  AddedToCartEquipmentPSEAction,
  EquipmentPSESearchAction,
  ResetEquipmentAgendaHistoryAction,
  GetCheckedInEquipmentsAction,
  EquipmentWorkOrderPlanAction,
  EquipmentCostCategoryListAction,
  GetEquipmentCostList,
  ResetEquipmentCostListAction,
  GetEquipmentQuotationsAction,
  GetEquipmentServiceHistoryAction
} from '../action/equipment.action';
import { EquipmentService } from '../service/equipment.service';
import { FilterFieldsModel } from '../model/filter.model';

export interface EquipmentStateModel {
  equipmentListSearch: EquipmentModel[];
  equipmentListFilterFields: FilterFieldsModel[];
  equipmentDetail: EquipmentModel;
  equipmentInspectionResults: EquipmentInspectionModel[];
  equipmentSosAnalyzes: EquipmentSosAnalyzesModel[];
  equipmentListPaging: PagingModel;
  equipmentListLoading: boolean;
  equipmentDetailLoading: boolean;
  equipmentDiagnosticDataPaging: PagingModel;
  equipmentDiagnosticDataList: DiagnosticData[];
  equipmentAllDiagnosticData: DiagnosticData[];
  equipmentDiagnosticDataLoading: boolean;
  equipmentSosAnalyzesLoading: boolean;
  equipmentMDAPmaPlanner: EquipmentMDAPmPlannerModel[];
  EquipmentMDAPmPlannerLoading: boolean;
  inspectionResultsLoading: boolean;
  equipmentInspections: EquipmentInspectionModel[];
  equipmentsListAll: EquipmentModel[];
  equipmentLocation: any[];
  equipmentInspectionHistory: EquipmentInspectionModel[];
  equipmentInspectionHistoryLoading: boolean;
  equipmentPSE: any;
  equipmentPSELoading: boolean;
  equipmentPSEAddToCartResponse: any;
  equipmentLocationLoading: boolean;
  equipmentAgendaHistory: any;
  equipmentAgendaDetails: any;
  equipmentAgendaLoading: boolean;
  checkedInEquipments: any[];
  PSESearchResult: any;
  equipmentAgendaDetailLoading: boolean;
  workOrderPlans: workOrderPlanModel[];
  equipmentCostCategoryList: CostCategoryListModel[];
  equipmentCostCategoryListLoading: boolean;
  equipmentCostList: CostListModel[];
  equipmentCostListLoading: boolean;
  equipmentQuotations: EquipmentQuotationModel[];
  equipmentQuotationsLoading: boolean;
  equipmentServiceHistory: EquipmentServiceHistoryModel[];
  equipmentServiceHistoryLoading: boolean;
}

@State<EquipmentStateModel>({
  name: 'equipment',
  defaults: {
    equipmentListSearch: null,
    equipmentListFilterFields: [],
    equipmentDetail: null,
    equipmentInspectionResults: null,
    equipmentSosAnalyzes: null,
    equipmentListLoading: false,
    equipmentListPaging: null,
    equipmentDetailLoading: false,
    equipmentDiagnosticDataPaging: null,
    equipmentDiagnosticDataList: null,
    equipmentAllDiagnosticData: null,
    equipmentDiagnosticDataLoading: false,
    equipmentSosAnalyzesLoading: false,
    equipmentMDAPmaPlanner: null,
    EquipmentMDAPmPlannerLoading: false,
    equipmentInspections: null,
    inspectionResultsLoading: false,
    equipmentsListAll: [],
    equipmentInspectionHistory: null,
    equipmentInspectionHistoryLoading: false,
    equipmentPSE: null,
    equipmentPSELoading: false,
    equipmentPSEAddToCartResponse: null,
    equipmentLocationLoading: false,
    equipmentLocation: [],
    equipmentAgendaHistory: null,
    equipmentAgendaDetails: null,
    equipmentAgendaLoading: false,
    checkedInEquipments: [],
    PSESearchResult: null,
    equipmentAgendaDetailLoading: false,
    workOrderPlans: null,
    equipmentCostCategoryList: [],
    equipmentCostCategoryListLoading: false,
    equipmentCostList: [],
    equipmentCostListLoading: false,
    equipmentQuotations: [],
    equipmentQuotationsLoading: false,
    equipmentServiceHistory: [],
    equipmentServiceHistoryLoading: false
  },
})
@Injectable()
export class EquipmentState {
  constructor(private readonly equipmentService: EquipmentService) { }

  @Selector()
  public static equipmentListSearch({ equipmentListSearch }: EquipmentStateModel): EquipmentModel[] {
    return equipmentListSearch;
  }
  @Selector()
  public static equipmentsListAll({ equipmentsListAll }: EquipmentStateModel): EquipmentModel[] {
    return equipmentsListAll;
  }

  @Selector()
  public static equipmentLocation({ equipmentLocation }: EquipmentStateModel) {
    return equipmentLocation;
  }

  @Selector()
  public static equipmentLocationLoading({ equipmentLocationLoading }: EquipmentStateModel) {
    return equipmentLocationLoading;
  }

  @Selector()
  public static equipmentFilterFields({ equipmentListFilterFields }: EquipmentStateModel): FilterFieldsModel[] {
    return equipmentListFilterFields;
  }

  @Selector()
  public static equipmentDetail({
    equipmentDetail,
  }: EquipmentStateModel): EquipmentModel {
    return equipmentDetail;
  }

  @Selector()
  public static equipmentInspectionResults({
    equipmentInspectionResults,
  }: EquipmentStateModel): EquipmentInspectionModel[] {
    return equipmentInspectionResults;
  }

  @Selector()
  public static equipmentSosAnalyzes({
    equipmentSosAnalyzes,
  }: EquipmentStateModel): EquipmentSosAnalyzesModel[] {
    return equipmentSosAnalyzes;
  }

  @Selector()
  public static equipmentSosAnalyzesLoading({
    equipmentSosAnalyzesLoading,
  }: EquipmentStateModel): boolean {
    return equipmentSosAnalyzesLoading;
  }

  @Selector()
  public static equipmentListLoading({
    equipmentListLoading,
  }: EquipmentStateModel): boolean {
    return equipmentListLoading;
  }

  @Selector()
  public static equipmentListPaging({
    equipmentListPaging,
  }: EquipmentStateModel): PagingModel {
    return equipmentListPaging;
  }

  @Selector()
  public static equipmentDetailLoading({
    equipmentDetailLoading,
  }: EquipmentStateModel): boolean {
    return equipmentDetailLoading;
  }

  @Selector()
  public static EquipmentMDAPmPlannerLoading({
    EquipmentMDAPmPlannerLoading,
  }: EquipmentStateModel): boolean {
    return EquipmentMDAPmPlannerLoading;
  }

  @Selector()
  public static inspectionResultsLoading({
    inspectionResultsLoading,
  }: EquipmentStateModel): boolean {
    return inspectionResultsLoading;
  }

  @Selector()
  public static equipmentMDAPmaPlanner({
    equipmentMDAPmaPlanner,
  }: EquipmentStateModel): EquipmentMDAPmPlannerModel[] {
    return equipmentMDAPmaPlanner;
  }

  @Selector()
  public static equipmentInspections({
    equipmentInspections,
  }: EquipmentStateModel): EquipmentInspectionModel[] {
    return equipmentInspections;
  }

  @Selector()
  public static equipmentDiagDataList({
    equipmentDiagnosticDataList,
  }: EquipmentStateModel): DiagnosticData[] {
    return equipmentDiagnosticDataList;
  }

  @Selector()
  public static equipmentDiagDataPaging({
    equipmentDiagnosticDataPaging,
  }: EquipmentStateModel): PagingModel {
    return equipmentDiagnosticDataPaging;
  }

  @Selector()
  public static equipmentDiagDataLoading({
    equipmentDiagnosticDataLoading,
  }: EquipmentStateModel): boolean {
    return equipmentDiagnosticDataLoading;
  }

  @Selector()
  public static equipmentInspectionHistory({
    equipmentInspectionHistory,
  }: EquipmentStateModel): EquipmentInspectionModel[] {
    return equipmentInspectionHistory;
  }

  @Selector()
  public static equipmentAgendaHistory({
    equipmentAgendaHistory,
  }: EquipmentStateModel) {
    return equipmentAgendaHistory;
  }

  @Selector()
  public static equipmentAgendaDetails({
    equipmentAgendaDetails,
  }: EquipmentStateModel) {
    return equipmentAgendaDetails;
  }

  @Selector()
  public static equipmentAgendaLoading({
    equipmentAgendaLoading,
  }: EquipmentStateModel) {
    return equipmentAgendaLoading;
  }

  @Selector()
  public static equipmentAgendaDetailLoading({
    equipmentAgendaDetailLoading,
  }: EquipmentStateModel) {
    return equipmentAgendaDetailLoading;
  }

  @Selector()
  public static checkedInEquipments({
    checkedInEquipments,
  }: EquipmentStateModel) {
    return checkedInEquipments;
  }

  @Selector()
  public static equipmentInspectionHistoryLoading({
    equipmentInspectionHistoryLoading,
  }: EquipmentStateModel): boolean {
    return equipmentInspectionHistoryLoading;
  }

  @Selector()
  public static equipmentPSE({
    equipmentPSE,
  }: EquipmentStateModel): boolean {
    return equipmentPSE;
  }
  @Selector()
  public static equipmentPSEAddToCartResponse({
    equipmentPSEAddToCartResponse,
  }: EquipmentStateModel): boolean {
    return equipmentPSEAddToCartResponse;
  }

  @Selector()
  public static equipmentPSELoading({
    equipmentPSELoading,
  }: EquipmentStateModel): boolean {
    return equipmentPSELoading;
  }

  @Selector()
  public static PSESearchResult({
    PSESearchResult,
  }: EquipmentStateModel): boolean {
    return PSESearchResult;
  }

  @Selector()
  public static equipmentWorkOrder({
    workOrderPlans,
  }: EquipmentStateModel): workOrderPlanModel[] {
    return workOrderPlans;
  }

  @Selector()
  public static equipmentCostCategoryList({
    equipmentCostCategoryList,
  }: EquipmentStateModel) {
    return equipmentCostCategoryList;
  }

  @Selector()
  public static equipmentCostCategoryListLoading({
    equipmentCostCategoryListLoading,
  }: EquipmentStateModel) {
    return equipmentCostCategoryListLoading;
  }

  @Selector()
  public static equipmentCostList({
    equipmentCostList,
  }: EquipmentStateModel) {
    return equipmentCostList;
  }

  @Selector()
  public static equipmentCostListLoading({
    equipmentCostListLoading,
  }: EquipmentStateModel) {
    return equipmentCostListLoading;
  }

  @Selector()
  public static equipmentQuotation({
    equipmentQuotations,
  }: EquipmentStateModel) {
    return equipmentQuotations;
  }

  @Selector()
  public static equipmentQuotationsLoading({
    equipmentQuotationsLoading,
  }: EquipmentStateModel) {
    return equipmentQuotationsLoading;
  }

  @Selector()
  public static equipmentServiceHistory({
    equipmentServiceHistory,
  }: EquipmentStateModel) {
    return equipmentServiceHistory;
  }

  @Selector()
  public static equipmentServiceHistoryLoading({
    equipmentServiceHistoryLoading,
  }: EquipmentStateModel) {
    return equipmentServiceHistoryLoading;
  }

  @Action(GetEquipmentListFilterAction)
  getEquipmentListFilterAction(
    { getState, patchState }: StateContext<EquipmentStateModel>,
    { customerId, language }: GetEquipmentListFilterAction,
  ) {
    return this.equipmentService.equipmentListFilterFields(customerId, language)
      .pipe(
        tap((res) => {
            patchState({
              equipmentListFilterFields: res,
            });
          },
        ),
        catchError((err) => {
          return throwError(err);
        }),
      );
  }

  @Action(GetEquipmentAgendaHistory)
  getEquipmentAgendaHistory(
    { patchState }: StateContext<EquipmentStateModel>,
    {SerialNumber, EquipmentNumber, SelectedDate}: GetEquipmentAgendaHistory
  ){
    patchState({
      equipmentAgendaLoading: true
    })
    const body = {
      SerialNumber,
      EquipmentNumber,
      IncludeNotes: true,
      IncludeCheckIns: true,
      IncludeInspections: true,
      IncludeSos: true,
      IncludeDiagData: true,
      IncludeServices: true,
      IncludePMPlanners: true,
      IncludeCatInspections: true,
      SelectedDate
    }

    return this.equipmentService.equipmentAgendaHistory(body).pipe(
      tap((res) => {
        patchState({ equipmentAgendaHistory: res, equipmentAgendaLoading: false });
      }),
      catchError((err) => {
        patchState({  equipmentAgendaLoading: false });
        return throwError(err);
      }),
    );
  }

  @Action(ResetEquipmentAgendaHistoryAction)
  resetEquipmentAgendaHistory(
    { patchState }: StateContext<EquipmentStateModel>,
  ){
    patchState({ equipmentAgendaHistory: null });
  }

  @Action(GetCheckedInEquipmentsAction)
  getCheckedInEquipments(
    { patchState }: StateContext<EquipmentStateModel>,
  ){
    return this.equipmentService.checkedInEquipments().pipe(
      tap((res) => {
        patchState({ checkedInEquipments: res });
      }),
      catchError((err) => {
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentAgendaDetails)
  getEquipmentAgendaDetails(
    {patchState}: StateContext<EquipmentStateModel>,
    {SerialNumber, Model, EquipmentNumber}: GetEquipmentAgendaDetails
  ) {
    patchState({
      equipmentAgendaDetailLoading: true
    })
    return this.equipmentService.equipmentAgendaDetails({ SerialNumber, Model, EquipmentNumber }).pipe(
      tap((res) => {
        patchState({ equipmentAgendaDetails: res, equipmentAgendaDetailLoading: false })
      }),
      catchError((err) => {
        patchState({ equipmentAgendaDetailLoading: false })
        return throwError(err)
      })
    )
  }

  @Action(GetEquipmentInspectionsAction)
  getEquipmentInspectionResultsAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: GetEquipmentInspectionsAction,
  ) {
    patchState({
      inspectionResultsLoading: true,
    });
    return this.equipmentService.equipmentInspections(serialNumber).pipe(
      tap((value) => {
        patchState({
          equipmentInspectionResults: value,
          inspectionResultsLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentInspectionResults: null,
          inspectionResultsLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentNewSearchAction)
  getEquipmentNewSearchAction(
    { getState, patchState }: StateContext<EquipmentStateModel>,
    { page, searchParameter, top }: GetEquipmentNewSearchAction,
  ) {
    patchState({
      equipmentListLoading: true,
    });
    const search: any = { page, items: searchParameter, top };
    return this.equipmentService.equipmentNewListSearch(search)
      .pipe(
        // map((res) => {
        //   res.data = (res.data || []).sort((a, b) => (a.serialNumber > b.serialNumber ? 1 : -1));
        //   return res;
        // }),
        tap((res) => {
          let list = page === 1 ? [] : getState().equipmentListSearch;
          list = list.concat(res.data || []);

          patchState({
            equipmentListSearch: list,
            equipmentListPaging: res.paging,
            equipmentListLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            equipmentListLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(GetEquipmentListAllAction)
  getEquipmentListAllAction(
    { getState, patchState }: StateContext<EquipmentStateModel>,
    { page, searchParameter, top }: GetEquipmentListAllAction,
  ) {
    // patchState({
    //   equipmentListLoading: true,
    // });
    const search: any = { page, items: searchParameter, top };
    return this.equipmentService.equipmentNewListSearch(search)
      .pipe(
        tap((res) => {
          patchState({
            equipmentsListAll: res.data,
            // equipmentListPaging: res.paging,
            // equipmentListLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            // equipmentListLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(GetEquipmentLocationAction)
  getEquipmentLocation({ patchState }: StateContext<EquipmentStateModel>) {
    patchState({
      equipmentLocationLoading: true,
    });
    return this.equipmentService.equipmentLocationList().pipe(
      tap((res) => {
        patchState({
          equipmentLocation: res.data,
          equipmentLocationLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentLocationLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetEquipmentDetailAction)
  getEquipmentDetailAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { equipmentNumber }: GetEquipmentDetailAction,
  ) {
    patchState({
      equipmentDetailLoading: true,
      equipmentDetail: null,
    });
    return this.equipmentService.equipmentDetail(equipmentNumber).pipe(
      tap((value) => {
        patchState({
          equipmentDetail: value,
          equipmentDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentDetailLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentSosAnalyzesAction)
  getEquipmentSosAnalyzesAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: GetEquipmentSosAnalyzesAction,
  ) {
    patchState({
      equipmentSosAnalyzesLoading: true,
    });
    return this.equipmentService.equipmentSosAnalyzes(serialNumber).pipe(
      tap((value) => {
        patchState({
          equipmentSosAnalyzes: value,
          equipmentSosAnalyzesLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentSosAnalyzesLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentMDAPmPlannerAction)
  getEquipmentMDAPmPlanner(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: GetEquipmentMDAPmPlannerAction,
  ) {
    patchState({
      EquipmentMDAPmPlannerLoading: true,
    });
    return this.equipmentService.equipmentMDAPmaPlanner(serialNumber).pipe(
      tap((value) => {
        patchState({
          equipmentMDAPmaPlanner: value,
          EquipmentMDAPmPlannerLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          EquipmentMDAPmPlannerLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentDiagnosticDataPagingAction)
  getEquipmentDiagnosticDataAction(
    { getState, patchState }: StateContext<EquipmentStateModel>,
    { equipmentNumber, serialNumber, page, top }: GetEquipmentDiagnosticDataPagingAction,
  ) {
    patchState({
      equipmentDiagnosticDataLoading: true,
    });
    return this.equipmentService.diagData(equipmentNumber, serialNumber, top, page).pipe(
      tap((value: any) => {
        let list = page === 1 ? [] : getState().equipmentDiagnosticDataList;
        list = list.concat(value?.data?.diagDetails || []);
        patchState({
          equipmentDiagnosticDataList: list,
          equipmentDiagnosticDataPaging: value?.paging,
          equipmentDiagnosticDataLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentDiagnosticDataLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentAllDiagnosticDataAction)
  getEquipmentAllDiagnosticDataAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { equipmentNumber, serialNumber }: GetEquipmentAllDiagnosticDataAction,
  ) {
    patchState({
      equipmentDiagnosticDataLoading: true,
    });
    return this.equipmentService.diagData(equipmentNumber, serialNumber).pipe(
      tap((value: any) => {
        patchState({
          equipmentAllDiagnosticData: value?.diagDetails,
          equipmentDiagnosticDataLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentDiagnosticDataLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentInspectionHistoryAction)
  getInspectionHistory(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: GetEquipmentInspectionHistoryAction,
  ) {
    patchState({
      equipmentInspectionHistoryLoading: true,
    });
    return this.equipmentService.inspectionHistory(serialNumber).pipe(
      tap((value) => {
        patchState({
          equipmentInspectionHistory: value,
          equipmentInspectionHistoryLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentInspectionHistoryLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetEquipmentHasPSEAction)
  getEquipmentsPSE(
    { patchState }: StateContext<EquipmentStateModel>,
    { pseRequestId }: GetEquipmentHasPSEAction,
  ) {
    return this.equipmentService.equipmentHasPSE(pseRequestId).pipe(
      tap((value) => {
        patchState({
          equipmentPSE: value,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentPSELoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(PostEquipmentPSEAction)
  postEquipmentsPSE(
    { patchState }: StateContext<EquipmentStateModel>,
    { ProductCode, Quantity, RequestId }: PostEquipmentPSEAction,
  ) {
    patchState({
      equipmentPSELoading: true,
    });
    const body = {
      ProductCode,
      Quantity,
      RequestId
    }
    return this.equipmentService.equipmentAddPSEToCart(body).pipe(
      tap((value) => {
        patchState({
          equipmentPSEAddToCartResponse: value,
          equipmentPSELoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          equipmentPSELoading: false,
          equipmentPSEAddToCartResponse: null,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ClearEquipmentPSEAction)
  ClearEquipmentPSEAction(
    { patchState }: StateContext<EquipmentStateModel>,
  ) {
    patchState({
      equipmentPSE: null,
    });
  }
  @Action(EquipmentPSESearchAction)
  EquipmentPSESearchAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { productCode, requestId }: EquipmentPSESearchAction,
  ) {
    const body = {
      productCode,
      requestId
    }
    return this.equipmentService.equipmentSearchPSE(body).pipe(
      tap((value) => {
        patchState({
          PSESearchResult: value
        })
      })
    )
  }

  @Action(EquipmentWorkOrderPlanAction)
  EquipmentWorkOrderPlanAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: EquipmentWorkOrderPlanAction,
  ) {
    return this.equipmentService.equipmentWorkOrderPlan(serialNumber).pipe(
      tap((value) => {
        patchState({
          workOrderPlans: value,
        });
      }),
    );
  }

  @Action(EquipmentCostCategoryListAction)
  equipmentCostCategoryListAction(
    { patchState }: StateContext<EquipmentStateModel>,
  ) {
    patchState({
      equipmentCostCategoryListLoading: true
    });

    return this.equipmentService.getCostCategoryList()
      .pipe(
        tap((res) => {
            patchState({
              equipmentCostCategoryList: res,
              equipmentCostCategoryListLoading: false
            });
          },
        ),
        catchError((err) => {
          patchState({
            equipmentCostCategoryListLoading: false
          });
          return throwError(err);
        }),
      );
  }


  @Action(GetEquipmentCostList)
  getEquipmentCostList(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber, searchText }: GetEquipmentCostList
  ) {
    patchState({
      equipmentCostListLoading: true
    });

    return this.equipmentService.getEquipmentCostList({ serialNumber, searchText })
      .pipe(
        tap((res) => {
            patchState({
              equipmentCostList: res,
              equipmentCostListLoading: false
            });
          },
        ),
        catchError((err) => {
          patchState({
            equipmentCostListLoading: false
          });
          return throwError(err);
        }),
      );
  }

  @Action(ResetEquipmentCostListAction)
  resetEquipmentCostList(
    { patchState }: StateContext<EquipmentStateModel>,
  ){
    patchState({
      equipmentCostList: [],
    });
  }

  @Action(GetEquipmentQuotationsAction)
  getEquipmentQuotationsAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { serialNumber }: GetEquipmentQuotationsAction
  ) {
    patchState({
      equipmentQuotationsLoading: true
    });

    return this.equipmentService.getEquipmentQuotation(serialNumber)
      .pipe(
        tap((res) => {
            patchState({
              equipmentQuotations: res,
              equipmentQuotationsLoading: false
            });
          },
        ),
        catchError((err) => {
          patchState({
            equipmentQuotationsLoading: false
          });
          return throwError(err);
        }),
      );
  }

  @Action(GetEquipmentServiceHistoryAction)
  getEquipmentServiceHistoryAction(
    { patchState }: StateContext<EquipmentStateModel>,
    { equipmentNumber }: GetEquipmentServiceHistoryAction
  ) {
    patchState({
      equipmentServiceHistoryLoading: true
    });

    return this.equipmentService.getEquipmentServiceHistory(equipmentNumber)
      .pipe(
        tap((res) => {
            patchState({
              equipmentServiceHistory: res,
              equipmentServiceHistoryLoading: false
            });
          },
        ),
        catchError((err) => {
          patchState({
            equipmentServiceHistoryLoading: false
          });
          return throwError(err);
        }),
      );
  }
}
