import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { LoyalityAction } from '../action/loyality.action';
import { LoyalityService } from '../service/loyality.service';

export interface LoyalityModal {
  url: string;
  token: string;
  loading: boolean;
}

@State<LoyalityModal>({
  name: 'loyality',
  defaults: {
    url: '',
    token: '',
    loading: false,
  },
})
@Injectable()
export class LoyalityState {
  constructor(private readonly loyalityService: LoyalityService) {}

  @Selector()
  public static getLoading({ loading }: LoyalityModal): boolean {
    return loading;
  }

  @Selector()
  public static getRedirectParameters({ url, token }: LoyalityModal): any {
    return {
      url,
      token,
    };
  }

  @Action(LoyalityAction)
  getJwtToken({ patchState }: StateContext<LoyalityModal>) {
    patchState({ loading: true });
    return this.loyalityService.getLoyalityJwtToken().pipe(
      tap((response) => {
        patchState({
          //loading: false,
          url: response?.data?.Url,
          token: response?.data?.Token,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }
}
