export interface WorkOrderModel {
  workOrderNumber: string;
  currentStatus: number;
  currentStatusName: string;
  areaInfos: AreaInfo[];
  // "startDate": "2020-12-18T08:00:00",
  // "endDate": "2020-12-18T17:00:00",
  attachments: WorkOrderAttachmentsModel[];
}

export interface AreaInfo {
  areaId: number;
  areaName: string;
  activeCameras: string[];
}

export interface WorkOrderAttachmentsModel {
  base64EncodedContent: any;
  dateUpload: string;
  extension: string;
  id: string;
  name: string;
  type: number;
  uploadType: number;
  uploadTypeDescription: string;
}
