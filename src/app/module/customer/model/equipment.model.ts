import { OfferModel } from './offer.model';

export interface EquipmentModel {
  id: string;
  productId: string;
  productCode: string;
  newLocationInfo: { latitude: number, longitude: number };
  make: string;
  applicationCode: string;
  yearOfManufacture: string;
  diagnosticData: DiagnosticData[];
  hasDiagnosticData?: any;
  fuel: string;
  fuelText: string;
  division: string;
  displayModel: string;
  empc: string;
  equipmentType: string;
  pwc: string;
  pwcText: string;
  emc: string;
  engineModel: string;
  territory: string;
  dueDate: string;
  arrangementNumber: string;
  transactionStoreCode: string;
  addressId: string;
  workingHours: number;
  serialNumber: string;
  revisionIndicator: string;
  revisionIndicatorDescriptions: any;
  isServiceCreateAvailable: boolean;
  material: string;
  mainParty: string;
  counter: string;
  name: string;
  base64EncodedContent: string;
  customerId: string;
  engineSerialNumber: string;
  readingUnit: string;
  readingDate: string;
  openZMDV: number;
  completedZMDV: number;
  guaranteeEndDate: string;
  mda: string;
  hasPseCampaign: boolean;
  isCmPse: boolean;
  cmPseMessages: {
    en: string,
    tr: string
  };
  pseRequestId: string;
  brand: string;
  equipmentNumber: string;
  lastProductLinkDetail: {
    date: Date | string;
    latitude: string;
    longitude: string;
    serialNumber: string;
    time: string;
    value: string
  };
  location: {
    locationName: string;
    latitude: number | null;
    longtitude: number | null;
  };
  model: string;
  serviceInfo: {
    startDate: Date;
    serviceStatus: string;
    serviceType: string;
    workingType: string;
    description: string;
  };
  serviceHistory: {
    startDate: string;
    serviceStatus: string;
    serviceType: string;
    workingType: string;
    description: string;
    equipmentGuid: string;
    serviceNumber: string;
    serviceStatusColor: string;
    serviceStatusDescription: string;
    equipmentNumber: string;
    isCrc: boolean;
    serviceOrganization: string;
  }[];
  pssrList: PssrListModel[];
  workingHourDate: Date;
  workingHoursUnit: null;
  productHierarchy: string;

  city: string;
  region: string;
  district: string;
  isProductLink: boolean;
  productLinkColor: string;
  isSoundDiagEnabled: boolean;
  quotations: OfferModel[];

  equipmentRevisionCampaign: EquipmentRevisionCampaign;
  warranties?: WarrantiesModel[];
}

export interface DiagnosticData {
  dCode: string;
  messageId: string;
  masterMsgId: string;
  moduleCode: string;
  moduleTime: string;
  receivedTime: string;
  owner: string;
  numberOfDnostic: string;
  serialNumber: string;
  make: string;
  model: string;
  nickName: string;
  equipmentVin: string;
  level: string;
  mid: string;
  spn: string;
  fmi: string;
  occurances: string;
  timeStamp: string;
  cId: string;
  typeId: string;
  description: string;
  date: string;
  time: null;
  cnt: number;
  serviceId: string;
  messageTmsp: string;
}

export interface PssrListModel {
  telephoneList: TelephoneList[];
  mailList: MailList[];
  pssrName: string;
  pssrNumber: string;
}

export interface TelephoneList {
  customerNumber: string;
  telephoneNumber: string;
  adressNumber: string;
  telephoneNumberShort: string;
  telephoneNumberExtension: string;
}

export interface MailList {
  addressNumber: string;
  customerNumber: string;
  mailAdress: string;
}

export interface EquipmentSosAnalyzesModel {
  analyzeDocumentNumber: string;
  compName: string;
  compDescription: string;
  analyzeDate: string;
  analyzePdf: boolean;
}

export interface EquipmentInspectionModel {
  dCode: string;
  finishedDate: string;
  formName: string;
  inspectionNumber: string;
  model: string;
  overallResponse: string;
  status: string;
  typeName: string;
}

export interface SosAnalyzePdfModel {
  base64Pdf: string;
  compartmentSampleList: SosCompartmentSampleList[];
}

export interface CatInspectionPdfModel {
  base64Pdf: string;
  equipmentInspectionList: EquipmentInspectionModel[];
}

export interface SosCompartmentSampleList {
  level: string;
  overallInterpretation: string;
  serialNumber: string;
  compartmentName: string;
  compartmentDescription: string;
  controlNumber: string;
  sampleDate: Date;
  analysisDate: Date;
}

export interface EquipmentRevisionCampaign {
  id: string;
  description: string;
  amount?: number;
  currency?: string;
  restOfRevisionIndicatorDate?: number;
  title: string;
  type: string;
}

export interface EquipmentMDAPmPlannerModel {
  contractNumber: string;
  workOrderNumber: string;
  workOrderStartDate: string;
  workOrderPlanDate: string;
  contractStatusText: string;
  workOrderStatusText: string;
  workOrderStatus: MDAWorkOrderStatusEnum | string;
  plannedHour: string;
  order: number;
  nextProcess: string;
  isNextProcess: boolean;
  maintenanceHour: string;
  maintenanceDate: string;
}

export enum MDAWorkOrderStatusEnum {
  open, cancel, estimate, closed
}

export interface WarrantiesModel {
  warranty: string;
  warrantyDescription: string;
  startDate: string;
  endDate: string;
}

export interface EquipmentLocationModel {
  serialNumber: string;
  model: string;
  country: string;
  longitude: string;
  latitude: string;
}

export interface workOrderPlanModel {
  workorderNumber: string;
  startDate: string;
  endDate: string;
  status: string;
  statusDescription: string;
  plate: string;
}

export enum EquipmentAgendaEnum {
  All = 0,
  Sos = 1,
  CheckIn = 2,
  CheckOut = 3,
  Note = 4,
  CatInspection = 5,
  Diagnostic = 6,
  PMPlanner = 7,
  Service = 8,
  Inspection = 9,
}

export interface CostCategoryListModel {
  categoryTag: string;
  localizedCategoryName: string;
}

export interface AddCostFormBodyModel {
  serialNumber: string;
  cost: string;
  description: string;
  category: string;
  currency: string;
}

export interface CostListModel {
  id: string;
  serialNumber: string;
  cost: number;
  currency: string;
  description: string;
  category: string;
  isDeletable: boolean;
}

export interface EquipmentQuotationModel {
  guid: string;
  quotationNumber: string;
  paymentTerm: string;
  paymentMethod: string;
  equipmentSerialNumber: string;
  quotationValidDate: string;
  totalAmount: number;
  taxAmount: number;
  netAmount: number;
  currency: string;
  isApproved: any;
  status: any;
  paymentStatus: any;
  paymentResultMessage: any;
  responsibleName: string;
  responsibleEmail: string;
  pssrEmail: string;
  paymentMethods: PaymentMethod[];
  processType: string;
  processTypeDescription: string;
  orderGuid: string;
  orderObjectId: string;
  orderPostingDate: any;
  orderStatus: string;
  orderStatusText: string;
  actions: string[];
}

export interface PaymentMethod {
  type: number;
  name: string;
  isDefault: boolean;
}

export interface EquipmentServiceHistoryModel {
  equipmentGuid: string;
  equipmentNumber: string;
  serviceNumber: string;
  startDate: string;
  serviceStatus: string;
  serviceStatusColor: string;
  serviceStatusDescription: string;
  serviceOrganization: string;
  serviceType: string;
  workingType: string;
  description: string;
  isCrc: boolean;
}
