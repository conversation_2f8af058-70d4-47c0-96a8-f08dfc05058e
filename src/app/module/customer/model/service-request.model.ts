export interface ServiceDetailedRequestModel {
  equipmentGuid: string;
  serviceNumber: string;
  model?: any;
  equipmentSerialNumber?: any;
  equipmentNumber?: any;
  startDate: string;
  serviceStatus: string;
  serviceType: string;
  serviceTypeDescription: string;
  description: string;
  isCrc: boolean;
  serviceOrganization: string;
  items: ServiceDetailedRequestItems[];
  surveys: Survey;
  workOrderStatus: ServiceDetailedRequestWorkOrderStatus | null;
  serviceStatusColor: string;
  workOrderId: string;
}

export interface ServiceDetailedRequestItems {
  partName: string;
  description: string;
  itemNumber: string;
}

export interface Survey {
  id: string;
  customerNumber: string;
  externalId: string;
  name: string;
  language: string;
  schedulerId: string;
  workOrderNumber: string;
  smsLinkUrl: string;
  linkExpireDateUtc: string;
  smsSentDateUtc: string;
  answerDateUtc: string;
  surveyStatus: number;
}

export interface ServiceDetailedRequestWorkOrderStatus {
  serviceApplication: string;
  workOrderNumber: string;
  currentStatus: number;
  currentStatusName: string;
  startDate: string;
  endDate: string;
  areaInfos: ServiceDetailedRequestAreaInfos[];
  attachments: ServiceDetailedRequestAttachment[];
  servicePlateNumber: string;
  hasArventoNode: boolean;
}

export interface ServiceDetailedRequestAreaInfos {
  areaId: number;
  areaName: string;
  activeCameras: string[];
}

export interface ServiceDetailedRequestAttachment {
  id: string;
  name: string;
  type: number;
  uploadType: number;
  uploadTypeDescription: string;
  extension: string;
  base64EncodedContent?: any;
  dateUpload: string;
}
