export interface FilterFieldsModel extends FilterItemModel {
  fieldLabel?: string;
  fieldType?: string;
  fieldValidations?: {
    regex: string,
    description: string;
  }[];
  fieldValues?: {
    label: string,
    value: any
  }[];
}

export interface FilterSearchModel {
  page?: number;
  top?: number;
  items?: FilterItemModel[];
}

export interface FilterItemModel {
  fieldName: string;
  fieldValue: any;
}
