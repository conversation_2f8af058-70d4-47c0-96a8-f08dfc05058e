export interface FinancialInformationSummaryModel {
  overdueDebtSummary: Row[];
  currentDebtSummary: Row[];
  forwardTermDebtSummary: Row[];
  totalDebtSummary: Row[];
  totalDebtSummaryByCostCenter: CostCenterRow[];
  chequeSummary: Row[];
  bondSummary: Row[];
  advanceSummary: Row[];
}

export interface FinancialModel extends FinancialInformationSummaryModel {

  // overdueDebtDetails: Row[];
  // currentDebtDetails: Row[];
  // forwardTermDebtDetails: Row[];
  // chequeDetails: Row[];
  // bondDetails: Row[];
  // advanceDetails: Row[];
  // [key: string]: Row[];

  companyDetails: CompanyDetails[];

}

export interface Row {
  description: string;
  chequeNumber: string;
  valueDate: Date;
  amount: number;
  documentNumber: string;
  currency: string;
}

export interface CostCenterRow {
  amount: number;
  companyCode: string;
  costCenterCode: string;
  costCenterDescription: string;
  currency: string;
}

export interface CompanyDetails extends FinancialInformationSummaryModel {
  overdueDebtDetails: Row[];
  currentDebtDetails: Row[];
  forwardTermDebtDetails: Row[];
  chequeDetails: Row[];
  bondDetails: Row[];
  advanceDetails: Row[];
  companyName: string;
}

export interface MutabakatlarListModel {
  items: MutabakatListModel[];
}

export interface MutabakatListModel {
  termEndDate: Date;
  status: number;
  accountNumber: string;
  companyCode: string;
  companyName: string;
  formNumber: string;
  guuid: string;
  partnerNumber: string;
  sequenceNumber: string;
  canBeReplied: boolean;
}

export interface MutabakatDetailRequestModel {
  formNumber: string;
  guuid: string;
  partnerNumber?: string;
  sequenceNumber: string;
}

export interface MutabakatReplyRequestModel {
  formNumber: string;
  guuid: string;
  sequenceNumber: string;
  action: string;
  reason?: string;
  partnerNumber: string;
  description?: string;
}

// export interface MutabakatReplyResponseModel {
//   isSuccess?: boolean;
//   errorMessage?: string;
// }

export interface MutabakatDetailModal {
  item: MutabakatDetailItemModal;
  sender: MutabakatDetailSenderModal;
  lines: MutabakatDetailLinesModal[];
  totalLine: MutabakatDetailTotalLineModal;
}

export interface MutabakatDetailItemModal {
  termEndDate: Date;
  status: number;
  accountNumber: string;
  companyCode: string;
  companyName: string;
  formNumber: string;
  guuid: string;
  partnerNumber: string;
  sequenceNumber: string;
  canBeReplied: boolean;
}

export interface MutabakatDetailSenderModal {
  name: string;
  email: string;
  phone: string;
  fax: string;
  companyName: string;
  address: string;
  taxOffice: string;
  taxNo: string;
}

export interface MutabakatDetailLinesModal {
  amount: number;
  amountCurrency: string;
  amountInTRY: number;
  debitCredit: number;
}

export interface MutabakatDetailTotalLineModal {
  amountInTRY: number;
}

export interface MutabakatReplyReasonListModel {
  text: string;
  value: string;
  needDescription: boolean;
}
