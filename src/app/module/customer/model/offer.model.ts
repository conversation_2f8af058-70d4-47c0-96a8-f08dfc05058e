export interface OfferModel {
  // TODO OLD model
  // offerNumber: string;
  // category: string;
  // date: string;
  // price: number;
  // currency: string;
  // status: string;
  // count: number;
  // processType: string;
  // processTypeDesc: string;
  // employeeResp: string;
  // employeeRespNo: string;
  // items: OfferItemModel[];

  // new
  // quotationNumber: string;
  // quotationType: string;
  // quotationValidDate: Date;
  // fileContent: string;
  // totalAmount: number;

  actions: string[];
  guid: string;
  quotationNumber: string;
  paymentTerm: string;
  paymentMethod: string;
  equipmentSerialNumber: string;
  quotationValidDate: string;
  totalAmount: number;
  cc_TotalAmount: number;
  cc_TaxAmount: number;
  cc_NetAmount: number;
  currency: string;
  isApproved: boolean;
  status: string;
  paymentStatus: string;
  paymentResultMessage: string;
  paymentMethods: PaymentMethodModel[];
  processType: any;
  processTypeDescription: string;
  postingDate: string;
}

// TODO OLD model
// export interface OfferItemModel {
//   itemNumber: string;
//   product: string;
//   productDescription: string;
//   createdAt: number;
//   changedAt: number;
// }

export interface PaymentMethodModel {
  type: number;
  name: string;
  isDefault: boolean;
}

export interface CreateOrderModel {
  traceId: string;
  url?: string;
  errorMessage: string;
  paymentTransactionStatus: number;
  paymentProviderType: number;
  data?: CreateOrderModelData;
}

export interface CreateOrderModelData {
  invoiceId: string;
  backLink: string;
  failureBackLink: string;
  postLink: string;
  failurePostLink: string;
  language: string;
  description: string;
  accountId: string;
  terminal: string;
  amount: string;
  currency: string;
  phone: string;
  email: string;
  cardSave: string;
  auth: {
    access_token: string;
    expires_in: string;
    refresh_token: string;
    scope: string;
    token_type: string;
  };
}

export interface PaymentStatusModel {
  paymentTransactionStatus: number;
  paymentTransactionStatusText: string; // Success,Error,Warning
  url: string;
  orderId: any;
  errorMessage: string;
}

export interface OfferDetailModel extends OfferModel {
  address: Address;
  items: any[];
  orderGuid: string;
  orderObjectId: string;
  orderPostingDate: string;
  orderStatus: string;
  orderStatusText: string;
  responsibleEmail: string;
  responsibleName: string;
  isShowDistanceSaleAgreement: boolean;
  isShowPreInformAgreement: boolean;
}

export interface Address {
  addressName: string;
  postalCode: string;
  cityCode: string;
  city: string;
  district: string;
  region: string;
  country: string;
  address: string;
  email: string;
}
