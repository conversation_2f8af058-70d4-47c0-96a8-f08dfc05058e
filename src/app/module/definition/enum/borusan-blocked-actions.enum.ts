export enum BorusanBlockedActionsEnum {
  SubmitServiceRequestForm = 'SubmitServiceRequestForm',
  SubmitMdaRequestForm = 'SubmitMdaRequestForm',
  SubmitPartRequestForm = 'SubmitPartRequestForm',
  SubmitCallRequestForm = 'SubmitCallRequestForm',
  SubmitWriteUsForm = 'SubmitWriteUsForm',
  SubmitQuotationForm = 'SubmitQuotationForm',
  SubmitCreateEquipmentForm = 'SubmitCreateEquipmentForm',
  SubmitDeleteEquipmentForm = 'SubmitDeleteEquipmentForm',
  SubmitUpdateEquipmentForm = 'SubmitUpdateEquipmentForm',
  SubmitMobileSupportForm = 'SubmitMobileSupportForm',
  SubmitSellEquipmentForm = 'SubmitSellEquipmentForm',
  SubmitDeleteAccountForm = 'SubmitDeleteAccountForm',
  SubmitSitechQuotationForm = 'SubmitSitechQuotationForm',
  SubmitPowerSystemQuotationForm = 'SubmitPowerSystemQuotationForm',
  SubmitEquipmentQuotationForm = 'SubmitEquipmentQuotationForm',
  SubmitAttachmentQuotationForm = 'SubmitAttachmentQuotationForm',
  SubmitApprovedQuotationForm = 'SubmitApprovedQuotationForm',
  SubmitRejectedQuotationForm = 'SubmitRejectedQuotationForm',
  SubmitRevisedQuotationForm = 'SubmitRevisedQuotationForm',
  SubmitRfmsForm = 'SubmitRfmsForm',
  SubmitMediaCenterInformationForm = 'SubmitMediaCenterInformationForm',
  SubmitBorusanUserSoundDiagCollectionForm = 'SubmitBorusanUserSoundDiagCollectionForm',
  SubmitBoomClubForm = 'SubmitBoomClubForm',
  UseSoundCheckFile = 'UseSoundCheckFile',
  UseSoundDiagnosticsCollectionFile = 'UseSoundDiagnosticsCollectionFile',
  ViewReconciliationList = 'ViewReconciliationList',
  ReconciliationStatementInquiry = 'ReconciliationStatementInquiry',
  SubmitReconciliationAnswer = 'SubmitReconciliationAnswer',
  ViewUserManagementList = 'ViewUserManagementList',
  EditUserManagementList = 'EditUserManagementList',
  ApproveQuotation = 'ApproveQuotation',
  RejectQuotation = 'RejectQuotation',
  ReviseQuotation = 'ReviseQuotation',
  CreateOrder = 'CreateOrder',
  TechnicalSupportVideoCall = 'TechnicalSupportVideoCall',
  DigitalBankoVideoCall = 'DigitalBankoVideoCall',
  ViewBoomCoinPage = 'ViewBoomCoinPage',
  UsePromotionPortal = 'UsePromotionPortal',
  ConfirmPhoneNumber = 'ConfirmPhoneNumber',
  UseBoomClub = 'UseBoomClub',
  UseMediaCenter = 'UseMediaCenter',
  UseSitech = 'UseSitech',
  UseBdaha = 'UseBdaha',
  UseRental = 'UseRental',
  UseUsed = 'UseUsed',
  SendFeedback = 'SendFeedback',
  ViewAddresses = 'ViewAddresses',
  AddAccount = 'AddAccount',
  ChangePassword = 'ChangePassword',
}
