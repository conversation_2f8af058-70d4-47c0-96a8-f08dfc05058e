export enum PermissionEnum {
  FileUpload = 'FileUpload',
  Location = 'location',
  AccountManagement = 'AccountManagement',
  BoomClub = 'BoomClub',
  Ecommerce = 'Ecommerce',
  CatalogLeasing = 'Catalog_Leasing',
  DocumentsCatInspections = 'Documents_CatInspections',
  DocumentsSosAnalyzes = 'Documents_SosAnalyzes',
  DocumentsMdaPlan = 'Documents_MDAPlan',
  DocumentsServiceHistory = 'Documents_ServiceHistory',
  DocumentsServiceRequests = 'Documents_ServiceRequests',
  DocumentsInspectionHistory = 'Documents_InspectionHistory',
  EquipmentDetail = 'Equipment_Detail',
  EquipmentAgendaDetails = 'Equipment_Agenda_Details',
  EquipmentAgendaAddNote = 'Equipment_Agenda_AddNote',
  EquipmentAgendaCheckinout = 'Equipment_Agenda_Checkinout',
  EquipmentAgendaSetAlias = 'Equipment_Agenda_SetAlias',
  EquipmentDetailCampaign = 'Equipment_Detail_Campaign',
  EquipmentDetailPse = 'Equipment_Detail_PSE',
  EquipmentDetailCmPse = 'Equipment_Detail_CM_PSE',
  Equipments = 'Equipments',
  EquipmentDetailWarnings = 'Equipment_Detail_Warnings',
  EquipmentDetailSpecialDeals = 'Equipment_Detail_SpecialDeals',
  Financials = 'Financials',
  FinancialReconciliationSend = 'Financial_Reconciliation_Send',
  FinancialReconciliation = 'Financial_Reconciliation',
  MenuBoomCoin = 'Menu_BoomCoin',
  MenuPromotionPortal = 'Menu_PromotionPortal',
  MenuQr = 'Menu_QR',
  MenuSettingsAddresses = 'Menu_Settings_Addresses',
  MenuFeedback = 'Menu_Feedback',
  NotificationsNull = 'Notifications_NULL',
  Quotations = 'Quotations',
  QuotationActions = 'Quotation_Actions',
  RequestsCreateEquipment = 'Requests_CreateEquipment',
  RequestsUpdateEquipment = 'Requests_UpdateEquipment',
  RequestsInspection = 'Requests_Inspection',
  RequestsDeleteEquipment = 'Requests_DeleteEquipment',
  RequestsService = 'Requests_Service',
  RequestsSparePart = 'Requests_SparePart',
  RequestsMda = 'Requests_MDA',
  RequestsRental = 'Requests_Rental',
  RequestsQuotation = 'Requests_Quotation',
  RequestsPromotion = 'Requests_Promotion',
  RequestsApplicationSupport = 'Requests_ApplicationSupport',
  RequestsAttachment = 'Requests_Attachment',
  RequestsUsed = 'Requests_Used',
  RequestsBdaha = 'Requests_Bdaha',
  RequestsSitech = 'Requests_Sitech',
  Services = 'Services',
  ServiceCrcCamera = 'Service_CRCCamera',
  ServiceChat = 'Service_Chat',
  ServiceSurvey = 'Service_Survey',
  ServiceTrackLocation = 'Service_TrackLocation',
  SoundDiagnosticsCheck = 'SoundDiagnostics_Check',
  SoundDiagnosticsFeedback = 'SoundDiagnostics_Feedback',
  VideoCallBanko = 'VideoCall_Banko',
  VideoCallTechnicalSupport = 'VideoCall_TechnicalSupport',
}
