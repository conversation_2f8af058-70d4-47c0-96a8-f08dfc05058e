export interface AgreementModel {
  name: string;
  description: string;
  descParts: AgreementLink[];
  contentType: number;
  selectable: boolean;
  order: number;
  details: AgreementDetailModel[];
  overrideDescriptions: { position: string, description: string }[],
  positions: number[];
  availableCompanies: {
    countryCode: string;
    companyCode: string;
  }[];
}

export interface AgreementDetailModel {
  name: string;
  url: string;
  order: number;
}

export interface AgreementLink {
  text: string;
  linkText: string;
  url: string;
  method?: string;
  params?: string;
}

export interface UserAgreementsListModel {
  name: string;
  url: string;
  approvedVersion: number;
  approveDate: string;
  latestVersion: number;
  reapproveNeeded: boolean;
  position: string;
  isApproved: boolean;
}

export interface QuotationAgreementRequest {
  quotationNumber: string;
  paymentMethod: number;
}
export interface OrderAgreementModel {
  name: string;
  descParts?: AgreementLink[];
  description: string;
  method?: string;
}
