import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import { City } from '../model/city.model';
import { Country } from '../model/country.model';
import {
  AgreementDetailModel,
  AgreementModel,
  QuotationAgreementRequest,
  UserAgreementsListModel
} from '../model/agreement.model';
import { AgreementTypeEnum } from '../enum/agreement-type.enum';
import { WorkingHourControlModel } from '../model/working-hour.model';

@Injectable({
  providedIn: 'root',
})
export class DefinitionService {
  constructor(private readonly http: HttpClient) { }

  countryList(all = false): Observable<Country[]> {
    const url = all
      ? `${environment.api}/geolocation/allcountries`
      : `${environment.api}/geolocation/countries`;

    return this.http.get<HttpResponse<Country[]>>(url).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  cityList(countryId: string): Observable<City[]> {
    return this.http
      .get<HttpResponse<City[]>>(`${environment.api}/geolocation/cities`, {
        params: { countryId },
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  agreement(position: AgreementTypeEnum): Observable<AgreementModel[]> {
    return this.http
      .get<HttpResponse<AgreementModel[]>>(`${environment.api}/agreement/get`, {
        params: { position },
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            val.data.map((item) => {
              const nextIndex = 0;
              const override = item.overrideDescriptions.find(
                (o) => o.position === position
              );
              if (override) {
                item.description = override.description;
              }

              item.descParts = item.description?.split('{').map((str) => {
                const [text, linkText] = str.split('}').reverse();

                let url = linkText ? item?.details[nextIndex]?.url : null;
                if (url && !environment.production) {
                  url = url.replace(
                    'https://prod.borusancat.com/lgnd/api',
                    window.location.origin + '/api'
                  );
                }
                return { linkText, text, url };
              });
            });

            return val.data;
          }
          return null;
        })
      );
  }

  getAgreementContent(url: string): Observable<string> {
    return this.http.get(url, { responseType: 'text' });
  }

  checkWorkingHours(
    serviceOrganization: string
  ): Observable<WorkingHourControlModel> {
    return this.http
      .get<HttpResponse<WorkingHourControlModel>>(
        `${environment.api}/geolocation/checkWorkingHours`,
        {
          params: { serviceOrganization },
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  agreementDetails(agreementName: string): Observable<AgreementDetailModel[]> {
    return this.http
      .get<HttpResponse<AgreementDetailModel[]>>(`${environment.api}/agreement/detail?agreementName=${agreementName}`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  approveUserAgreement(headers): Observable<any> {
    return this.http
      .get<any>(`${environment.api}/agreement/approveagreements`, {
        headers,
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getUserAgreements(position: AgreementTypeEnum): Observable<UserAgreementsListModel[]> {
    return this.http
      .get<HttpResponse<UserAgreementsListModel[]>>(`${environment.api}/agreement/getuseragreements`, {
        params: { position },
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getOrderPreAgreement(params: QuotationAgreementRequest): Observable<string> {
    return this.http
      .post(`${environment.api}/quotation/agreement/orderpreinform`, null, {
        params: params as any,
        responseType: 'text'
      });
  }

  getOrderDistanceAgreement(params: QuotationAgreementRequest): Observable<string> {
    return this.http
      .post(`${environment.api}/quotation/agreement/distancesale`, null, {
        params: params as any,
        responseType: 'text'
      });
  }
}
