import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  ApproveUserAgreementAction,
  GetAllCountryListAction,
  GetCityListAction,
  GetCountryListAction, GetOrderPreAgreementAction,
  GetUserAgreementsAction
} from '../action/definition.actions';
import { City } from '../model/city.model';
import { Country } from '../model/country.model';
import { DefinitionService } from '../service/definition.service';
import { UserAgreementsListModel } from '../model/agreement.model';

export interface DefinitionStateModel {
  countryList: Country[];
  cityList: City[];
  countryListLoading: boolean;
  cityListLoading: boolean;
  userAgreements: UserAgreementsListModel[];
  agreementLoading: boolean;
  getForceApprovalAgreement: UserAgreementsListModel[];
  approveUserAgreementSended: boolean;
  orderPreAgreements: UserAgreementsListModel[];
  orderPreAgreementsLoading: boolean;
}

@State<DefinitionStateModel>({
  name: 'definition',
  defaults: {
    countryList: [],
    cityList: [],
    countryListLoading: false,
    cityListLoading: false,
    userAgreements: [],
    agreementLoading: false,
    getForceApprovalAgreement: null,
    approveUserAgreementSended: null,
    orderPreAgreements: [],
    orderPreAgreementsLoading: false
  },
})
@Injectable()
export class DefinitionState {

  constructor(
    private readonly definitionService: DefinitionService,
  ) {
  }

  @Selector()
  public static allCountryList({ countryList }: DefinitionStateModel): Country[] {
    return countryList;
  }

  @Selector()
  public static countryList({ countryList }: DefinitionStateModel): Country[] {
    return countryList;
  }

  @Selector()
  public static cityList({ cityList }: DefinitionStateModel): City[] {
    return cityList;
  }

  @Selector()
  public static countryListLoading({ countryListLoading }: DefinitionStateModel): boolean {
    return countryListLoading;
  }

  @Selector()
  public static cityListLoading({ cityListLoading }: DefinitionStateModel): boolean {
    return cityListLoading;
  }

  @Selector()
  public static getUserAgreements({ userAgreements }: DefinitionStateModel): UserAgreementsListModel[] {
    return userAgreements;
  }

  @Selector()
  public static getAgreementLoading({ agreementLoading }: DefinitionStateModel): boolean {
    return agreementLoading;
  }

  @Selector()
  public static getForceApprovalAgreement({ getForceApprovalAgreement }: DefinitionStateModel): UserAgreementsListModel[] {
    return getForceApprovalAgreement;
  }

  @Selector()
  public static approveUserAgreementSend({ approveUserAgreementSended }: DefinitionStateModel): boolean {
    return approveUserAgreementSended;
  }

  @Selector()
  public static getOrderPreAgreements({ orderPreAgreements }: DefinitionStateModel): UserAgreementsListModel[] {
    return orderPreAgreements;
  }

  @Selector()
  public static getOrderPreAgreementsLoading({ orderPreAgreementsLoading }: DefinitionStateModel): boolean {
    return orderPreAgreementsLoading;
  }

  @Action(GetCountryListAction)
  getCountryListAction({ patchState }: StateContext<DefinitionStateModel>) {
    patchState({
      countryListLoading: true,
    });
    return this.definitionService.countryList().pipe(
      tap((value) => {
        patchState({
          countryList: value,
          countryListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          countryListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetAllCountryListAction)
  getAllCountryListAction({ patchState }: StateContext<DefinitionStateModel>) {
    patchState({
      countryListLoading: true,
    });
    return this.definitionService.countryList(true).pipe(
      tap((value) => {
        patchState({
          countryList: value,
          countryListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          countryListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetCityListAction)
  getCityListAction({ patchState }: StateContext<DefinitionStateModel>, { countryId }: GetCityListAction) {
    patchState({
      cityListLoading: true,
    });
    return this.definitionService.cityList(countryId).pipe(
      tap((value) => {
        patchState({
          cityList: value,
          cityListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          cityListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetUserAgreementsAction)
  getUserAgreements({ patchState }: StateContext<DefinitionStateModel>, { agreementType }: GetUserAgreementsAction) {
    patchState({
      agreementLoading: true,
    });

    return this.definitionService.getUserAgreements(agreementType)
      .pipe(tap((value) => {
          const forceApprovals: any = value.filter((a) => !a.isApproved || a.reapproveNeeded);
          patchState({
            userAgreements: value,
            agreementLoading: false,
            getForceApprovalAgreement: forceApprovals,
          });
        }),
        catchError((err) => {
          patchState({
            agreementLoading: false,
          });
          return throwError(err);
        }),
      );
  }


  @Action(ApproveUserAgreementAction)
  approveUserAgreementState(
    { patchState }: StateContext<DefinitionStateModel>,
    { header }: ApproveUserAgreementAction
  ) {
    patchState({
      agreementLoading: true,
    });
    return this.definitionService.approveUserAgreement(header)
      .pipe(tap((value) => {
          patchState({
            agreementLoading: false,
            approveUserAgreementSended: value === 'OK',
          });
        }),
        catchError((err) => {
          patchState({
            agreementLoading: false,
          });
          return throwError(err);
        }),
      );
  }

}
