import { AgreementTypeEnum } from '../enum/agreement-type.enum';
import { QuotationAgreementRequest } from '../model/agreement.model';

export class GetCountryListAction {
  public static readonly type = '[Definition] Get Country List';
}

export class GetCityListAction {
  public static readonly type = '[Definition] Get City List';

  constructor(public countryId: string) {
  }
}

export class GetAllCountryListAction {
  public static readonly type = '[Definition] Get All Country List';
}

export class GetUserAgreementsAction {
  public static readonly type = '[Definition] Get Approved Agreements';
  constructor(public agreementType: AgreementTypeEnum) {}
}

export class ApproveUserAgreementAction {
  public static readonly type = '[Definition] Approve User Agreement';
  constructor(public header: any) {}
}

export class GetOrderPreAgreementAction {
  static readonly type = '[Definition] Get Order Pre Agreement';
  constructor(public params: QuotationAgreementRequest) {}
}
