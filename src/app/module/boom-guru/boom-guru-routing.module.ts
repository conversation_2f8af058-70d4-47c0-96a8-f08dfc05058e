import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { BoomGuruLayoutComponent } from './container/boom-guru-layout/boom-guru-layout.component';
import { BoomGuruListComponent } from './container/boom-guru-list/boom-guru-list.component';
import { BoomGuruDetailComponent } from './container/boom-guru-detail/boom-guru-detail.component';
import { BoomGuruInitComponent } from './container/boom-guru-init/boom-guru-init.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'list',
  },
  {
    path: '',
    component: BoomGuruLayoutComponent,
    children: [
      { path: 'init', component: BoomGuruInitComponent },
      { path: 'list', component: BoomGuruListComponent },
      { path: 'detail/:id', component: BoomGuruDetailComponent }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BoomGuruRoutingModule {
}
