import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { MediaModule } from 'src/app/export/media/media.module';
import { SharedModule } from 'src/app/module/shared/shared.module';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { BoomGuruRoutingModule } from './boom-guru-routing.module';
import { BoomGuruLayoutComponent } from './container/boom-guru-layout/boom-guru-layout.component';
import { BoomGuruListComponent } from './container/boom-guru-list/boom-guru-list.component';
import { BoomGuruSubmitComponent } from './container/boom-guru-submit/boom-guru-submit.component';
import { BoomGuruDetailComponent } from './container/boom-guru-detail/boom-guru-detail.component';
import { BoomGuruInitComponent } from './container/boom-guru-init/boom-guru-init.component';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';

@NgModule({
  declarations: [
    BoomGuruLayoutComponent,
    BoomGuruListComponent,
    BoomGuruSubmitComponent,
    BoomGuruDetailComponent,
    BoomGuruInitComponent
  ],
  exports: [
    BoomGuruSubmitComponent
  ],
  imports: [
    CommonModule,
    BoomGuruRoutingModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NgxLoadingModule,
    MediaModule,
    SharedModule,
    NgbTooltipModule,
    UserEventLogModule
  ],
})
export class BoomGuruModule {
  constructor() {
    stopLoadingAnimation();
  }
}
