export interface BoomGuruResponse {
  guruResponses: BoomGuruItemModel[];
  totalCount: number;
}

export interface BoomGuruItemModel {
  guruRequestId: string;
  imageUrl: string;
  thumbnailUrl: string;
  serialNumber: string;
  requestDate: string;
  isInspectionForm: boolean;
  guruAnswer: string;
}

export interface BoomGuruDetailModel {
  guruRequestId: string;
  customerId: string;
  imageUrl: string;
  thumbnailUrl: string;
  serialNumber: string;
  requestDate: string;
  questionDescription: string;
  guruAnswer: string;
  guruFeedback: number;
}

export interface BoomGuruFeedbackRequest {
  GuruRequestId: string;
  Feedback: number; // 1: <PERSON><PERSON>, 2: <PERSON><PERSON><PERSON>, 3: Bil<PERSON>yorum
  FeedbackComment?: string;
}

export interface BoomGuruFeedbackResponse {
  success: boolean;
  message?: string;
}
