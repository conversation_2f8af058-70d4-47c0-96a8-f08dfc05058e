import { Component, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { ModalService } from '../../../shared/service/modal.service';
import { environment } from '../../../../../environments/environment';
import { ModalRef } from '../../../shared/model/modal.model';
import { Router } from '@angular/router';
import { LogService } from '../../../customer/service/log.service';

@Component({
  selector: 'cat-boom-guru-init',
  templateUrl: './boom-guru-init.component.html',
  styleUrls: ['./boom-guru-init.component.scss']
})
export class BoomGuruInitComponent implements OnInit {

  showSubmitModal = false;


  constructor(
    private readonly modalService: ModalService,
    private readonly router: Router,
    private readonly logger: LogService,
  ) { }

  ngOnInit() {
    this.logger.action('BOOM_GURU', 'OPENED', {}).subscribe();
  }

  closeSubmitModal(): void {
    this.showSubmitModal = false;
  }

  onSubmitSuccess(): void {
    this.showSubmitModal = false;

    // Show success message or perform any other actions after successful submission
    this.modalService.successModal({
        message: '_boom_guru_submit_success',
        button: '_ok',
        buttonClick: (modal: ModalRef) => {
          this.navigateToList(true);
          modal.close('');
        },
        translate: true
      }
    );
  }

  navigateToList(refreshIn = false): void {
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'boom-guru',
      'list'
    ], {
      queryParams: refreshIn ? {
        refreshIn: 8000
      } : {}
    });
  }
}
