import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ImageSizeEnum } from 'src/app/export/media/enum/image-size.enum';
import { GetBoomGuruItemsAction } from '../../action/boom-guru.action';
import { BoomGuruItemModel } from '../../model/boom-guru.model';
import { BoomGuruState } from '../../state/boom-guru.state';
import { environment } from '../../../../../environments/environment';
import { ModalService } from '../../../shared/service/modal.service';
import { LogService } from '../../../customer/service/log.service';

@Component({
  selector: 'cat-boom-guru-list',
  templateUrl: './boom-guru-list.component.html',
  styleUrls: ['./boom-guru-list.component.scss']
})
export class BoomGuruListComponent implements OnInit, OnDestroy {
  @Select(BoomGuruState.getItems)
  items$: Observable<BoomGuruItemModel[]>;

  @Select(BoomGuruState.getLoading)
  loading$: Observable<boolean>;

  @Select(BoomGuruState.getTotalCount)
  totalCount$: Observable<number>;

  items: BoomGuruItemModel[] = [];
  totalCount: number = 0;
  imageSizeEnum = ImageSizeEnum;

  showSubmitModal = false;
  selectedSerialNumber = '';

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly modalService: ModalService,
    private readonly logger: LogService,
  ) { }

  ngOnInit(): void {
    this.logger.action('BOOM_GURU', 'LIST_OPENED', {}).subscribe();

    this.store.dispatch(new GetBoomGuruItemsAction());

    this.items$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(items => {
        if (items) {
          this.items = items;
        }
      });

    const refreshIn = this.route.snapshot.queryParams.refreshIn;

    if (refreshIn) {
      timer(parseInt(refreshIn, 10))
        .subscribe(() => {
          console.log('Refreshing...');
          this.store.dispatch(new GetBoomGuruItemsAction());
        });
    }
  }

  doRefresh(): void {
    this.store.dispatch(new GetBoomGuruItemsAction());
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickItem(item: BoomGuruItemModel): void {
    // Navigate to the detail page
    this.router.navigate([
      '/',
      ...environment.rootUrl.split('/'),
      'boom-guru', 'detail', item.guruRequestId
    ]);
  }

  openSubmitModal(): void {
    this.showSubmitModal = true;
  }

  closeSubmitModal(): void {
    this.showSubmitModal = false;
  }

  onSubmitSuccess(): void {
    this.showSubmitModal = false;
    // Refresh the list after successful submission
    this.store.dispatch(new GetBoomGuruItemsAction());
    // Show success message or perform any other actions after successful submission
    this.modalService.successModal({
        message: '_boom_guru_submit_success',
        button: '_ok',
        translate: true
      }
    );
  }


  navigateToBack() {
    window.history.back();
  }

}
