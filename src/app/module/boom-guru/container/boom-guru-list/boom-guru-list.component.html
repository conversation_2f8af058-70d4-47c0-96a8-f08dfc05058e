<cat-pull-to-refresh (refresh)="doRefresh()" [turnBack]="loading$ | async">
  <div class="px-3 py-4">
    <header class="header mb-3 mt-1">
      <div>
        <a (click)="navigateToBack()">
          <i class="icon icon-back"></i>
        </a>
      </div>
      <div class="h5 mb-0 text-center">
        {{ "_boom_guru_list" | translate }}
      </div>
    </header>


    <ng-container *ngIf="items?.length; else emptyList">
      <div
        (click)="onClickItem(item)"
        *ngFor="let item of items" class="boom-guru-item d-flex mb-4 p-2"
        catUserClick
        [section]="'BOOM_GURU'"
        [subsection]="'DETAIL_CLICK'"
        [data]="{guruRequestId: item.guruRequestId}"

      >
        <div class="align-self-center boom-guru-image mr-2">
          <img [src]="item.thumbnailUrl || item.imageUrl" alt="Guru Image">
        </div>

        <div class="boom-guru-item-center">
          <div class="font-weight-bold text-secondary">
            {{ item.serialNumber | serialFormat }}
          </div>
          <div class="font-size-13px text-secondary mb-2">
            {{ item.requestDate | date:'dd.MM.yyyy HH:mm' }}
          </div>
          <div *ngIf="item.guruAnswer" class="font-size-13px text-secondary mt-2"
               [innerHTML]="(item.guruAnswer | slice:0:85) + (item.guruAnswer.length > 100 ? '...' : '') | markdownSmallHtml | safeHtml"
          >
          </div>
          <div *ngIf="item.isInspectionForm" class="badge badge-success">
            {{ "_inspection_form" | translate }}
          </div>
        </div>
        <div class="align-self-center ml-2">
          <i class="icon icon-chevron-right text-secondary"></i>
        </div>
      </div>
    </ng-container>

    <cat-loader [show]="loading$ | async"></cat-loader>
  </div>

  <ng-template #emptyList>
    <div *ngIf="!(loading$ | async)">
      <cat-empty-content [message]="'_boom_guru_list_empty'" [iconName]="'equipment'">
      </cat-empty-content>
    </div>
  </ng-template>
</cat-pull-to-refresh>


<!-- Submit Modal -->
<cat-basic-modal [(status)]="showSubmitModal">
  <cat-boom-guru-submit
    [serialNumber]="selectedSerialNumber"
    (close)="closeSubmitModal()"
    (success)="onSubmitSuccess()">
  </cat-boom-guru-submit>
</cat-basic-modal>
