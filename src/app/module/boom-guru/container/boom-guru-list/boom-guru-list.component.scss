.boom-guru-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .boom-guru-image {
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    img {
      max-height: 100%;
      max-width: 100%;
    }
  }
  &-center{
    flex: 1;
  }
}

.header{
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
}
