<div class="boom-guru-submit p-2 mb-2">
  <div class="h5 mb-4 text-center mx-2">{{ "_ask_boom_guru" | translate }}</div>

  <cat-info-box>
    {{ '_ask_boom_guru_info' | translate }}
  </cat-info-box>

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <!-- Serial Number Input -->
    <div class="form-group" *ngIf="serialNumberRequired && !serialNumber">
      <label for="serialNumber">{{ "_serial_number" | translate }}</label>
      <input
        type="text"
        id="serialNumber"
        class="form-control"
        formControlName="serialNumber"
        [placeholder]="'_enter_serial_number' | translate"
      >
      <div *ngIf="form.get('serialNumber').invalid && form.get('serialNumber').touched" class="text-danger small mt-1">
        {{ "_serial_number_required" | translate }}
      </div>
    </div>

    <!-- Image Upload Area -->
    <div class="image-upload-area mb-4">
      <div *ngIf="!imagePreview"
           class="upload-placeholder d-flex flex-column align-items-center justify-content-center">
        <i class="icon icon-equipment mb-2"></i>
        <div class="text-center">{{ "_upload_guru_instruction" | translate }}</div>
        <input
          type="file"
          id="imageUpload"
          class="d-none"
          accept="image/*"
          (change)="onFileSelected($event)"
        >
        <label for="imageUpload" class="btn btn-sm btn-info mt-3 ">
          {{ "_select_image" | translate }}
        </label>
      </div>

      <div *ngIf="imagePreview" class="image-preview-container">
        <div class="position-relative">
          <img [src]="imagePreview" class="img-fluid rounded" alt="Preview">
          <button
            [disabled]="isSubmitting"
            type="button"
            class="btn btn-sm btn-danger position-absolute remove-image-btn"
            (click)="removeImage()"
          >
            <i class="icon icon-x-bold"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
<!--    <div *ngIf="errorMessage" class="alert alert-danger">-->
<!--      {{ errorMessage }}-->
<!--    </div>-->

    <!-- Submit Button -->
    <div class="d-flex justify-content-between mt-4">
      <div>
        <button
          *ngIf="showCancel"
          type="button"
          class="btn btn-sm btn-secondary min-width-100"
          (click)="onClose()"
        >
          {{ "_cancel" | translate }}
        </button>
      </div>
      <button
        type="submit"
        class="btn btn-sm btn-warning text-white min-width-100"
        [disabled]="form.invalid || !selectedImage || isSubmitting"
        catUserClick
        [section]="'BOOM_GURU'"
        [subsection]="'PHOTO_SEND'"
      >
        <span
          *ngIf="isSubmitting"
          class="spinner-border spinner-border-sm mr-2" role="status"
              aria-hidden="true"></span>
        {{ "_send" | translate }}
      </button>
    </div>
  </form>
</div>
