import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BoomGuruService } from '../../service/boom-guru.service';

@Component({
  selector: 'cat-boom-guru-submit',
  templateUrl: './boom-guru-submit.component.html',
  styleUrls: ['./boom-guru-submit.component.scss']
})
export class BoomGuruSubmitComponent implements OnInit {
  @Input() showCancel = false;
  @Input() serialNumberRequired = false;
  @Input() serialNumber: string;
  @Output() close = new EventEmitter<void>();
  @Output() success = new EventEmitter<void>();

  form: FormGroup;
  selectedImage: File = null;
  imagePreview: string | ArrayBuffer = null;
  isSubmitting = false;
  errorMessage: string = null;

  constructor(
    private fb: FormBuilder,
    private boomGuruService: BoomGuruService
  ) { }

  ngOnInit(): void {
    this.form = this.fb.group({
      serialNumber: [this.serialNumber || '']
    });
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files[0];
    if (file) {
      this.selectedImage = file;

      // Create a preview of the image
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result;
      };
      reader.readAsDataURL(file);
    }
  }

  onSubmit(): void {
    if (this.form.valid && this.selectedImage) {
      this.isSubmitting = true;
      this.errorMessage = null;

      const serialNumber = this.form.get('serialNumber').value;

      this.boomGuruService.submitImage(this.selectedImage, serialNumber)
        .subscribe(
          () => {
            this.isSubmitting = false;
            this.success.emit();
            this.resetStatus();
          },
          error => {
            this.isSubmitting = false;
            this.errorMessage = error.message || 'An error occurred while submitting the image.';
          }
        );
    }
  }

  onClose(): void {
    this.close.emit();
  }

  removeImage(): void {
    this.selectedImage = null;
    this.imagePreview = null;
  }

  private resetStatus() {
    this.form.reset();
    this.selectedImage = null;
    this.imagePreview = null;
  }
}
