.boom-guru-detail {
  max-width: 800px;
  margin: 0 auto;
}

.image-container {
  img {
    max-height: 200px;
    object-fit: contain;
  }
}
.image-container-full {
  img {
    max-height: 600px;
  }
}

.question-container {
  .question-text {
    font-size: 13px;
    font-weight: 500;
    color: #333;
  }
}
.disclaimer-text {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.answer-section {
  .answer-title {
    font-size: 15px;
    font-weight: 500;
    color: #555;
  }
}

.chat-bubble {
  background-color: #f1f0f0;
  border-radius: 18px;
  padding: 12px 18px;
  max-width: 100%;
  margin-bottom: 10px;
  position: relative;

  .chat-text {
    font-size: 16px;
    color: #333;
    line-height: 1.5;

    // Markdown styling
    h1, h2, h3 {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
      font-weight: 600;
    }

    h1 {
      font-size: 1.5em;
    }

    h2 {
      font-size: 1.3em;
    }

    h3 {
      font-size: 1.1em;
    }

    p {
      margin-bottom: 0.75em;
    }

    ul {
      padding-left: 20px;
      margin-bottom: 0.75em;
    }

    li {
      margin-bottom: 0.25em;
    }

    a {
      color: #007bff;
      text-decoration: underline;
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }
  }

  &:after {
    content: '';
    position: absolute;
    left: -10px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #f1f0f0;
  }
}

.feedback-section {
  //border-top: 1px solid #e9ecef;
  padding-top: 16px;
  margin-top: 16px;

  .feedback-question {
    .feedback-title {
      color: #6c757d;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .feedback-options {
    gap: 12px;
  }

  .feedback-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 8px;
    border-radius: 8px;

    &:hover {
      background-color: #f8f9fa;
      transform: scale(1.05);
    }

    &.selected {
      background-color: #ececeb;
      transform: scale(1.1);
      border: 1px solid #c9c9c9;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }

    .feedback-emoji {
      font-size: 20px;
      margin-bottom: 4px;
      line-height: 1;
    }

    .feedback-label {
      font-size: 11px;
      font-weight: 500;
      color: #6c757d;
      text-align: center;
      white-space: nowrap;
    }

    &.selected .feedback-label {
      //color: #0d6efd;
      font-weight: 600;
    }
  }
}

.feedback-success {
  .success-message {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #d1edff;
    border-radius: 20px;
    font-size: 14px;
    //color: #0c63e4;
    font-weight: 500;

    .success-emoji {
      font-size: 16px;
    }

    .success-text {
      line-height: 1;
    }
  }
}
.detail-info {
  text-align: right;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}
