<div class="px-3 py-4">
  <div class="h4 py-2 mb-3 text-center nav-back">
    <i class="icon icon-back mr-2 float-left" (click)="goBack()"></i>
    {{ "_boom_guru_detail" | translate }}
  </div>

  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div *ngIf="error && !loading" class="alert alert-danger">
    {{ error }}
  </div>

  <div *ngIf="detail && !loading" class="boom-guru-detail">
    <div class="detail-info text-muted mt-1 ">
        <span class="font-weight-bold " *ngIf="detail.serialNumber">
          {{ detail.serialNumber | serialFormat }}
        </span>
      {{ detail.requestDate | date:'dd.MM.yyyy HH:mm' }}
    </div>
    <!-- Image Section -->
    <div class="image-container text-center mb-2"
     [class.image-container-full]="showFullImage"
         (click)="showFullImage = !showFullImage"
    >
      <img [src]="detail.imageUrl" class="img-fluid rounded" alt="Guru Image">
    </div>

    <!-- Question Section -->
    <div class="question-container text-center mb-3">
      <div class="question-text text-muted ">
        {{ detail.questionDescription }}
      </div>
    </div>


    <!-- Feedback Section -->
    <div class="feedback-section mt-2" *ngIf="!feedbackSubmitted">
      <div class="feedback-question text-center mb-2">
        <span class="feedback-title">{{ "_feedback_question" | translate }}</span>
      </div>
      <div class="feedback-options d-flex justify-content-center align-items-center">
        <div class="feedback-option"
             [class.selected]="selectedFeedback === 1"
             [class.disabled]="feedbackLoading"
             (click)="submitFeedback(1)">
          <div class="feedback-emoji">👍</div>
          <div class="feedback-label">{{ "_yes" | translate }}</div>
        </div>

        <div class="feedback-option"
             [class.selected]="selectedFeedback === 2"
             [class.disabled]="feedbackLoading"
             (click)="submitFeedback(2)">
          <div class="feedback-emoji">👎</div>
          <div class="feedback-label">{{ "_no" | translate }}</div>
        </div>
        <div class="feedback-option"
             [class.selected]="selectedFeedback === 3"
             [class.disabled]="feedbackLoading"
             (click)="submitFeedback(3)">
          <div class="feedback-emoji">😐</div>
          <div class="feedback-label">{{ "_i_dont_know" | translate }}</div>
        </div>
      </div>

    </div>

    <!-- Feedback Success Message -->
    <div class="feedback-success text-center mt-3 mb-2" *ngIf="feedbackSubmitted">
      <div class="success-message">
        <span class="success-emoji">✅</span>
        <span class="success-text">{{ "_feedback_success" | translate }}</span>
      </div>
    </div>

    <!-- Answer Section -->
    <div class="answer-section">
      <div class="answer-title mb-3">
        {{ "_guru_answer" | translate }}:
      </div>
      <div class="chat-bubble">
        <div class="chat-text" [innerHTML]="guruAnswerHtml | safeHtml">
        </div>
      </div>
      <div class="disclaimer-text text-muted ">
        {{ '_boom_guru_disclaimer' |translate }}
      </div>
    </div>

  </div>
</div>
