import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BoomGuruDetailModel, BoomGuruFeedbackRequest } from '../../model/boom-guru.model';
import { BoomGuruService } from '../../service/boom-guru.service';
import { convertMarkdownToHtml } from '../../../../util/mardown-to-html.util';

@Component({
  selector: 'cat-boom-guru-detail',
  templateUrl: './boom-guru-detail.component.html',
  styleUrls: ['./boom-guru-detail.component.scss']
})
export class BoomGuruDetailComponent implements OnInit, OnDestroy {
  guruRequestId: string;
  detail: BoomGuruDetailModel;
  loading = true;
  error: string = null;
  guruAnswerHtml: string;

  feedbackSubmitted = false;
  feedbackLoading = false;
  selectedFeedback: number | null = null;

  showFullImage = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private boomGuruService: BoomGuruService
  ) { }

  ngOnInit(): void {
    this.guruRequestId = this.route.snapshot.paramMap.get('id');

    if (this.guruRequestId) {
      this.loadDetail();
    } else {
      this.error = 'Invalid Guru Request ID';
      this.loading = false;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  loadDetail(loading = true): void {
    this.loading = loading;
    this.error = null;

    this.boomGuruService.getDetail(this.guruRequestId)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        detail => {
          this.detail = detail;
          if (detail.guruAnswer) {
            this.guruAnswerHtml = convertMarkdownToHtml(detail.guruAnswer);
          }
          this.loading = false;
          this.selectedFeedback = detail.guruFeedback;
        },
        error => {
          this.error = error.message || 'Failed to load detail';
          this.loading = false;
        }
      );
  }


  goBack(): void {
    window.history.back();
  }

  submitFeedback(feedbackValue: number): void {
    if (this.feedbackSubmitted || this.feedbackLoading || !this.guruRequestId) {
      return;
    }

    this.feedbackLoading = true;
    this.selectedFeedback = feedbackValue;

    const feedbackRequest: BoomGuruFeedbackRequest = {
      GuruRequestId: this.guruRequestId,
      Feedback: feedbackValue,
      FeedbackComment: ''
    };

    this.boomGuruService.submitFeedback(feedbackRequest)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        response => {
          this.feedbackSubmitted = true;
          this.loadDetail(false);
          timer(2000).subscribe(() => this.feedbackSubmitted = false);
          this.feedbackLoading = false;
        },
        error => {
          this.feedbackLoading = false;
          this.selectedFeedback = null;
          console.error('Feedback submission failed:', error);
        }
      );
  }
}
