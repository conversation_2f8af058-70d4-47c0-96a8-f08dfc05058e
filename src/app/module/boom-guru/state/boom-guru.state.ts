import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ClearBoomGuruItemsAction, GetBoomGuruItemsAction } from '../action/boom-guru.action';
import { BoomGuruItemModel, BoomGuruResponse } from '../model/boom-guru.model';
import { BoomGuruService } from '../service/boom-guru.service';

export interface BoomGuruStateModel {
  items: BoomGuruItemModel[];
  loading: boolean;
  totalCount: number;
}

@State<BoomGuruStateModel>({
  name: 'boomGuru',
  defaults: {
    items: [],
    loading: false,
    totalCount: 0
  }
})
@Injectable()
export class BoomGuruState {
  constructor(
    private readonly boomGuruService: BoomGuruService
  ) { }

  @Selector()
  static getItems(state: BoomGuruStateModel): BoomGuruItemModel[] {
    return state.items;
  }

  @Selector()
  static getLoading(state: BoomGuruStateModel): boolean {
    return state.loading;
  }

  @Selector()
  static getTotalCount(state: BoomGuruStateModel): number {
    return state.totalCount;
  }

  @Action(GetBoomGuruItemsAction)
  getItems(
    { patchState }: StateContext<BoomGuruStateModel>
  ) {
    patchState({
      loading: true
    });
    return this.boomGuruService.getItems().pipe(
      tap((response) => {
        patchState({
          loading: false,
          items: response.guruResponses,
          totalCount: response.totalCount
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(ClearBoomGuruItemsAction)
  clearItems(
    { patchState }: StateContext<BoomGuruStateModel>,
  ) {
    patchState({
      items: [],
      totalCount: 0
    });
  }
}
