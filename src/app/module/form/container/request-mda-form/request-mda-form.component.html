<div class="px-4 pb-5 tablet-form-container" [class.pt-4]="!showHeader">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="showHeader">
    <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
    {{ "_create_mda_request" | translate }}
  </div>
  <div class="text-warning ml-1" *ngIf="form.value.EquipmentSerialNumber !== null && hasPmPlanner && !mdaInfo">
    <cat-warning-box [title]="'_warning' | translate"> {{ '_pm_planner_already_exist_warning' | translate }} </cat-warning-box>
  </div>
  <div class="" *ngIf="mdaInfo">
    <div
      class="mx-2 font-size-14px"
      [innerHTML]="getMDAText() | translate"
    ></div>
    <div class="d-flex justify-content-center">
      <a
        class="min-width-200 btn mt-2 font-weight-semi-bold btn-warning btn-gradient text-white shadow rounded-lg mx-auto"
        [routerLink]="'./'"
        [queryParams]="{ mdaInfo: 0 }"
      >
        {{ "_get_offer" | translate }}
      </a>
    </div>
  </div>

  <form
    *ngIf="!formPreviewStatus && !mdaInfo"
    (submit)="onSubmitForm()"
    [formGroup]="form"
    class="tablet-form"
  >
    <div *ngIf="!user">
      <div class="form-group">
        <input
          catInputLength
          [name]="'Name'"
          [placeholder]="'_name' | translate"
          class="form-control form-control"
          formControlName="Name"
          type="text"
          minlength="3"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Name) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          catInputLength
          [name]="'Surname'"
          [placeholder]="'_surname' | translate"
          class="form-control form-control"
          formControlName="Surname"
          type="text"
          minlength="2"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Surname) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          catInputLength
          [name]="'Email'"
          [placeholder]="'_email' | translate"
          class="form-control form-control"
          formControlName="Email"
          type="email"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Email) | translate }}
        </div>
      </div>
      <div class="mx-n4"></div>
    </div>

    <div class="form-group">
      <input
        catInputLength
        [name]="'Phone'"
        [placeholder]="'_company_phone_number' | translate"
        class="form-control form-control"
        formControlName="CompanyPhoneNumber"
        type="tel"
        (input)="onInputPhone($event)"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyPhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyPhoneNumber) | translate }}
      </div>
    </div>

    <div *ngIf="!companyName" class="form-group">
      <input
        catInputLength
        [name]="'CompanyName'"
        [placeholder]="'_company_name' | translate"
        class="form-control form-control"
        formControlName="CompanyName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>

    <div *ngIf="!equipmentSerialNumber" class="form-group">
      <cat-machine-serial [form]="form" [fieldName]="'EquipmentSerialNumber'" (selectEquipment)="onChangeSerial($event)">
      </cat-machine-serial>
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.EquipmentSerialNumber)
        }"
        class="invalid-feedback pl-3"
      >
        {{
          getFormErrorMessage(form.controls.EquipmentSerialNumber) | translate
        }}
      </div>
    </div>
    <div class="country-label">
      <label>{{ "_select_country_and_city" | translate }}</label>
    </div>
    <div class="form-group">
      <!--      <cat-form-label [label]="'_equipment_location' | translate"></cat-form-label>-->
      <ng-select
        [searchable]="true"
        (change)="onChangeCountry($event)"
        *ngIf="!(countryListLoading$ | async)"
        [placeholder]="'_country' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="false"
        formControlName="CountryCode"
        #country
      >
        <ng-option
          *ngFor="let country of searchCity(countryList$ | async, country.searchTerm)"
          [value]="country.code"
          >{{ country.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
      </div>
    </div>
    <div
      *ngIf="
        (!!form.controls['CountryCode'].value ||
          (cityList$ | async)?.length > 0) &&
        isActiveCountry(form.controls['CountryCode'].value)
      "
      class="form-group"
    >
      <ng-select
        [searchable]="true"
        *ngIf="!(cityListLoading$ | async)"
        [placeholder]="'_city' | translate"
        [clearable]="false"
        formControlName="City"
        [dropdownPosition]="'bottom'"
        (keypress)="scrollTop()"
        #city
      >
        <ng-option
          *ngFor="let city of searchCity(cityList, city.searchTerm)"
          [value]="city.name"
          >{{ city.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.City) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.City) | translate }}
      </div>
    </div>
    <div class="d-flex" *ngIf="form.value.EquipmentSerialNumber !== null && !hasPmPlanner && form.value.CountryCode === 'TR' && !(equipmentDetailLoading$ | async)">
      <div class="form-group w-50 mx-1">
        <ng-select
        [searchable]="false"
        [placeholder]="'_with_labor_cost' | translate"
        formControlName="LaborCostStatus"
        [dropdownPosition]="'bottom'"
        #LaborCostStatus
      >
        <ng-option
        *ngFor="let labor of RequestMdaFormLaborEnum | enumToArray"
        [value]="labor.value"
          >{{ '_labor.' + labor.value | translate }}</ng-option
        >
      </ng-select>
      <div
      [ngClass]="{ 'd-block': isShowError(form.controls.LaborCostStatus) }"
      class="invalid-feedback pl-3"
    >
      {{ getFormErrorMessage(form.controls.LaborCostStatus) | translate }}
    </div>
      </div>
      <div class="form-group w-50 mx-1" *ngIf="!(equipmentDetailLoading$ | async)">
        <ng-select
        [searchable]="false"
        [placeholder]="'_with_oil_status' | translate"
        formControlName="OilIncludedStatus"
        [dropdownPosition]="'bottom'"
        #OilIncludedStatus
      >
        <ng-option
        *ngFor="let oil of RequestMdaFormOilEnum | enumToArray"
        [value]="oil.value"
          >{{ '_oil_status.' + oil.value | translate }}</ng-option
        >
      </ng-select>
      <div
      [ngClass]="{ 'd-block': isShowError(form.controls.OilIncludedStatus) }"
      class="invalid-feedback pl-3"
    >
      {{ getFormErrorMessage(form.controls.OilIncludedStatus) | translate }}
    </div>
      </div>

    </div>
    <div class="form-group" *ngIf="form.value.EquipmentSerialNumber && !hasPmPlanner && form.value.CountryCode === 'TR' && !(equipmentDetailLoading$ | async)">
      <input
      catInputLength
      [name]="'HourDemand'"
      [placeholder]="'_hour_demand' | translate "
      class="form-control w-100"
      formControlName="HourDemand"
      type="tel"
      (input)="onInputPhone($event)"
    />
    <div
    [ngClass]="{ 'd-block': isShowError(form.controls.HourDemand) }"
    class="invalid-feedback pl-3"
  >
    {{ getFormErrorMessage(form.controls.HourDemand) | translate }}
  </div>
    </div>
    <div class="form-group textarea-form-element-container" *ngIf="!(equipmentDetailLoading$ | async)">
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control textarea-form-element"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <div class="form-group">
      <cat-file-upload
        formControlName="Attachments"
        [id]="attachId"
        [formType]="FormType.MdaRequest"
      ></cat-file-upload>
    </div>

    <div class="mb-3">
      <cat-file-upload-preview
        [deleteButtonStatus]="true"
        [files]="form.get('Attachments').value"
        (deleteFile)="onDeleteAttachmentFile($event)"
      ></cat-file-upload-preview>
    </div>

    <div>
      <cat-agreement-list
        *ngIf="!user"
        [form]="form"
        [formType]="agreementTypeEnum.PublicCVAForm"
      ></cat-agreement-list>
    </div>

    <input
      [value]="'_send' | translate"
      class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
      type="submit"
    />
  </form>
  <cat-loader
    [show]="
      (countryListLoading$ | async) ||
      (cityListLoading$ | async) ||
      (equipmentListLoading$ | async) ||
      (equipmentDetailLoading$ | async)
    "
  ></cat-loader>
</div>
<div *ngIf="formPreviewStatus" [@preview] class="form-preview px-4">
  <div class="h4 py-4 mb-0 nav-back">
    <i (click)="back()" class="icon icon-back mr-2"></i>
    {{ "_mda_request_preview" | translate }}
  </div>

  <div>
    <div class="small mb-3">
      <div>{{ form.value?.Name }} {{ form.value?.Surname }}</div>
      <div>{{ form.value?.Email }}</div>
      <div>{{ form.value?.CompanyPhoneNumber || form.value?.PhoneNumber }}</div>
      <div *ngIf="form.value?.EquipmentSerialNumber" class="mt-3">
        {{ form.value?.EquipmentSerialNumber }}
      </div>

      <div class="mt-3">{{ form.value.CompanyName }}</div>
      <div>{{ getCountryByCode(form.value?.CountryCode).name }}</div>
      <div>{{ form.value?.City }}</div>

      <div class="mt-3 EquipmentLongText">{{ form.value.Description }}</div>
      <div class="mt-3" *ngIf="form.value.LaborCostStatus">{{ ('_labor.' + form.value.LaborCostStatus) | translate }} </div>
      <div class="mt-3" *ngIf="form.value.OilIncludedStatus">{{ ('_oil_status.' + form.value.OilIncludedStatus) | translate }} </div>
      <div class="mt-3" *ngIf="form.value.HourDemand">{{ form.value.HourDemand | translate}}</div>
    </div>
  </div>
  <div class="mb-3">
    <cat-file-upload-preview
      [files]="form.get('Attachments').value"
    ></cat-file-upload-preview>
  </div>
  <button
    catUserClick
    [section]="'MDA_FORM'"
    [subsection]="'CREATE'"
    (click)="sendForm()"
    [disabled]="borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitMdaRequestForm) >= 0"
    class="btn btn-warning btn-gradient btn-block text-white shadow"
  >
    {{ "_confirm" | translate }}
  </button>
</div>
<div *ngIf="formSendStatus" [@preview] class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_successfully_send_form" | translate }}
    </div>
    <!-- <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      *ngIf="navigatedPage !== 'Contact'"
      (click)="navigateToBack()"
    >
      {{ "_back_to_equipment_detail" | translate }}
    </div> -->
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="navigateToBack()"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
<cat-fullscreen-modal *ngIf="modalContent && !user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-fullscreen-modal>
<cat-big-modal *ngIf="modalContent && user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-big-modal>
