import { animate, state, style, transition, trigger, } from '@angular/animations';
import { ChangeDetectorRef, Component, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import snq from 'snq';
import { FileModel, FormType, } from '../../../../export/file-upload/model/file.model';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { validateAllFormFields } from '../../../../util/validate-all-form-fields.util';
import { UserModel } from '../../../authentication/model/user.model';
import { LoginState } from '../../../authentication/state/login/login.state';
import { EquipmentMDAPmPlannerModel, EquipmentModel } from '../../../customer/model/equipment.model';
import { GetAllCountryListAction, GetCityListAction, } from '../../../definition/action/definition.actions';
import { City } from '../../../definition/model/city.model';
import { Country } from '../../../definition/model/country.model';
import { DefinitionState } from '../../../definition/state/definition.state';
import { FormService } from '../../service/form/form.service';
import { PermissionEnum } from '../../../definition/enum/permission.enum';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import uuidv4 from 'src/app/util/uuidv4';
import { CommonState } from '../../../shared/state/common/common.state';
import { CustomerRelationModel } from '../../../shared/model/company.model';
import { map, takeUntil } from 'rxjs/operators';
import { CustomValidator } from '../../../../util/custom-validator';
import { DefinitionService } from 'src/app/module/definition/service/definition.service';
import { AgreementTypeEnum } from 'src/app/module/definition/enum/agreement-type.enum';
import { AgreementModel } from 'src/app/module/definition/model/agreement.model';
import { agreementHeaders } from 'src/app/util/agreement-headers.util';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { GetEquipmentDetailAction, GetEquipmentMDAPmPlannerAction } from 'src/app/module/customer/action/equipment.action';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { RequestMdaFormLaborEnum, RequestMdaFormOilEnum } from '../../enum/request-mda-form.enum';
import { BorusanBlockedActionsEnum } from '../../../definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../shared/state/settings/settings.state';

@Component({
  selector: 'cat-request-mda-form',
  templateUrl: './request-mda-form.component.html',
  styleUrls: [
    './request-mda-form.component.scss',
    '../request-service-form/request-service-form.component.scss',
  ],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class RequestMdaFormComponent implements OnInit, OnDestroy, OnChanges {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(EquipmentState.equipmentMDAPmaPlanner)
  equipmentMDAPmPlanner$: Observable<EquipmentMDAPmPlannerModel[]>;

  @Select(EquipmentState.equipmentDetailLoading)
  equipmentDetailLoading$: Observable<boolean>;

  cityList: City[];
  user: UserModel;
  FormType = FormType;
  attachId = uuidv4();

  equipmentSerialNumber: string;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    CompanyPhoneNumber: new FormControl(null, [Validators.required]),
    PhoneNumber: new FormControl(null, []),
    EquipmentSerialNumber: new FormControl(null, [Validators.required]),
    CountryCode: new FormControl(null, [Validators.required]),
    City: new FormControl(null, [Validators.required]),
    CompanyId: new FormControl(),
    CompanyName: new FormControl(null),
    Description: new FormControl(null, [Validators.required]),
    Attachments: new FormControl([]),
    AttachmentIdList: new FormControl([]),
    permission: new FormControl(),
    LaborCostStatus: new FormControl(null, []),
    OilIncludedStatus: new FormControl(null, []),
    HourDemand: new FormControl(null, [Validators.required, Validators.max(2147483647), Validators.pattern('^[0-9]*$')]),

  });
  companyName: string;
  formPreviewStatus = 0;
  showHeader = true;
  navigatedPage: string;
  formSendStatus: boolean;
  mdaInfo: any = 1;
  agreements: AgreementModel[];
  modalContent: any;
  protected subscriptions$: Subject<boolean> = new Subject();
  private id: any;
  hasPmPlanner: boolean;
  RequestMdaFormLaborEnum = RequestMdaFormLaborEnum;
  RequestMdaFormOilEnum = RequestMdaFormOilEnum;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  agreementTypeEnum = AgreementTypeEnum;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly formService: FormService,
    private readonly messageFrameService: MessageFrameService,
    private readonly ref: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.loadForm();

    this.route.queryParams.subscribe(q => {
      this.mdaInfo = 'mdaInfo' in q ? parseInt(q.mdaInfo, 10) : 1;
      this.formPreviewStatus = 'formPreviewStatus' in q ? parseInt(q.formPreviewStatus, 10) : 0;

      if (!this.mdaInfo) {
        this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
          PermissionEnum.FileUpload,
        ]);
      }
    });

    this.cityList$.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          this.cityList = data;
        }
      );

    this.equipmentMDAPmPlanner$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data?.length) {
          this.hasPmPlanner = !!data?.length;
          if (this.hasPmPlanner) {
            ['LaborCostStatus', 'OilIncludedStatus', 'HourDemand'].map((item) => {
              this.form.controls[item].clearValidators();
              this.form.controls[item].updateValueAndValidity();
            });
          }
        }
        else {
          this.hasPmPlanner = false;
        }
      });

  }

  loadForm() {
    this.store.dispatch(new GetAllCountryListAction());
    this.user = this.store.selectSnapshot(LoginState.user);
    const company = this.store.selectSnapshot(LoginState.company);
    const customer = this.store.selectSnapshot(LoginState.customer);

    this.autoSelectCountry();

    const {
      showHeader,
      navigatedPage,
      ...queryParams
    } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    this.navigatedPage = navigatedPage;
    const patchValues: any = {
      ...queryParams,
    };
    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
      patchValues.Email = this.user.email.toLowerCase();
      patchValues.PhoneNumber = this.user.mobile;
      patchValues.CompanyPhoneNumber = this.user.mobile;
    }

    if (company) {
      patchValues.CompanyId = company.id;
      // patchValues.CompanyName = company.name;
    }
    if (customer) {
      // console.log('customer in mda', customer);
      patchValues.CompanyName = customer.name;
      this.companyName = customer.name;

    }
    const { id } = this.route.snapshot.params;
    this.id = id;
    if (id) {
      this.equipmentDetail$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(
          (equipmentDetail: EquipmentModel) => {
            if (equipmentDetail) {
              this.form.patchValue({
                EquipmentSerialNumber: equipmentDetail.serialNumber,
              });
              this.equipmentSerialNumber = equipmentDetail.serialNumber;
              this.onChangeSerial({ serialNumber: equipmentDetail.serialNumber });
            }
          }
        );
      this.store.dispatch(new GetEquipmentDetailAction(snq(() => id)));
    }
    this.form.patchValue(patchValues);
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  onChangeCountry(countryCode: string) {
    const selectedCountry = this.getCountryByCode(countryCode);
    this.form.patchValue({ EquipmentCity: null });

    this.form.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.form.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.City.clearValidators();
      this.form.controls.City.updateValueAndValidity();
      return of([]);
    }
  }

  onChangeSerial(serialNumber: any) {
    this.store.dispatch(new GetEquipmentMDAPmPlannerAction(serialNumber.serialNumber));
  }

  getCountryByCode(countryCode: string) {
    ['LaborCostStatus', 'OilIncludedStatus'].forEach((item) => {
      if (countryCode === 'TR') {
        if (!this.hasPmPlanner) {
          this.form.controls[item].setValidators([Validators.required]);
        } else {
          this.form.controls[item].setValidators([]);
        }
        this.form.controls[item].setValue(this.form.controls[item].value);
      }
      else {
        this.form.controls[item].setValidators([]);
        this.form.controls[item].setValue(null);
      }
    });
    // HourDemand validasyonları farklı olduğu için ayrı yazıldı.
    if (countryCode === 'TR'){
      if (!this.hasPmPlanner) {
        this.form.controls.HourDemand.setValidators([Validators.required, Validators.max(2147483647), Validators.pattern('^[0-9]*$')]);
      }
      this.form.controls.HourDemand.setValue(this.form.controls.HourDemand.value);
    }
    else {
      this.form.controls.HourDemand.setValidators([]);
      this.form.controls.HourDemand.setValue(null);
    }
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  onSubmitForm() {
    if (this.form.valid) {
      this.openPreviewModal();
    } else {
      validateAllFormFields(this.form);
    }
  }

  sendForm() {
    const { value } = this.form;

    let headers = {};
    if (!this.user) {
      headers = agreementHeaders(this.form.get('agreements').value);
    }


    this.formService
      .sendMDARequest({
        Name: value.Name,
        Surname: value.Surname,
        Email: value.Email.toLowerCase(),
        PhoneNumber: value.PhoneNumber,
        CompanyPhoneNumber: value.CompanyPhoneNumber,
        CompanyId: value.CompanyId,
        EquipmentSerialNumber: value.EquipmentSerialNumber,
        CountryCode: value.CountryCode,
        CompanyName: value.CompanyName,
        City: value.City,
        Description: value.Description,
        AttachmentIdList: this.form.value.Attachments.map((item) => item.id),
        LaborCostStatus: value.LaborCostStatus,
        OilIncludedStatus: value.OilIncludedStatus,
        HourDemand: value.HourDemand,
      }, headers)
      .subscribe(
        () => {
          this.formSendStatus = true;
          window.history.go(-2); // skip prev text page
        },
        () => {
          // this.formSendStatus = true;
          // window.history.go(-2);
        }
      );
  }

  openPreviewModal() {
    this.router.navigate([], {
      queryParams: { formPreviewStatus: 1 },
      queryParamsHandling: 'merge',
    });
    // this.formPreviewStatus = true;
  }

  navigateToBack() {
    if (this.navigatedPage === 'Contact') {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }
    // if public page
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    this.back();
  }

  back() {
    window.history.back();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


  onDeleteAttachmentFile(file: FileModel) {
    this.form.patchValue({
      Attachments: snq(() => this.form.value.Attachments, []).filter(
        (f: FileModel) => !(f.id === file.id)
      ),
    });
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  protected autoSelectCountry() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        if (!currentCustomer) {
          return;
        }
        this.countryList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe((countries) => {
            if (!countries?.length) {
              return;
            }
            let countryCode = currentCustomer?.groupKey;

            this.equipmentDetail$
              .pipe(takeUntil(this.subscriptions$))
              .subscribe((equipment) => {
                if (!equipment) {
                  if (this.id) {
                    return;
                  }
                }
                let found;
                if (equipment?.location?.locationName) {
                  found = countries.find(item => item.name === equipment?.location?.locationName);
                  countryCode = found?.code || countryCode;
                }

                this.form.patchValue({ CountryCode: countryCode });
                this.onChangeCountry(countryCode)
                  .pipe(takeUntil(this.subscriptions$))
                  .pipe(map(() => this.store.selectSnapshot(DefinitionState.cityList)))
                  .subscribe((cities) => {
                    const city = found ? equipment?.city : currentCustomer?.cityName;
                    if (city) {
                      const foundCity = cities?.find(item => item.name.toLowerCase() === city.toLowerCase())
                      if (foundCity) {
                        this.form.patchValue({ City: foundCity.name });
                      }
                    }
                  });
              });
          });
      });
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  showContent(item: any) {
    this.modalContent = item;
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter(d => d.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  getMDAText() {
    let countryCode = 'default';
    const company = this.store.selectSnapshot(LoginState.company);
    const countryCodeMobile = this.store.selectSnapshot(CommonState.countryCode);
    if (company) {
      countryCode = company.countryCode;
    } else if (countryCodeMobile === 'RU') {
      countryCode = 'RU';
      console.log('countryCode: ', countryCodeMobile);
    }
    const mdaText = {
      RU: '_mda_text_ru',
      default: '_mda_text'
    };

    return mdaText[countryCode] ? mdaText[countryCode] : mdaText.default;
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.ref.detectChanges();
  }
}
