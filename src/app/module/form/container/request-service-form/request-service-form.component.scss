.form-preview, .after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
}

.form-preview{
  overflow-y: scroll;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.take-a-photo {
  &-modal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: #FAFAFA;

    &-x {
      position: absolute;
      right: 12px;
      top: 12px;
      z-index: 2;
      text-shadow: 0 0 1px white;

      i {
        font-size: 22px;
      }
    }
  }
}

.ng-select.service-drp ::ng-deep .ng-select-container  {
  border-radius: 6px;
  border: 1px solid #D7E5EA;
}

.success-message{
  font-size: 26px;
  font-weight: 700;
}

.agreement {
  //padding-left: 1.25rem;
  //z-index: 0;
}
.country-label {
  font-size: 12px;
  font-weight: 400;
}

.EquipmentLongText{
  overflow-wrap: anywhere;
}



[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}