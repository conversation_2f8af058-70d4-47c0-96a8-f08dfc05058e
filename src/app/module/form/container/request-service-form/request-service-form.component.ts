import { animate, state, style, transition, trigger, } from '@angular/animations';
import { AfterViewChecked, ChangeDetector<PERSON>ef, Component, HostListener, On<PERSON>estroy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import snq from 'snq';
import { environment } from '../../../../../environments/environment';
import { FileModel, FormType, } from '../../../../export/file-upload/model/file.model';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { UserModel } from '../../../authentication/model/user.model';
import { LoginState } from '../../../authentication/state/login/login.state';
import { EquipmentModel } from '../../../customer/model/equipment.model';
import { GetAllCountryListAction, GetCityListAction, } from '../../../definition/action/definition.actions';
import { EquipmentTypeEnum } from '../../../definition/enum/equipment-type.enum';
import { ServiceCategoryEnum, EquipmentStatusEnum } from '../../../definition/enum/service-category.enum';
import { City } from '../../../definition/model/city.model';
import { Country } from '../../../definition/model/country.model';
import { DefinitionState } from '../../../definition/state/definition.state';
import { FormService } from '../../service/form/form.service';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { PermissionEnum } from '../../../definition/enum/permission.enum';
import { DefinitionService } from '../../../definition/service/definition.service';
import { AgreementModel } from '../../../definition/model/agreement.model';
import { validateAllFormFields } from '../../../../util/validate-all-form-fields.util';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { CustomValidator } from '../../../../util/custom-validator';
import uuidv4 from 'src/app/util/uuidv4';
import { CommonState } from '../../../shared/state/common/common.state';
import { CustomerRelationModel } from '../../../shared/model/company.model';
import { takeUntil } from 'rxjs/operators';
import { agreementHeaders } from '../../../../util/agreement-headers.util';
import { GetBasisSettingsAction, SystemFeatureAction } from 'src/app/module/shared/state/settings/settings.actions';
import { GetEquipmentDetailAction } from 'src/app/module/customer/action/equipment.action';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { FormState } from '../../state/form.state';
import { RequestServiceReservationDayStatusModel } from '../../model/form.modal';
import { FormRequestServiceGetReservationsDatesAction } from '../../action/form.actions';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';
import { BorusanBlockedActionsEnum } from '../../../definition/enum/borusan-blocked-actions.enum';
import { ModalService } from 'src/app/module/shared/service/modal.service';

@Component({
  selector: 'cat-request-service-form',
  templateUrl: './request-service-form.component.html',
  styleUrls: ['./request-service-form.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class RequestServiceFormComponent implements OnInit, OnDestroy, AfterViewChecked {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  EquipmentTypeEnum = EquipmentTypeEnum;
  ServiceCategoryEnum = ServiceCategoryEnum;
  EquipmentStatusEnum = EquipmentStatusEnum;

  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;
  equipmentDetail: EquipmentModel;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  user: UserModel;
  cityList: City[];

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  isReservationsDates = false;

  @Select(FormState.getServiceReservationsDate)
  reservationsDates$: Observable<RequestServiceReservationDayStatusModel[]>;
  reservationsDates: RequestServiceReservationDayStatusModel[];
  editedReservationDates: any = [];
  minReservation: NgbDateStruct;
  maxReservation: NgbDateStruct;

  @Select(FormState.formLoading)
  formLoading$: Observable<boolean>;
  formLoading: boolean;

  currentCustomersCountryCode: string;
  FormType = FormType;
  attachId = uuidv4();

  form: FormGroup = new FormGroup({
    EquipmentType: new FormControl(null, []),
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormatV2,
    ]),
    CompanyName: new FormControl(),
    CompanyPhone: new FormControl(),
    CountryCode: new FormControl(null, [Validators.required]),
    City: new FormControl(null, [Validators.required]),
    // Address: new FormControl(null, []),
    EquipmentSerialNumber: new FormControl(null, [Validators.required]),
    CompanyId: new FormControl(),
    ServiceCategory: new FormControl(null, [Validators.required]),
    EquipmentStatus: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, [Validators.required]),
    Attachments: new FormControl([]),
    AttachmentIdList: new FormControl([]),
    permission: new FormControl(),
    RevisionIndicator: new FormControl(),
    ReservationsDate: new FormControl(null),
    WorkingHours: new FormControl(null, [Validators.max(2147483647), Validators.pattern('^[0-9]*$')]),
  });
  formPreviewStatus = false;
  showHeader = true;
  navigatedPage: string;
  formSendStatus: boolean;

  takeAPhotoStatus: boolean;
  snapshotBase64Data: string;
  equipmentSerialNumber: string;
  companyName: string;
  agreements: AgreementModel[];
  modalContent: any;
  id: any;
  source: string;
  sourceRoot: string;

  selectedDate: string;
  selectedDateNgbStruct: NgbDateStruct;
  dateSelectIsDisabled = true;
  isShowServiceCategoryError = false;
  selectedEquipment: EquipmentModel;
  featureRevisionCampaignBuffer = false;

  protected subscriptions$: Subject<boolean> = new Subject();
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  agreementTypeEnum = AgreementTypeEnum;


  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly formService: FormService,
    private readonly messageFrameService: MessageFrameService,
    private readonly definitionService: DefinitionService,
    private readonly fb: FormBuilder,
    protected readonly cdr: ChangeDetectorRef,
    private readonly modalService: ModalService
  ) { }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.scrollActiveElementIntoView();
  }

  scrollActiveElementIntoView() {
    const activeElement = document.activeElement as HTMLElement;
    console.log('test123')

    if (activeElement && ['INPUT', 'TEXTAREA'].includes(activeElement.tagName)) {
      activeElement.scrollIntoView();
    }
  }

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.user = this.store.selectSnapshot(LoginState.user);
    const company = this.store.selectSnapshot(LoginState.company);
    const customer = this.store.selectSnapshot(LoginState.customer);

    this.autoSelectCountry();

    const { id } = this.route.snapshot.params;
    this.id = id;
    const {
      showHeader,
      navigatedPage,
      ...queryParams
    } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    this.navigatedPage = navigatedPage;
    this.source = queryParams.source || 'Service';
    this.sourceRoot = queryParams.sourceRoot || 'Service';
    const patchValues: any = {
      ...queryParams,
    };
    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
      patchValues.Email = this.user.email;
      patchValues.CompanyPhone = this.user.mobile;
    } else {
      this.form.get('CompanyPhone').setValidators(Validators.required);
      this.form.get('EquipmentType').setValidators(Validators.required);
    }

    if (company) {
      patchValues.CompanyId = company.id;
      // patchValues.CompanyName = company.name;
    }
    if (customer) {
      patchValues.CompanyName = customer.name;
      this.companyName = customer.name;
    }
    this.form.patchValue(patchValues);
    this.cityList$.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          this.cityList = data;
        }
      );
    if (id) {
      this.equipmentDetail$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(
          (equipmentDetail: EquipmentModel) => {
            if (equipmentDetail) {
              this.equipmentDetail = equipmentDetail;
              this.form.patchValue({
                EquipmentSerialNumber: equipmentDetail.serialNumber,
                EquipmentType: equipmentDetail?.equipmentType,
                RevisionIndicator: equipmentDetail.revisionIndicator
              });
              this.equipmentSerialNumber = equipmentDetail.serialNumber;
            }
          }
        );
      this.store.dispatch(new GetEquipmentDetailAction(snq(() => id)));
    }

    this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);

    this.loadSystemFeatures();
    this.formLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(load => {
        this.formLoading = load;
      });
  }

  getReservationsDates() {
    this.reservationsDates$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => {
        if (x && x[0]?.date) {
          this.reservationsDates = x;
          const minDate = new Date(x[0].date);
          const maxDate = new Date(x[x.length - 1].date);
          this.minReservation = {
            year: minDate.getFullYear(),
            month: minDate.getMonth() + 1,
            day: minDate.getDate()
          };
          this.maxReservation = {
            year: maxDate.getFullYear(),
            month: maxDate.getMonth() + 1,
            day: maxDate.getDate()
          };
          this.reservationsDates.map(dates => {
            const dateParse = new Date(dates.date);
            if (this.editedReservationDates.filter(y => y?.data?.date === dates?.date)?.length === 0) {
              this.editedReservationDates.push({
                day: {
                  year: dateParse.getFullYear(),
                  month: dateParse.getMonth() + 1,
                  day: dateParse.getDate()
                },
                data: {
                  date: dates.date,
                  code: dates.status,
                  color: dates?.color,
                  isDisable: dates.status === 0 || dates.status === 1,
                }
              });
            }
          });
        }
      });
  }

  onChangeCountry(countryCode: string) {
    this.resetReservationsDates();
    this.getReservationDate();
    const selectedCountry = this.getCountryByCode(countryCode);
    console.log('countryChange', countryCode);
    this.form.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.form.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.City.clearValidators();
      this.form.controls.City.updateValueAndValidity();
      console.log('city', countryCode);
      return of([]);
    }
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  onSubmitForm() {
    console.log('onSubmitForm', this.form);
    if (this.form.valid) {
      this.formSendStatus = false;
      this.openPreviewModal();
    } else {
      validateAllFormFields(this.form);
    }
  }

  sendForm() {
    if (this.equipmentDetail && !this.equipmentDetail?.isServiceCreateAvailable && this.featureRevisionCampaignBuffer){
      return this.modalService.errorModal({
        message: '_not_available_service_create',
        translate: true
      });
    }
    const { value } = this.form;
    console.log('send', value);

    const data = {
      Name: value.Name,
      Surname: value.Surname,
      CompanyName: value.CompanyName,
      CompanyId: value.CompanyId,
      CompanyPhone: value.CompanyPhone,
      Email: value.Email.toLowerCase(),
      EquipmentSerialNumber: value.EquipmentSerialNumber?.trim(),
      ServiceCategory: value.ServiceCategory,
      EquipmentStatus: value.EquipmentStatus,
      CountryCode: value.CountryCode,
      EquipmentCity: value.City,
      EquipmentCityCrmCode: this.findCityObject(value.City)?.crmCode,
      EquipmentTown: value.EquipmentTown,
      Description: value.Description,
      RevisionIndicator: value.RevisionIndicator,
      ReservationDate: this.selectedDate,
      AttachmentIdList: this.form.value.Attachments.map((item) => item.id),
      EquipmentType: value.EquipmentType || 'Machine',
      EquipmentRevisionCampaign: this.selectedEquipment?.equipmentRevisionCampaign?.id
        || this.equipmentDetail?.equipmentRevisionCampaign?.id,
      Source: this.source,
      SourceRoot: this.sourceRoot,
      WorkingHours: value.WorkingHours
    } as any;

    let headers = {};
    if (!this.user) {
      headers = agreementHeaders(this.form.get('agreements').value);
    }

    this.formLoading = true;
    this.formService.sendRequestService(data, headers).subscribe(
      () => {
        this.formPreviewStatus = false;
        this.formSendStatus = true;
        this.formLoading = false;
      },
      () => {
        this.formPreviewStatus = true;
        this.formSendStatus = false;
        this.formLoading = false;
      }
    );
  }

  openPreviewModal() {
    this.formPreviewStatus = true;
  }

  onClickBackToServiceList() {
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'customer',
        'service-list',
      ])
      .then();
  }

  navigateToBack() {
    if (this.navigatedPage === 'Contact') {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }
    // if public page
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    window.history.back();
  }

  onDeleteAttachmentFile(file: FileModel) {
    this.form.patchValue({
      Attachments: snq(() => this.form.value.Attachments, []).filter(
        (f: FileModel) => !(f.id === file.id)
      ),
    });
  }

  openTakeAPhotoModal() {
    this.takeAPhotoStatus = true;
  }

  closeTakeAPhotoModal() {
    this.takeAPhotoStatus = false;
  }

  onTakeAPhoto(imageAsBase64) {
    this.snapshotBase64Data = imageAsBase64;
    this.closeTakeAPhotoModal();
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  showContent(item: any) {
    this.modalContent = item;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }


  protected autoSelectCountry() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        this.currentCustomersCountryCode = currentCustomer?.countryCode;
        if (!currentCustomer) {
          return;
        }
        this.countryList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe((countries) => {
            if (!countries?.length) {
              return;
            }
            let countryCode = currentCustomer?.groupKey;

            this.equipmentDetail$
              .pipe(takeUntil(this.subscriptions$))
              .subscribe((equipment) => {
                if (!equipment) {
                  if (this.id) {
                    return;
                  }
                }
                let found;
                if (equipment?.location?.locationName) {
                  found = countries.find(item => item.name === equipment?.location?.locationName);
                  countryCode = found?.code || countryCode;
                }

                this.form.patchValue({ CountryCode: countryCode });
                this.onChangeCountry(countryCode)
                  .pipe(takeUntil(this.subscriptions$))
                  .subscribe();
                  // otomatik city doldurma kaldırıldı
                  // .subscribe(() => {
                  //   const city = found ? equipment?.city : currentCustomer?.cityName;
                  //   if (city) {
                  //     this.form.patchValue({ City: city });
                  //   }
                  // });
              });
          });
      });
  }

  formPreviewclose() {
    if (this.selectedDate) {
      const date = this.selectedDate.split('.');
      this.selectedDateNgbStruct = {
        year: parseInt(date.shift(), 10),
        month: parseInt(date.shift(), 10),
        day: parseInt(date.shift(), 10)
      };
    }
    this.formPreviewStatus = false;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter(x => x.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  dateSelectedConvert(e: NgbDateStruct) {
    this.selectedDate = `${e.year}.${e.month}.${e.day}`;
    this.form.patchValue({ ReservationsDate: this.selectedDate });
  }

  loadSystemFeatures() {
    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.isReservationsDates = systemFeature('reservation_date', features, true);
        this.featureRevisionCampaignBuffer = systemFeature('revision_campaign_buffer ', features, false);
        if (this.isReservationsDates) {
          this.getReservationsDates();
        }
      }
      this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    });
  }

  findCityObject(cityName: string): City {
    return this.cityList.find(city => city.name === cityName);
  }

  serviceCategoryChange() {
    this.getReservationDate();
    if (this.form.value?.ServiceCategory !== 'Defect' ) {
      this.form.controls.EquipmentStatus.setValidators([]);
      this.form.patchValue({
        EquipmentStatus: null
      });
    }
    else {
      this.form.controls.EquipmentStatus.setValidators([Validators.required]);
    }
  }

  getReservationDate() {
    if (this.form.value.ServiceCategory && this.currentCustomersCountryCode === this.form.value.CountryCode && this.isReservationsDates && this.user) {
      this.dateSelectIsDisabled = false;
      this.isShowServiceCategoryError = false;
      this.store.dispatch(new FormRequestServiceGetReservationsDatesAction(this.form.value.ServiceCategory, this.form.value?.City));
    } else {
      this.dateSelectIsDisabled = true;
    }
  }

  resetReservationsDates() {
    this.form.controls.ReservationsDate.reset();
    this.selectedDate = null;
  }

  showServiceCategoryError() {
    this.isShowServiceCategoryError = this.form.get('ServiceCategory')?.value === null;
  }

  selectEquipment($event: EquipmentModel) {
    this.selectedEquipment = $event;
  }
}
