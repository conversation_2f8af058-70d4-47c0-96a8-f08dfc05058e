<div class="px-4 pb-5 tablet-form-container" [class.pt-4]="!user || !showHeader">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="!!user && showHeader">
    <i (click)="navigateToBack()" class="icon icon-back mr-2 float-left"></i>
    {{ "_create_service_request" | translate }}
  </div>
  <form class="tablet-form" [class.d-none]="formPreviewStatus" (submit)="onSubmitForm()" [formGroup]="form">
    <cat-info-box [title]="'_info' | translate">
      {{ "_service_form_info" | translate }}
    </cat-info-box>
    <!-- ? EquipmentType -->
    <div class="form-group" *ngIf="!user">
      <ng-select
        class="service-drp"
        [placeholder]="'_category' | translate"
        formControlName="EquipmentType"
        [searchable]="false"
        [clearable]="false"
      >
        <ng-option
          *ngFor="let equipmentType of EquipmentTypeEnum | enumToArray"
          [value]="equipmentType.key"
          >{{ equipmentType.value | translate }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.EquipmentType) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.EquipmentType) | translate }}
      </div>
    </div>
    <!-- ? Name -->
    <div class="form-group" *ngIf="!user?.firstName">
      <input
        catInputLength
        [name]="'Name'"
        [placeholder]="'_name' | translate"
        class="form-control form-control"
        formControlName="Name"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>
    <!-- ? Surname -->
    <div class="form-group" *ngIf="!user?.lastName">
      <input
        catInputLength
        [name]="'Surname'"
        [placeholder]="'_surname' | translate"
        class="form-control form-control"
        formControlName="Surname"
        type="text"
        minlength="2"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div>
    <!-- ? Email -->
    <div class="form-group" *ngIf="!user?.email">
      <input
        catInputLength
        [name]="'Email'"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="email"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Email) | translate }}
      </div>
    </div>
    <!-- ? Company Phone -->
    <div class="form-group" *ngIf="!user?.mobile">
      <input
        catInputLength
        [name]="'Phone'"
        [placeholder]="'_phone_number' | translate"
        class="form-control form-control"
        formControlName="CompanyPhone"
        type="tel"
        (input)="onInputPhone($event)"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyPhone) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyPhone) | translate }}
      </div>
    </div>
    <div class="mx-n4"></div>
    <div *ngIf="(!equipmentSerialNumber && !this.id) else showCampaignCart" class="form-group">
      <cat-machine-serial [form]="form"
                          [fieldName]="'EquipmentSerialNumber'"
                          [showMuneccimIcon]="true"
                          [showCampaignIcon]="true"
                          [showCampaignDetail]="true"
                          (selectEquipment)="selectEquipment($event)"
      ></cat-machine-serial>
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.EquipmentSerialNumber)
        }"
        class="invalid-feedback pl-3"
      >
        {{
        getFormErrorMessage(form.controls.EquipmentSerialNumber) | translate
        }}
      </div>
    </div>
    <ng-template #showCampaignCart>
      <ng-container *ngIf="selectedEquipment || equipmentDetail">
        <cat-equipment-revision-campaign [equipmentDetail]="selectedEquipment || equipmentDetail">
        </cat-equipment-revision-campaign>
      </ng-container>
    </ng-template>
    <!-- Company Name -->
    <div *ngIf="!companyName" class="form-group">
      <input
        catInputLength
        [name]="'CompanyName'"
        [placeholder]="'_company_name' | translate"
        class="form-control form-control"
        formControlName="CompanyName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>
    <!--    Country & City-->
    <div class="country-label">
      <label>{{ "_select_country_and_city" | translate }}</label>
    </div>
    <div class="row">
      <div
        [ngClass]="{
          'col-6 pr-2':
            !!form.controls['CountryCode'].value &&
            isActiveCountry(form.controls['CountryCode'].value),
          col:
            !form.controls['CountryCode'].value ||
            !isActiveCountry(form.controls['CountryCode'].value)
        }"
      >
        <div class="form-group">
          <!--          <cat-form-label [label]="'_equipment_location' | translate"></cat-form-label>-->
          <ng-select
            class="service-drp"
            [searchable]="true"
            (change)="onChangeCountry($event)"
            *ngIf="!(countryListLoading$ | async)"
            [placeholder]="'_country' | translate"
            [clearable]="false"
            [dropdownPosition]="'bottom'"
            formControlName="CountryCode"
            #country
          >
            <ng-option
              *ngFor="let country of searchCity(countryList$ | async, country.searchTerm)"
              [value]="country.code"
              >{{ country.name }}</ng-option
            >
          </ng-select>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
          </div>
        </div>
      </div>
      <div
        *ngIf="
          (!!form.controls['CountryCode'].value ||
            (cityList$ | async)?.length > 0) &&
          isActiveCountry(form.controls['CountryCode'].value)
        "
        class="col-6 pl-2"
      >
        <div class="form-group">
          <ng-select
            class="service-drp"
            [searchable]="true"
            *ngIf="!(cityListLoading$ | async)"
            [placeholder]="'_city' | translate"
            [clearable]="false"
            [dropdownPosition]="'bottom'"
            formControlName="City"
            (keypress)="scrollTop()"
            #city
          >
            <ng-option
              *ngFor="let city of searchCity(cityList, city.searchTerm)"
              [value]="city.name"
              >{{ city.name }}</ng-option
            >
          </ng-select>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.City) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.City) | translate }}
          </div>
        </div>
      </div>
    </div>
    <!-- Service Category -->
    <div class="form-group">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [placeholder]="'_service_category' | translate"
        [dropdownPosition]="'bottom'"
        formControlName="ServiceCategory"
        (change)="serviceCategoryChange()"
      >
        <ng-option
          *ngFor="let serviceCategory of ServiceCategoryEnum | enumToArray"
          [value]="serviceCategory.key"
        >{{ serviceCategory.value | translate }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.ServiceCategory) && !isShowServiceCategoryError }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.ServiceCategory) | translate }}
      </div>
      <div
        [ngClass]="{ 'd-block': isShowServiceCategoryError }"
        class="invalid-feedback pl-3"
      >
        {{ '_service_category_not_selected' | translate }}
      </div>
    </div>
    <!--    Reservations Data-->
    <div class="form-group" *ngIf="isReservationsDates && editedReservationDates?.length && !dateSelectIsDisabled" (click)="showServiceCategoryError()">
      <cat-date-picker (dateSelected)="dateSelectedConvert($event)"
        [dayListAndStatus]="editedReservationDates"
        [minDate]="minReservation"
        [maxDate]="maxReservation"
        [disabled]="dateSelectIsDisabled"
        [defaultSelectedDate]="selectedDateNgbStruct"></cat-date-picker>
    </div>
    <div class="form-group" *ngIf="form.value.ServiceCategory === 'Defect'">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [placeholder]="'_machine_status' | translate"
        [dropdownPosition]="'bottom'"
        formControlName="EquipmentStatus"
      >
      <ng-option
      *ngFor="let status of EquipmentStatusEnum | enumToArray"
      [value]="status.key"
    >{{ '_status_' + status.key | translate }}</ng-option
    >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.EquipmentStatus) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.EquipmentStatus) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
      catInputLength
      [name]="'WorkingHours'"
      [placeholder]="'_working_hours' | translate "
      class="form-control w-100"
      formControlName="WorkingHours"
      type="tel"
      (input)="onInputPhone($event)"
    />
    <div
    [ngClass]="{ 'd-block': isShowError(form.controls.WorkingHours) }"
    class="invalid-feedback pl-3"
  >
    {{ getFormErrorMessage(form.controls.WorkingHours) | translate }}
  </div>
    </div>
    <div class="form-group textarea-form-element-container">
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control textarea-form-element"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <div class="form-group">
      <cat-file-upload
        formControlName="Attachments"
        [id]="attachId"
        [formType]="FormType.ServiceRequest"
      ></cat-file-upload>
    </div>
    <div class="mb-3">
      <cat-file-upload-preview
        (deleteFile)="onDeleteAttachmentFile($event)"
        [deleteButtonStatus]="true"
        [files]="form.get('Attachments').value"
      ></cat-file-upload-preview>
    </div>

    <div>
      <cat-agreement-list
        *ngIf="!user"
        [form]="form"
        [formType]="agreementTypeEnum.PublicServiceForm"
      ></cat-agreement-list>
    </div>

    <input
      [value]="'_send' | translate"
      class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
      type="submit"
    />
  </form>
  <cat-loader
    [show]="
      (countryListLoading$ | async) ||
      (cityListLoading$ | async) ||
      (equipmentListLoading$ | async) ||
      formLoading
    "
  ></cat-loader>
</div>

<div *ngIf="formPreviewStatus" [@preview] class="form-preview px-4 pb-3">
  <div class="h4 py-4 mb-0 nav-back">
    <i (click)="formPreviewclose()" class="icon icon-back mr-2"></i>
    {{ "_service_request_preview" | translate }}
  </div>
  <div *ngIf="equipmentDetail" class="equipment d-flex mb-4 align-items-center">
    <div
      class="equipment-image d-flex align-items-center justify-content-center"
      style="height: auto"
    >
      <cat-image-preview
        [id]="equipmentDetail.productHierarchy"
        [model]="equipmentDetail.model"
        [title]="equipmentDetail.brand + ' ' + equipmentDetail.model"
      ></cat-image-preview>
    </div>
    <div class="flex-fill ml-2">
      <div class="font-weight-bold">
        {{ equipmentDetail?.brand }} {{ equipmentDetail.model }}
      </div>
      <div class="font-size-12px mb-2">
        {{ equipmentDetail?.serialNumber | serialFormat }}
      </div>
      <!--      <div class="row">-->
      <!--        <div class="col-4 font-size-12px">-->
      <!--          <i-->
      <!--            class="icon icon-clock mr-1"></i> {{equipmentDetail.workingHours}} {{equipmentDetail.workingHoursUnit | translate}}-->
      <!--        </div>-->
      <!--        <div class="col-8 font-size-12px">-->
      <!--          <i class="icon icon-location mr-1"></i> {{equipmentDetail?.location?.locationName}}-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
  </div>

  <div class="small mb-3">
    <div *ngIf="form.value.EquipmentType">
      {{ EquipmentTypeEnum[form.value.EquipmentType] | translate }}
    </div>
    <div>{{ form.value.Name }} {{ form.value.Surname }}</div>
    <div>{{ form.value.Email }}</div>
    <div *ngIf="!!form.value.CompanyPhone">{{ form.value.CompanyPhone }}</div>

    <div class="mt-3">{{ form.value.CompanyName }}</div>
    <div>
      {{ getCountryByCode(form.value?.CountryCode).name }} /
      {{ form.value?.City }}
    </div>
    <!--    <div *ngIf="form.value?.Address">{{form.value.Address}}</div>-->
    <div *ngIf="!equipmentSerialNumber" class="EquipmentLongText">
      {{ form.value.EquipmentSerialNumber }}
    </div>
    <div>
      {{ "_service_category" | translate }}:
      {{ ServiceCategoryEnum[form.value.ServiceCategory] | translate }}
    </div>
    <div *ngIf="form.value?.EquipmentStatus && form.value?.ServiceCategory === 'Defect'">
      {{ '_status_' + form.value?.EquipmentStatus | translate }}
    </div>
    <div *ngIf="form.value?.ReservationsDate">
      {{ form.value?.ReservationsDate }}
    </div>
    <div class="mt-3 EquipmentLongText">{{ form.value.Description }}</div>
    <div class="mt-3 EquipmentLongText" *ngIf="form.value.WorkingHours">{{ form.value.WorkingHours }}</div>
  </div>
  <div class="mb-3">
    <cat-file-upload-preview
      [files]="form.get('Attachments').value"
    ></cat-file-upload-preview>
  </div>
  <button
    catUserClick
    [section]="'SERVICE_FORM'"
    [subsection]="'CREATE'"
    [data]="{
      EquipmentSerialNumber: form.value.EquipmentSerialNumber,
      source: source,
      sourceRoot: sourceRoot
    }"
    (click)="sendForm()"
    [disabled]="borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitServiceRequestForm) >= 0"
    class="btn btn-warning btn-gradient btn-block text-white shadow"
  >
    {{ "_confirm" | translate }}
  </button>
</div>
<div *ngIf="formSendStatus" [@preview] class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_successfully_send_form" | translate }}
    </div>
    <!-- <div
      (click)="navigateToBack()"
      *ngIf="(equipmentDetail$ | async) && navigatedPage !== 'Contact'"
      class="btn btn-warning btn-gradient btn-block text-white shadow"
    >
      {{ "_back_to_equipment_detail" | translate }}
    </div>
    <div
      (click)="onClickBackToServiceList()"
      *ngIf="!(equipmentDetail$ | async) && user && navigatedPage !== 'Contact'"
      class="btn btn-warning btn-gradient btn-block text-white shadow"
    >
      {{ "_back_to_service_list" | translate }}
    </div> -->
    <div
      (click)="navigateToBack()"
      class="btn btn-warning btn-gradient btn-block text-white shadow"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
<div *ngIf="takeAPhotoStatus" class="take-a-photo-modal">
  <cat-take-a-photo (takeAPhoto)="onTakeAPhoto($event)"></cat-take-a-photo>
  <div (click)="closeTakeAPhotoModal()" class="take-a-photo-modal-x">
    <i class="icon icon-x"></i>
  </div>
</div>
<cat-fullscreen-modal *ngIf="modalContent && !user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-fullscreen-modal>
<cat-big-modal *ngIf="modalContent && user" [(status)]="!!modalContent">
  <cat-agreement-modal [content]="modalContent"></cat-agreement-modal>
</cat-big-modal>
