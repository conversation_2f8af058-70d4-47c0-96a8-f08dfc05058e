<div class="px-4 pb-5 tablet-short-form-container" [class.pt-4]="!showHeader">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="showHeader">
    <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
    {{ "_create_equipment_part_request" | translate }}
  </div>
  <form class="tablet-form" *ngIf="!formPreviewStatus" (submit)="onSubmitForm()" [formGroup]="form">
    <cat-info-box [title]="'_info' | translate">
      {{ "_spare_part_form_info" | translate }}
      <div [hasPermission]="PermissionEnum.VideoCallBanko">
        <hr *ngIf="digitalBankoVideoCall" class="my-1">
        <span *ngIf="digitalBankoVideoCall" (click)="digitalBankoOpen()"
          [innerHtml]="'_digital_banko_click_text' | translate
          | safeHtml">
        </span>
      </div>
    </cat-info-box>
    <div *ngIf="!user">
      <div class="form-group">
        <input
          catInputLength
          [name]="'Name'"
          [placeholder]="'_name' | translate"
          class="form-control form-control"
          formControlName="Name"
          type="text"
          minlength="3"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Name) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          catInputLength
          [name]="'Surname'"
          [placeholder]="'_surname' | translate"
          class="form-control form-control"
          formControlName="Surname"
          type="text"
          minlength="2"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Surname) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          catInputLength
          [name]="'Email'"
          [placeholder]="'_email' | translate"
          class="form-control form-control"
          formControlName="Email"
          type="email"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Email) | translate }}
        </div>
      </div>
      <div class="mx-n4"></div>
    </div>
    <div class="form-group">
      <input
        catInputLength
        [name]="'Phone'"
        [placeholder]="'_company_phone_number' | translate"
        class="form-control form-control"
        formControlName="CompanyPhoneNumber"
        type="tel"
        (input)="onInputPhone($event)"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyPhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyPhoneNumber) | translate }}
      </div>
    </div>

    <div *ngIf="!companyName" class="form-group">
      <input
        catInputLength
        [name]="'CompanyName'"
        [placeholder]="'_company_name' | translate"
        class="form-control form-control"
        formControlName="CompanyName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>
    <div *ngIf="!equipmentSerialNumber" class="form-group">
      <cat-machine-serial [form]="form">
        [fieldName]="'EquipmentSerialNumber'"
      </cat-machine-serial>
      <div
        [ngClass]="{
          'd-block': isShowError(form.controls.EquipmentSerialNumber)
        }"
        class="invalid-feedback pl-3"
      >
        {{
          getFormErrorMessage(form.controls.EquipmentSerialNumber) | translate
        }}
      </div>
    </div>

    <div
      *ngIf="productGroups?.length > 0"
      class="form-group"
    >
      <ng-select
        [searchable]="true"
        *ngIf="productGroups?.length > 0"
        [placeholder]="'_product_groups' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="true"
        formControlName="ProductGroup"
        (keydown)="scrollTop()"
      >
        <ng-option
          *ngFor="let group of productGroups"
          [value]="group?.key"
        >{{ group.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.ProductGroup) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.ProductGroup) | translate }}
      </div>
    </div>

    <div class="country-label">
      <label>{{ "_select_country_and_city" | translate }}</label>
    </div>
    <div class="form-group">
      <!--      <cat-form-label [label]="'_equipment_location' | translate"></cat-form-label>-->
      <ng-select
        [searchable]="true"
        (change)="onChangeCountry($event)"
        *ngIf="!(countryListLoading$ | async)"
        [placeholder]="'_country' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="false"
        formControlName="CountryCode"
        #country
      >
        <ng-option
          *ngFor="let country of searchCity(countryList$ | async, country.searchTerm)"
          [value]="country.code"
          >{{ country.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
      </div>
    </div>
    <div
      *ngIf="
        (!!form.controls['CountryCode'].value ||
          (cityList$ | async)?.length > 0) &&
        isActiveCountry(form.controls['CountryCode'].value)
      "
      class="form-group"
    >
      <ng-select
        [searchable]="true"
        *ngIf="!(cityListLoading$ | async)"
        [placeholder]="'_city' | translate"
        [dropdownPosition]="'bottom'"
        [clearable]="false"
        formControlName="City"
        (keypress)="scrollTop()"
        #city
      >
        <ng-option
          *ngFor="let city of searchCity(cityList, city.searchTerm)"
          [value]="city.name"
          >{{ city.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.City) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.City) | translate }}
      </div>
    </div>
    <div class="form-group textarea-form-element-container">
      <textarea
        catInputLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control textarea-form-element"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <div class="form-group">
      <cat-file-upload
        formControlName="Attachments"
        [id]="attachId"
        [formType]="FormType.PartRequest"
      ></cat-file-upload>
    </div>

    <div class="mb-3">
      <cat-file-upload-preview
        [deleteButtonStatus]="true"
        [files]="form.get('Attachments').value"
        (deleteFile)="onDeleteAttachmentFile($event)"
      ></cat-file-upload-preview>
    </div>

    <div class="">
      <cat-agreement-list
        *ngIf="!user"
        [form]="form"
        [formType]="agreementTypeEnum.PublicServiceForm"
      ></cat-agreement-list>
    </div>
    <input
      [value]="'_send' | translate"
      class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg"
      type="submit"
    />
  </form>
  <cat-loader
    [show]="
      (countryListLoading$ | async) ||
      (cityListLoading$ | async) ||
      (equipmentListLoading$ | async)
    "
  ></cat-loader>
</div>
<div *ngIf="formPreviewStatus" [@preview] class="form-preview px-4">
  <div class="h4 py-4 mb-0">
    <i (click)="formPreviewStatus = false" class="icon icon-back mr-2"></i>
    {{
      (navigatedPage === "Catalog"
        ? "_part_request_preview_catalog"
        : "_part_request_preview"
      ) | translate
    }}
  </div>
  <div *ngIf="user">
    <div class="small mb-3">
      <div>{{ user?.firstName }} {{ user?.lastName }}</div>
      <div>{{ user?.email }}</div>
      <div *ngIf="user?.mobile">{{ user?.mobile }}</div>

      <div class="mt-3">{{ form.value.CompanyName }}</div>
      <div>{{ getCountryByCode(form.value?.CountryCode).name }}</div>
      <div>{{ form.value?.City }}</div>

      <div class="mt-3 EquipmentLongText">{{ form.value?.Description }}</div>
    </div>
  </div>
  <div *ngIf="!user">
    <div class="small mb-3">
      <div>{{ form.value?.Name }} {{ form.value?.Surname }}</div>
      <div>{{ form.value?.Email }}</div>
      <div>{{ form.value?.CompanyPhoneNumber }}</div>

      <div class="mt-3">{{ form.value?.EquipmentSerialNumber }}</div>
      <div>{{ form.value?.CompanyName }}</div>
      <div>{{ getCountryByCode(form.value?.CountryCode).name }}</div>
      <div>{{ form.value?.City }}</div>

      <div class="mt-3">{{ form.value.Description }}</div>
    </div>
  </div>
  <div class="mb-3">
    <cat-file-upload-preview
      [files]="form.get('Attachments').value"
    ></cat-file-upload-preview>
  </div>
  <button
    catUserClick
    [section]="'SPARE_PART_FORM'"
    [subsection]="'CREATE'"
    (click)="sendForm()"
    [disabled]="borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitPartRequestForm) >= 0"
    class="btn btn-warning btn-gradient btn-block text-white shadow"
  >
    {{ "_confirm" | translate }}
  </button>
</div>
<div *ngIf="formSendStatus" [@preview] class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_successfully_send_form" | translate }}
    </div>
    <!-- <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      *ngIf="navigatedPage !== 'Contact'"
      (click)="navigateToBack()"
    >
      {{ "_back_to_equipment_detail" | translate }}
    </div> -->
    <div
      (click)="navigateToBack()"
      class="btn btn-warning btn-gradient btn-block text-white shadow"
    >
      {{ "_go_back" | translate }}
    </div>
  </div>
</div>
<cat-loader [show]="loading"></cat-loader>
