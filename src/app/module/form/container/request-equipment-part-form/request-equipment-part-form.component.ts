import { animate, state, style, transition, trigger, } from '@angular/animations';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import snq from 'snq';
import { FileModel, FormType } from '../../../../export/file-upload/model/file.model';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { validateAllFormFields } from '../../../../util/validate-all-form-fields.util';
import { UserModel } from '../../../authentication/model/user.model';
import { LoginState } from '../../../authentication/state/login/login.state';
import { EquipmentModel } from '../../../customer/model/equipment.model';
import { GetAllCountryListAction, GetCityListAction, } from '../../../definition/action/definition.actions';
import { City } from '../../../definition/model/city.model';
import { Country } from '../../../definition/model/country.model';
import { DefinitionState } from '../../../definition/state/definition.state';
import { FormService } from '../../service/form/form.service';
import { PermissionEnum } from '../../../definition/enum/permission.enum';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import uuidv4 from 'src/app/util/uuidv4';
import { CommonState } from '../../../shared/state/common/common.state';
import { CustomerRelationModel } from '../../../shared/model/company.model';
import { map, takeUntil } from 'rxjs/operators';
import { CustomValidator } from 'src/app/util/custom-validator';
import { AgreementModel } from 'src/app/module/definition/model/agreement.model';
import { AgreementTypeEnum } from 'src/app/module/definition/enum/agreement-type.enum';
import { agreementHeaders } from '../../../../util/agreement-headers.util';
import { GetBasisSettingsAction, SystemFeatureAction } from 'src/app/module/shared/state/settings/settings.actions';
import { GetEquipmentDetailAction } from 'src/app/module/customer/action/equipment.action';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { CustomerModuleService } from '../../../customer/service/customer-module.service';
import { systemFeature } from '../../../shared/util/system-feature.util';
import { SettingsState } from '../../../shared/state/settings/settings.state';
import { SystemFeature } from '../../../shared/model/settings.model';
import { BorusanBlockedActionsEnum } from '../../../definition/enum/borusan-blocked-actions.enum';
import { FormRequestServiceGetProductGroupsAction } from '../../action/form.actions';
import { FormState } from '../../state/form.state';
import { FormServiceProductGroupsModel } from '../../model/form.modal';

@Component({
  selector: 'cat-request-equipment-part-form',
  templateUrl: './request-equipment-part-form.component.html',
  styleUrls: [
    './request-equipment-part-form.component.scss',
    '../request-service-form/request-service-form.component.scss',
  ],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class RequestEquipmentPartFormComponent implements OnInit, OnDestroy {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  PermissionEnum = PermissionEnum;
  @Select(EquipmentState.equipmentDetail)
  equipmentDetail$: Observable<EquipmentModel>;

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(FormState.getProductGroups)
  productGroups$: Observable<FormServiceProductGroupsModel[]>;
  productGroups: FormServiceProductGroupsModel[];

  cityList: City[];
  user: UserModel;
  FormType = FormType;
  attachId = uuidv4();

  form: FormGroup = new FormGroup({
    EquipmentSerialNumber: new FormControl(null, [Validators.required]),
    CountryCode: new FormControl(null, [Validators.required]),
    City: new FormControl(null, [Validators.required]),
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    CompanyPhoneNumber: new FormControl(null, [Validators.required]),
    PhoneNumber: new FormControl(null, []),
    CompanyId: new FormControl(),
    CompanyName: new FormControl(null),
    Description: new FormControl(null, [Validators.required]),
    Attachments: new FormControl([]),
    AttachmentIdList: new FormControl([]),
    permission: new FormControl(),
    ProductGroup: new FormControl(),
  });
  formPreviewStatus = false;
  showHeader = true;
  navigatedPage: string;
  formSendStatus: boolean;
  equipmentSerialNumber: string;
  companyName: string;
  agreements: AgreementModel[];
  modalContent: any;
  agreementTypeEnum = AgreementTypeEnum;
  loading = false;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  protected subscriptions$: Subject<boolean> = new Subject();
  private id: any;
  digitalBankoVideoCall = false;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly formService: FormService,
    private readonly messageFrameService: MessageFrameService,
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.loadForm();
    this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);
    this.cityList$.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          this.cityList = data;
        }
      );

    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.digitalBankoVideoCall = systemFeature('digital_banko_videocall', features, false);
      }
      this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    });
  }

  loadForm() {
    this.store.dispatch(new GetAllCountryListAction());
    this.user = this.store.selectSnapshot(LoginState.user);
    this.store.dispatch(new FormRequestServiceGetProductGroupsAction());
    const company = this.store.selectSnapshot(LoginState.company);
    const customer = this.store.selectSnapshot(LoginState.customer);

    this.autoSelectCountry();

    const { showHeader, navigatedPage, ...queryParams } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    this.navigatedPage = navigatedPage;
    const patchValues: any = {
      ...queryParams,
    };
    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
      patchValues.Email = this.user.email;
      patchValues.PhoneNumber = this.user.mobile;
      patchValues.CompanyPhoneNumber = this.user.mobile;
    }
    if (company) {
      patchValues.CompanyId = company.id;
      // patchValues.CompanyName = company.name;
    }
    if (customer) {
      patchValues.CompanyName = customer.name;
      this.companyName = customer.name;
    }
    const { id } = this.route.snapshot.params;
    this.id = id;
    if (id) {
      this.equipmentDetail$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(
          (equipmentDetail: EquipmentModel) => {
            if (equipmentDetail) {
              this.form.patchValue({
                EquipmentSerialNumber: equipmentDetail.serialNumber,
              });
              this.equipmentSerialNumber = equipmentDetail.serialNumber;
            }
          }
        );
      this.store.dispatch(new GetEquipmentDetailAction(snq(() => id)));
    }
    this.productGroups$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((groups) => {
        this.productGroups = groups;
      });

    this.form.patchValue(patchValues);
  }

  onChangeCountry(countryCode: string) {
    const selectedCountry = this.getCountryByCode(countryCode);
    this.form.patchValue({ EquipmentCity: null });

    this.form.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.form.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.City.clearValidators();
      this.form.controls.City.updateValueAndValidity();
      return of([]);
    }
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  onSubmitForm() {
    console.log(this.form);

    if (this.form.valid) {
      this.openPreviewModal();
    } else {
      validateAllFormFields(this.form);
    }
  }

  sendForm() {
    console.log('send');
    this.loading = true;
    const { value } = this.form;
    if (this.user) {
      this.formService
        .sendPartRequest({
          Name: this.user.firstName,
          Surname: this.user.lastName,
          Email: this.user.email.toLowerCase(),
          PhoneNumber: this.user.mobile,
          CompanyPhoneNumber: value.CompanyPhoneNumber,
          CompanyId: value.CompanyId,
          CompanyName: value.CompanyName,
          EquipmentSerialNumber: value.EquipmentSerialNumber,
          CountryCode: value.CountryCode,
          City: value.City,
          Description: value.Description,
          AttachmentIdList: this.form.value.Attachments.map((item) => item.id),
          ProductGroup: value.ProductGroup
        })
        .subscribe(
          () => {
            this.formSendStatus = true;
            this.loading = false;
          },
          () => {
            this.formSendStatus = false;
            this.loading = false;
          }
        );
    }
    if (!this.user) {
      let headers = {};
      if (!this.user) {
        headers = agreementHeaders(this.form.get('agreements').value);
      }

      this.formService
        .sendPartRequest({
          Name: value.Name,
          Surname: value.Surname,
          Email: value.Email.toLowerCase(),
          CompanyPhoneNumber: value.CompanyPhoneNumber,
          CompanyId: value.CompanyId,
          CompanyName: value.CompanyName,
          EquipmentSerialNumber: value.EquipmentSerialNumber,
          CountryCode: value.CountryCode,
          City: value.City,
          Description: value.Description,
          AttachmentIdList: this.form.value.Attachments.map((item) => item.id),
          ProductGroup: value.ProductGroup
        }, headers)
        .subscribe(
          () => {
            this.formSendStatus = true;
            this.loading = false;
          },
          () => {
            this.formSendStatus = true;
            this.loading = false;
          }
        );
    }
  }

  openPreviewModal() {
    this.formPreviewStatus = true;
  }

  navigateToBack() {
    if (this.navigatedPage === 'Contact') {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }
    // if public page
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    window.history.back();
  }


  onDeleteAttachmentFile(file: FileModel) {
    this.form.patchValue({
      Attachments: snq(() => this.form.value.Attachments, []).filter(
        (f: FileModel) => !(f.id === file.id)
      ),
    });
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  protected autoSelectCountry() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        if (!currentCustomer) {
          return;
        }
        this.countryList$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe((countries) => {
            if (!countries?.length) {
              return;
            }
            let countryCode = currentCustomer?.groupKey;

            this.equipmentDetail$
              .pipe(takeUntil(this.subscriptions$))
              .subscribe((equipment) => {
                if (!equipment) {
                  if (this.id) {
                    return;
                  }
                }
                let found;
                if (equipment?.location?.locationName) {
                  found = countries.find(item => item.name === equipment?.location?.locationName);
                  countryCode = found?.code || countryCode;
                }

                this.form.patchValue({ CountryCode: countryCode });
                this.onChangeCountry(countryCode)
                  .pipe(takeUntil(this.subscriptions$))
                  .pipe(map(() => this.store.selectSnapshot(DefinitionState.cityList)))
                  .subscribe((cities) => {
                    const city = found ? equipment?.city : currentCustomer?.cityName;
                    if (city) {
                      const foundCity = cities?.find(item => item.name.toLowerCase() === city.toLowerCase())
                      if (foundCity) {
                        this.form.patchValue({ City: foundCity.name });
                      }
                    }
                  });
              });
          });
      });
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        // tslint:disable-next-line:no-shadowed-variable
        .filter(data => data.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  digitalBankoOpen() {
    this.customerModuleService.openDigitalBanko(!!this.user);
  }
}
