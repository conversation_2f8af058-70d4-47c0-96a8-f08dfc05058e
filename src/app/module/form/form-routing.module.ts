import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormLayoutComponent } from './container/form-layout/form-layout.component';
import { RequestEquipmentPartFormComponent } from './container/request-equipment-part-form/request-equipment-part-form.component';
import { RequestMdaFormComponent } from './container/request-mda-form/request-mda-form.component';
import { RequestServiceFormComponent } from './container/request-service-form/request-service-form.component';
import { MyEquipmentAgendaComponent } from '../customer/container/equipment/my-equipment-agenda/my-equipment-agenda.component';
import { MyEquipmentAgendaNotesComponent } from '../customer/container/equipment/my-equipment-agenda-notes/my-equipment-agenda-notes.component';
import { MyEquipmentAgendaCheckInComponent } from '../customer/container/equipment/my-equipment-agenda-check-in/my-equipment-agenda-check-in.component';
import { MyEquipmentAgendaCheckOutComponent } from '../customer/container/equipment/my-equipment-agenda-check-out/my-equipment-agenda-check-out.component';
import { MyEquipmentAgendaCalendarComponent } from '../customer/container/equipment/my-equipment-agenda-calendar/my-equipment-agenda-calendar.component';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'request-service',
  },
  {
    path: '',
    component: FormLayoutComponent,
    children: [
      { path: 'request-service', component: RequestServiceFormComponent },
      { path: 'request-service/:id', component: RequestServiceFormComponent },
      { path: 'request-mda', component: RequestMdaFormComponent },
      { path: 'request-mda/:id', component: RequestMdaFormComponent },
      { path: 'request-equipment-part', component: RequestEquipmentPartFormComponent },
      { path: 'request-equipment-part/:id', component: RequestEquipmentPartFormComponent },
      { path: 'request-my-equipment-agenda/:id', component: MyEquipmentAgendaComponent,
        children: [
          { path: 'notes', component: MyEquipmentAgendaNotesComponent},
          { path: 'check-in', component: MyEquipmentAgendaCheckInComponent},
          { path: 'check-out', component: MyEquipmentAgendaCheckOutComponent},
          { path: 'calendar', component: MyEquipmentAgendaCalendarComponent}
        ]
      }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FormRoutingModule {
}
