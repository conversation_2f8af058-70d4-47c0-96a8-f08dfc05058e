export interface RequestServiceReservationDayStatusModel {
  date: string;
  status: number;
  color: string;
}

export interface SurveyFormModel {
  surveyId: string;
  code: string;
  name: string;
  description: string;
  targetAudience: number;
  isActive: boolean;
  greetings: string;
  isAuth: boolean;
  assignedCompanies: any;
  assignedCountries: any;
  assignedUsers: any;
  surveyQuestions: SurveyQuestion[];
}

export interface SurveyQuestion {
  surveyId: string;
  questionId: string;
  question: string;
  order: number;
  isActive: boolean;
  questionType: number;
  isRequired: boolean;
  hasOptions: boolean;
  isCommentBoxEnabled: boolean;
  isCommentBoxRequired: boolean;
  commentBoxMaxLength: number;
  questionOptions: any[];
}

export interface SurveyFormAnswerBodyModel {
  SurveyId: string;
  SurveyPosition: string;
  AdditionalData: any;
  RelatedContextId: any;
  Answers: SurveyFormAnswersModel[];
}

export interface SurveyFormAnswersModel {
  QuestionId: string;
  Answer: string;
  Comment: null;
}

export interface FormServiceProductGroupsModel {
  key: string;
  name: string;
}
