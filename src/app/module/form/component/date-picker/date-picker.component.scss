.custom-day {
  display: flex;
  justify-content: center;
  align-content: center;
  width: 100%;
  height: 100%;

  .day-number {
    align-self: center;
    height: fit-content;
  }

  .selected-day {
    background-color: #fd7e14;
    color: #fff;
  }

  .status {

    &-secondary {
      background-color: #505050;
      height: 10px;
      width: 10px;
      left: 10px;
      border-radius: 50%;
    }

    &-danger {
      background-color: #DA3A3C;
      height: 10px;
      width: 10px;
      left: 10px;
      border-radius: 50%;
    }

    &-warning {
      background-color: #FFA300;
      height: 10px;
      width: 10px;
      left: 10px;
      border-radius: 50%;
    }

    &-success {
      background-color: #5E9731;
      height: 10px;
      width: 10px;
      left: 10px;
      border-radius: 50%;
    }
  }

  .color {

    &-secondary {
      color: #505050;
      font-weight: bolder;
    }

    &-danger {
      color: #DA3A3C;
      font-weight: bolder;
    }

    &-warning {
      color: #FFA300;
      font-weight: bolder;
    }

    &-success {
      color: #5E9731;
      font-weight: bolder;
    }
  }
}

.date-picker {

  ::ng-deep .ngb-dp-day,
  ::ng-deep .ngb-dp-week-number,
  ::ng-deep .ngb-dp-weekday {
    width: calc(90vw / 7) !important;
    height: calc(90vw / 7) !important;
    display: flex;
    justify-content: center;
  }

  ::ng-deep .ngb-dp-header{
    height: 2.6rem;
  }

  ::ng-deep ngb-datepicker ngb-datepicker-month {
    margin-top: 5px !important;
  }

  ::ng-deep ngb-datepicker-navigation-select>.custom-select {
    font-size: inherit !important;
  }

  ::ng-deep ngb-datepicker.dropdown-menu {
    border-radius: 0.25rem;
  }

  ::ng-deep ngb-datepicker .ngb-dp-navigation-select {
    flex: 1 1 calc(90vw - 90px) !important;
  }

  ::ng-deep ngb-datepicker-navigation-select>.custom-select {
    padding-left: 30px !important;
  }
}
