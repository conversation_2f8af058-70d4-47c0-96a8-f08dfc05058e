<div class="date-picker">
  <input class="form-control" name="dp"
    (click)="openDatePicker()"
    [readonly]="true"
    [placeholder]="'_select_date' | translate"
    ngbDatepicker
    [dayTemplate]="customDay"
    [showWeekdays]="showWeekDaysName"
    [minDate]="minDate"
    [maxDate]="maxDate"
    [markDisabled]="isDisabled"
    [placement]="'bottom'"
    (dateSelect)="dateSelectClick($event)"
    [(ngModel)]="defaultSelectedDate"
    [disabled]="disabled"
    #datePicker="ngbDatepicker" />
  <ng-template #customDay let-date let-currentMonth="currentMonth" let-selected="selected" let-disabled="disabled" let-focused="focused">
    <div class="custom-day" [class.focused]="focused" [class.selected-day]="selected"
      [class.color-secondary]="disabled">
      <span [class]="getDayStatusColor(date)"></span>
      <span class="day-number" [class]="getDayTextColor(date)">
        {{ date.day }}
      </span>
    </div>
  </ng-template>
</div>
