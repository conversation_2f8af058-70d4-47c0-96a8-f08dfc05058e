import { ChangeDetectorRef, Component, EventEmitter, Injectable, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import {
  NgbDateStruct,
  NgbDate,
  Ng<PERSON><PERSON>n<PERSON><PERSON><PERSON>pic<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ndar,
  <PERSON><PERSON><PERSON><PERSON>picker<PERSON>18n,
  NgbDateParserFormatter
} from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
@Injectable()
export class CustomDatepickerI18n extends NgbDatepickerI18n {
  constructor(
    private readonly translateService: TranslateService,
  ) {
    super();
  }

  getWeekdayShortName(weekday: number): string {
    return this.translateService.instant(`_weekday_name_${weekday}`);
  }

  getMonthShortName(month: number): string {
    return this.translateService.instant(`_month_name_${month}`);
  }
  getMonthFullName(month: number): string {
    return this.getMonthShortName(month);
  }

  getDayAriaLabel(date: NgbDateStruct): string {
    return `${date.day}-${date.month}-${date.year}`;
  }

}
@Injectable()
export class NgbDateCustomParserFormatter extends NgbDateParserFormatter {
  parse(value: string): NgbDateStruct {
    if (value) {
      const dateParts = value.trim().split('/');
      if (dateParts.length === 1 && isNumber(dateParts[0])) {
        return { day: toInteger(dateParts[0]), month: null, year: null };
      } else if (dateParts.length === 2 && isNumber(dateParts[0]) && isNumber(dateParts[1])) {
        return {
          day: toInteger(dateParts[0]),
          month: toInteger(dateParts[1]),
          year: null
        };
      } else if (dateParts.length === 3 && isNumber(dateParts[0]) && isNumber(dateParts[1]) && isNumber(dateParts[2])) {
        return {
          day: toInteger(dateParts[0]),
          month: toInteger(dateParts[1]),
          year: toInteger(dateParts[2])
        };
      }
    }
    return null;
  }

  format(date: NgbDateStruct): string {
    return date
      ? `${date.year}-${isNumber(date.month) ? padNumber(date.month) : ''}-${isNumber(date.day) ? padNumber(date.day) : ''}`
      : '';
  }
}
export function toInteger(value: any): number {
  return parseInt(`${value}`, 10);
}

export function isNumber(value: any): value is number {
  return !isNaN(toInteger(value));
}

export function padNumber(value: number) {
  if (isNumber(value)) {
    return `0${value}`.slice(-2);
  } else {
    return '';
  }
}
@Component({
  selector: 'cat-date-picker',
  templateUrl: './date-picker.component.html',
  styleUrls: ['./date-picker.component.scss'],
  providers: [
    { provide: NgbDatepickerI18n, useClass: CustomDatepickerI18n }
  ]
})
export class DatePickerComponent implements OnInit, OnChanges {

  constructor(
    private readonly calendar: NgbCalendar,
    private readonly ref: ChangeDetectorRef
  ) { }
  @Input()
  weekendDisable = true;

  @Input()
  showWeekDaysName = false;

  @Input()
  minDate: NgbDateStruct;

  @Input()
  maxDate: NgbDateStruct;

  @Input()
  defaultSelectedDate: NgbDateStruct;

  @Input()
  dayListAndStatus: [{ day: NgbDateStruct, data?: { date: any, code?: number, color?: string, isDisable?: boolean } }];

  @Input()
  disabled: boolean;

  @Output()
  dateSelected: EventEmitter<NgbDateStruct> = new EventEmitter();

  @ViewChild('datePicker') datePicker: NgbInputDatepicker;

  isDisabled = (date: NgbDate) =>
    this.getCustomDisableDate(date) || this.isWeekend(date)

  ngOnInit() {
  }

  getCustomDisableDate(date: NgbDate) {
    return this.dayListAndStatus.find(d =>
      d.day.year === date.year &&
      d.day.month === date.month &&
      d.day.day === date.day)?.data?.isDisable;
  }

  getDayTextColor(date: NgbDateStruct) {
    if (this.dayListAndStatus?.length) {
      return 'color-' + this.dayListAndStatus.find(d =>
        d.day.year === date.year &&
        d.day.month === date.month &&
        d.day.day === date.day)?.data?.color;
    }
  }

  getDayStatusColor(date: NgbDateStruct) {
    if (this.dayListAndStatus?.length) {
      return 'status-' + this.dayListAndStatus.find(d =>
        d.day.year === date.year &&
        d.day.month === date.month &&
        d.day.day === date.day)?.data?.color;
    }
  }

  isWeekend(date) {
    return this.weekendDisable ? this.calendar.getWeekday(date) >= 6 : false;
  }

  dateSelectClick(e: NgbDateStruct) {
    this.dateSelected.emit(e);
    this.closeDatePicker();
  }

  openDatePicker() {
    this.datePicker.toggle();
  }

  closeDatePicker() {
    this.datePicker.close();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.ref.detectChanges();
  }

}
