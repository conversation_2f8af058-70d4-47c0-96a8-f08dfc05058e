@import "variable/bootstrap-variable";

.dp-hidden {
  visibility: hidden;
  width: 0;
  margin: 0;
  border: none;
  padding: 0;
  top: 32px;
  left: 50%;
}

.dp-hidden ::ng-deep ngb-datepicker {
  visibility: visible;
}

.custom-day {
  text-align: center;
  padding: 0.185rem 0.25rem;
  display: inline-block;
  height: 2rem;
  width: 2rem;
}

.custom-day.focused {
  background-color: #e6e6e6;
}

.custom-day.range,
.custom-day:hover {
  background-color: var(--warning);
  color: white;
}

.custom-day.faded {
  background-color: var(--warning);
}

::ng-deep .ngb-dp-day.disabled {
  opacity: 0.5;
}

::ng-deep .ngb-dp-header {
  height: 2.6rem;
}

::ng-deep ngb-datepicker-month .ngb-dp-week:first-child {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.custom-icon {
  position: absolute;
  z-index: 111;
  top: 50%;
  right: 35px;
}
