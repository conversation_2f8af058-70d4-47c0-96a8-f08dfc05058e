<ng-container class="ngb-date-picker-range">
  <form [formGroup]="form" class="row row-cols-sm-auto">
    <div class="dp-hidden position-absolute" [ngStyle]="{'top': showLabel ? '161px' : '97px'}">
      <div class="input-group">
        <input
          name="datepicker"
          class="form-control"
          ngbDatepicker
          #datepicker="ngbDatepicker"
          [autoClose]="'outside'"
          (dateSelect)="onDateSelection($event)"
          [displayMonths]="1"
          [dayTemplate]="t"
          [showWeekdays]="showWeekDaysName"
          [markDisabled]="isDisabled"
          [maxDate]="maxDate"
          [minDate]="minDate"
          [placement]="'bottom'"
          outsideDays="hidden"
          [startDate]="fromDate!"
          tabindex="-1"
        />
        <ng-template #t let-date let-focused="focused">
					<span
            class="custom-day"
            [class.focused]="focused"
            [class.range]="isRange(date)"
            [class.faded]="isHovered(date) || isInside(date)"
            (mouseenter)="hoveredDate = date"
            (mouseleave)="hoveredDate = null"
          >
						{{ date.day }}
					</span>
        </ng-template>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group">
        <div>
          <label *ngIf="showLabel" [for]="startDateFormControlName">
            {{ startInputLabel | translate }}
          </label>
          <input
            #dpFromDate
            class="form-control"
            [placeholder]="startInputPlaceHolder | translate"
            [formControlName]="startDateFormControlName"
            [name]="startDateFormControlName"
            [value]="formatter.format(fromDate)"
            [readOnly]="readOnly"
            (click)="datepicker.toggle()"
            (input)="fromDate = validateInput(fromDate, dpFromDate.value)"
          />
          <i class="icon icon-calendar custom-icon" [ngStyle]="{'top': showLabel ? '36px' : '18px'}"
             (click)="datepicker.toggle()"></i>
        </div>
        <div
          *ngIf="!form.controls[startDateFormControlName]?.value && !datepicker.isOpen()"
          [ngClass]="{ 'd-block': isShowError(form.controls[startDateFormControlName]) || isShowError(form.controls[startDateFormControlName]) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls[startDateFormControlName]) | translate }}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group">
        <div>
          <label *ngIf="showLabel" [for]="endDateFormControlName">
            {{ endInputLabel | translate }}
          </label>
          <input
            #dpToDate
            class="form-control"
            [placeholder]="endInputPlaceHolder | translate"
            [formControlName]="endDateFormControlName"
            [name]="endDateFormControlName"
            [value]="formatter.format(toDate)"
            [readOnly]="readOnly"
            (click)="datepicker.toggle()"
            (input)="toDate = validateInput(toDate, dpToDate.value)"
          />
          <i class="icon icon-calendar custom-icon" [ngStyle]="{'top': showLabel ? '36px' : '18px'}"
             (click)="datepicker.toggle()"></i>
        </div>
        <div
          *ngIf="!form.controls[endDateFormControlName]?.value && !datepicker.isOpen()"
          [ngClass]="{ 'd-block': isShowError(form.controls[endDateFormControlName]) || isShowError(form.controls[endDateFormControlName]) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls[endDateFormControlName]) | translate }}
        </div>
      </div>
    </div>
  </form>
</ng-container>
