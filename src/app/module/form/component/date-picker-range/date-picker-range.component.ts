import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Ng<PERSON><PERSON><PERSON><PERSON><PERSON>, NgbDate, NgbDateParserFormatter, NgbDatepickerI18n, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { CustomDatepickerI18n, NgbDateCustomParserFormatter } from '../date-picker/date-picker.component';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';

@Component({
  selector: 'cat-date-picker-range',
  templateUrl: './date-picker-range.component.html',
  styleUrls: ['./date-picker-range.component.scss'],
  providers: [
    { provide: NgbDatepickerI18n, useClass: CustomDatepickerI18n },
    { provide: NgbDateParserFormatter, useClass: NgbDateCustomParserFormatter }
  ]
})
export class DatePickerRangeComponent implements OnInit {
  @Input()
  maxDate: NgbDateStruct = null;

  @Input()
  minDate: NgbDateStruct = null;

  @Input()
  weekendDisable = true;

  @Input()
  showWeekDaysName = false;
  @Input()
  readOnly = true;

  @Input()
  form: FormGroup = new FormGroup({
    startDate: new FormControl(null, [Validators.required]),
    endDate: new FormControl(null, [Validators.required]),
  });

  @Input()
  startDateFormControlName = 'startDate';

  @Input()
  endDateFormControlName = 'endDate';

  @Input()
  startInputPlaceHolder = '_start_date';
  @Input()
  endInputPlaceHolder = '_end_date';
  @Input()
  startInputLabel = this.startInputPlaceHolder;
  @Input()
  endInputLabel = this.endInputPlaceHolder;
  @Input()
  showLabel = false;

  @Output()
  fromDateValue = new EventEmitter();
  @Output()
  toDateValue = new EventEmitter();

  hoveredDate: NgbDate | null = null;

  fromDate: NgbDate | null;
  toDate: NgbDate | null;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  isDisabled = (date: NgbDate) => this.getMinDisableDate(date) || this.getMaxDisableDate(date) || this.isWeekend(date);

  ngOnInit() {
    const nowDate = new Date(Date.now());
    if (!this.maxDate) {
      this.maxDate = {
        year: nowDate.getFullYear(),
        month: nowDate.getMonth() + 1,
        day: nowDate.getDate()
      };
    }
    if (!this.minDate) {
      this.minDate = {
        year: nowDate.getFullYear() - 1,
        month: nowDate.getMonth() + 1,
        day: nowDate.getDate()
      };
    }
  }

  constructor(
    private calendar: NgbCalendar,
    public formatter: NgbDateParserFormatter
  ) {}

  public clearFilter(): void {
    this.form.reset();
    this.fromDate = null;
    this.toDate = null;
  }

  isWeekend(date) {
    return this.weekendDisable ? this.calendar.getWeekday(date) >= 6 : false;
  }

  getMinDisableDate(date) {
    return date.day > this.maxDate.day && date.month === this.maxDate.month && date.year === this.maxDate.year;
  }

  getMaxDisableDate(date) {
    return date.day < this.minDate.day && date.month === this.minDate.month && date.year === this.minDate.year;
  }

  onDateSelection(date: NgbDate) {
    if (!this.fromDate && !this.toDate) {
      this.fromDate = date;
    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {
      this.toDate = date;
    } else {
      this.toDate = null;
      this.fromDate = date;
    }
    this.fromDateValue.emit(this.formatter.format(this.fromDate));
    this.toDateValue.emit(this.formatter.format(this.toDate));
  }

  isHovered(date: NgbDate) {
    return (
      this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.fromDate) ||
      (this.toDate && date.equals(this.toDate)) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
  }
}
