<div [formGroup]="form">
  <ng-select
    *ngIf="equipmentList?.length || dropdownSearchText || !firstsearch"
    #serialSelect
    class="service-drp"
    [placeholder]="'_machine_serial_number' | translate"
    [searchable]="true"
    [clearable]="false"
    [minTermLength]="apiSearch ? 3 : 0"
    [dropdownPosition]="'bottom'"
    [formControlName]="fieldName"
    [ngbTooltip]="'_search_min_length_3' | translate"
    placement="top"
    [addTag]="addTagDisable ? !addTagDisable : addTagFn"
    [addTagText]="'_serial_number' | translate"
    [searchFn]="apiSearch ? searchFn: null"
    (open)="openClickEvent()"
    (keypress)="maxlength($event)"
    (keyup)="onKeyUp($event)"
    (change)="onSelectEquipment($event)"
    (close)="serialSelect.blur()"
    (scrollToEnd)="scrollToEndEvent()"
    [searchWhileComposing]="false"
    #tooltip="ngbTooltip"
  >
    <ng-option
      #serialList
      *ngFor="let equipmentType of equipmentList"
      [value]="equipmentType.serialNumber"
    >
      {{ equipmentType.model }} /
      {{ equipmentType.serialNumber | serialFormat }}
      <ng-container *ngIf="showCampaignIcon && equipmentType?.equipmentRevisionCampaign">
        <div class="d-inline-block ml-2" *ngIf="equipmentType?.equipmentRevisionCampaign">
          <div [ngSwitch]="equipmentType?.equipmentRevisionCampaign?.type"
               class="d-flex justify-content-center text-success font-size-16px fw-bolder">
            <i *ngSwitchCase="'_campaign'" class="icon icon-campaign"></i>
            <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon"></i>
            <i *ngSwitchCase="'_discount'" class="icon {{ equipmentType?.equipmentRevisionCampaign?.currency | currenyIcon }}"></i>
            <span *ngSwitchCase="'_percent_discount'">%</span>
            <i *ngSwitchDefault class="icon icon-campaign"></i>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="showMuneccimIcon && equipmentType?.revisionIndicator">
        <div *ngIf="
                  equipmentType?.revisionIndicator === 'yellow' ||
                  equipmentType?.revisionIndicator === 'orange' ||
                  equipmentType?.revisionIndicator === 'red'
                " class="repair-icon d-inline-block ml-2" [ngClass]="{
                  'text-yellow': equipmentType?.revisionIndicator === 'yellow',
                  'text-warning': equipmentType?.revisionIndicator === 'orange',
                  'text-danger': equipmentType?.revisionIndicator === 'red'
                }">
          <div class="d-flex justify-content-center">
            <i class="icon icon-repair"></i>
          </div>
        </div>
      </ng-container>
    </ng-option>
  </ng-select>

  <input
    *ngIf="!equipmentList?.length && !dropdownSearchText && firstsearch"
    catInputLength
    [name]="'MachineSerialNumber'"
    [placeholder]="'_machine_serial_number' | translate"
    class="form-control form-control"
    [formControlName]="fieldName"
    type="text"
    minlength="3"
  />
</div>

<!--<cat-basic-modal [(status)]="showCampaignDetailModal">-->
<ng-container *ngIf="showCampaignDetail && equipmentDetail">
 <cat-equipment-revision-campaign [equipmentDetail]="equipmentDetail">
 </cat-equipment-revision-campaign>
</ng-container>
<!--</cat-basic-modal>-->
