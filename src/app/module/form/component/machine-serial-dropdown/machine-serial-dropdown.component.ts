import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Select, Store } from '@ngxs/store';
import { fromEvent, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, takeUntil, tap } from 'rxjs/operators';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { GetEquipmentNewSearchAction } from 'src/app/module/customer/action/equipment.action';
import { EquipmentModel } from 'src/app/module/customer/model/equipment.model';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { PagingModel } from 'src/app/module/definition/model/paging.model';

@Component({
  selector: 'cat-machine-serial-dropdown',
  templateUrl: './machine-serial-dropdown.component.html',
  styleUrls: ['./machine-serial-dropdown.component.scss']
})
export class MachineSerialDropdownComponent implements OnInit, OnDestroy {
  protected subscriptions$: Subject<boolean> = new Subject();
  // protected subscriptionFilter$: Subject<boolean> = new Subject();

  @Select(EquipmentState.equipmentListSearch)
  equipmentListSearch$: Observable<EquipmentModel[]>;

  @Select(EquipmentState.equipmentListLoading)
  equipmentListLoading$: Observable<boolean>;

  @Select(EquipmentState.equipmentListPaging)
  equipmentListPaging$: Observable<PagingModel>;

  @Input()
  equipmentList: EquipmentModel[];

  @Input()
  showMuneccimIcon = false;

  @Input()
  showCampaignIcon = false;
  @Input()
  showCampaignDetail = false;

  equipmentDetail: EquipmentModel;
  paging: PagingModel;
  dropdownSearchText = '';
  firstsearch = true;
  showCampaignDetailModal = false;

  @Input()
  form: FormGroup;

  @Input()
  fieldName = 'EquipmentSerialNumber';

  @Input()
  apiSearch = true;

  @Input()
  pageSize = 10;

  @Input()
  externalData = false;

  @Input()
  addTagDisable = false;

  @Output()
  selectEquipment: EventEmitter<EquipmentModel> = new EventEmitter<EquipmentModel>();

  private page = 1;
  private customerNumber: string;
  descriptionLink: any;

  @ViewChild('tooltip') tooltip: NgbTooltip;

  constructor(
    private readonly store: Store,
  ) { }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  ngOnInit() {
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    this.customerNumber = loginResponse.customer?.customerNumber;

    if (this.externalData) {
      return;
    }

    this.loadEquipments();

    this.equipmentListPaging$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(page => {
        if (page) {
          this.paging = page;
          this.page = this.paging.pageNumber;
        }
      });

    this.equipmentListSearch$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((list) => {
        this.equipmentList = list;
        // this.firstsearch = true;
        // this.subscriptionFilter$.next(true);
        // this.subscriptionFilter$.complete();
      });
  }

  onKeyUp(event) {
    if (this.firstsearch) {
      this.firstsearch = false;
      fromEvent(event.target, 'keyup')
        .pipe(
          filter(Boolean),
          map(() => {
            return event.target.value;
          }),
          tap((value) => {
            if (value.length < 3) {
              this.tooltip?.open();
            }
          }),
          takeUntil(this.subscriptions$),
          // takeUntil(this.subscriptionFilter$),
          debounceTime(400),
          distinctUntilChanged(),
          filter((value) =>
            value.length >= 3 ||
            value.length === 0,
          ),
          tap(() => {
            this.tooltip?.close();
          }),
        )
        .subscribe((value: string) => {
          this.dropdownScrollTop();
          this.dropdownSearchText = value.substring(0, 50);
          if (this.apiSearch) {
            this.page = 1;
            const searchFilter = value.length ? [
              {
                fieldName: 'search',
                fieldValue: value.substring(0, 50)
              }
            ] : [];

            this.store.dispatch(new GetEquipmentNewSearchAction(this.page, searchFilter, this.pageSize));
          }
        });
    }
  }

  onSelectEquipment(serialNumber: string) {
    this.equipmentDetail = null;
    this.equipmentDetail = this.equipmentList.find(e => e.serialNumber === serialNumber);
    this.selectEquipment.emit(this.equipmentDetail);
    this.showCampaignDetailModal = true;
    // console.log('Equipment selected: ', serialNumber);
  }

  loadEquipments() {
    const searchFilter = this.dropdownSearchText ? [
      {
        fieldName: 'search',
        fieldValue: this.dropdownSearchText
      }
    ] : [];

    this.store.dispatch(new GetEquipmentNewSearchAction(this.page, searchFilter, this.pageSize));
  }

  scrollToEndEvent() {
    this.nextPage();
  }

  dropdownScrollTop() {
    if (document.querySelectorAll('cat-machine-serial-dropdown .ng-dropdown-panel .scroll-host') !== undefined) {
      document.querySelectorAll('cat-machine-serial-dropdown .ng-dropdown-panel .scroll-host')[0]?.scrollTo(0, 0);
    }
  }

  openClickEvent() {
    this.tooltip?.close();
  }

  nextPage() {
    if (!this.paging) {
      return;
    }
    if (this.paging.pageNumber * this.paging.pageSize < this.paging.totalCount) {
      this.page++;
      this.loadEquipments();
    }
  }

  searchFn() {
    return true;
  }


  addTagFn(name) {
    if (name.length > 50) {
      return name.substring(0, 50);
    }
    return name;
  }

  maxlength(event) {
    event.target.maxLength = 50;
  }
}
