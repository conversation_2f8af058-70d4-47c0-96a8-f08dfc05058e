import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { UserEventLogModule } from 'src/app/export/user-event-log/user-event-log.module';
import { SharedModule } from '../../shared/shared.module';
import { MachineSerialDropdownComponent } from './machine-serial-dropdown/machine-serial-dropdown.component';
import { MachineSerialComponent } from './machine-serial/machine-serial.component';

@NgModule({
  declarations: [
    MachineSerialDropdownComponent,
    MachineSerialComponent
  ],
  exports: [
    MachineSerialDropdownComponent,
    MachineSerialComponent
  ],
  imports: [
    CommonModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NgxLoadingModule,
    SharedModule,
    UserEventLogModule,
    NgbTooltipModule,
  ],
  providers: []
})
export class MachineSerial { }
