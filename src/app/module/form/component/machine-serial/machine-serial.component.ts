import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngxs/store';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { EquipmentModel } from 'src/app/module/customer/model/equipment.model';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';

@Component({
  selector: 'cat-machine-serial',
  templateUrl: './machine-serial.component.html',
  styleUrls: ['./machine-serial.component.scss']
})
export class MachineSerialComponent implements OnInit {

  customerNumber: string;

  @Input()
  form: FormGroup;

  @Input()
  fieldName = 'EquipmentSerialNumber';

  @Output()
  selectEquipment: EventEmitter<EquipmentModel> = new EventEmitter<EquipmentModel>();

  @Input()
  apiSearch = true;

  @Input()
  pageSize = 10;

  @Input()
  showMuneccimIcon = false;

  @Input()
  showCampaignIcon = false;
  @Input()
  showCampaignDetail = false;

  constructor(
    private readonly store: Store,
  ) { }

  ngOnInit() {
    this.store.dispatch(new GetBasisSettingsAction());
    const loginResponse = this.store.selectSnapshot(LoginState.loginResponse);
    this.customerNumber = loginResponse?.customer?.customerNumber;
    this.form.get(this.fieldName).setValidators([
      Validators.pattern('^[0-9A-Za-z/-]*$'),
      Validators.required
    ]);
  }

  selectedEquipment(equipment: EquipmentModel) {
    this.selectEquipment.emit(equipment);
  }

  getMakeCat2Serial() {
    if (this.form.value.Make) {
      return this.form.value.Make.slice(0, 3).toLowerCase() === 'cat' ? 8 : 50;
    } else {
      return 50;
    }
  }
  onInput(e) {
    e.target.value = e.target.value.replace(/[^A-Za-z0-9]/g, '');
  }

}
