import { SurveyFormTypeEnum } from '../enum/form.enum';
import { SurveyFormAnswerBodyModel } from '../model/form.modal';

export class FormAction {
  public static readonly type = '[Form] Add item';

  constructor(public payload: any) {
  }
}
export class FormRequestServiceGetReservationsDatesAction {
  public static readonly type = '[Form] Get Request Service Reservations Dates';

  constructor(public serviceCategory: string, public city?: string) {
  }
}
export class FormRequestServiceGetProductGroupsAction {
  public static readonly type = '[Form] Get Request Service Product Groups';

  constructor() {
  }
}
export class SurveyFormGetQuestionsAction {
  public static readonly type = '[Form] Get Survey Form Questions';

  constructor(public Position: SurveyFormTypeEnum, public GetDetails: boolean) {
  }
}
export class SurveyFormAnswerAction {
  public static readonly type = '[Form] Send Survey Form Answer';

  constructor(public body: SurveyFormAnswerBodyModel) {
  }
}
export class SurveyFormClearAction {
  public static readonly type = '[Form] Send Survey Form Clear';

  constructor() {
  }
}
