import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { HttpResponse } from '../../../../response/http.response';
import { SurveyFormTypeEnum } from '../../enum/form.enum';
import {
  FormServiceProductGroupsModel,
  RequestServiceReservationDayStatusModel,
  SurveyFormAnswerBodyModel,
  SurveyFormModel
} from '../../model/form.modal';

@Injectable({
  providedIn: 'root',
})
export class FormService {
  constructor(
    private readonly http: HttpClient,
  ) {
  }

  sendRequestService(form: any, headers: any = {}): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/service/create`, form, { headers });
  }

  sendPartRequest(form: any, headers: any = {}): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/part/create`, form, { headers });
  }

  sendMDARequest(form: any, headers: any = {}): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/mda/create`, form, { headers });
  }

  equipmentCreate(form: any): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/equipment/create`, form);
  }

  equipmentUpdate(form: any): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/equipment/update`, form);
  }

  equipmentRemove(form: any): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/equipment/delete`, form);
  }

  serviceFormAvailableReservationDates(serviceCategory: string, city?: string): Observable<RequestServiceReservationDayStatusModel[]> {
    return this.http.post<HttpResponse<RequestServiceReservationDayStatusModel[]>>(`${environment.api}/form/getformavailablereservationdates`, {
      ServiceCategory: serviceCategory, City: city
    })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  getProductGroups(): Observable<FormServiceProductGroupsModel[]> {
    return this.http.get<HttpResponse<FormServiceProductGroupsModel[]>>(`${environment.api}/form/get/productgroups`)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  getSurveyForm(Position: SurveyFormTypeEnum, GetDetails: boolean): Observable<SurveyFormModel[]> {
    return this.http.post<HttpResponse<SurveyFormModel[]>>(`${environment.api}/survey/getsurveys`, {Position, GetDetails})
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  surveyFormAnsver(body: SurveyFormAnswerBodyModel): Observable<string> {
    return this.http.post<HttpResponse<string>>(`${environment.api}/survey/getsurveys`, body)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

}
