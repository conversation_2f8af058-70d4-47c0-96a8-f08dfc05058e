import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { EnumToArrayModule } from '../../export/enum-to-array/enum-to-array.module';
import { FileUploadModule } from '../../export/file-upload/file-upload.module';
import { MediaModule } from '../../export/media/media.module';
import { TakeAPhotoModule } from '../../export/take-a-photo/take-a-photo.module';
import { FormLayoutComponent } from './container/form-layout/form-layout.component';
import { RequestEquipmentPartFormComponent } from './container/request-equipment-part-form/request-equipment-part-form.component';
import { RequestMdaFormComponent } from './container/request-mda-form/request-mda-form.component';
import { RequestServiceFormComponent } from './container/request-service-form/request-service-form.component';
import { SellEquipmentFormComponent } from './container/sell-equipment-form/sell-equipment-form.component';
import { FormRoutingModule } from './form-routing.module';
import { SharedModule } from '../shared/shared.module';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { FormLabelComponent } from './container/form-label/form-label.component';
import { NgbDatepickerModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { MachineSerial } from './component/machine-serial.module';
import { DatePickerComponent } from './component/date-picker/date-picker.component';
import { DatePickerRangeComponent } from './component/date-picker-range/date-picker-range.component';
import { MyEquipmentAgendaComponent } from '../customer/container/equipment/my-equipment-agenda/my-equipment-agenda.component';
import { HasPermissionsModule } from 'src/app/export/permissions/has-permissions.module';

@NgModule({
  declarations: [
    FormLayoutComponent,
    RequestServiceFormComponent,
    SellEquipmentFormComponent,
    RequestEquipmentPartFormComponent,
    RequestMdaFormComponent,
    FormLabelComponent,
    DatePickerComponent,
    DatePickerRangeComponent,
    MyEquipmentAgendaComponent
  ],
  imports: [
    CommonModule,
    FormRoutingModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NgxLoadingModule,
    EnumToArrayModule,
    FileUploadModule,
    MediaModule,
    TakeAPhotoModule,
    SharedModule,
    UserEventLogModule,
    NgbTooltipModule,
    MachineSerial,
    NgbDatepickerModule,
    HasPermissionsModule
  ],
  exports: [
    DatePickerRangeComponent
  ]
})
export class FormModule {

  constructor() {
    stopLoadingAnimation();
  }
}
