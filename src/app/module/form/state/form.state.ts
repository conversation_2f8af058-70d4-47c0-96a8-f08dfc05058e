import { Injectable } from '@angular/core';
import { State, Selector, Action, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  FormRequestServiceGetProductGroupsAction,
  FormRequestServiceGetReservationsDatesAction,
  SurveyFormAnswerAction,
  SurveyFormClearAction,
  SurveyFormGetQuestionsAction
} from '../action/form.actions';
import { FormServiceProductGroupsModel, RequestServiceReservationDayStatusModel, SurveyFormModel } from '../model/form.modal';
import { FormService } from '../service/form/form.service';

export interface FormStateModel {
  items: string[];
  formLoading: boolean;
  reqServiceReservationDates: RequestServiceReservationDayStatusModel[];
  getSurveyFormQuestions: SurveyFormModel[];
  surveyFormLoading: boolean;
  productGroups: FormServiceProductGroupsModel[];
}

@State<FormStateModel>({
  name: 'form',
  defaults: {
    items: [],
    formLoading: false,
    reqServiceReservationDates: [],
    getSurveyFormQuestions: [],
    surveyFormLoading: false,
    productGroups: [],
  }
})
@Injectable()
export class FormState {

  constructor(private readonly formService: FormService) { }

  @Selector()
  public static getState(state: FormStateModel) {
    return state;
  }

  @Selector()
  public static formLoading({ formLoading }: FormStateModel): boolean {
    return formLoading;
  }

  @Selector()
  public static getServiceReservationsDate({ reqServiceReservationDates }: FormStateModel): RequestServiceReservationDayStatusModel[] {
    return reqServiceReservationDates;
  }

  @Selector()
  public static getProductGroups({ productGroups }: FormStateModel): any {
    return productGroups;
  }

  @Selector()
  public static getSurveyFormQuestions({ getSurveyFormQuestions }: FormStateModel): SurveyFormModel[] {
    return getSurveyFormQuestions;
  }

  @Selector()
  public static surveyFormLoading({ surveyFormLoading }: FormStateModel): boolean {
    return surveyFormLoading;
  }

  @Action(FormRequestServiceGetReservationsDatesAction)
  getPaymentStatusState(
    { patchState }: StateContext<FormStateModel>,
    { serviceCategory, city }: FormRequestServiceGetReservationsDatesAction
  ) {
    patchState({
      formLoading: true
    });
    return this.formService.serviceFormAvailableReservationDates(serviceCategory, city).pipe(
      tap((value) => {
        patchState({
          formLoading: false,
          reqServiceReservationDates: value
        });
      }),
      catchError((err) => {
        patchState({
          formLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(FormRequestServiceGetProductGroupsAction)
  getProductGroupsState(
    { patchState }: StateContext<FormStateModel>
  ) {
    patchState({
      formLoading: true
    });
    return this.formService.getProductGroups().pipe(
      tap((value) => {
        patchState({
          formLoading: false,
          productGroups: value
        });
      }),
      catchError((err) => {
        patchState({
          formLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(SurveyFormGetQuestionsAction)
  getSurveyFormQuestionsState(
    { patchState }: StateContext<FormStateModel>,
    { Position, GetDetails }: SurveyFormGetQuestionsAction
  ) {
    patchState({
      surveyFormLoading: true
    });
    return this.formService.getSurveyForm(Position, GetDetails).pipe(
      tap((value) => {
        patchState({
          surveyFormLoading: false,
          getSurveyFormQuestions: value,
        });
      }),
      catchError((err) => {
        patchState({
          surveyFormLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(SurveyFormAnswerAction)
  sendSurveyFormAnswerState(
    { patchState }: StateContext<FormStateModel>,
    { body }: SurveyFormAnswerAction
  ) {
    patchState({
      surveyFormLoading: true
    });
    return this.formService.surveyFormAnsver(body).pipe(
      tap(() => {
        patchState({
          surveyFormLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          surveyFormLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(SurveyFormClearAction)
  surveyFormClearState(
    { patchState }: StateContext<FormStateModel>,
  ) {
    patchState({
      getSurveyFormQuestions: []
    });
  }
}
