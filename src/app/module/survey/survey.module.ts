import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SurveyLayoutComponent } from './component/survey-layout/survey-layout.component';
import { SurveyInitComponent } from './component/survey-init/survey-init.component';
import { SurveyStepComponent } from './component/survey-step/survey-step.component';
import { SurveyService } from './service/survey.service';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';
import { UserEventLogModule } from '../../export/user-event-log/user-event-log.module';
import { RouterModule } from '@angular/router';
import { SurveyRoutingModule } from './survey-routing.module';
import { SurveyQuestionBuilderComponent } from './component/question-builder/survey-question-builder.component';
import { ListBoxComponent } from './component/field-list-box/list-box.component';
import { FieldTextComponent } from './component/field-text/field-text.component';
import { SurveyFieldTextareaComponent } from './component/survey-field-textarea/survey-field-textarea.component';
import { RadioBoxComponent } from './component/field-radio-box/radio-box.component';

@NgModule({
  declarations: [
    SurveyLayoutComponent,
    SurveyInitComponent,
    SurveyStepComponent,
    SurveyQuestionBuilderComponent,
    ListBoxComponent,
    RadioBoxComponent,
    FieldTextComponent,
    SurveyFieldTextareaComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    UserEventLogModule,
    SurveyRoutingModule,
  ],
  providers: [SurveyService]
})
export class SurveyModule {}
