import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import {
  GetSurveysAction,
  GetPulseSurveyAction,
  SetActiveSurveyAction,
  SetActiveSurveyPulseAction,
} from './inspection.actions';
import { SurveyService } from '../service/survey.service';
import { SurveyModel } from '../model/survey.model';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';

export interface SurveyStateModel {
  surveyList: SurveyModel[];
  surveyLoading: boolean;
  activeForm: SurveyModel;
}

@State<SurveyStateModel>({
  name: 'survey',
  defaults: {
    surveyList: [],
    surveyLoading: false,
    activeForm: null,
  }
})
@Injectable()
export class SurveyState {

  constructor(
    private readonly surveyService: SurveyService
  ) {}

  @Selector()
  public static getState(state: SurveyStateModel) {
    return state;
  }

  @Selector()
  public static surveyList({ surveyList }: SurveyStateModel): SurveyModel[] {
    return surveyList;
  }

  @Selector()
  public static surveyLoading({ surveyLoading }: SurveyStateModel): boolean {
    return surveyLoading;
  }

  @Selector()
  public static activeForm({ activeForm }: SurveyStateModel): SurveyModel {
    return activeForm;
  }


  @Action(GetSurveysAction)
  getInspectionFormListAction(
    { patchState }: StateContext<SurveyStateModel>,
    action: GetSurveysAction,
  ) {
    patchState({ surveyLoading: true });
    return this.surveyService.getList()
      .pipe(
        tap(list => {
          patchState({
            surveyList: this.buildList(list),
            surveyLoading: false
          });
        }),
        catchError((err) => {
          patchState({ surveyLoading: true });
          return throwError(err);
        })
      );
  }

  @Action(SetActiveSurveyAction)
  setActiveSurveyAction(
    { patchState }: StateContext<SurveyStateModel>,
    { form }: SetActiveSurveyAction,
  ) {
    patchState({ activeForm: form });
  }

  @Action(GetPulseSurveyAction)
  getInspectionFormListPulseAction(
    { patchState }: StateContext<SurveyStateModel>,
    action: GetSurveysAction,
  ) {
    patchState({ surveyLoading: true });
    return this.surveyService.getListPulse()
      .pipe(
        tap(list => {
          patchState({
            surveyList: this.buildList(list),
            surveyLoading: false
          });
        }),
        catchError((err) => {
          patchState({ surveyLoading: true });
          return throwError(err);
        })
      );
  }

  @Action(SetActiveSurveyPulseAction)
  setActiveSurveyPulseAction(
    { patchState }: StateContext<SurveyStateModel>,
    { form }: SetActiveSurveyAction,
  ) {
    patchState({ activeForm: form });
  }

  protected buildList(list: SurveyModel[]) {
    return (list || []).map(item => {
      return {
        ...item,
        groups: item.groups.map(group => {
          return {
            ...group,
            questions: group.questions.map(question => {
              return {
                ...question,
                answers: [
                  ...question.answers,
                  ...(question.conditionalQuestions || []).reduce((acc, condition) => {
                    return acc.concat(condition.answers.map(answer => {
                      return {
                        scoreCriteria: condition.scoreCriteria,
                        answerId: answer.optionId,
                        title: answer.title,
                        answerType: 'Text',
                        isMultipleAnswerAllowed: false,
                        options: [],
                      };
                    }));
                  }, [])
                ],
              };
            })
          };
        })
      };
    });
  }
}
