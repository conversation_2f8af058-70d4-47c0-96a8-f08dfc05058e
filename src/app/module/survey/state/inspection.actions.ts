import { SurveyModel } from '../model/survey.model';

export class GetSurveysAction {
  public static readonly type = '[Survey] Get Survey List';

  constructor() {
  }
}


export class SetActiveSurveyAction {
  public static readonly type = '[Survey] set active form';

  constructor(public form: SurveyModel) {
  }
}

export class GetPulseSurveyAction {
  public static readonly type = '[Survey] Get Survey List pulse';

  constructor() {
  }
}


export class SetActiveSurveyPulseAction {
  public static readonly type = '[Survey] set active form pulse';

  constructor(public form: SurveyModel) {
  }
}
