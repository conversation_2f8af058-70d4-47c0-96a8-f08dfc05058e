import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../response/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { SurveyModel } from '../model/survey.model';
import { of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SurveyService {

  constructor(
    private readonly http: HttpClient
  ) { }


  public getList() {
    return this.http.get<HttpResponse<SurveyModel[]>>(`${environment.api}/survey/instantsurvey`).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public answer(body, headers = {}) {
    return this.http.post<HttpResponse<SurveyModel>>(`${environment.api}/survey/answeractivitysurvey`,
      body,
      {
        headers
      }
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }


  public getListPulse() {
    return this.http.post<HttpResponse<SurveyModel>>(`${environment.api}/survey/instantsurvey`,
      {
        "Position":"INSTANT_SURVEY_MAIN",
        "GetDetails":true
      },
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  public answerPulse(body, headers = {}) {
    return this.http.post<HttpResponse<SurveyModel>>(`${environment.api}/survey/answerpulsesurvey`,
      body,
      {
        headers
      }
    ).pipe(
      map(val => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

}
