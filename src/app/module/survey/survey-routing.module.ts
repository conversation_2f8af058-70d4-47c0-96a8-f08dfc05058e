import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { SurveyLayoutComponent } from './component/survey-layout/survey-layout.component';
import { SurveyInitComponent } from './component/survey-init/survey-init.component';
import { SurveyStepComponent } from './component/survey-step/survey-step.component';

const routes: Routes = [
  {
    path: '',
    component: SurveyLayoutComponent,
    children: [
      { path: '', component: SurveyInitComponent },
      { path: ':formId', redirectTo: ':formId/step/1' },
      { path: ':formId/step/:id', component: SurveyStepComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  declarations: [],
})
export class SurveyRoutingModule {
  constructor() {
    stopLoadingAnimation();
  }
}
