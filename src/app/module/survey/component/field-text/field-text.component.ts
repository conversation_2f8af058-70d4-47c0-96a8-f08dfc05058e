import { Component, Input, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { FormGroup } from '@angular/forms';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { Store } from '@ngxs/store';
import { SurveyAnswer, SurveyQuestion } from '../../model/survey.model';

@Component({
  selector: 'cat-field-text',
  templateUrl: './field-text.component.html',
  styleUrls: ['./field-text.component.scss']
})
export class FieldTextComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Input()
  form: FormGroup;

  @Input()
  question: SurveyQuestion;

  @Input()
  field: SurveyAnswer;

  constructor(
    readonly store: Store
  ) { }

  ngOnInit(): void {
    // this.store.dispatch(new GetBasisSettingsAction());
  }
}
