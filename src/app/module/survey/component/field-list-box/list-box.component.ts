import { Component } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { Validators } from '@angular/forms';
import { SurveyAnswer, SurveyOption, SurveyQuestionOption } from '../../model/survey.model';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'cat-list-box',
  templateUrl: './list-box.component.html',
  styleUrls: ['./list-box.component.scss']
})
export class ListBoxComponent extends FieldTextComponent {

  badRateIcon = `${environment.assets}/bad-rate.png`;
  goodRateIcon = `${environment.assets}/good-rate.png`;
  middleRateIcon = `${environment.assets}/middle-rate.png`;
  averageRateIcon = `${environment.assets}/average-rate.png`;

  onChange(opt: SurveyOption) {
    this.field.selected = opt;

    // // this.form.get(this.field.questionId).get('Comment').setErrors(null);
    // this.form.get(this.field.questionId).get('Comment').setValidators(
    //   opt.isCommentBoxRequired ? [Validators.required] : []);
    // this.form.get(this.field.questionId).get('Comment').updateValueAndValidity();

  }

  getSurveyRateIconByRate(rate: number) {
    if (rate < 3) {
      return this.badRateIcon;
    } else if (rate < 7 && rate > 2) {
      return this.averageRateIcon;
    } else if (rate > 6 && rate < 9) {
      return this.middleRateIcon;
    } else {
      return this.goodRateIcon;
    }
  }

  getSurveyRateColorByRate(rate: number): string {
    const colors = [
      '#921f1d', // 1
      '#bc2e1d', // 2
      '#c83825', // 3
      '#c83825', // 4
      '#cb512b', // 5
      '#db8e2c', // 6
      '#efc44b', // 7
      '#f0df4c', // 8
      '#acca7f', // 9
      '#9cc390', // 10
    ];

    return colors[rate - 1];
  }
}
