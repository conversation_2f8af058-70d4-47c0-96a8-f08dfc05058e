<div [formGroup]="form">
  <div [formGroupName]="question.questionId">
    <div
      *ngIf="field.options?.length"
      class="form-check btn-group w-100 mb-3"
      style="align-items: center; padding: 0; white-space: nowrap"
    >
      <ng-container *ngFor="let opt of field.options; let i = index">
        <input
          type="radio"
          class="btn-check d-none"
          [id]="opt.optionId"
          [formControlName]="'Answer'"
          [value]="opt.optionId"
          (change)="onChange(opt)"
        />
        <label
          class="rate-container btn btn-sm d-flex flex-column align-items-center justify-content-center"
          style="padding: 0.25rem; height: 45px"
          [class.selected]="field?.selected?.optionId === opt.optionId"
          [class.unselected]="
            field?.selected?.optionId !== opt.optionId &&
            field?.selected?.optionId
          "
          [for]="opt.optionId"
        >
          <div
            class="rate-box"
            [ngStyle]="{ 'background-color': getSurveyRateColorByRate(i + 1) }"
          >
            <img
              class="rate-icon"
              [src]="getSurveyRateIconByRate(i + 1)"
              width="10"
              height="10"
              *ngIf="opt.title !== 'Empty'"
            />
          </div>
          <span
            class="rate-number"
            [class.selected]="field?.selected?.optionId === opt.optionId"
          >
            {{ opt.title }}
          </span>
        </label>
      </ng-container>
    </div>
  </div>
</div>
