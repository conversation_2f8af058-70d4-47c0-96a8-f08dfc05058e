@import "variable/bootstrap-variable";

label {
  margin-bottom: 0;
}

.btn-sm {
  padding: 0.27rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 8px;
}

.btn-group > .btn {
  position: unset;
}

.form-check {
  position: unset;
  overflow-wrap: anywhere;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.btn-group .btn {
  border-radius: 6px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:nth-child(n + 3),
.btn-group > :not(.btn-check) + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rate-number {
  background: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  line-height: 18px;
  margin: 0 auto;
  margin-top: 8px;
  border: 1px solid transparent;
}

.rate-number.selected {
  border-color: #000000;
}


.rate-icon{
  min-height: 10px;
}

.rate-box{
  width: 23px;
  height: 23px;
  min-height: 23px;
  border-radius: 50%;
}

.rate-container.unselected{
  opacity: .5;
}
