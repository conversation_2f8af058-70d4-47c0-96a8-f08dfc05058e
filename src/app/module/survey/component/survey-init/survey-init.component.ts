import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Router } from '@angular/router';
import { SerialFormatPipe } from '../../../shared/pipe/serial-format.pipe';
import { Observable } from 'rxjs';
import { SurveyModel } from '../../model/survey.model';
import { SurveyState } from '../../state/survey.state';
import {
  GetPulseSurveyAction,
  GetSurveysAction,
  SetActiveSurveyAction,
  SetActiveSurveyPulseAction
} from '../../state/inspection.actions';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'cat-survey-init',
  templateUrl: './survey-init.component.html',
  styleUrls: ['./survey-init.component.scss'],
  providers: [SerialFormatPipe]
})
export class SurveyInitComponent implements OnInit {
  @Select(SurveyState.surveyList)
  surveyList$: Observable<SurveyModel[]>;

  @Select(SurveyState.surveyLoading)
  surveyLoading$: Observable<boolean>;
  surveyList = [];
  private isPulse: boolean;

  constructor(
    private store: Store,
    private router: Router
  ) { }

  ngOnInit() {
    this.isPulse = this.router.url.includes('/pulse-survey');

    const action = !this.isPulse ? new GetSurveysAction() : new GetPulseSurveyAction();
    this.store.dispatch(action);

    this.surveyList$.subscribe(item => {
      if (item) {
        this.surveyList = item;
      }
    });
  }

  openSurvey(survey: SurveyModel) {
    const action = !this.isPulse ? new SetActiveSurveyAction(survey) : new SetActiveSurveyPulseAction(survey);

    this.store.dispatch(action);

    this.router
      .navigate([
        ...environment.rootUrl.split('/'),
        this.isPulse ? 'pulse-survey' : 'survey',
        survey.activityId, 'step', 1
      ]);
  }


}
