<div class="survey-list p-3">
  <div class="h4 py-2 mb-0 text-center">{{ "_survey_list" | translate }}</div>

  <p class="text-justify px-2">
    {{ "_survey_list_description" | translate }}
  </p>

  <div *ngFor="let item of surveyList" class="flex flex-column">
    <button
      class="btn p-4 mb-2 w-100 border-bottom d-flex justify-content-between align-items-start"
      style="border-radius: 0"
      (click)="openSurvey(item)"

    >
      <div class="d-flex flex-column">
        <span class="text-left font-weight-bold">{{ item.formId }}</span>

        <div class="d-flex align-items-center">
          <span class="text-left font-weight-bold">{{ "_invoice_date" | translate }}:</span>
          <span class="text-left">&nbsp;{{ item.invoiceDate | date : "dd.MM.yyyy" }}</span>
        </div>
        <div class="d-flex align-items-center">
          <span class="text-left font-weight-bold">{{ "_invoice_number" | translate }}:</span>
          <span class="text-left">&nbsp;{{ item.invoiceNumber }}</span>
        </div>
        <div class="d-flex align-items-center">
          <span class="text-left font-weight-bold">{{ "_serial_number" | translate }}:</span>
          <span class="text-left">&nbsp;{{ item.serialNumber }}</span>
        </div>
      </div>
      <div class="d-flex align-items-center">
        <span class="text-info mr-2">{{ "_go_to_survey" | translate }}</span>
        <i class="icon icon-chevron-right text-info"></i>
      </div>
    </button>
  </div>
  <cat-empty-content
    *ngIf="!surveyList?.length && !(surveyLoading$ | async)"
    [message]="'_no_survey_found'"
    [extraMessage]="'_check_later'"
    [iconName]="'contract'">
  </cat-empty-content>
</div>

<cat-loader [show]="surveyLoading$ | async"></cat-loader>
