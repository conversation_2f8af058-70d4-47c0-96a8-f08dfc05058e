<div [formGroup]="form">
  <div [formGroupName]="question.questionId">
    <div
      *ngIf="field.options?.length"
      class="form-check btn-group w-100 mb-3 ml-2"
      style="align-items: center; padding: 0; white-space: nowrap"
    >
      <ng-container *ngFor="let opt of field.options; let i = index">
        <input type="radio"
               class="btn-check"
               [id]="opt.optionId"
               [formControlName]="'Answer'"
               [value]="opt.optionId"
               (change)="onChange(opt)"
        >
        <label class="col-3 "
               [class.selected]="field?.selected?.optionId === opt.optionId"

               [for]="opt.optionId">{{opt.title}}</label>

      </ng-container>
    </div>
  </div>
</div>
