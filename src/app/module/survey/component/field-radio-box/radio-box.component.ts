import { Component } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';
import { SurveyOption } from '../../model/survey.model';


@Component({
  selector: 'cat-radio-box',
  templateUrl: './radio-box.component.html',
  styleUrls: ['./radio-box.component.scss']
})
export class RadioBoxComponent extends FieldTextComponent {

  onChange(opt: SurveyOption) {
    this.field.selected = opt;
  }

}
