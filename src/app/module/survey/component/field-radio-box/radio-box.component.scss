@import "variable/bootstrap-variable";

label {
  margin-bottom: 0;
}

.btn-sm {
  padding: 0.27rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 8px;
}

.btn-group > .btn {
  position: unset;
}

.form-check {
  position: unset;
  overflow-wrap: anywhere;
}

.btn-check {

}

.btn-group .btn {
  border-radius: 6px;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:nth-child(n + 3),
.btn-group > :not(.btn-check) + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
