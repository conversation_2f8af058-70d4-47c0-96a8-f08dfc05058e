import { AfterViewInit, Component, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { SurveyState } from '../../state/survey.state';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonState } from '../../../shared/state/common/common.state';
import { FrameMessageEnum } from '../../../shared/enum/frame-message.enum';
import { MessageFrameService } from '../../../shared/service/message-frame.service';
import { disableBack } from '../../../../util/disable-back.util';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { SurveyGroup, SurveyModel, SurveySubmitRequest } from '../../model/survey.model';
import { SurveyService } from '../../service/survey.service';
import {
  GetPulseSurveyAction,
  GetSurveysAction,
  SetActiveSurveyAction,
  SetActiveSurveyPulseAction
} from '../../state/inspection.actions';
import { validateAllFormFieldsRecursive } from '../../../../util/validate-all-form-fields-recursive.util';
import { LoginState } from '../../../authentication/state/login/login.state';

@Component({
  selector: 'cat-survey-step',
  templateUrl: './survey-step.component.html',
  styleUrls: ['./survey-step.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
})
export class SurveyStepComponent implements OnInit, AfterViewInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  disableBack = disableBack;

  form: FormGroup = new FormGroup({});
  step: number;
  disableSubmitButton: boolean;
  @Select(SurveyState.activeForm)
  activeForm$: Observable<SurveyModel>;

  @Select(SurveyState.surveyLoading)
  formLoading$: Observable<boolean>;

  activeForm: SurveyModel;
  activeGroup: SurveyGroup;
  loading: boolean;
  successfullyDone: number;
  equipmentNumber: string;
  PermissionEnum = PermissionEnum;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private store: Store,
    private surveyService: SurveyService,
    private messageFrameService: MessageFrameService,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const { successfullyDone } = params;
      this.successfullyDone = parseInt(successfullyDone, 10);
    });

    this.route.params.subscribe(params => {
      const isPulse = this.router.url.includes('/pulse-survey');
      window.scroll({ top: 0 });

      this.form = new FormGroup({});
      const { id, formId } = params;

      this.step = parseInt(id, 10);

      this.activeForm = this.store.selectSnapshot(SurveyState.activeForm);
      if (!this.activeForm) {
        const action = !isPulse ? new GetSurveysAction() : new GetPulseSurveyAction();

        this.store.dispatch(action)
          .subscribe(() => {
            const current = this.store.selectSnapshot(SurveyState.surveyList).find(i => i.activityId === formId);
            if (!current) {
              console.warn('Survey not found, routing the list');
              this.router.navigate(['../../..'], { relativeTo: this.route });
              return;
            }
            const activeAction = true ? new SetActiveSurveyAction(current) : new SetActiveSurveyPulseAction(current);

            this.store.dispatch(activeAction);
          });
      } else {
        this.activeGroup = this.activeForm.groups.find(i => i.groupOrder === this.step);
      }
    });

    this.activeForm$.subscribe(form => {
      if (form) {
        this.activeForm = form;
        this.activeGroup = form.groups[this.step - 1];
      }
    });
  }

  ngAfterViewInit() {
  }

  navigateToBack() {
    this.back();
  }

  back() {
    window.history.back();
  }

  onSubmitForm() {
    console.log('submit', this.form.value);
    if (this.form.valid) {
      const body: SurveySubmitRequest = {
        activityId: this.activeForm.activityId,
        serialNumber: this.activeForm.serialNumber,
        invoiceNumber: this.activeForm.invoiceNumber,
        surveyId: this.activeForm.formId,
        language: this.store.selectSnapshot(LoginState.language),
        answers: []
      };
      //
      Object.entries(this.form.value).map((item: [string, any]) => {
        const [key, value] = item;
        body.answers.push({
          ...value,
          extraQuestions: value.extraQuestions?.filter(Boolean)
        });
      });

      // console.log('body', body);
      this.loading = true;
      // if (this.activeForm.groups.length > this.step) {
      //   this.router
      //     .navigate(['..', this.step + 1], { relativeTo: this.route });
      // } else {
      //
      // }

      const isPulse = this.router.url.includes('/pulse-survey');
      const obs = isPulse ? this.surveyService.answerPulse(body) : this.surveyService.answer(body);

      obs.subscribe((data: any) => {
        this.disableSubmitButton = true;
        this.loading = false;
        this.successfullyDone = 1;
      }, error => {
        this.loading = false;
        this.disableSubmitButton = false;
      }, () => {
        this.disableSubmitButton = false;
      });
    } else {
      validateAllFormFieldsRecursive(this.form);
      console.log('not valid', this.form.errors);
    }

  }

  close() {
    if (this.store.selectSnapshot(CommonState.currentCustomer)) {
      this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
      return;
    }

    this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
  }


}
