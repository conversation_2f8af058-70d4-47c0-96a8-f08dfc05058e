<div *ngIf="!successfullyDone">
  <div class="px-3 pb-3" style="background-color: #fafafa;" *ngIf="activeGroup">
    <div class="h4 py-4 mb-0 text-center nav-back">
      <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
      {{ activeGroup.groupTitle || ("_survey_form" | translate) }}
      ({{ step + " / " + activeForm?.groups.length | translate }})
    </div>

    <cat-info-box>
      {{ activeForm.greetings | translate }}
    </cat-info-box>

    <form (submit)="onSubmitForm()" [formGroup]="form">
      <ng-container [ngSwitch]="activeGroup.groupType">
        <ng-container *ngSwitchCase="'Questions'">
          <div class="d-flex flex-column content">
            <div class="flex-grow-1">
              <div
                *ngFor="let question of activeGroup.questions; let i = index"
              >
                <cat-survey-question-builder
                  [id]="step + '.' + (i + 1)"
                  [question]="question"
                  [form]="form"
                >
                </cat-survey-question-builder>
              </div>
            </div>

            <div class="d-flex justify-content-between">
              <a
                *ngIf="this.step !== 1"
                class="btn btn-secondary btn-gradient text-white shadow rounded-lg min-width-100"
                (click)="navigateToBack()"
              >
                <!--              [routerLink]="['../', step - 1]"-->

                {{ "_prev" | translate }}
              </a>
              <input
                [disabled]="disableSubmitButton"
                catUserClick
                [section]="'SURVEY'"
                [subsection]="
                  activeForm?.groups?.length === this.step
                    ? 'SURVEY_COMPLETE'
                    : 'SURVEY_CONTINUE'
                "
                [data]="{
                  surveyStep:
                    activeForm?.groups?.length === this.step
                      ? 'SURVEY_COMPLETE'
                      : this.step,
                  formId: activeForm?.formId
                }"
                [value]="
                  (activeForm?.groups?.length === this.step
                    ? '_complete_survey'
                    : '_next'
                  ) | translate
                "
                class="ml-auto btn btn-warning btn-gradient text-white shadow rounded-lg min-width-100"
                type="submit"
              />
            </div>
          </div>
        </ng-container>
      </ng-container>
    </form>
  </div>
</div>

<cat-loader [show]="loading || (formLoading$ | async)"></cat-loader>

<div *ngIf="successfullyDone" [@preview] class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">
      {{ "_survey_successfully_send" | translate }}
    </div>

    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="close()"
    >
      {{ "_close" | translate }}
    </div>
  </div>
</div>
