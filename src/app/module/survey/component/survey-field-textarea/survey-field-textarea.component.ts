import { Component, Input, OnInit } from '@angular/core';
import { FieldTextComponent } from '../field-text/field-text.component';

@Component({
  selector: 'cat-survey-field-textarea',
  templateUrl: './survey-field-textarea.component.html',
  styleUrls: ['./survey-field-textarea.component.scss']
})
export class SurveyFieldTextareaComponent extends FieldTextComponent implements OnInit {

  @Input()
  index: number;

  ngOnInit(): void {
    // this.store.dispatch(new GetBasisSettingsAction());
  }

}
