<div class="form-group mb-4" [formGroup]="form" *ngIf="question">
  <div class="d-flex mb-2">
        <div class="pr-2" style="font-size: 22px; line-height: 24px">
          <label class="orange"
                 [ngClass]="{
                  green: !!question?.answers[0]?.selected
                 }"
          >{{ id }}</label>
        </div>
    <div>
      <label *ngIf="question.question" class="form-control-label question-label font-size-14px"
             [attr.for]="question.question">
        {{ question.question }}
        <strong class="text-danger" *ngIf="question.isRequired">*</strong>
      </label>
    </div>
  </div>

  <div
    *ngFor="let answer of question.answers; let i = index"
    class="" [ngSwitch]="answer.answerType">
    <!--    <cat-field-text *ngSwitchCase="'text'" [field]="question" [form]="form"></cat-field-text>-->
    <!--    <cat-field-textarea *ngSwitchCase="'textarea'" [field]="question" [form]="form"></cat-field-textarea>-->
    <!--    <cat-field-select *ngSwitchCase="'dropdown'" [field]="question" [form]="form"></cat-field-select>-->
    <ng-container *ngSwitchCase="'Listbox'">
      <cat-list-box
        [question]="question"
        [field]="answer"
        [form]="form"
      ></cat-list-box>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Answer']) }"
        class="invalid-feedback pl-3"
      >
        {{
          getFormErrorMessage(form.controls[question.questionId]['controls']['Answer'], {
            required: '_required_answer'
          }) | translate
        }}
      </div>
    </ng-container>


    <ng-container *ngSwitchCase="'Radio'">
      <cat-radio-box
        [question]="question"
        [field]="answer"
        [form]="form"
      ></cat-radio-box>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Answer']) }"
        class="invalid-feedback pl-3"
      >
        {{
          getFormErrorMessage(form.controls[question.questionId]['controls']['Answer'], {
            required: '_required_answer'
          }) | translate
        }}
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="'Text'">
      <cat-survey-field-textarea
        *ngIf="!answer.scoreCriteria ||
        (answer.scoreCriteria && stateCheck(form.controls[question.questionId],question,answer.scoreCriteria ))"
        [question]="question"
        [field]="answer"
        [index]="i"
        [form]="form">
      </cat-survey-field-textarea>
      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Answer']) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Answer']) | translate }}-->
      <!--      </div>-->
    </ng-container>

    <!--    <div class="mt-2" *ngIf="question.isCommentBoxEnabled || question.selected?.isCommentBoxRequired">
          &lt;!&ndash;      <label class="col-form-label-sm">Yorumunuz:</label>&ndash;&gt;
          <cat-field-textarea *ngIf="question.isCommentBoxMultiLine"
                              [field]="question" [form]="form"></cat-field-textarea>
          <cat-field-text *ngIf="!question.isCommentBoxMultiLine"
                          [field]="question" [form]="form"></cat-field-text>

          <div
            [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Comment']) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls[question.questionId]['controls']['Comment']) | translate }}
          </div>
        </div>

        <ng-container *ngIf="question.attachmentEnabled || question.selected?.isAttachmentEnabled">
          <cat-field-file [field]="question" [form]="form"></cat-field-file>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls[question.questionId]['controls']['Attachments']) }"
            class="invalid-feedback pl-3"
          >
            {{
              getFormErrorMessage(form.controls[question.questionId]['controls']['Attachments'], {
                required: '_required_file',
                minlength: '_min_length_file'
              }) | translate
            }}
          </div>
        </ng-container>-->


  </div>
</div>
