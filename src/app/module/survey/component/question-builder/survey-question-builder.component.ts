import { Component, Input, OnInit } from '@angular/core';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { SurveyQuestion } from '../../model/survey.model';

@Component({
  selector: 'cat-survey-question-builder',
  templateUrl: './survey-question-builder.component.html',
  styleUrls: ['./survey-question-builder.component.scss']
})
export class SurveyQuestionBuilderComponent implements OnInit {

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Input()
  form: FormGroup;

  @Input()
  question: SurveyQuestion;

  @Input()
  id: string;

  constructor() { }

  ngOnInit(): void {
    const originals = this.question;
    this.question = {
      ...this.question,
      answers: [
        ...this.question.answers
          .map(item => {
            return {
              ...item,
              answerType: item.options.length === 2 ? 'Radio' : item.answerType,
              options: item.options.filter(option => !['Empty', 'Boş'].includes(option.title))
            };
          })
      ]
    };
    const defaultAnswer = originals.answers[0]?.options
      ?.find(option => option.title === 'Empty')?.optionId;

    const extras = this.question.answers
      .map((item, index) => {
        if (item.answerType !== 'Text') {
          return new FormControl();
        }

        return new FormGroup({
          extraReasonAnswer: new FormControl(null, []),
          extraReasonQuestion: new FormControl(item?.answerId),
          scoreCriteria: new FormControl(item?.scoreCriteria)
        });
      });

    this.form.addControl(this.question.questionId, new FormGroup({
      questionId: new FormControl(this.question.questionId),
      Answer: new FormControl(defaultAnswer, this.question.isRequired ? [Validators.required] : []),
      extraQuestions: new FormArray(extras)
    }));
  }

  stateCheck(value: any, question: SurveyQuestion, criteria: number) {
    const selection = question.answers[0]?.options
      ?.find(item => item.optionId === value?.controls?.Answer.value);
    if (!selection) {
      return false;
    }
    return parseInt(selection.title, 10) <= criteria;
  }
}
