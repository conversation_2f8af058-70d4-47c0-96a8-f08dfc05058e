export interface SurveyModel {
  activityId: string;
  formId: string;
  serialNumber: string;
  invoiceNumber: string;
  invoiceDate: string;
  surveyType: string;
  formName: string;
  formDescription: string;
  greetings: string;
  groups: any[];
}

export interface SurveyGroup {
  groupId: string;
  groupTitle: string;
  groupType: string;
  isSoundDiagRequired: boolean;
  questions: SurveyQuestion[];
}

export interface SurveyQuestion {
  questionId: string;
  question: string;
  order: number;
  attachmentEnabled: boolean;
  isRequired: boolean;
  isCommentBoxEnabled: boolean;
  answers: SurveyAnswer[];
  conditionalQuestions: ConditionalQuestion[];
}

export interface ConditionalQuestion {
  questionId: string;
  scoreCriteria: number;
  answers: SurveyOption[];
}

export interface SurveyQuestionOption {
  optionId: string;
  title: string;
  value: string;
  color: string;
  leadScore: number;
  order: number;
  isCommentBoxRequired: boolean;
  isAttachmentEnabled: boolean;
}

export interface SurveyAnswer {
  answerId: string;
  title: string;
  answerType: string;
  isMultipleAnswerAllowed: boolean;
  options: SurveyOption[];
  selected?: SurveyOption;
  scoreCriteria?: number;
}

export interface SurveyOption {
  optionId: string;
  title: string;
}


export interface SurveySubmitRequest {
  activityId: string;
  serialNumber: string;
  invoiceNumber: string;
  surveyId: string;
  language: string;
  answers: SurveySubmitAnswer[];
}

export interface SurveySubmitAnswer {
  questionId: string;
  answer: string;
  extraQuestions: SurveyExtraAnswer[];
}

export interface SurveyExtraAnswer {
  extraReasonQuestion: string;
  extraReasonAnswer: string;
}
