import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import {
  ClearVideocallMessageAction,
  SetVideocallMessageAction,
} from '../action/videocall-message.action';
import { VideocallHandlerModal } from '../model/videocal.model';

export interface VideocallMessageStateModel {
  message: VideocallHandlerModal;
}

@State<VideocallMessageStateModel>({
  name: 'videocallMessage',
  defaults: {
    message: null,
  },
})
@Injectable()
export class VideocallMessageState {
  constructor() { }

  @Selector()
  public static getVideocallMessage({ message }: VideocallMessageStateModel): {} {
    return message;
  }

  @Action(SetVideocallMessageAction)
  public SetVideocallMessageAction(
    { patchState }: StateContext<VideocallMessageStateModel>,
    { message }: SetVideocallMessageAction
  ) {
    patchState({
      message,
    });
  }

  @Action(ClearVideocallMessageAction)
  public ClearVideocallMessageAction({
    patchState,
  }: StateContext<VideocallMessageStateModel>) {
    patchState({
      message: null,
    });
  }
}
