import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { MediaModule } from 'src/app/export/media/media.module';
import { TakeAPhotoModule } from 'src/app/export/take-a-photo/take-a-photo.module';
import { UserEventLogModule } from 'src/app/export/user-event-log/user-event-log.module';
import { MachineSerial } from 'src/app/module/form/component/machine-serial.module';
import { SharedModule } from 'src/app/module/shared/shared.module';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { LiveSupportRoutingModule } from './live-support-routing.module';
import { SupportFormComponent } from './container/support-form/support-form.component';
import { VideocallComponent } from './container/videocall/videocall.component';
import { LiveSupportLayoutComponent } from './container/live-support-layout/live-support-layout.component';
import { VideocallAgreementComponent } from './container/videocall-agreement/videocall-agreement.component';
import { VideocallAfterComponent } from './container/videocall-after/videocall-after.component';
import { PublicVideocallComponent } from './container/public-videocall/public-videocall.component';

@NgModule({
  declarations: [
    LiveSupportLayoutComponent,
    VideocallComponent,
    SupportFormComponent,
    VideocallAgreementComponent,
    VideocallAfterComponent,
    PublicVideocallComponent
  ],
  imports: [
    CommonModule,
    LiveSupportRoutingModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    NgxLoadingModule,
    MediaModule,
    TakeAPhotoModule,
    SharedModule,
    UserEventLogModule,
    NgbTooltipModule,
    MachineSerial
  ],
})
export class LiveSupportModule {

  constructor() {
    stopLoadingAnimation();
  }
}
