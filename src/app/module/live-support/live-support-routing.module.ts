import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LiveSupportLayoutComponent } from './container/live-support-layout/live-support-layout.component';
import { SupportFormComponent } from './container/support-form/support-form.component';
import { VideocallComponent } from './container/videocall/videocall.component';
import { VideocallAfterComponent } from './container/videocall-after/videocall-after.component';
import { PublicVideocallComponent } from './container/public-videocall/public-videocall.component';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'form',
  },
  {
    path: '',
    component: LiveSupportLayoutComponent,
    children: [
      { path: 'form', component: SupportFormComponent },
      { path: 'videocall', component: VideocallComponent },
      { path: 'after', component: VideocallAfterComponent },
      { path: 'digital-banko', component: PublicVideocallComponent },
      { path: 'public-videocall', component: PublicVideocallComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LiveSupportRoutingModule {
}
