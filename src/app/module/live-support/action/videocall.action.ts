import { VideocallFormStartModel, VideocallFormUpdateModel, VideocallLogModel } from '../model/videocal.model';
import { VideocallQueueEnum } from '../enum/videocall-queue.enum';

export class VideocallGetFaultyComponentsAction {
  public static readonly type = '[Videocall] Get Faulty Components';
  constructor() { }
}

export class VideocallStartAction {
  public static readonly type = '[Videocall] Start';
  constructor(public form: VideocallFormStartModel) { }
}

export class VideocallUpdateAction {
  public static readonly type = '[Videocall] Update';
  constructor(public form: VideocallFormUpdateModel) { }
}

export class VideocallSetupAction {
  public static readonly type = '[Videocall] Setup';
  constructor(public provider: string, public queueName: VideocallQueueEnum | string) { }
}

export class GetVideocallAvailableAgentAction {
  public static readonly type = '[Videocall] Get Videocall Available Agent';
  constructor(public queue: {queueCode?: string, queueName?: string}) { }
}

export class ClearVideocallAvailableAgentAction {
  public static readonly type = '[Videocall] Clear Videocall Available Agent';
  constructor() { }
}

export class VideocallLogAction {
  public static readonly type = '[Videocall] Log';
  constructor(public form: VideocallLogModel) { }
}

export class VideocallClearAction {
  public static readonly type = '[Videocall] Clear';
  constructor() { }
}
