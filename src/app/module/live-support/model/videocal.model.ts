export interface VideocallHandlerModal {
  event: string;
  message?: {
    appointmentId?: number | any;
    claimId?: number | any;
    participantId?: string | any;
  };
}

export interface VideocallFormValuesModel {
  Provider: number | string;
  videoCallId: string;
  videoCallFormId: string;
  VideoCallType?: number;
}

export interface VideocallFormStartModel {
  Provider?: string | number;
  VideoCallType?: number;
  QueueName: string;
  ProviderQueueId?: string;
  ProviderCallerId?: string;
  CallReason?: number;
  NearMachine?: boolean;
  InternetAccess?: boolean;
  MachineSafe?: boolean;
  MachineWorking?: boolean;
  FaultyComponent?: number;
  EquipmentSerialNumber?: string;
  city?: string;
  category1?: string;
  category2?: string;
}

export interface VideocallFormUpdateModel extends VideocallFormValuesModel {
  VideoCallId?: string;
  ProviderQueueId?: string;
  ProviderCallId?: string;
  // claimId => providerQueueId
  // appointmentId => providerCallId
  status: number;
  category3?: string;
  category4?: string;
  category5?: string;
  description?: string;
  videoCallResult?: number;
}

export interface VideocallFormFaultyComponentsModel {
  id: number;
  text: string;
}

export interface VideocallSetupModel {
  baseUrl: string;
  queueCode: string;
  apiKey: string;
  endPoint: string;
  iFrame: string;
  language: string;
  queueWaitTime: number;
  backupQueue?: string;
  videoCallType: number;
  queueName: string;
}

export interface VideocallLogModel {
  videoCallId: string;
  status: number;
  transaction: string;
  logdata?: string;
  videoCallProvider?: number;
  ProviderQueueId?: string;
  ProviderCallId?: string;
  CallerName?: string;
  CallerSurname?: string;
  CallerPhone?: string;
  videoCallType: number | any;
  queueName?: string;
}

export interface VideocallAvailableAgentModel {
  availableAgentCount: number;
  backupQueueName: string;
}
