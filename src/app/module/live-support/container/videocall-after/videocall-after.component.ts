import { Component, OnInit } from '@angular/core';
import { getOS } from '../../../../util/os.util';

@Component({
  selector: 'cat-videocall-after',
  templateUrl: './videocall-after.component.html',
  styleUrls: ['./videocall-after.component.scss']
})
export class VideocallAfterComponent implements OnInit {
  isIOS: boolean;

  constructor() { }

  ngOnInit() {
    this.isIOS = getOS() === 'IOS';
  }

  closeViewer() {
    self.close();
  }
}
