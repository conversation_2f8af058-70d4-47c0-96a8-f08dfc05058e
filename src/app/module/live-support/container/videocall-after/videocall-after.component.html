<div class="text-center">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-videocall text-danger"></i>
    </div>
    <div class="empty-content-message text-center"
         [innerHtml]="'_live_support_without_survey_form_ended' | translate | safeHtml">
    </div>
    <div class="empty-content-extra text-center mb-5">
      {{ '_live_support_close_message' | translate }}
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>
