import { <PERSON><PERSON>iew<PERSON>nit, Component, NgZone, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { LogService } from 'src/app/module/customer/service/log.service';
import { SurveyFormAnswerAction, SurveyFormGetQuestionsAction } from 'src/app/module/form/action/form.actions';
import { SurveyFormTypeEnum } from 'src/app/module/form/enum/form.enum';
import { SurveyFormAnswerBodyModel, SurveyFormModel } from 'src/app/module/form/model/form.modal';
import { FormState } from 'src/app/module/form/state/form.state';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { ClearVideocallMessageAction } from '../../action/videocall-message.action';
import { VideocallLogAction, VideocallSetupAction, VideocallUpdateAction } from '../../action/videocall.action';
import { VideocallMessageEnum } from '../../enum/videocall-message.enum';
import { VideocallStatusEnum } from '../../enum/videocall.enum';
import { VideocallFormUpdateModel, VideocallHandlerModal, VideocallLogModel, VideocallSetupModel } from '../../model/videocal.model';
import { VideocallMessageState } from '../../state/videocall-message.state';
import { VideocallState } from '../../state/videocall.state';
import { getOS } from '../../../../util/os.util';
import { environment } from '../../../../../environments/environment';
import { VideocallQueueEnum } from '../../enum/videocall-queue.enum';

@Component({
  selector: 'cat-videocall',
  templateUrl: './videocall.component.html',
  styleUrls: ['./videocall.component.scss']
})
export class VideocallComponent implements OnInit, AfterViewInit, OnDestroy {
  loading = true;

  user: UserModel;
  userLang: string;

  videocallURL: string;
  queueCode: string;
  apiKey: string;
  endpoint: string;
  iFrame: string;
  // Default Language
  lang = 'en';
  firstName: string;
  lastName: string;
  email: string;

  @ViewChild('assistBoxIframe') public assistBoxIframe;

  @Select(VideocallMessageState.getVideocallMessage)
  videocallMessage$: Observable<VideocallHandlerModal>;
  videocallMessage: VideocallHandlerModal;

  @Select(VideocallState.getVideocallSetup)
  videocallSetup$: Observable<VideocallSetupModel>;
  videocallSetup: VideocallSetupModel;

  isPolicyDenied = false;

  videoCallId: string;
  videoCallFormId: string;

  time = 0;
  meetTime = 0;
  timerMinute: any;
  interval: any;

  startMeetTimer = false;
  callLastWaitTime = 60;
  tooManyTimeWait = false;

  showSurveyForm = false;
  disabledSendButton = true;
  withoutSurveyForm = false;
  showRedirectQueue = false;
  meetSurveyTime = 20;

  @Select(FormState.getSurveyFormQuestions)
  surveyQuestion$: Observable<SurveyFormModel[]>;
  surveyQuestion: SurveyFormModel;
  surveyFormSended = false;

  backupQueue: string;

  ratingArray = [];
  isIOS = false;

  showMediaPermissionError = false;
  notAllowRestart = false;
  protected callType = 'TECHNICIAN_CALL';

  protected subscriptions$: Subject<boolean> = new Subject();
  private lastMessageEvent: VideocallHandlerModal;
  constructor(
    private readonly store: Store,
    private readonly logService: LogService,
    private readonly translateService: TranslateService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
  ) {
    stopLoadingAnimation();
    document.querySelector('head > title').innerHTML = 'Borusan';
  }

  ngOnInit() {
    this.user = this.store.selectSnapshot(LoginState.user);
    if (this.user) {
      this.route.queryParams.subscribe(params => {
        this.videoCallId = params?.videoCallId;
        this.videoCallFormId = params?.videoCallFormId;
        this.notAllowRestart = params?.notAllowRestart;
      });
      this.isIOS = getOS() === 'IOS';
      this.getMediaPermission();
      this.userLang = this.store.selectSnapshot(LoginState.language);
      this.store.dispatch(new VideocallSetupAction('assistbox', VideocallQueueEnum.SWAT));
      this.videocallSetup$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(data => {
          if (data) {
            this.videocallSetup = data;
            this.callLastWaitTime = data?.queueWaitTime || 60;
            this.backupQueue = data?.backupQueue;
            this.videocallPostMessageSubscribe();
            this.videocallJsInject();
            this.logService
              .action('VIDEOCALL', this.callType,
                {
                  queueCode: this.videocallSetup.queueCode,
                  videoCallType: this.videocallSetup?.videoCallType,
                  language: this.videocallSetup.queueCode,
                  isIOS: this.isIOS,
                }).subscribe();
          }
        });
      this.getSurveyFormQuestions();
    }
    this.loading = false;
  }

  videocallJsInject() {
    this.videocallLogSend(VideocallStatusEnum.Started, 'CALL_INIT').subscribe();

    // Inside the iframe
    this.videocallURL = this.urlLastSlashRemover(this.videocallSetup.baseUrl);
    this.queueCode = '?queueCode=' + this.videocallSetup.queueCode;
    this.apiKey = '&apiKey=' + this.videocallSetup.apiKey;
    this.endpoint = '&endpoint=' + encodeURIComponent(this.urlLastSlashRemover(this.videocallSetup.endPoint));
    this.iFrame = '&iFrame=' + this.videocallSetup.iFrame;
    this.lang = '&lang=' + (this.userLang === 'tr' ? this.userLang : 'en');

    const userData = this.user
      ? '&firstName=' +
      this.user?.firstName +
      '&lastName=' +
      this.user?.lastName +
      '&email=' +
      this.user?.email.toLowerCase() +
      '&phoneNo=' +
      this.user?.mobile
      : '&showContactForm=true';

    // Inject IFRAME
    const iframe = document.createElement('iframe');
    iframe.id = 'assistBoxIframe';
    // iframe.allow = 'camera https://go.assistbox.io;microphone https://go.assistbox.io;encrypted-media;';
    iframe.allow = 'camera;microphone';
    iframe.setAttribute('allowusermedia', 'allowusermedia');
    iframe.src =
      this.videocallURL +
      this.queueCode +
      this.apiKey +
      this.endpoint +
      this.iFrame +
      this.lang +
      userData;
    iframe.style.cssText =
      'width: 100vw; height: calc(100vh - 61px); border: none; overflow: hidden;';
    document.body.appendChild(iframe);
    this.loading = false;
  }

  getSurveyFormQuestions() {
    this.store.dispatch(new SurveyFormGetQuestionsAction(SurveyFormTypeEnum.Videocall, true));
    this.surveyQuestion$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(questions => {
        if (questions?.length) {
          this.surveyQuestion = questions?.find(x => x?.code === 'VIDEOCALL_END_SURVEY');
        }
      });
  }

  getMediaPermission() {
    if (this.isIOS) {
      return;
    }
    console.log('::::: Media Permission Request');
    window?.navigator?.mediaDevices?.getUserMedia({
      audio: true,
      video: true
    })
      .then(stream => {
        console.log('stream: ', stream);
      })
      .catch(err => {
        this.permissionDenied();
        console.log('err: ', err);
      });
  }

  permissionDenied() {
    console.log('Videocall - Permission Denied');
    this.showMediaPermissionError = true;
    this.frameRemove();
    this.pauseTimer();
    this.clearVideocallState();
  }

  urlLastSlashRemover(s: string) {
    return s.charAt(s.length - 1) === '/' ? s.slice(0, s.length - 1) : s;
  }

  videocallPostMessageSubscribe() {
    this.videocallMessage$.subscribe((message) => {
      console.log('::::: message geldi', message);
      if (!message && !message?.event) {
        return;
      }
      this.videocallMessage = {
        event: message?.event,
        message: {
          appointmentId: this.videocallMessage?.message?.appointmentId || message?.message?.appointmentId,
          claimId: this.videocallMessage?.message?.claimId || message?.message?.claimId,
          participantId: this.videocallMessage?.message?.participantId || message?.message?.participantId,
        }
      };
      this.videocallMessageEvent(message);
    });
  }

  videocallMessageEvent(m) {
    const that = this;
    const messageFunction: any = Object.keys(VideocallMessageEnum).find(
      (key) => VideocallMessageEnum[key] === m.event
    );
    console.log(':::::: message func', messageFunction);
    if (typeof that[messageFunction] === 'function') {
      if (JSON.stringify(m) === JSON.stringify(this.lastMessageEvent)) {
        return;
      }
      this.lastMessageEvent = m;
      that[messageFunction]();
    } else {
      console.log('ERROR ASSISTBOX MESSAGE EVENT FUNCTION NOT FOUND: ', m);
    }
  }

  assistboxConnected() {
    this.pauseTimer();
    this.startTimer();
    this.showRedirectQueue = false;
    this.videocallLogSend(VideocallStatusEnum.Queue, 'CONNECTED').subscribe();
    this.updateVideocallForm(VideocallStatusEnum.Queue);
    this.clearVideocallState();
  }

  assistboxPolicyDenied() {
    console.log('Videocall Policy Denied');
    this.videocallLogSend(VideocallStatusEnum.Cancelled, 'POLICY_DENIED').subscribe();
    this.updateVideocallForm(VideocallStatusEnum.Cancelled);
    this.frameRemove();
    this.pauseTimer();
    this.clearVideocallState();
    this.isPolicyDenied = true;
  }

  assistboxOpenCamera() {
    console.log('Videocall Open Camera');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'CAMERA_OPEN').subscribe();
    this.updateVideocallForm(VideocallStatusEnum.InCall);
    this.clearVideocallState();
  }


  assistboxCloseCamera() {
    console.log('Videocall Close Camera');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'CAMERA_CLOSE').subscribe();
    this.updateVideocallForm(VideocallStatusEnum.InCall);
    this.clearVideocallState();
  }

  assistboxStartMeeting() {
    console.log('Videocall Start Meeting');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'START_MEETING').subscribe();
    this.startMeetTimer = true;
    this.updateVideocallForm(VideocallStatusEnum.InCall);
    this.clearVideocallState();
  }

  assistboxCallEnded() {
    console.log('Videocall Call Ended');
    this.frameRemove();
    // TODO
    if (this.meetTime > this.meetSurveyTime) {
      this.showSurveyForm = true;
    } else {
      this.withoutSurveyForm = true;
    }
    this.videocallLogSend(
      VideocallStatusEnum.CallEnded,
      'CALL_ENDED',
      `{meetTime: ${this.meetTime}, isSurveyFormShow: ${this.showSurveyForm}}`,
    ).subscribe();
    this.updateVideocallForm(VideocallStatusEnum.CallEnded);
    this.pauseTimer();
    this.clearVideocallState();
  }

  clearVideocallState() {
    this.store.dispatch(new ClearVideocallMessageAction());
  }

  ratingClicked(questionIndex, ratingIndex) {
    this.ratingArray[questionIndex] = ratingIndex;
    if (this.ratingArray.filter(x => x >= 0)?.length > 3) {
      this.disabledSendButton = false;
    }
  }

  startTimer() {
    this.interval = setInterval(() => {
      this.time++;
      this.timerMinute = this.timeTransform(this.time);

      if ((Math.floor(this.time) % 10) === 0) {
        console.log('Time: ', this.time);
        console.log('Meet Time: ', this.meetTime);
      }

      if (this.startMeetTimer) {
        this.meetTime++;
      }

      if (this.time === this.callLastWaitTime && !this.startMeetTimer) {
        this.pauseTimer();
        this.frameRemove();
        console.log('Timer Paused.');
        this.updateVideocallForm(VideocallStatusEnum.NotAnswered);
        // ? NotAnswered
        this.videocallLogSend(VideocallStatusEnum.NotAnswered, 'TOO_MANY_TIME_WAIT').subscribe();

        if (this.backupQueue) {
          this.showRedirectQueue = true;
          this.store.dispatch(new VideocallSetupAction('Assistbox', this.backupQueue));
        } else {
          this.tooManyTimeWait = true;
        }
      }
    }, 1000);
  }

  timeTransform(value: number): string {
    const minutes: number = Math.floor(value / 60);
    return (Math.floor(value / 60) < 10 ? '0' : '') + minutes + ':'
      + ((value - minutes * 60) < 10 ? 0 : '') + (value - minutes * 60);
  }

  pauseTimer() {
    this.time = 0;
    this.meetTime = 0;
    clearInterval(this.interval);
  }

  frameRemove() {
    document.getElementById('assistBoxIframe')?.remove();
  }

  sendSurveyForm() {
    // TODO Survey Rating
    const answer: SurveyFormAnswerBodyModel = {
      SurveyId: this.surveyQuestion.surveyId,
      SurveyPosition: SurveyFormTypeEnum.Videocall,
      AdditionalData: null,
      RelatedContextId: null,
      Answers: [
        {
          QuestionId: this.surveyQuestion.surveyQuestions[0].surveyId,
          Answer: this.ratingArray[0].toString(),
          Comment: null
        },
        {
          QuestionId: this.surveyQuestion.surveyQuestions[1].surveyId,
          Answer: this.ratingArray[1].toString(),
          Comment: null
        },
        {
          QuestionId: this.surveyQuestion.surveyQuestions[2].surveyId,
          Answer: this.ratingArray[2].toString(),
          Comment: null
        },
        {
          QuestionId: this.surveyQuestion.surveyQuestions[3].surveyId,
          Answer: this.ratingArray[3].toString(),
          Comment: null
        }
      ]
    };
    this.store.dispatch(new SurveyFormAnswerAction(answer));
    this.surveyFormSended = true;
    this.showSurveyForm = false;
  }

  closeViewerAfterTooManyTime() {
    this.closeViewer();
  }

  closeViewerWithOutSurvey() {
    this.closeViewer();
  }

  setHeaderTitle() {
    this.translateService.get('_live_remote_technical_support')
      .subscribe(translate => {
        if (translate) {
          document.querySelector('head > title').innerHTML = 'Borusan - ' + translate;
        }
      });
  }

  updateVideocallForm(status: number) {
    const update: VideocallFormUpdateModel = {
      Provider: 'assistbox',
      videoCallId: this.videoCallId,
      videoCallFormId: this.videoCallFormId,
      ProviderQueueId: this.videocallMessage?.message?.claimId,
      ProviderCallId: this.videocallMessage?.message?.appointmentId,
      status
    };
    this.store.dispatch(new VideocallUpdateAction(update));
  }

  sendVideocallLog(status: number = 2, transaction: string, logData: string) {
    const log: VideocallLogModel = {
      videoCallId: this.videoCallId,
      status,
      transaction,
      logdata: logData,
      videoCallProvider: 0,
      ProviderQueueId: this.videocallMessage?.message?.claimId,
      ProviderCallId: this.videocallMessage?.message?.appointmentId,
      CallerName: this.user?.firstName,
      CallerSurname: this.user?.lastName,
      CallerPhone: this.user?.mobile,
      videoCallType: this.videocallSetup?.videoCallType,
      queueName: this.videocallSetup?.queueName,
    };
    this.store.dispatch(new VideocallLogAction(log));
  }

  closeViewer() {
    self.close();
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'live-support',
          'after'
        ],
        { replaceUrl: true }
      )
      .then();
  }

  ngAfterViewInit(): void {
    this.setHeaderTitle();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


  videocallLogSend(status: number = 2, subSection: string, logData = '') {
    this.sendVideocallLog(status, subSection, logData);

    return this.logService
      .action(this.callType, subSection,
        {
          queueCode: this.videocallSetup.queueCode,
          language: this.userLang,
          isIOS: this.isIOS,
          ProviderQueueId: this.videocallMessage?.message?.claimId,
          ProviderCallId: this.videocallMessage?.message?.appointmentId,
          videoCallType: this.videocallSetup?.videoCallType,
          queueName: this.videocallSetup?.queueName,
          user: this.user ? {
            id: this.user?.id,
            firstName: this.user?.firstName,
            lastName: this.user?.lastName,
            mobile: this.user?.mobile
          } : {}
        });
  }
}


// ? Survey Model
// {
//   QuestionareName:
//   Tag:
//   Questions: [
//     QuestionText:
//     IsPointActive:
//     MaxPoint:
//     IsCommentBoxActive:
//     CommentBoxMultiline:
//     CommentBoxCharacterLimit:
//     IsRequired:
//   ]
// }
