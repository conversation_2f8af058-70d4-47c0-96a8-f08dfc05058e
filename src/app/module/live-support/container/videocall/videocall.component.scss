.empty-content {
  padding: 10rem 4rem;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}

.videocall-empty-content {
  height: 100vh;
  z-index: 100;
  padding: 10rem 5rem;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}

#survey-stars {
  .rating {
    &-group{
      display: inline-flex;
    }
    &-icon {
      pointer-events: none;
    }
    &-input {
      position: absolute !important;
      left: -9999px !important;
      &-none{
        display: none
      }
    }
    &-icon {
      pointer-events: none;
    }
    &-label {
      cursor: pointer;
      padding: 0 0.1em;
      font-size: 2rem;
    }
    &-icon-star {
      color: #ddd;
    }
    &-selected {
      color: orange;
    }
  }
}
