<!--<cat-loader [show]="loading"></cat-loader>-->
<div id="frameDiv" style="z-index: 1"></div>
<!-- ? Without User Login -->
<div class="text-center" *ngIf="!user && (!tooManyTimeWait && !showSurveyForm && !surveyFormSended) || notAllowRestart">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-close-circle text-warning"></i>
    </div>
    <div class="empty-content-message text-center ">
      {{ '_unauthorised' | translate }}
    </div>
    <div class="empty-content-extra text-center mb-5">
      {{ '_live_support_reconnect_again' | translate }}
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Media Permission Denied Error -->
<div class="text-center permission-content" *ngIf="showMediaPermissionError">
  <div class="videocall-empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="videocall-empty-content-logo mb-2">
      <i class="icon icon-close-circle text-warning"></i>
    </div>
    <div class="videocall-empty-content-message text-center ">
      {{ '_phone_media_permission_denied' | translate }}
    </div>
<!--    <button class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="getLocalStream()">-->
<!--      {{ "_request_media_permission_again" | translate }}-->
<!--    </button>-->
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Policy Denied Error -->
<div class="text-center" *ngIf="isPolicyDenied && (!tooManyTimeWait && !showSurveyForm && !surveyFormSended)">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-close-circle text-warning"></i>
    </div>
    <div class="empty-content-message text-center ">
      {{ '_kvkk_unauthorized' | translate }}
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Too Many Time Wait -->
<div class="text-center" *ngIf="tooManyTimeWait">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-videocall text-warning"></i>
    </div>
    <div class="empty-content-message text-center"
         [innerHtml]="'_live_support_many_time_wait' | translate | safeHtml">
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewerAfterTooManyTime()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Wait After Redirect Queue -->
<!--<div class="text-center" *ngIf="showRedirectQueue">-->
<!--  <div class="empty-content d-flex align-items-center flex-column justify-content-center">-->
<!--    <div class="empty-content-logo mb-2">-->
<!--      <i class="icon icon-videocall text-warning"></i>-->
<!--    </div>-->
<!--    <div class="empty-content-message text-center"-->
<!--         [innerHtml]="'_live_support_redirect_queue_wait' | translate | safeHtml">-->
<!--    </div>-->
<!--  </div>-->
<!--</div>-->

<!-- ? Call End Survey Form -->
<div class="p-3 m-2" *ngIf="showSurveyForm">
  <div class="mb-4">
    <h5>
      {{ surveyQuestion?.name }}
    </h5>
    <span>
      {{ surveyQuestion?.greetings }}
    </span>
  </div>
  <div class="d-flex flex-column justify-content-center"
    *ngFor="let question of surveyQuestion?.surveyQuestions; let q = index">
    <span [ngSwitch]="question.questionType">
      <div *ngSwitchCase="1">
        <div>
          {{ q + 1 }}. {{ question.question }}
        </div>
        <div id="survey-stars">
          <div class="rating-group">
            <input disabled class="rating-input rating-input-none" name="live-support-survey-rating{{q}}"
              id="live-support-survey-rating-none" value="{{q}}" type="radio">
            <ng-container *ngFor="let star of [].constructor(5); let i = index">
              <label [attr.aria-label]="i + ' star'" class="rating-label" for="live-support-survey-rating{{q}}-{{i}}">
                <i class="rating-icon rating-icon-star icon icon-star" [ngClass]="{'rating-selected': (i <= ratingArray[q])}"></i>
              </label>
              <input (click)="ratingClicked(q, i)" class="rating-input" name="live-support-survey-rating"
                id="live-support-survey-rating{{q}}-{{i}}" value="{{i}}" type="radio">
            </ng-container>
          </div>
        </div>
      </div>
    </span>
  </div>
  <button class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm mt-3" (click)="sendSurveyForm()" [disabled]="disabledSendButton">
    {{ "_send" | translate }}
  </button>
  <hr>
  <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewerWithOutSurvey()">
    {{ "_close" | translate }}
  </button>
</div>

<!-- ? Call End Without Survey Form -->
<div class="text-center" *ngIf="!surveyFormSended && !showSurveyForm && withoutSurveyForm">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-videocall text-danger"></i>
    </div>
    <div class="empty-content-message text-center ">
      {{ '_live_support_without_survey_form_ended' | translate }}
    </div>
    <div class="empty-content-extra text-center mb-5">
      {{ '_live_support_close_message' | translate }}
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Survey Form Sended -->
<div class="text-center" *ngIf="surveyFormSended">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-videocall text-danger"></i>
    </div>
    <div class="empty-content-message text-center ">
      {{ '_live_support_survey_form_sended_message' | translate }}
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>
<cat-loader [show]="showRedirectQueue"></cat-loader>
