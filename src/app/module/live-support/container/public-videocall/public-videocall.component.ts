import { AfterViewInit, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { ClearVideocallMessageAction } from '../../action/videocall-message.action';
import { VideocallMessageEnum } from '../../enum/videocall-message.enum';
import {
  VideocallFormStartModel,
  VideocallFormUpdateModel,
  VideocallFormValuesModel,
  VideocallHandlerModal,
  VideocallLogModel,
  VideocallSetupModel
} from '../../model/videocal.model';
import { VideocallMessageState } from '../../state/videocall-message.state';
import { Activated<PERSON>oute, Router } from '@angular/router';
import { environment } from '../../../../../environments/environment';
import { getOS } from '../../../../util/os.util';
import { LogService } from '../../../customer/service/log.service';
import { VideocallLogAction, VideocallSetupAction, VideocallStartAction, VideocallUpdateAction } from '../../action/videocall.action';
import { VideocallQueueEnum } from '../../enum/videocall-queue.enum';
import { takeUntil } from 'rxjs/operators';
import { VideocallState } from '../../state/videocall.state';
import { getFormErrorMessage } from '../../../../util/get-form-error-message.util';
import { isShowFormError } from '../../../../util/is-show-form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { validateAllFormFields } from '../../../../util/validate-all-form-fields.util';
import { VideocallStatusEnum } from '../../enum/videocall.enum';

@Component({
  selector: 'cat-videocall',
  templateUrl: './public-videocall.component.html',
  styleUrls: ['./public-videocall.component.scss']
})
export class PublicVideocallComponent implements OnInit, AfterViewInit, OnDestroy {
  loading = true;

  @Select(VideocallState.getVideocallSetup)
  videocallSetup$: Observable<VideocallSetupModel>;

  @Input()
  videocallSetup: VideocallSetupModel = {
    baseUrl: 'https://go.assistbox.io/rest/api/embedService/assistbox.c2c.embed.bundle.js',
    queueCode: 'BORUSAN_DEMO_HESABI-Dijita-b4939be2-73c4-40ea-af0f-4935c1180f34',
    apiKey: 'Mmy9RlS6t98AeYkYlL2gH5uCHnAXLjsc',
    endPoint: 'https://go.assistbox.io',
    iFrame: 'true',
    language: 'tr',
    queueWaitTime: 60,
    videoCallType: 1,
    queueName: null,
  };

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  form: FormGroup = new FormGroup({
    firstName: new FormControl(null, [Validators.required]),
    lastName: new FormControl(null, [Validators.required]),
    mobile: new FormControl(null, [Validators.required]),
  });


  videocallURL: string;
  queueCode: string;
  apiKey: string;
  endpoint: string;
  iFrame: string;
  lang: string;

  @Input()
  user: UserModel;

  @Input()
  userLang = 'en';

  @Input()
  headerTitle = 'Dijital Banko';

  @ViewChild('assistBoxIframe') public videocallIframe;

  @Select(VideocallMessageState.getVideocallMessage)
  videocallMessage$: Observable<VideocallHandlerModal>;
  videocallMessage: VideocallHandlerModal;

  isPolicyDenied = false;

  time = 0;
  meetTime = 0;
  timerMinute: any;
  interval: any;

  startMeetTimer = false;
  callLastWaitTime = 120;
  tooManyTimeWait = false;
  showRedirectQueue = false;

  // meetSurveyTime = 180;
  isIOS = false;

  showMediaPermissionError = false;

  protected subscriptions$: Subject<boolean> = new Subject();
  backupQueue: string;
  openForm = false;

  @Select(VideocallState.getVideocallValues)
  getVideocallFormValues$: Observable<VideocallFormValuesModel>;

  protected callType = 'DIGITAL_BANKO';
  protected provider = 'assistbox';
  private videoCallId: string;
  private startSended: boolean;
  private lastMessageEvent: VideocallHandlerModal;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly logService: LogService,
  ) {
    stopLoadingAnimation();
    document.querySelector('head > title').innerHTML = 'Borusan';
  }

  ngOnInit() {
    const externalQueueCode = this.route.snapshot.queryParams?.queueCode;
    const queueName = this.route.snapshot.queryParams?.queueName;
    this.callType = this.route.snapshot.queryParams?.callType;
    this.openForm = this.route.snapshot.queryParams?.form;

    this.isIOS = getOS() === 'IOS';
    this.getMediaPermission();
    this.userLang = this.store.selectSnapshot(LoginState.language);
    if (!this.showMediaPermissionError) {
      this.user = this.user || this.store.selectSnapshot(LoginState.user);
    }
    if (queueName === 'SWAT' || queueName === 'LiveSupport') {
      this.callType = 'TECHNICIAN_CALL';
    }

    if (externalQueueCode) {
      this.videocallSetup.queueCode = externalQueueCode;
      this.callType = 'TECHNICIAN_CALL';
    }

    this.store.dispatch(new VideocallSetupAction(this.provider, queueName || VideocallQueueEnum.DijitalBanko));
    this.videocallSetup$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.videocallSetup = { ...data };
          this.callLastWaitTime = data?.queueWaitTime || 60;
          this.backupQueue = data?.backupQueue;
          if (externalQueueCode) {
            this.videocallSetup.queueCode = externalQueueCode;
          }

          this.videocallStartSend();
          if (this.user || !this.openForm) {
            this.videocallJsInject();
          }
          this.logService
            .action('VIDEOCALL', this.callType,
              {
                queueCode: this.videocallSetup.queueCode,
                videoCallType: this.videocallSetup?.videoCallType,
                language: this.videocallSetup.queueCode,
                isIOS: this.isIOS,
              }).subscribe();
        }
      });
    this.videocallPostMessageSubscribe();
  }

  videocallJsInject() {
    this.videocallLogSend(VideocallStatusEnum.Started, 'CALL_INIT').subscribe();

    // Inside the iframe
    this.videocallURL = this.urlLastSlashRemover(this.videocallSetup.baseUrl);
    this.queueCode = '?queueCode=' + this.videocallSetup.queueCode;
    this.apiKey = '&apiKey=' + this.videocallSetup.apiKey;
    this.endpoint = '&endpoint=' + encodeURIComponent(this.urlLastSlashRemover(this.videocallSetup.endPoint));
    this.iFrame = '&iFrame=' + this.videocallSetup.iFrame;
    this.lang = '&lang=' + (this.userLang === 'tr' ? this.userLang : 'en');

    const userData = this.user
      ? '&firstName=' +
      this.user?.firstName +
      '&lastName=' +
      this.user?.lastName +
      '&email=' +
      this.user?.email.toLowerCase() +
      '&phoneNo=' +
      this.user?.mobile
      : '&showContactForm=true';

    // Inject IFRAME
    const iframe = document.createElement('iframe');
    iframe.id = 'assistBoxIframe';
    // iframe.allow = 'camera https://go.assistbox.io;microphone https://go.assistbox.io;encrypted-media;';
    iframe.allow = 'camera;microphone';
    iframe.setAttribute('allowusermedia', 'allowusermedia');
    iframe.src =
      this.videocallURL +
      this.queueCode +
      this.apiKey +
      this.endpoint +
      this.iFrame +
      this.lang +
      userData;
    iframe.style.cssText =
      'width: 100vw; height: 100vh; border: none; overflow: hidden;';
    document.body.appendChild(iframe);
    this.loading = false;
  }

  getMediaPermission() {
    if (this.isIOS) {
      return;
    }
    console.log('::::: Media Permission Request');
    window?.navigator?.mediaDevices?.getUserMedia({
      audio: true,
      video: true
    })
      .then(stream => {
        console.log('stream: ', stream);
      })
      .catch(err => {
        this.permissionDenied();
        console.log('err: ', err);
      });
  }

  permissionDenied() {
    console.log('Videocall - Permission Denied');
    this.showMediaPermissionError = true;
    this.frameRemove();
    this.pauseTimer();
    this.clearVideocallState();
  }

  urlLastSlashRemover(s: string) {
    return s.charAt(s.length - 1) === '/' ? s.slice(0, s.length - 1) : s;
  }

  videocallPostMessageSubscribe() {
    this.videocallMessage$.subscribe((message) => {
      if (!message) {
        return;
      }
      this.videocallMessage = {
        event: message?.event,
        message: {
          appointmentId: this.videocallMessage?.message?.appointmentId || message?.message?.appointmentId,
          claimId: this.videocallMessage?.message?.claimId || message?.message?.claimId,
          participantId: this.videocallMessage?.message?.participantId || message?.message?.participantId,
        }
      };
      this.videocallMessageEvent(message);
    });
  }

  videocallLogSend(status: number = 2, subSection: string, logData = '') {
    this.sendVideocallLog(status, subSection, logData);

    return this.logService
      .action(this.callType, subSection,
        {
          queueCode: this.videocallSetup.queueCode,
          language: this.userLang,
          isIOS: this.isIOS,
          ProviderQueueId: this.videocallMessage?.message?.claimId,
          ProviderCallId: this.videocallMessage?.message?.appointmentId,
          videoCallType: this.videocallSetup?.videoCallType,
          queueName: this.videocallSetup?.queueName,
          user: this.user ? {
            id: this.user?.id,
            firstName: this.user?.firstName,
            lastName: this.user?.lastName,
            mobile: this.user?.mobile
          } : {}
        });
  }


  videocallMessageEvent(m) {
    const that = this;
    const messageFunction: any = Object.keys(VideocallMessageEnum).find(
      (key) => VideocallMessageEnum[key] === m.event
    );
    if (typeof that[messageFunction] === 'function') {
      if (JSON.stringify(m) === JSON.stringify(this.lastMessageEvent)) {
        return;
      }
      this.lastMessageEvent = m;
      that[messageFunction]();
    } else {
      console.log('ERROR ASSISTBOX MESSAGE EVENT FUNCTION NOT FOUND: ', m);
    }
  }

  assistboxConnected() {
    console.log('Videocall Connected');
    this.pauseTimer();
    this.startTimer();
    this.showRedirectQueue = false;
    this.videocallLogSend(VideocallStatusEnum.Queue, 'CONNECTED').subscribe();
    this.clearVideocallState();
  }

  assistboxPolicyDenied() {
    console.log('Videocall Policy Denied');
    this.videocallLogSend(VideocallStatusEnum.Cancelled, 'POLICY_DENIED').subscribe();
    this.frameRemove();
    this.pauseTimer();
    this.clearVideocallState();
    this.isPolicyDenied = true;
  }

  assistboxOpenCamera() {
    console.log('Videocall Open Camera');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'CAMERA_OPEN').subscribe();
    this.clearVideocallState();
  }

  assistboxCloseCamera() {
    console.log('Videocall Close Camera');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'CAMERA_CLOSE').subscribe();
    this.clearVideocallState();
  }

  assistboxStartMeeting() {
    console.log('Videocall Start Meeting');
    this.videocallLogSend(VideocallStatusEnum.InCall, 'START_MEETING').subscribe();
    this.startMeetTimer = true;
    this.clearVideocallState();
  }

  assistboxCallEnded() {
    console.log('Videocall Call Ended');

    this.pauseTimer();
    this.videocallLogSend(
      VideocallStatusEnum.CallEnded,
      'CALL_ENDED',
      `{meetTime: ${this.meetTime}, isSurveyFormShow: 0}`,
    ).subscribe(() => {
      this.closeViewer();
    });

    this.clearVideocallState();
    this.frameRemove();
  }

  clearVideocallState() {
    this.store.dispatch(new ClearVideocallMessageAction());
  }

  startTimer() {
    this.interval = setInterval(() => {
      this.time++;
      this.timerMinute = this.timeTransform(this.time);

      if ((Math.floor(this.time) % 10) === 0) {
        console.log('Time: ', this.time);
        console.log('Meet Time: ', this.meetTime);
      }

      if (this.startMeetTimer) {
        this.meetTime++;
      }

      if (this.time === this.callLastWaitTime && !this.startMeetTimer) {
        this.pauseTimer();
        this.frameRemove();
        if (this.backupQueue) {
          this.store.dispatch(new VideocallSetupAction(this.provider, this.backupQueue));
        } else {
          this.tooManyTimeWait = true;
        }
        this.videocallLogSend(VideocallStatusEnum.NotAnswered, 'TOO_MANY_TIME_WAIT').subscribe();
        console.log('Timer Paused.');
      }
    }, 1000);
  }

  timeTransform(value: number): string {
    const minutes: number = Math.floor(value / 60);
    return (Math.floor(value / 60) < 10 ? '0' : '') + minutes + ':'
      + ((value - minutes * 60) < 10 ? 0 : '') + (value - minutes * 60);
  }

  pauseTimer() {
    this.time = 0;
    this.meetTime = 0;
    clearInterval(this.interval);
  }

  frameRemove() {
    document.getElementById('assistBoxIframe')?.remove();
  }

  closeViewerAfterTooManyTime() {
    // this.videocallLogSend('TOO_MANY_TIME_CLOSE_CLICKED').subscribe();
    self.close();
    // this.closeViewer();
  }

  setHeaderTitle() {
    document.querySelector('head > title').innerHTML = 'Borusan - ' + this.headerTitle;
  }

  closeViewer() {
    this.router
      .navigate(
        [
          '/',
          ...environment.rootUrl.split('/'),
          'live-support',
          'after'
        ],
        { replaceUrl: true }
      )
      .then();
    // self.close();
  }

  ngAfterViewInit(): void {
    this.setHeaderTitle();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  saveUser() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }
    this.user = {
      email: '<EMAIL>',
      ...this.form.value,
    };

    this.logService
      .action(this.callType, 'FORM_SUBMITTED',
        {
          queueCode: this.videocallSetup.queueCode,
          language: this.userLang,
          isIOS: this.isIOS,
          ProviderQueueId: this.videocallMessage?.message?.claimId,
          ProviderCallId: this.videocallMessage?.message?.appointmentId,
          user: this.user
        }).subscribe();
    this.videocallLogSend(VideocallStatusEnum.Started, 'FORM_SUBMITTED');

    this.videocallJsInject();
  }

  sendVideocallLog(status: number = 2, transaction: string, logData: string = '') {
    const log: VideocallLogModel = {
      videoCallId: this.videoCallId,
      status,
      transaction,
      logdata: logData,
      videoCallProvider: 0,
      ProviderQueueId: this.videocallMessage?.message?.claimId,
      ProviderCallId: this.videocallMessage?.message?.appointmentId,
      CallerName: this.user?.firstName,
      CallerSurname: this.user?.lastName,
      CallerPhone: this.user?.mobile,
      videoCallType: this.videocallSetup?.videoCallType,
      queueName: this.videocallSetup?.queueName,
    };
    this.store.dispatch(new VideocallLogAction(log));
    this.updateVideocallForm(status);
  }

  updateVideocallForm(status: number) {
    if (!this.startSended) {
      return;
    }
    const update: VideocallFormUpdateModel = {
      Provider: this.provider,
      videoCallId: this.videoCallId,
      VideoCallType: this.videocallSetup?.videoCallType,
      videoCallFormId: null,
      ProviderQueueId: this.videocallMessage?.message?.claimId,
      ProviderCallId: this.videocallMessage?.message?.appointmentId,
      status
    };
    this.store.dispatch(new VideocallUpdateAction(update));
  }

  videocallStartSend() {
    const startData: VideocallFormStartModel = {
      Provider: 'assistbox',
      VideoCallType: this.videocallSetup?.videoCallType,
      QueueName: this.videocallSetup?.queueName,
      ProviderQueueId: this.videocallMessage?.message?.claimId,
    };
    this.store.dispatch(new VideocallStartAction(startData));
    this.getVideocallFormValues$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((x) => {
        if (x) {
          this.startSended = true;
          this.videoCallId = x?.videoCallId;
        }
      });
  }
}
