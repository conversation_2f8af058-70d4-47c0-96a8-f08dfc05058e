.empty-content {
  padding: 10rem 5rem;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 26px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}

.empty-content {
  padding: 10rem 5rem;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 26px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}

.videocall-empty-content {
  padding: 10rem 5rem;
  height: 100vh;
  z-index: 100;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}

#survey-stars {
  .rating {
    &-group{
      display: inline-flex;
    }
    &-icon {
      pointer-events: none;
    }
    &-input {
      position: absolute !important;
      left: -9999px !important;
      &-none{
        display: none
      }
    }
    &-icon {
      pointer-events: none;
    }
    &-label {
      cursor: pointer;
      padding: 0 0.1em;
      font-size: 2rem;
    }
    &-icon-star {
      color: #ddd;
    }
    &-selected {
      color: orange;
    }
  }
}
