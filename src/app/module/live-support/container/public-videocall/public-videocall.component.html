<cat-basic-modal [status]="openForm && !user"
                 [headerText]="'_technical_support_title' | translate"
                 [showCloseButton]="false"
>
  <cat-info-box *ngIf="('_technical_support_body' | translate)!=='-'"
                [title]="'_info'|translate">
    {{'_technical_support_body' | translate}}
  </cat-info-box>
  <div [formGroup]="form" class="mt-3 mb-3">
    <div class="form-group" *ngIf="!user?.firstName">
      <input
        catInputLength
        [name]="'firstName'"
        [placeholder]="'_name' | translate"
        class="form-control form-control"
        formControlName="firstName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.firstName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.firstName) | translate }}
      </div>
    </div>
    <!-- ? Surname -->
    <div class="form-group" *ngIf="!user?.lastName">
      <input
        catInputLength
        [name]="'lastName'"
        [placeholder]="'_surname' | translate"
        class="form-control form-control"
        formControlName="lastName"
        type="text"
        minlength="2"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.lastName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.lastName) | translate }}
      </div>
    </div>
    <!-- ? Company Phone -->
    <div class="form-group" *ngIf="!user?.mobile">
      <input
        catInputLength
        [name]="'Phone'"
        [placeholder]="'_phone_number' | translate"
        class="form-control form-control"
        formControlName="mobile"
        type="tel"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.mobile) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.mobile) | translate }}
      </div>
    </div>
    <button
      class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm"
      (click)="saveUser()">
      {{ "_start" | translate }}
    </button>
  </div>

</cat-basic-modal>
<!-- ? Media Permission Denied Error -->
<div class="text-center" *ngIf="showMediaPermissionError && !tooManyTimeWait">
  <div class="videocall-empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-close-circle text-warning"></i>
    </div>
    <div class="videocall-empty-content-message text-center ">
      {{ '_phone_media_permission_denied' | translate }}
    </div>
    <!--    <button class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="getLocalStream()">-->
    <!--      {{ "_request_media_permission_again" | translate }}-->
    <!--    </button>-->
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm"
            (click)="closeViewer()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<!-- ? Too Many Time Wait -->
<div class="text-center" *ngIf="tooManyTimeWait">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-videocall text-warning"></i>
    </div>
    <div class="empty-content-message text-center"
         [innerHtml]="'_videocall_many_time_wait_message' | translate | safeHtml">
    </div>
    <button *ngIf="!isIOS" class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm"
            (click)="closeViewerAfterTooManyTime()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<cat-loader [show]="showRedirectQueue"></cat-loader>
