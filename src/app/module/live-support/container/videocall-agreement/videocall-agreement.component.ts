import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { AgreementTypeEnum } from 'src/app/module/definition/enum/agreement-type.enum';
import { AgreementModel, UserAgreementsListModel } from 'src/app/module/definition/model/agreement.model';
import { DefinitionService } from 'src/app/module/definition/service/definition.service';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { ApproveUserAgreementAction } from '../../../definition/action/definition.actions';
import { DefinitionState } from '../../../definition/state/definition.state';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cat-videocall-agreement',
  templateUrl: './videocall-agreement.component.html',
  styleUrls: ['./videocall-agreement.component.scss']
})
export class VideocallAgreementComponent implements OnInit {
  @Input()
  showHeader = true;

  @Output()
  accept: EventEmitter<boolean> = new EventEmitter();

  @Output()
  reject: EventEmitter<boolean> = new EventEmitter();

  agreements: AgreementModel[];
  form: FormGroup = new FormGroup({
    // agreements: new FormControl()
  });

  user: UserModel;
  agreementTypeEnum = AgreementTypeEnum;
  formSubmitDisable = false;

  @Select(DefinitionState.getAgreementLoading)
  agreementsLoading$: Observable<boolean>;

  @Select(DefinitionState.getUserAgreements)
  getUserAgreements$: Observable<UserAgreementsListModel[]>;

  @Select(DefinitionState.approveUserAgreementSend)
  approvedUserAgreementSend$: Observable<boolean>;

  headers = {};
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly definitionService: DefinitionService,
    private readonly fb: FormBuilder,
    private readonly store: Store,
    private readonly messageFrameService: MessageFrameService,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit() {
    const { showHeader } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    this.user = this.store.selectSnapshot(LoginState.user);
  }

  onSubmitForm() {
    this.headers = this.agreements?.map(({name}) => name);
    this.store.dispatch(new ApproveUserAgreementAction(
      { ApprovedAgreementNames: this.headers }
    ));
    this.approvedUserAgreementSend$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => {
        if (x) {
          this.sendAccept();
        }
      });
  }

  sendAccept() {
    this.accept.emit(true);
  }

  cancelClick() {
    this.reject.emit(true);
  }

  getAgreementsData(data) {
    this.agreements = data;
  }

  navigateToBack() {
    // if public page
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    this.back();
  }

  back() {
    window.history.back();
  }
}
