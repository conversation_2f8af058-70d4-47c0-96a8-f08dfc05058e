import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UserModel } from 'src/app/module/authentication/model/user.model';
import { LoginState } from 'src/app/module/authentication/state/login/login.state';
import { CustomerDetailAction } from 'src/app/module/customer/action/customer.actions';
import { CustomerModel, PssrList } from 'src/app/module/customer/model/customer.model';
import { CustomerService } from 'src/app/module/customer/service/customer.service';
import { LogService } from 'src/app/module/customer/service/log.service';
import { CustomerState } from 'src/app/module/customer/state/customer.state';
import { GetAllCountryListAction, GetCityListAction } from 'src/app/module/definition/action/definition.actions';
import { AgreementModel } from 'src/app/module/definition/model/agreement.model';
import { City } from 'src/app/module/definition/model/city.model';
import { Country } from 'src/app/module/definition/model/country.model';
import { DefinitionState } from 'src/app/module/definition/state/definition.state';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { CustomerRelationModel } from 'src/app/module/shared/model/company.model';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { CommonState } from 'src/app/module/shared/state/common/common.state';
import { GetBasisSettingsAction } from 'src/app/module/shared/state/settings/settings.actions';
import { CustomValidator } from 'src/app/util/custom-validator';
import { getFormErrorMessage } from 'src/app/util/get-form-error-message.util';
import { isShowFormError } from 'src/app/util/is-show-form-error.util';
import { validateAllFormFields } from 'src/app/util/validate-all-form-fields.util';
import { environment } from 'src/environments/environment';
import { VideocallClearAction, VideocallGetFaultyComponentsAction, VideocallStartAction } from '../../action/videocall.action';
import { VideocallFormFaultyComponentsModel, VideocallFormStartModel, VideocallFormValuesModel } from '../../model/videocal.model';
import { VideocallState } from '../../state/videocall.state';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { DefinitionService } from '../../../definition/service/definition.service';

@Component({
  selector: 'cat-support-form',
  templateUrl: './support-form.component.html',
  styleUrls: ['./support-form.component.scss']
})
export class SupportFormComponent implements OnInit, OnDestroy {
  // ? Videocall Data
  @Select(VideocallState.getVideocallFormFaultyComponents)
  faultyComponents$: Observable<VideocallFormFaultyComponentsModel[]>;

  @Select(VideocallState.getVideocallLoading)
  videocallLoading$: Observable<boolean>;

  @Select(VideocallState.getVideocallValues)
  getVideocallFormValues$: Observable<VideocallFormValuesModel>;
  getVideocallFormValues: VideocallFormValuesModel;

  // ? County&City data
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  // ? Customer Data
  @Select(CommonState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  @Select(LoginState.language)
  language$: Observable<string>;
  language: string;

  supportForm: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    CompanyPhoneNumber: new FormControl(null),
    PhoneNumber: new FormControl(null, []),
    EquipmentSerialNumber: new FormControl(null, [Validators.required]),
    EquipmentNumber: new FormControl(null),
    CountryCode: new FormControl(null),
    City: new FormControl(null),
    CompanyId: new FormControl(),
    CompanyName: new FormControl(null),
    CallReason: new FormControl(null, [Validators.required]),
    BesideEquipment: new FormControl(null, [Validators.required]),
    IsInternet: new FormControl(null, [Validators.required]),
    IsSafeArea: new FormControl(null, [Validators.required]),
    IsEquipmentWorking: new FormControl(null, [Validators.required]),
    ThinkFaulty: new FormControl(null),
  });

  user: UserModel;
  companyName: string;
  company = null;
  customer = null;
  cityList: City[];
  pssrList: PssrList[] | any = [];

  equipmentSerialNumber: string;
  equipmentNumber: string;
  showHeader = true;

  stateModalShow = false;
  stateModelTypes = '';
  contactModal = false;
  formSubmitDisable = true;
  showVideocallAgreement = false;
  skipAgreement = true;

  agreements: AgreementModel[];
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly store: Store,
    private readonly messageFrameService: MessageFrameService,
    private readonly logger: LogService,
    private readonly customerService: CustomerService,
    private readonly definitionService: DefinitionService,
  ) { }

  ngOnInit() {
    const { showHeader, serialNumber, equipmentNumber } = this.route.snapshot.queryParams;
    if (showHeader !== undefined) {
      this.showHeader = showHeader === 'true';
    }
    if (serialNumber !== undefined) {
      this.equipmentSerialNumber = serialNumber;
    }
    if (equipmentNumber !== undefined) {
      this.equipmentNumber = equipmentNumber;
    }
    this.company = this.store.selectSnapshot(LoginState.company);
    this.customer = this.store.selectSnapshot(LoginState.customer);

    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.user = this.store.selectSnapshot(LoginState.user);

    this.store.dispatch(
      new CustomerDetailAction(this.customer?.customerNumber)
    );
    this.customer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customer => {
        if (customer) {
          this.pssrList = customer.details?.pssrList.filter(pssr => {
            if (pssr.titles.find(data => data === 'PSSR')) {
              return pssr.mailList.length || pssr.telephoneList.length;
            }
            return false;
          });
          this.language$
            .pipe(takeUntil(this.subscriptions$))
            .subscribe((language) => {
              this.language = language;
            });
          this.definitionService.getUserAgreements(AgreementTypeEnum.VideoCall)
            .subscribe((data) => {
              if (data && data?.length) {
                this.skipAgreement = !(data.filter((x) => x.reapproveNeeded === true)?.length ||
                  data.filter((x) => x.isApproved === false)?.length);
              }
            });
        }
      });

    this.store.dispatch(new VideocallGetFaultyComponentsAction());
    this.loadForm();
  }

  loadForm() {
    this.cityList$.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          this.cityList = data;
        }
      );

    const patchValues: any = {};
    if (this.user) {
      patchValues.Name = this.user.firstName;
      patchValues.Surname = this.user.lastName;
      patchValues.Email = this.user.email.toLowerCase();
      patchValues.PhoneNumber = this.user.mobile;
      patchValues.CompanyPhoneNumber = this.user.mobile;
    }
    if (this.company) {
      patchValues.CompanyId = this.company.id;
    }
    if (this.customer) {
      patchValues.CompanyName = this.customer.name;
      this.companyName = this.customer.name;
    }
    if (this.equipmentSerialNumber) {
      patchValues.EquipmentSerialNumber = this.equipmentSerialNumber;
      patchValues.EquipmentNumber = this.equipmentNumber;
    }
    this.supportForm.patchValue(patchValues);
  }

  showAgreement() {
    if (this.supportForm.valid) {
      if (this.skipAgreement) {
        this.onSubmitForm();
        return;
      }
      this.showVideocallAgreement = true;
    } else {
      validateAllFormFields(this.supportForm);
    }
  }

  onSubmitForm() {
    if (this.supportForm.valid) {
      const form: VideocallFormStartModel = {
        VideoCallType: 0,
        QueueName: null,
        Provider: 0,
        ProviderQueueId: null,
        ProviderCallerId: null,
        CallReason: this.supportForm.value.CallReason === 'fault' ? 1 : 2,
        NearMachine: this.getBoolean(this.supportForm.value.BesideEquipment),
        InternetAccess: this.getBoolean(this.supportForm.value.IsInternet),
        MachineSafe: this.getBoolean(this.supportForm.value.IsSafeArea),
        MachineWorking: this.getBoolean(this.supportForm.value?.IsEquipmentWorking),
        FaultyComponent: this.supportForm.value?.ThinkFaulty || 8,
        EquipmentSerialNumber: this.supportForm.value?.EquipmentSerialNumber,
        city: this.supportForm.value?.City
      };
      console.log('Sended form: ', JSON.stringify(form));
      this.store.dispatch(new VideocallStartAction(form));
      this.getVideocallFormValues$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(data => {
          if (data) {
            this.getVideocallFormValues = data;
            this.callStart(form);
          }
        });
    } else {
      validateAllFormFields(this.supportForm);
    }
  }

  aggrementAccept() {
    this.onSubmitForm();
  }

  aggrementReject() {
    this.navigateToBack();
  }

  getBoolean(s: any): boolean {
    return s === 'true';
  }

  callStart(form: VideocallFormStartModel) {
    this.logger
      .action('LIVE_REMOTE_TECHNICAL_SUPPORT', 'SUBMIT_FORM_CLICK', { supportForm: this.supportForm.controls.value })
      .subscribe();
    this.customerService.getOTT().subscribe(ott => {
      if (ott) {
        this.messageFrameService.sendMessage(FrameMessageEnum.openAssistBox, {
          url: window.location.origin + '/' + environment.rootUrl + '/live-support/videocall?ott=' + ott.oneTimeToken
            + '&videoCallId=' + this.getVideocallFormValues.videoCallId
            + '&videoCallFormId=' + this.getVideocallFormValues.videoCallFormId
            + '&language=' + this.language || 'en',
          data: {
            videoCallId: this.getVideocallFormValues.videoCallId,
            videoCallFormId: this.getVideocallFormValues.videoCallFormId,
            formData: form,
          }
        });
        this.store.dispatch(new VideocallClearAction());
        // Live Support After Returned Equipment Detail
        setTimeout(() => this.back(), 100);
      }

    });
  }

  sendFormStateControl(type: string) {
    this.stateModelTypes = type;
    if ((['debriafing', 'false'].indexOf(this.supportForm.controls[type].value) >= 0)) {
      this.stateModalShow = true;
    }

    const listValue = ['CallReason', 'BesideEquipment', 'IsInternet', 'IsSafeArea']
      .map(x => ['debriafing', 'false'].indexOf(this.supportForm.controls[x].value) >= 0);
    this.formSubmitDisable = !listValue.every(v => v === false);
  }

  onChangeCountry(countryCode: string) {
    const selectedCountry = this.getCountryByCode(countryCode);
    this.supportForm.patchValue({ EquipmentCity: null });

    this.supportForm.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.supportForm.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.supportForm.controls.City.clearValidators();
      this.supportForm.controls.City.updateValueAndValidity();
      return of([]);
    }
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);
    return country?.isActive;
  }

  showContactModalClick() {
    this.loadForm();
    this.stateModalShow = false;
    this.contactModal = true;
  }

  requestService() {
    this.logger
      .action('LIVE_REMOTE_TECHNICAL_SUPPORT', 'REQUEST_SERVICE', { supportForm: this.supportForm.controls.value })
      .subscribe();
    this.router
      .navigate([
        '/',
        ...environment.rootUrl.split('/'),
        'form',
        'request-service',
        this.equipmentNumber
      ])
      .then();
  }

  onCall(phone: string) {
    this.logger
      .action('LIVE_REMOTE_TECHNICAL_SUPPORT', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.messageFrameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('LIVE_REMOTE_TECHNICAL_SUPPORT', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.messageFrameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter(d => d.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  navigateToBack() {
    // if public page
    if (this.showVideocallAgreement) {
      this.showVideocallAgreement = false;
      return;
    }
    if (!this.user) {
      // event for close webview
      this.messageFrameService.sendMessage(FrameMessageEnum.formBack);
    }
    this.back();
  }

  back() {
    window.history.back();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
