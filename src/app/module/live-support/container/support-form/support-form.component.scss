@import "variable/bootstrap-variable";


.live-support {

  .question-label {
    padding-left: 0.5rem !important;
    margin-bottom: 0.5rem !important;

    &.grey {
      border-left: 7px solid;
      border-color: #adb5bd !important;
      padding-left: 5px;
    }

    &.red {
      border-left: 7px solid;
      border-color: red !important;
      padding-left: 5px;
    }

    &.green {
      border-left: 7px solid;
      border-color: green !important;
      padding-left: 5px;
    }

    &.orange {
      border-left: 7px solid;
      border-color: #FFA300 !important;
      padding-left: 5px;
    }
  }

  .radio-button {

    label {
      margin-bottom: 0;
    }

    .btn-outline-danger:hover,
    .btn-outline-danger:active {
      background-color: transparent;
      border-color: #DA3A3C;
      color: #DA3A3C;
    }

    .btn-outline-warning:hover,
    .btn-outline-warning:active {
      background-color: transparent;
      border-color: #5E9731;
      color: #5E9731;
    }

    .btn-outline-success:hover,
    .btn-outline-success:active {
      background-color: transparent;
      border-color: #5E9731;
      color: #5E9731;
    }

    .btn-sm {
      padding: 0.27rem 0.75rem;
      font-size: 0.875rem;
      line-height: 1.5;
      border-radius: 8px;
    }

    .btn-group>.btn {
      position: unset;
    }

    .radio-btn-special {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      height: 50px;
    }

    .form-check {
      position: unset;
      overflow-wrap: anywhere;
    }

    .btn-check {
      position: absolute;
      clip: rect(0, 0, 0, 0);
      pointer-events: none;
    }

    .btn-group .btn {
      border-radius: 6px;
    }

    .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .btn-group>.btn-group:not(:first-child)>.btn,
    .btn-group>.btn:nth-child(n+3),
    .btn-group> :not(.btn-check)+.btn {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    .btn-outline-secondary.selected {
      color: #fff;
      background-color: $secondary;
      border-color: $secondary;
    }

    .btn-outline-danger.selected {
      color: #fff;
      background-color: #DA3A3C;
      border-color: #DA3A3C;
    }

    .btn-outline-success.selected {
      color: #fff;
      background-color: #5E9731;
      border-color: #5E9731;
    }

    .btn-outline-warning.selected {
      color: #212529;
      background-color: #FFA300;
      border-color: #FFA300;
    }

  }

  .ng-select ::ng-deep .ng-select-container {
    border-radius: 6px;
    border: 1px solid #d7e5ea;
  }


  .success-message {
    font-size: 26px;
    font-weight: 700;
  }


  [type="checkbox"]:checked,
  [type="checkbox"]:not(:checked) {
    position: absolute;
    left: -9999px;
  }

  [type="checkbox"]:checked+label,
  [type="checkbox"]:not(:checked)+label {
    position: relative;
    padding-left: 36px;
    cursor: pointer;
    display: inline-block;
    color: #666;
    font-size: 16px;
    line-height: 18px;
    margin-bottom: 0.8rem;
  }

  [type="checkbox"]:checked+label:before,
  [type="checkbox"]:not(:checked)+label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 18px;
    height: 18px;
    border: 1px solid #ddd;
    background: #fff;
  }

  [type="checkbox"]:checked+label:before {
    border-color: #ffa300;
  }

  [type="checkbox"]:checked+label:after,
  [type="checkbox"]:not(:checked)+label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: #ffa300;
    position: absolute;
    top: 3px;
    left: 3px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }

  [type="checkbox"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  [type="checkbox"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}


.service-person {
  margin: 0;
  padding: 0;
  list-style: none;
  border-bottom: 1px solid #dbdbdb;

  &-item {
    border-top: 1px solid #dbdbdb;
    padding: 1.25rem;

    &-name {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #505050;
    }

    &-detail {
      margin: 0;
      padding: 0;
      list-style: none;

      &-item {
        overflow-wrap: anywhere;
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        color: #505050;
        margin-top: 1rem;

        .icon-area {
          margin-right: 1rem;
          border-radius: 50%;
          background-color: #ebebeb;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0.625rem;
        }
      }
    }
  }
}
