<div class="px-4 pb-5 live-support" [class.pt-4]="!showHeader">
  <div class="h4 py-4 mb-0 text-center nav-back" *ngIf="showHeader">
    <i class="icon icon-back mr-2 float-left" (click)="navigateToBack()"></i>
    {{ "_live_remote_technical_support" | translate }}
  </div>
  <!-- ? SupportForm -->
  <form (submit)="showAgreement()" [formGroup]="supportForm" class="question">

    <div class="form-group">{{ user.firstName }} {{ user.lastName }}</div>
    <div class="form-group">{{ companyName }}</div>
    <!-- ? EquipmentSerialNumber -->
    <div *ngIf="equipmentSerialNumber else notHaveEquipmentNumber" class="form-group">
      {{ equipmentSerialNumber }}
    </div>
    <ng-template #notHaveEquipmentNumber>
      <!-- ? EquipmentSerialNumber Dropdown -->
      <div class="form-group">
        <cat-machine-serial [form]="supportForm" [fieldName]="'EquipmentSerialNumber'">
        </cat-machine-serial>
        <div [ngClass]="{
            'd-block': isShowError(supportForm.controls.EquipmentSerialNumber)
          }" class="invalid-feedback pl-3">
          {{
            getFormErrorMessage(supportForm.controls.EquipmentSerialNumber)
              | translate
          }}
        </div>
      </div>
    </ng-template>
    <!-- ? CallReason -->
    <div class="form-group radio-button">
      <label [for]="'CallReason'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.CallReason.value,
          red: supportForm.controls.CallReason.value === 'fault',
          orange: supportForm.controls.CallReason.value === 'debriafing'
        }">
        1. {{ "_call_reason" | translate }}
      </label>
      <div class="form-check btn-group w-100 row">
        <input (change)="sendFormStateControl('CallReason')" type="radio" id="fault" name="CallReason" class="btn-check d-none"
          formControlName="CallReason" value="fault" />
        <label class="col-4 btn btn-sm btn-outline-danger radio-btn-special" for="fault" [ngClass]="{
            selected: supportForm.controls.CallReason.value === 'fault'
          }">{{ "_fault" | translate }}</label>
        <input (change)="sendFormStateControl('CallReason')" type="radio" id="debriafing" name="CallReason" class="btn-check d-none"
          formControlName="CallReason" value="debriafing" />
        <label class="col-4 btn btn-sm btn-outline-warning radio-btn-special" for="debriafing" [ngClass]="{
            selected: supportForm.controls.CallReason.value === 'debriafing'
          }">{{ "_debriafing" | translate }}</label>
      </div>

      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.CallReason)
        }" class="invalid-feedback pl-3">
        {{ getFormErrorMessage(supportForm.controls.CallReason) | translate }}
      </div>
    </div>
    <!-- ? BesideEquipment -->
    <div class="form-group radio-button">
      <label [for]="'BesideEquipment'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.BesideEquipment.value,
          green: supportForm.controls.BesideEquipment.value === 'true',
          red: supportForm.controls.BesideEquipment.value === 'false'
        }">
        2. {{ "_beside_equipment" | translate }}
      </label>
      <div class="form-check btn-group w-100 row">
        <input (change)="sendFormStateControl('BesideEquipment')" type="radio" id="besideTrue" name="BesideEquipment"
          class="btn-check d-none" formControlName="BesideEquipment" value="true" />
        <label class="col-4 btn btn-sm btn-outline-success radio-btn-special" for="besideTrue" [ngClass]="{
            selected: supportForm.controls.BesideEquipment.value === 'true'
          }">{{ "_yes" | translate }}</label>
        <input (change)="sendFormStateControl('BesideEquipment')" type="radio" id="besideFalse" name="BesideEquipment"
          class="btn-check d-none" formControlName="BesideEquipment" value="false" />
        <label class="col-4 btn btn-sm btn-outline-danger radio-btn-special" for="besideFalse" [ngClass]="{
            selected: supportForm.controls.BesideEquipment.value === 'false'
          }">{{ "_no" | translate }}</label>
      </div>

      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.BesideEquipment)
        }" class="invalid-feedback pl-3">
        {{
          getFormErrorMessage(supportForm.controls.BesideEquipment) | translate
        }}
      </div>
    </div>
    <!-- ? IsInternet -->
    <div class="form-group radio-button">
      <label [for]="'IsInternet'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.IsInternet.value,
          green: supportForm.controls.IsInternet.value === 'true',
          red: supportForm.controls.IsInternet.value === 'false'
        }">
        3. {{ "_is_internet_access" | translate }}
      </label>
      <div class="form-check btn-group w-100 row">
        <input (change)="sendFormStateControl('IsInternet')" type="radio" id="isInternetTrue" name="IsInternet" class="btn-check d-none"
          formControlName="IsInternet" value="true" />
        <label class="col-4 btn btn-sm btn-outline-success radio-btn-special" for="isInternetTrue" [ngClass]="{
            selected: supportForm.controls.IsInternet.value === 'true'
          }">{{ "_yes" | translate }}</label>
        <input (change)="sendFormStateControl('IsInternet')" type="radio" id="isInternetFalse" name="IsInternet" class="btn-check d-none"
          formControlName="IsInternet" value="false" />
        <label class="col-4 btn btn-sm btn-outline-danger radio-btn-special" for="isInternetFalse" [ngClass]="{
            selected: supportForm.controls.IsInternet.value === 'false'
          }">{{ "_no" | translate }}</label>
      </div>

      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.IsInternet)
        }" class="invalid-feedback pl-3">
        {{ getFormErrorMessage(supportForm.controls.IsInternet) | translate }}
      </div>
    </div>
    <!-- ? IsSafeArea -->
    <div class="form-group radio-button">
      <label [for]="'IsSafeArea'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.IsSafeArea.value,
          green: supportForm.controls.IsSafeArea.value === 'true',
          red: supportForm.controls.IsSafeArea.value === 'false'
        }">
        4. {{ "_is_safe_area_suitable" | translate }}
      </label>
      <div class="form-check btn-group w-100 row">
        <input (change)="sendFormStateControl('IsSafeArea')" type="radio" id="isSafeAreaTrue" name="IsSafeArea" class="btn-check d-none"
          formControlName="IsSafeArea" value="true" />
        <label class="col-4 btn btn-sm btn-outline-success radio-btn-special" for="isSafeAreaTrue" [ngClass]="{
            selected: supportForm.controls.IsSafeArea.value === 'true'
          }">{{ "_yes" | translate }}</label>
        <input (change)="sendFormStateControl('IsSafeArea')" type="radio" id="isSafeAreaFalse" name="IsSafeArea" class="btn-check d-none"
          formControlName="IsSafeArea" value="false" />
        <label class="col-4 btn btn-sm btn-outline-danger radio-btn-special" for="isSafeAreaFalse" [ngClass]="{
            selected: supportForm.controls.IsSafeArea.value === 'false'
          }">{{ "_no" | translate }}</label>
      </div>

      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.IsSafeArea)
        }" class="invalid-feedback pl-3">
        {{ getFormErrorMessage(supportForm.controls.IsSafeArea) | translate }}
      </div>
    </div>
    <!-- ? IsEquipmentWorking -->
    <div class="form-group radio-button">
      <label [for]="'IsEquipmentWorking'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.IsEquipmentWorking.value,
          green: supportForm.controls.IsEquipmentWorking.value === 'true',
          red: supportForm.controls.IsEquipmentWorking.value === 'false'
        }">
        5. {{ "_is_equipment_working" | translate }}
      </label>
      <div class="form-check btn-group w-100 row">
        <input type="radio" id="isEquipmentWorkingTrue" name="IsEquipmentWorking" class="btn-check d-none"
          formControlName="IsEquipmentWorking" value="true" />
        <label class="col-4 btn btn-sm btn-outline-success radio-btn-special" for="isEquipmentWorkingTrue" [ngClass]="{
            selected: supportForm.controls.IsEquipmentWorking.value === 'true'
          }">{{ "_yes" | translate }}</label>
        <input type="radio" id="isEquipmentWorkingFalse" name="IsEquipmentWorking" class="btn-check d-none"
          formControlName="IsEquipmentWorking" value="false" />
        <label class="col-4 btn btn-sm btn-outline-danger radio-btn-special" for="isEquipmentWorkingFalse" [ngClass]="{
            selected: supportForm.controls.IsEquipmentWorking.value === 'false'
          }">{{ "_no" | translate }}</label>
      </div>

      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.IsEquipmentWorking)
        }" class="invalid-feedback pl-3">
        {{
          getFormErrorMessage(supportForm.controls.IsEquipmentWorking)
            | translate
        }}
      </div>
    </div>
    <!-- ? Sound -->
    <div class="form-group" *ngIf="supportForm.controls.IsEquipmentWorking.value === 'true' || false">

    </div>
    <!-- ? ThinkFaulty -->
    <div class="form-group">
      <label [for]="'IsEquipmentWorking'" class="font-size-14px question-label" [ngClass]="{
          grey: !supportForm.controls.ThinkFaulty.value,
          green: supportForm.controls.ThinkFaulty.value
        }">
        6. {{ "_equipment_think_faulty" | translate }}
      </label>
      <ng-select [searchable]="false" [placeholder]="'_select' | translate" formControlName="ThinkFaulty">
        <ng-option *ngFor="let faulty of (faultyComponents$ | async)" [value]="faulty?.id">
          {{ faulty.text | translate }}
        </ng-option>
      </ng-select>
      <div [ngClass]="{
          'd-block': isShowError(supportForm.controls.ThinkFaulty)
        }" class="invalid-feedback pl-3">
        {{
          getFormErrorMessage(supportForm.controls.ThinkFaulty)
            | translate
        }}
      </div>
    </div>

    <!-- ? Submit -->
    <input [value]="'_call_start' | translate" class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg" type="submit"
      [disabled]="formSubmitDisable" />
  </form>
  <cat-basic-modal [ngClass]="{'d-none': !showVideocallAgreement && !skipAgreement}" [(status)]="showVideocallAgreement">
    <cat-videocall-agreement
      [showHeader]="false"
      (accept)="aggrementAccept()"
      (reject)="aggrementReject()">
    </cat-videocall-agreement>
  </cat-basic-modal>
</div>

<cat-basic-modal *ngIf="stateModalShow" [(status)]="stateModalShow">
  <ng-container [ngSwitch]="stateModelTypes">
    <!-- ? Step Stop CallReason -->
    <ng-container *ngSwitchCase="'CallReason'">
      <div class="mb-3">
        <div class="mt-4">
          {{ '_call_pssr_message' | translate }}
        </div>
        <div class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="showContactModalClick()">
          {{ '_contact_with_customer_service' | translate }}
        </div>
      </div>
    </ng-container>
    <!-- ? Step Stop BesideEquipment -->
    <ng-container *ngSwitchCase="'BesideEquipment'">
      <div class="mb-3">
        <div class="mt-4">
          {{ '_dont_beside_equipment_message' | translate }}
        </div>
        <div class="btn btn-info btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="requestService()">
          {{ '_request_service' | translate }}
        </div>
        <div class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="showContactModalClick()">
          {{ '_contact_with_customer_service' | translate }}
        </div>
      </div>
    </ng-container>
    <!-- ? Step Stop IsInternet -->
    <ng-container *ngSwitchCase="'IsInternet'">
      <div class="mb-3">
        <div class="mt-4">
          {{ '_live_support_internet_access_not_good_message' | translate }}
        </div>
        <div class="btn btn-info btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="requestService()">
          {{ '_request_service' | translate }}
        </div>
        <div class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="showContactModalClick()">
          {{ '_contact_with_customer_service' | translate }}
        </div>
      </div>
    </ng-container>
    <!-- ? Step Stop IsSafeArea -->
    <ng-container *ngSwitchCase="'IsSafeArea'">
      <div class="mb-3">
        <div class="mt-4">
          {{ '_live_support_dont_safe_area_message' | translate }}
        </div>
        <div class="btn btn-info btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="requestService()">
          {{ '_request_service' | translate }}
        </div>
        <div class="btn btn-warning btn-gradient btn-block text-white shadow rounded-lg mt-4" (click)="showContactModalClick()">
          {{ '_contact_with_customer_service' | translate }}
        </div>
      </div>
    </ng-container>
    <!-- ? Step Stop Default -->
    <ng-container *ngSwitchDefault></ng-container>
  </ng-container>
</cat-basic-modal>

<!-- ? Contact Modal -->
<cat-basic-modal *ngIf="contactModal" [(status)]="contactModal" [headerText]="'_contact_with_customer_service' | translate">
  <div class="mb-3">
    <div *ngIf="pssrList">
      <ul class="service-person">
        <li class="service-person-item" *ngFor="let persons of pssrList">
          <div class="service-person-item-name">
            {{ persons.pssrName }}
          </div>
          <div class="pssr-title">
            <ng-container *ngFor="let title of persons.titles; let i = index">
              {{ "_customer_title_" + title | translate }}
              {{ i !== persons.titles.length - 1 ? "-" : "" }}
            </ng-container>
          </div>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item" *ngFor="let phones of persons.telephoneList"
              (click)="onCall(phones.telephoneNumber)">
              <div class="icon-area">
                <i class="icon icon-phone-call"></i>
              </div>
              {{ phones.telephoneNumberShort || phones.telephoneNumber }}
            </li>
          </ul>

          <ul class="service-person-item-detail">
            <li class="service-person-item-detail-item" *ngFor="let mail of persons.mailList" (click)="onMail(mail.mailAdress)">
              <div class="icon-area">
                <i class="icon icon-contact"></i>
              </div>
              {{ mail.mailAdress }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</cat-basic-modal>

<!-- ? Call Start Success Modal -->


<cat-loader [show]="videocallLoading$ | async"></cat-loader>
