import { Injectable } from '@angular/core';
import { Store } from '@ngxs/store';
import { SetVideocallMessageAction } from '../action/videocall-message.action';
import { VideocallHandlerModal } from '../model/videocal.model';

@Injectable({
  providedIn: 'root',
})
export class VideocallMessageService {
  constructor(private readonly store: Store) {}

  assistboxMessage(message: any) {
    if (!message) {
      console.log('Videocall message data not found', message);
      return;
    }
    const m: VideocallHandlerModal = {
      event: message?.event || message,
      message: message?.message,
    };
    return this.store.dispatch(new SetVideocallMessageAction(m));
  }
}
