import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/response/http.response';
import { environment } from 'src/environments/environment';
import {
  VideocallAvailableAgentModel,
  VideocallFormFaultyComponentsModel,
  VideocallFormStartModel,
  VideocallFormUpdateModel,
  VideocallFormValuesModel,
  VideocallLogModel,
  VideocallSetupModel
} from '../model/videocal.model';

@Injectable({
  providedIn: 'root'
})
export class VideocallService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  getFaultyComponents(): Observable<VideocallFormFaultyComponentsModel[]> {
    return this.http.get<HttpResponse<VideocallFormFaultyComponentsModel[]>>(`${environment.api}/videocall/faultycomponents`)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  startVideocallForm(form: VideocallFormStartModel): Observable<VideocallFormValuesModel> {
    return this.http.post<HttpResponse<VideocallFormValuesModel>>(`${environment.api}/videocall/start`, form)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  updateVideocallForm(form: VideocallFormUpdateModel): Observable<string> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/videocall/update`, form)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  setupVideocall(provider: string, queueName: string): Observable<VideocallSetupModel> {
    return this.http.post<HttpResponse<VideocallSetupModel>>(`${environment.api}/videocall/setup`, { provider, queueName })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getVideocallAvailableAgent(queue: { queueCode?: string, queueName?: string }):
    Observable<VideocallAvailableAgentModel> {
    return this.http.post<HttpResponse<VideocallAvailableAgentModel>>
    (`${environment.api}/videocall/getavailableagent`, queue?.queueName
      ? { queueName: queue.queueName }
      : { queueCode: queue.queueCode })
      .pipe(
        map(val => {
          if (val.code === 0) {
            if (typeof val.data === 'number') {
              return {
                availableAgentCount: val.data,
                backupQueueName: null,
              };
            } else {
              return val.data;
            }
          }
          return null;
        })
      );
  }

  logVideocall(form: VideocallLogModel): Observable<boolean> {
    return this.http.post<HttpResponse<string>>(`${environment.api}/videocall/log`, form )
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data === 'OK';
          }
          return null;
        })
      );
  }

}
