import { Component, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { LogService } from 'src/app/module/customer/service/log.service';
import { LoggerService } from 'src/app/module/shared/service/logger.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'cat-non-boom',
  templateUrl: './non-boom.component.html',
  styleUrls: ['./non-boom.component.scss']
})
export class NonBoomComponent implements OnInit {

  warningIcon = `${environment.assets}/warning.svg`;
  playStore = `${environment.assets}/badges/playstore.svg`;
  appStore = `${environment.assets}/badges/appstore.svg`;

  constructor(
    private readonly store: Store,
    private readonly logger: LoggerService,
    private readonly log: LogService,
  ) { }

  ngOnInit() {
    const isLocalDev = ['local', 'development'].indexOf(environment.envName) !== -1;
    const isBOOM = window.navigator.userAgent.indexOf('BOOM') !== -1;
    if (isBOOM && !isLocalDev) {
      this.boomErrorSend();
    }
    if (!isBOOM){
      this.log.log('NON_BOOM', 'NON_BOOM_REDIRECT', {
        message: 'The mobileview page was accessed outside of the BOOM360 application.',
        name: 'Access Outside the BOOM360 App',
        url: window.location.href,
      }).subscribe();
    }
  }
  private boomErrorSend() {
    const logMessage = {
      message: 'The mobileview/non-boom page was accessed inside of the BOOM360 application.',
      name: 'The BOOM360 App redirected non-boom',
      url: window.location.href,
    };
    this.logger.jsErrorLog(logMessage).subscribe();
  }
}
