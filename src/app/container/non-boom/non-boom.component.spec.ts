/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { NonBoomComponent } from './non-boom.component';

describe('NonBoomComponent', () => {
  let component: NonBoomComponent;
  let fixture: ComponentFixture<NonBoomComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NonBoomComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NonBoomComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
