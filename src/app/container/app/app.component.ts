import { Component, HostListener, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { NgSelectConfig } from '@ng-select/ng-select';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { fromEvent, merge, Observable } from 'rxjs';
import { IncomingMessageService } from 'src/app/module/shared/service/incoming-message.service';
import { UpdateLanguageCodeAction } from '../../module/authentication/action/login/login.actions';
import { LoginState } from '../../module/authentication/state/login/login.state';
import { ConnectionStatusAction, SetHistoryStateAction } from '../../action/app/app.actions';
import { registerLocaleData } from '@angular/common';
import localeTr from '@angular/common/locales/tr';
import { environment } from 'src/environments/environment';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { filter, mapTo } from 'rxjs/operators';
import { MessageFrameService } from 'src/app/module/shared/service/message-frame.service';
import { FrameMessageEnum } from 'src/app/module/shared/enum/frame-message.enum';
import { EquipmentState } from 'src/app/module/customer/state/equipment.state';
import { CustomerModel } from 'src/app/module/customer/model/customer.model';
import { UserPermissionAction } from 'src/app/module/shared/state/settings/settings.actions';


@Component({
  selector: 'cat-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  @Select(LoginState.loginLoading)
  loading$: Observable<boolean>;

  @Select(LoginState.language)
  language$: Observable<string>;

  @Select(LoginState.customer)
  customer$: Observable<CustomerModel>;

  private connectionMonitor$: Observable<boolean>;
  connectionStatus = true;
  connectionStatusModal = false;

  constructor(
    private readonly translateService: TranslateService,
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly selectConfig: NgSelectConfig,
    private readonly router: Router,
    private readonly frameMessageService: MessageFrameService
  ) {
    this.route.queryParams.subscribe((params) => {
      let { language } = params;
      if (language && language.length !== 2) {
        language = 'tr';
      }
      if (language) {
        this.store.dispatch(new UpdateLanguageCodeAction(language));
      }
    });
    const offline$ = fromEvent(window, 'offline').pipe(mapTo(false));
    const online$ = fromEvent(window, 'online').pipe(mapTo(true));
    this.connectionMonitor$ = merge(offline$, online$);
    this.managePostMessage();
  }

  ngOnInit(): void {
    registerLocaleData(localeTr, 'tr-TR');
    if (this.store.selectSnapshot(LoginState.language)) {
      this.translateService.setDefaultLang(this.store.selectSnapshot(LoginState.language));
    }

    this.language$.subscribe((languageCode) => {
      if (languageCode) {
        this.translateService.use(languageCode);
        this.translateService.get('_no_items_found').subscribe(trns => {
          this.selectConfig.notFoundText = trns;
        });
      }
    });

    this.connectionMonitor$.subscribe(status => {
      this.connectionStatus = status;
      this.store.dispatch(new ConnectionStatusAction(!status));
      console.log('WebView State => CONNECTION_STATUS: ', status);
      if(!status) {
        this.frameMessageService.sendMessage(FrameMessageEnum.offlineEquipmentData, {
          equipment: this.store.selectSnapshot(EquipmentState.equipmentDetail)
        });
      }
    });

    this.router.events
      .pipe(
        filter(event => event instanceof NavigationStart)
      )
      .subscribe(
        () => {
          this.connectionStatusModal = !this.connectionStatus;
        }
      );
      this.customer$.subscribe((customer) => {
        if(customer) {
          this.store.dispatch(new UserPermissionAction());
        }
      })
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event) {
    console.log('history.back', event);
    this.store.dispatch(new SetHistoryStateAction(event.state));

    event.preventDefault();
    event.stopPropagation();
  }

  private managePostMessage() {
    console.log('App Created');

    const w = window as any;
    w.onmessage = this.incomingMessageService.getHandlerFunction();

    // if (w.top === w.self) { // dogrudan app serve ediyorsa
    w.handlePostMessage = this.incomingMessageService.getHandlerFunction();
    if (
      typeof w.postMessageQueue !== 'undefined' &&
      w.postMessageQueue?.length > 0
    ) {
      while (w.postMessageQueue.length) {
        this.incomingMessageService.handlePostMessage({
          data: w.postMessageQueue.shift(),
        } as MessageEvent);
      }
    }
    // }
    this.boomControl();
  }

  private boomControl() {
    const isLocal = ['local', 'development'].indexOf(environment.envName) !== -1;
    // const isLocal = ['local'].indexOf(environment.envName) !== -1;
    const nonBOOM = !(window.navigator.userAgent.indexOf('BOOM') !== -1);

    const isVideocall = window.location.href.search('videocall') !== -1;
    const isLoyality = window.location.href.search('loyality') !== -1;
    const isDigitalBanko = window.location.href.search('digital-banko') !== -1;
    const externalError = window.location.href.search('external-error') !== -1;
    const qrDetail = window.location.href.search('work-order/:workOrderNumber') !== -1;
    const auctionRateUs = window.location.href.search('auction-rate-us') !== -1;
    // console.log('url:', window.location.href);

    if (isVideocall) {
      // console.log(' video call: ', isVideocall);
    }

    if (nonBOOM && !isLocal && !isVideocall && !isLoyality && !isDigitalBanko && !externalError && !qrDetail && !auctionRateUs) {
      console.log('NON-BOOM Redirected');
      stopLoadingAnimation();
      this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
        this.router.navigate(['non-boom']));
    }
    if (!nonBOOM && (window.location.href.indexOf('boom') !== -1)) {
      stopLoadingAnimation();
    }
  }

}
