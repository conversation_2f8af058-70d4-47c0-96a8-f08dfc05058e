import { Component, OnInit } from '@angular/core';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { ActivatedRoute } from '@angular/router';
import { FrameMessageEnum } from '../../module/shared/enum/frame-message.enum';
import { MessageFrameService } from '../../module/shared/service/message-frame.service';

@Component({
  selector: 'cat-external-error',
  templateUrl: './external-error.component.html',
  styleUrls: ['./external-error.component.scss']
})
export class ExternalErrorComponent implements OnInit {
  message: string;
  detailMessage: string;
  button = '_close';

  constructor(
    private readonly route: ActivatedRoute,
    private readonly messageFrameService: MessageFrameService,
  ) {
    stopLoadingAnimation();
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.message = params?.message || '_external_error_message';
      this.detailMessage = params?.detailMessage;
    });
  }

  onButtonClick() {
    this.backToHome();
    self?.close();
  }

  backToHome() {
    this.messageFrameService.sendMessage(FrameMessageEnum.back_home);
  }
}
