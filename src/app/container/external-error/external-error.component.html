<div class="external-error">
  <div class="d-flex flex-column align-items-center justify-content-center my-3">
    <i class="icon icon-close-circle d-inline-block mb-3"></i>
    <div class="external-error-message">

      <p *ngIf="message" class="text-center">
        {{ message | translate }}
      </p>

    </div>
    <div class="external-error-detail-message">

      <p *ngIf="message" class="text-center">
        {{ detailMessage | translate }}
      </p>

    </div>
    <button class="px-5 btn btn-warning btn-sm text-white shadow"
            (click)="onButtonClick()">
      {{ '_close' | translate }}
    </button>

  </div>
</div>
