import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ttp<PERSON>eaderResponse,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { ChangeTokenAction, } from '../module/authentication/action/login/login.actions';
import { AuthenticationService } from '../module/authentication/service/authentication.service';
import { LoginState } from '../module/authentication/state/login/login.state';
import { FrameMessageEnum } from '../module/shared/enum/frame-message.enum';
import { MessageFrameService } from '../module/shared/service/message-frame.service';
import { ModalService } from '../module/shared/service/modal.service';
import { ConnectDialogComponent } from '../module/shared/component/connect-dialog/connect-dialog.component';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly frameMessage: MessageFrameService,
    private readonly authenticationService: AuthenticationService,
    private readonly modalService: ModalService
  ) {}

  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<| HttpSentEvent
    | HttpHeaderResponse
    | HttpProgressEvent
    | HttpResponse<any>
    | HttpUserEvent<any>
    | any> {
    return next.handle(this.requestCheck(request)).pipe(
      catchError((error) => {
        if (error.status === 401 && !request.url.includes('refreshtoken')) {
          return this.handle401Error(request, next);
        } else if (error.error && error.status === 504) {
          this.modalService.open(ConnectDialogComponent);
        } else if(error.status === 400) {
          const body = JSON.parse(error.error);
          this.modalService.errorModal({ message: body.message });
          return throwError(error);
        }

        if (request.url.includes('refreshtoken')) {
          return throwError(error);
        }

        try {
          const body = JSON.parse(error.error);
          if (body.code !== undefined && body.code !== 0 && !request.headers.get('SkipError')) {
            this.modalService.errorModal({ message: body.message });
          }
        } catch (e) {
        }
        console.error('ERROR', error);

        return throwError(error);
      })
    );
  }

  private requestCheck(request) {
    if (request.headers.get('SkipError')) {
      return request.clone({
        headers: request.headers.delete('SkipError')
      });
    }
    return request;

  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);
      const token = this.store.selectSnapshot(LoginState.token);
      const refreshToken = this.store.selectSnapshot(LoginState.refreshToken);
      return this.authenticationService.refreshToken(token, refreshToken).pipe(
        catchError((error) => {
          this.redirectGetOtt();
          return throwError(error);
        }),
        switchMap((res: any) => {
          this.isRefreshing = false;
          // Token Güncelle
          this.store.dispatch(new ChangeTokenAction(res));
          // Main e Yolla
          this.frameMessage.sendMessage(FrameMessageEnum.changeToken, res);
          this.refreshTokenSubject.next(res);
          return next.handle(request);
        })
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter((token) => token != null),
        take(1),
        switchMap(() => {
          return next.handle(request);
        })
      );
    }
  }

  redirectGetOtt(): void {
    this.frameMessage.sendMessage(FrameMessageEnum.unauthorised);
  }
}
