import {
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { LoginState } from '../module/authentication/state/login/login.state';
import { FrameMessageEnum } from '../module/shared/enum/frame-message.enum';
import { MessageFrameService } from '../module/shared/service/message-frame.service';
import { CommonState } from '../module/shared/state/common/common.state';
import { v4 as uuidv4 } from 'uuid';


@Injectable()
export class TokenInterceptor implements HttpInterceptor {

  constructor(
    private readonly translateService: TranslateService,
    private readonly store: Store,
    protected readonly messageFrameService: MessageFrameService,
  ) {
  }

  intercept(
    request: HttpRequest<any>,
    next: HttpHand<PERSON>,
  ): Observable<HttpSentEvent | HttpHeaderResponse | HttpProgressEvent | HttpResponse<any> | HttpUserEvent<any> | any> {
    return next.handle(this.addTokenToRequest(request));
  }

  private addTokenToRequest(request: HttpRequest<any>): HttpRequest<any> {
    return request.clone({
      headers: this.addExtraHeaders(request.headers),
    });
  }

  private addExtraHeaders(headers: HttpHeaders): HttpHeaders {
    const token = this.store.selectSnapshot(LoginState.token);
    const headerCompanies = this.store.selectSnapshot(LoginState.headerCompanies);
    const customer = this.store.selectSnapshot(LoginState.customer);
    const timezoneId = this.store.selectSnapshot(CommonState.timezoneId);
    const deviceToken = this.store.selectSnapshot(CommonState.deviceToken);

    headers = headers.append('ReqId', uuidv4());

    if (headerCompanies) {
      headers = headers
        .append('HeaderCompanies', headerCompanies);
    } else {
      const publicMenuHeaderCompany = this.store.selectSnapshot(CommonState.publicMenuHeaderCompany);
      if (publicMenuHeaderCompany) {
        headers = headers
          .append('PublicMenuHeaderCompany', publicMenuHeaderCompany);
      }
    }
    if (customer && customer.id) {
      headers = headers
        .append('CustomerId', customer.id);
    }

    const lang = this.store.selectSnapshot(LoginState.language) || this.translateService.currentLang ||
      this.translateService.defaultLang;

    if (lang) {
      headers = headers.append('Language', lang);
      headers = headers.append('Accept-Language', lang);
    }

    if (timezoneId) {
      headers = headers.append('TimezoneId', timezoneId);
    }
    if (deviceToken) {
      headers = headers.append('AppSessionId', deviceToken);
    }

    if (token) {
      if (!headers.get('TOKEN_FREE')) {
        headers = headers.append('Authorization', `Bearer ${token}`);
      } else {
        headers = headers.delete('TOKEN_FREE');
      }
    }
    return headers;
  }
}
