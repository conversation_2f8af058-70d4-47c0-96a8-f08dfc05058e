import {
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ModalService } from '../module/shared/service/modal.service';
import { HttpResponse as BaseResponse } from '../response/http.response';
import { LoggerService } from '../module/shared/service/logger.service';

@Injectable()
export class ClientErrorInterceptorService implements HttpInterceptor {
  constructor(
    private modalService: ModalService,
    private logApi: LoggerService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<
    | HttpSentEvent
    | HttpHeaderResponse
    | HttpProgressEvent
    | HttpResponse<any>
    | HttpUserEvent<any>
    | any
  > {
    const responseType = request.responseType;

    const newReq = request.clone({
      responseType:
        request.responseType === 'json' ? 'text' : request.responseType,
    });

    return next.handle(newReq).pipe(
      map((evt) => {
        if (evt instanceof HttpResponse && evt.body) {
          if (this.isWafError(evt)) {
            console.log('WAF ERROR', evt);
            
            const _headerKeys = request.headers.keys();
            const _headers = {};
            _headerKeys.forEach((k) => {
              _headers[k] = request.headers.get(k);
            });

            this.logApi
              .cateventlog({
                request: request.body,
                requestPath: request.url,
                requestHeaders: this.getHeader(request.headers),
                responseBody: evt.body,
                responseCode: evt.status,
                supportID: this.getSupportId(evt.body),
              })
              .subscribe();
            return this.wafErrorHandler(evt);
          }

          if (responseType === 'json') {
            const _body = JSON.parse(evt.body);

            if (_body.code !== undefined && _body.code !== 0) {
              this.modalService.errorModal({ message: _body.message });
            }

            return evt.clone({ body: _body });
          }
        }
        return evt;
      })
    );
  }

  getHeader(headers: HttpHeaders) {
    const _headerKeys = headers.keys();
    const _headers = {};
    _headerKeys.forEach((k) => {
      _headers[k] = headers.get(k);
    });
    return _headers;
  }

  getSupportId(_body: string) {
    const lastInx = _body.lastIndexOf('</b>');
    const _bodyy = _body.slice(0, lastInx);
    const lastInxx = _bodyy.lastIndexOf('>');
    return _bodyy.slice(lastInxx + 1);
  }

  isWafError(evt: HttpResponse<any>) {
    const htmlWords = ['</html>', '</body>', '</head>'];
    const isHtml = new RegExp(htmlWords.join('|')).test(evt.body);

    const words = [
      'Güvenlik',
      'destek kodu',
      'Request Rejected',
      'Your support ID',
      'rejected',
      '<EMAIL>',
      '<EMAIL>',
    ];
    const value = new RegExp(words.join('|')).test(evt.body);

    return typeof evt.body === 'string' && isHtml && value;
  }

  wafErrorHandler(evt: HttpResponse<any>) {
    const _body: BaseResponse<any> = {
      code: 1,
      data: null,
      message: '_waf_error_message',
    };
    this.modalService.errorModal({ message: _body.message, translate: true });
    return evt.clone({ body: _body });
  }
}
