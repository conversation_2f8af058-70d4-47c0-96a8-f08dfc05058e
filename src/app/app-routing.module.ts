import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { environment } from '../environments/environment';
import { NonBoomComponent } from './container/non-boom/non-boom.component';
import { OttResolver } from './module/ott/resolver/ott.resolver';
import { ExternalErrorComponent } from './container/external-error/external-error.component';

const routes: Routes = [
  {
    path: environment.rootUrl,
    resolve: [
      OttResolver,
    ],
    children: [
      {
        path: 'customer',
        loadChildren: () => import('./module/customer/customer.module').then(m => m.CustomerModule),
      },
      {
        path: 'form',
        loadChildren: () => import('./module/form/form.module').then(m => m.FormModule),
      },
      {
        path: 'expertise',
        loadChildren: () => import('./module/expertise/expertise.module').then(m => m.ExpertiseModule),
      },
      {
        path: 'survey',
        loadChildren: () => import('./module/survey/survey.module').then(m => m.SurveyModule),
      },
      {
        path: 'pulse-survey',
        loadChildren: () => import('./module/survey/survey.module').then(m => m.SurveyModule),
      },
      {
        path: 'live-support',
        loadChildren: () => import('./module/live-support/live-support.module').then(m => m.LiveSupportModule),
      },
      {
        path: 'boom-guru',
        loadChildren: () => import('./module/boom-guru/boom-guru.module').then(m => m.BoomGuruModule),
      },
      {
        path: 'videocall',
        redirectTo: 'live-support/videocall'
      },
      {
        path: 'digital-banko',
        redirectTo: 'live-support/digital-banko'
      },
      {
        path: 'external-error',
        component: ExternalErrorComponent,
      },
    ],
  },
  {
    path: `${environment.rootUrl}/ott`,
    loadChildren: () => import('./module/ott/ott.module').then(m => m.OttModule),
  },
  {
    path: 'non-boom',
    component: NonBoomComponent,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
  providers: [OttResolver],
})
export class AppRoutingModule {
}
