import { Injectable } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';

@Injectable({
  providedIn: 'root'
})
export class HasPermissionsService {

  @Select(SettingsState.userPermission)
  userPermission$: Observable<string[]>;

  constructor() { }

  hasPermission(permission: string): Observable<boolean> {
    return this.userPermission$
      .pipe(
        map(authorizations => {
          return !!authorizations?.some(p => p === permission);
        })
      );
  }

  checkPermission(permission: string): boolean {
    let hasPermission = false;
    this.hasPermission(permission).subscribe(result => hasPermission = result);
    return hasPermission;
  }
}
