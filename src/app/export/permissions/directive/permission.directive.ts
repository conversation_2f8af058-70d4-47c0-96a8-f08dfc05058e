import { Directive, Input, ElementRef, Renderer2, OnChang<PERSON>, SimpleChanges } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PermissionEnum } from 'src/app/module/definition/enum/permission.enum';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';

@Directive({
  selector: '[hasPermission]'
})
export class HasPermissionDirective implements OnChanges {

  @Select(SettingsState.userPermission)
  userPermission$: Observable<string[]>;

  @Input('hasPermission') permission: PermissionEnum;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.permission) {
      this.userPermission$
        .pipe(
          map(authorizations => this.checkPermission(authorizations, this.permission))
        )
        .subscribe(hasPermission => this.toggleVisibility(hasPermission));
    }
  }

  private checkPermission(authorizations: string[], permission: PermissionEnum): boolean {
    return authorizations?.some(p => p === permission);
  }

  private toggleVisibility(hasPermission: boolean): void {
    if (hasPermission) {
      this.renderer.removeClass(this.el.nativeElement, 'hidden');
    } else {
      this.renderer.addClass(this.el.nativeElement, 'hidden');
    }
  }
}
