import { Component, ElementRef, EventEmitter, OnInit, Output, Renderer2, ViewChild } from '@angular/core';

@Component({
  selector: 'cat-take-a-photo',
  templateUrl: './take-a-photo.component.html',
  styleUrls: ['./take-a-photo.component.scss'],
})
export class TakeAPhotoComponent implements OnInit {
  @ViewChild('video', { static: true }) videoElement: ElementRef;
  @ViewChild('canvas', { static: true }) canvas: ElementRef;

  @Output()
  takeAPhoto: EventEmitter<string> = new EventEmitter<string>();

  videoWidth = 0;
  videoHeight = 0;
  constraints = {
    video: {
      facingMode: 'environment',
      width: { ideal: 4096 },
      height: { ideal: 2160 },
    },
  };

  constructor(private renderer: Renderer2) {
  }

  ngOnInit() {
    this.startCamera();
  }

  startCamera() {
    if (!!(
      navigator.mediaDevices && navigator.mediaDevices.getUserMedia
    )) {
      navigator.mediaDevices.getUserMedia(this.constraints).then(this.attachVideo.bind(this)).catch(this.handleError);
    } else {
      alert('Sorry, camera not available.');
    }
  }

  attachVideo(stream) {
    this.renderer.setProperty(this.videoElement.nativeElement, 'srcObject', stream);
    this.renderer.listen(this.videoElement.nativeElement, 'play', () => {
      this.videoHeight = this.videoElement.nativeElement.videoHeight;
      this.videoWidth = this.videoElement.nativeElement.videoWidth;
    });
  }

  onTakeAPhoto() {
    this.renderer.setProperty(this.canvas.nativeElement, 'width', this.videoWidth);
    this.renderer.setProperty(this.canvas.nativeElement, 'height', this.videoHeight);
    this.canvas.nativeElement.getContext('2d').drawImage(this.videoElement.nativeElement, 0, 0);
    this.takeAPhoto.emit(this.canvas.nativeElement.toDataURL('image/jpeg').split(';base64,')[1]);
  }

  handleError(error) {
    console.log('Error: ', error);
  }
}
