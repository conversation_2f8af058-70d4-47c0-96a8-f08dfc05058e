import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { SharedModule } from 'src/app/module/shared/shared.module';
import { FileUploadPreviewComponent } from './component/file-upload-preview/file-upload-preview.component';
import { FileUploadComponent } from './component/file-upload/file-upload.component';

@NgModule({
  declarations: [
    FileUploadComponent,
    FileUploadPreviewComponent,
  ],
  exports: [
    FileUploadComponent,
    FileUploadPreviewComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    NgxLoadingModule,
    SharedModule
  ],
})
export class FileUploadModule {
}
