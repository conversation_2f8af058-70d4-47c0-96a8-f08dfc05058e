import { ChangeDetectorRef, Component, ElementRef, EventEmitter, forwardRef, Input, OnDestroy, OnInit, ViewChild, } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import snq from 'snq';
import { ModalService } from '../../../../module/shared/service/modal.service';
import { FileModel, FileSettingsModel, FormType } from '../../model/file.model';
import { FileUploadService } from '../../service/file-upload.service';
import { FrameMessageEnum } from '../../../../module/shared/enum/frame-message.enum';
import { PermissionEnum } from '../../../../module/definition/enum/permission.enum';
import { MessageFrameService } from '../../../../module/shared/service/message-frame.service';
import { IncomingMessageService } from '../../../../module/shared/service/incoming-message.service';
import { IncomingMessageEnum } from '../../../../module/shared/enum/incoming-message.enum';
import { CommonState } from '../../../../module/shared/state/common/common.state';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { takeUntil } from 'rxjs/operators';
import { AppState } from '../../../../state/app/app.state';
import { ShowPermissionErrorAction } from '../../../../module/shared/state/common/common.actions';
import uuidv4 from 'src/app/util/uuidv4';
import { SystemFeature } from 'src/app/module/shared/model/settings.model';
import { SettingsState } from 'src/app/module/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/module/shared/util/system-feature.util';

@Component({
  selector: 'cat-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
  providers: [
    TranslatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileUploadComponent),
      multi: true,
    },
  ],
})
export class FileUploadComponent implements OnInit, OnDestroy {
  @Input()
  files: FileModel[];

  @Input()
  id: string;

  @Input()
  formType: FormType;

  @Input()
  name: string;

  @Input()
  disabledItem: boolean;

  @ViewChild('fileInput')
  fileInput: ElementRef;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  @Select(AppState.history)
  historyBack$: Observable<any>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  loading: boolean;
  loadingCount = 0;
  settings: FileSettingsModel;
  accept: string = '';
  val: FileModel[] = [];
  private permissionOkSub: any;
  errorModal: any;
  private subscriptions$: Subject<boolean> = new Subject();
  loadingCounter: EventEmitter<any[]> = new EventEmitter<any[]>();
  resizeImgDownloadSystemFeature: boolean;

  constructor(
    protected readonly fileUploadService: FileUploadService,
    protected readonly translatePipe: TranslatePipe,
    protected readonly modalService: ModalService,
    protected readonly messageFrameService: MessageFrameService,
    protected readonly incomingMessageService: IncomingMessageService,
    protected readonly translateService: TranslateService,
    protected readonly cdr: ChangeDetectorRef,
    protected readonly store: Store,
  ) {
    this.getSettings();
  }

  protected getSettings() {
    this.fileUploadService.settings().subscribe((res) => {
      if (res.code === 0) {
        this.settings = res.data;
        // this.accept = this.settings.availableMimeTypes.join(', ');
      }
    });
  }

  get value() {
    return this.val;
  }

  set value(val) {
    this.val = val;
    this.onChange(val);
    this.onTouch(val);
  }

  ngOnInit() {
    this.store.dispatch(new ShowPermissionErrorAction(null));
    this.listenPermissionError();

    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.resizeImgDownloadSystemFeature = systemFeature('resize_download_control', features, false);
      }
    });

    // this.historyBack$
    //   .pipe(tap(i => console.log('history111', i)))
    //   .pipe(takeUntil(this.subscriptions$), skip(1), filter(i => i))
    //   .subscribe(h => {
    //     console.log('hsss', h);
    //     this.errorModal?.close('t');
    //   });

  }


  onChange(val): any {}

  onTouch(val): any {}

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any) {
    this.onChange = fn;
  }

  registerOnTouched(fn: any) {
    this.onTouch = fn;
  }


  loadingCounters() {
    let itemList = [];
    for (let i = 0; i < this.loadingCount; i++) {
      itemList.push({});
    }
    this.loadingCounter.emit(itemList);
  }

  onUpload(data: any, fileName: string, size: number) {
    if (data) {
      const file = {
        AssignedId: this.id || uuidv4(),
        Size: size,
        Name: fileName.substr(0, fileName.lastIndexOf('.')),
        FileType: fileName.split('.').pop() || '',
        FormType: this.formType,
        ImageContent: snq(() => data.match(/,([\w\W]+)/g)[0], data).replace(
          ',',
          ''
        ),
      } as FileModel;

      if (file.Size > 1048576 && this.isImageFile(file.FileType) && this.resizeImgDownloadSystemFeature) {
        this.resizeImage(data, file.FileType)
          .then((resizedFile) => {
            this.uploadResizedFile(file, resizedFile);
          }).catch(() => {
            console.log('RESIZED IMAGE NOT LOADED')
          })
      } else if(this.isImageFile(file.FileType) && this.resizeImgDownloadSystemFeature) {
        this.bigImagePixel(data).then((bigPixel) => {
          if (bigPixel) {
            this.resizeImage(data, file.FileType)
              .then((resizedFile) => {
                this.uploadResizedFile(file, resizedFile);
              }).catch(() => {
                console.log('RESIZED IMAGE NOT LOADED')
              })
          } else {
            this.uploadFile(file);
          }
        });
      } else {
        this.uploadFile(file);
      }
    }
  }

  isImageFile(fileType: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png'];
    const extension = fileType.toLowerCase();
    return imageExtensions.includes(extension);
  }

  bigImagePixel(data: any): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      const img = new Image();
      img.onload = () => {
        if (img.width > 1000 || img.height > 1000) {
          resolve(true);
        } else {
          resolve(false);
        }
      };
      img.src = data;
    });
  }

  async resizeImage(data: any, fileType: string): Promise<File> {
    return new Promise((resolve, reject) => {
      const img: any = new Image();
      img.onload = () => {
        let width = img.width;
        let height = img.height;

        if ((width > 1000 || height > 1000) && (width > height)) {
          height = height*(1000/width);
          width = 1000;
        } else if ((width > 1000 || height > 1000) && (height > width)) {
          width = width*(1000/height);
          height = 1000;
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = width;
        canvas.height = height;

        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob((blob) => {
          if (blob) {
            const fileExtension = fileType.split('.').pop() || '';
            const fileName = `resized_image.${fileExtension}`;
            const resizedFile = new File([blob], fileName, {
              type: fileType,
              lastModified: Date.now(),
            });
            resolve(resizedFile);
          }
        }, fileType);
      };
      img.onerror = (error) => {
        reject(error);
      };
      img.src = data;
    });
  }

  uploadResizedFile(file: FileModel, resizedFile: File) {
    const reader = new FileReader();
    reader.onload = () => {
      const dataUrl = reader.result as string;
      const newFile = {
        AssignedId: file.AssignedId,
        Name: file.Name,
        FileType: file.FileType,
        FormType: file.FormType,
        Size: resizedFile.size,
        ImageContent: snq(() => dataUrl.match(/,([\w\W]+)/g)[0], dataUrl).replace(
          ',',
          ''
        )
      } as FileModel;
      console.log("RESIZEDFILE::::",newFile, "ORGINALFILE::::::", file);
      this.uploadFile(newFile);
    };
    reader.readAsDataURL(resizedFile);
  }

  uploadFile(file: FileModel) {
    this.loadingCount++;
    this.loadingCounters();
    this.uploadService(file).subscribe(
      (res) => {

        this.loading = false;
        this.loadingCount--;
        this.loadingCounters();

        if (!res.data.isSuccessful) {
          this.modalService.errorModal({
            message: res.data.errorMessage,
            translate: false,
          });
          return;
        }

        this.fileInput.nativeElement.value = '';

        if (res.code === 0) {
          file.id = res.data.attachmentId;
        }

        this.value = [...this.value, file];
      },
      () => {
        this.loading = false;
        this.loadingCount--;
        this.loadingCounters();
      }
    );
  }

  uploadService(file) {
    return this.fileUploadService.upload(file);
  }


  fileChangeListener($event: Event) {
    const element = $event.target as HTMLInputElement;
    const fileList: FileList | null = element.files;

    if (this.isFileValid(fileList)) {
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        const path: string = file.name;

        const that = this;
        const image: any = new Image();
        const myReader: FileReader = new FileReader();

        myReader.onloadend = (loadEvent: ProgressEvent<FileReader>) => {
          image.src = loadEvent.target.result;
          that.onUpload(image.src, path, file.size);
        };
        this.loading = true;
         this.loadingCounters();
        myReader.readAsDataURL(file);
      }
    } else {
      element.value = '';
    }
  }

  isFileValid(fileList: FileList) {
    let _valid: boolean = true;

    console.log('File Size', fileList.length, this.val);
    const totalCount = fileList.length + this.val.length;

    if (totalCount > this.settings?.maxAttachmentCount) {
      this.modalService.errorModal({
        message: this.translatePipe
          .transform('_max_attachment_count_error')
          .replace('{0}', this.settings?.maxAttachmentCount),
      });
      return false;
    }

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const path: string = file.name;
      const names = path.split('.');
      const extension = `${names[names.length - 1]}`;

      if (!this.settings.availableFileTypes.some(t => t.toLowerCase() === extension.toLowerCase())) {
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_type_not_valid')
            .replace('{0}', this.settings.availableFileTypes.join(', ')),
        });
        _valid = false;
        break;
      }

      const size = file.size;
      if (this.settings && size > this.settings.maxSizeInBytes) {
        const maxSize = (this.settings.maxSizeInBytes / 1024).toFixed(0);
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_is_too_big')
            .replace('{0}', maxSize),
        });
        _valid = false;
        break;
      }

      if (this.settings && size < this.settings.minSizeInBytes) {
        const minSize = (this.settings.minSizeInBytes / 1024).toFixed(0);
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_is_too_small')
            .replace('{0}', minSize),
        });
        _valid = false;
        break;
      }
    }
    return _valid;
  }

  permissionRequest() {
    this.messageFrameService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);
  }

  checkPermission() {
    this.messageFrameService.sendMessage(FrameMessageEnum.checkPermissions, [
      PermissionEnum.FileUpload,
    ]);
  }

  listenPermissionOK() {
    if (this.permissionOkSub) {
      return;
    }
    this.permissionOkSub = this.incomingMessageService
      .subscribe(IncomingMessageEnum.permissionOk, data => {
        // console.log('received data', data);
        this.fileInput.nativeElement.click();
        this.permissionOkSub?.unsubscribe();
        this.permissionOkSub = null;
      });
  }

  goToSettings(action) {
    this.messageFrameService.sendMessage(action);
  }

  listenPermissionError() {
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        // this.listenPermissionErrorSub = this.incomingMessageService
        //   .subscribe(IncomingMessageEnum.showPermissionError,
        data => {
          if (!data) {
            return;
          }
          // console.log('received data', data);
          this.permissionRequest();

          // this.errorModal = {
          //   message: data.message,
          //   button: this.translateService.instant('_go_to_settings'),
          //   buttonClick: () => this.goToSettings(data.action)
          // };

          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.goToSettings(data.action)
          });
        });
  }

  chooseFile() {
    console.log('choose', (this as any).question);
    // this.permissionRequest();
    this.listenPermissionOK();
    this.checkPermission();

    if (window.location.hostname === 'localhost') {
      (window as any).handlePostMessage('{"type":"permissionOk","data":null}');
      // (window as any).handlePostMessage('{"type":"showPermissionError","data":{"message" : "no permission"}}');
    }
  }

  ngOnDestroy() {
    this.permissionOkSub?.unsubscribe();
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.errorModal?.close();
  }

}
