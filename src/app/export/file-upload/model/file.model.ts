export interface FileModel {
  AssignedId: string;
  Name: string;
  FileType: string;
  FormType: FormType;
  Size: number;
  ImageContent: string;
  id: string;
}

export enum FormType {
  ServiceRequest = 1,
  MdaRequest = 2,
  PartRequest = 3,
  CallRequest = 4,
  WriteUs = 5,
  soundRecords = 6,
}

export interface FileModelResponse {
  attachmentId: string;
  name?: any;
  isSuccessful: boolean;
  errorMessage: string;
}

export interface FileSettingsModel {
  maxSizeInBytes: number;
  minSizeInBytes: number;
  maxAttachmentCount:number;
  availableFileTypes: string[];
  availableMimeTypes: string[];
}
