import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import {
  FileModel,
  FileModelResponse,
  FileSettingsModel,
} from '../model/file.model';

@Injectable({
  providedIn: 'root',
})
export class FileUploadService {
  constructor(private readonly http: HttpClient) {}

  upload(file: FileModel): Observable<HttpResponse<FileModelResponse>> {
    return this.http.post<HttpResponse<FileModelResponse>>(
      `${environment.api}/form/attachment/add`,
      file
    );
  }

  delete(id: string): Observable<HttpResponse<boolean>> {
    return this.http.post<HttpResponse<boolean>>(
      `${environment.api}/form/attachment/delete`,
      { id }
    );
  }

  settings(): Observable<HttpResponse<FileSettingsModel>> {
    return this.http.get<HttpResponse<FileSettingsModel>>(
      `${environment.api}/settings/file`
    );
  }
}
