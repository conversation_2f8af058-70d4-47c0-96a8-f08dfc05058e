<div>
  <ng-container
    *ngIf="
      id
        | snapshotSecure: workOrderNumber:serviceOrganization:refresh:slowlyFrequency
        | async as path;
      else loading
    "
  >
    <img
      [alt]="title"
      *ngIf="path.code !== 1"
      [attr.src]="path"
      [title]="title"
      class="img-fluid"
    />
    <div *ngIf="path.code === 1" class="error-message">
      {{ errorHandler(path) }}
    </div>
  </ng-container>

  <ng-template #loading>
    <div [class.small-spin]="!refresh">
      <ngx-loading
        [show]="true"
        [config]="{ fullScreenBackdrop: false }"
      ></ngx-loading></div
  ></ng-template>
</div>
