import { Component, EventEmitter, Input, Output } from '@angular/core';
import { HttpResponse } from 'src/app/response/http.response';

@Component({
  selector: 'cat-snapshot',
  templateUrl: './snapshot.component.html',
  styleUrls: ['./snapshot.component.scss'],
})
export class SnapshotComponent {
  @Input() id: string;

  @Input() workOrderNumber: string;

  @Input() serviceOrganization: string;

  @Input() title: string;

  @Input() refresh: boolean;

  @Input() slowlyFrequency = false;

  // tslint:disable-next-line:no-output-on-prefix
  @Output() onError: EventEmitter<HttpResponse<null>> = new EventEmitter();

  errorHandler(err) {
    this.onError.emit(err);
  }
}
