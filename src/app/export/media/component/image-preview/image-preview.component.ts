import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { ImageSizeEnum } from '../../enum/image-size.enum';

@Component({
  selector: 'cat-image-preview',
  templateUrl: './image-preview.component.html',
  styleUrls: ['./image-preview.component.scss'],
})
export class ImagePreviewComponent {
  @Input()
  id: string;

  @Input()
  title: string;

  @Input()
  imageSize = ImageSizeEnum.mobilethumbnailsize;

  @Input()
  model: string;

  @Output()
  imageHeight: EventEmitter<number> = new EventEmitter<number>();

  @ViewChild('image') imageElement: ElementRef;

  loadedImg() {
    this.imageHeight.emit(this.imageElement?.nativeElement?.offsetHeight);
  }

  @Input()
  maxHeight: string;

  @Input()
  minWidth: string;

}
