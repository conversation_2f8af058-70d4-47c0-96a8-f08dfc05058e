import { Pipe, PipeTransform } from '@angular/core';
import { SafeUrl } from '@angular/platform-browser';
import { defer, interval, Observable, of, Subject } from 'rxjs';
import { catchError, repeat, switchMap, take, tap } from 'rxjs/operators';

import { MediaService } from '../../service/media/media.service';
import { HttpErrorResponse } from '@angular/common/http';
import { ModalService } from '../../../../module/shared/service/modal.service';
import jsonSafeParse from '../../../../util/json-safe-parse';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'snapshotSecure',
})
export class SnapshotSecurePipe implements PipeTransform {
  private id: string;
  private workOrderNumber: string;
  private image: string;
  private slowlyFrequency: boolean;
  timeout = 3000;
  time = 0;

  constructor(
    private mediaService: MediaService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService
  ) {}

  transform(
    id: string,
    workOrderNumber: string,
    serviceOrganization: string,
    refresh: boolean,
    slowlyFrequency?: boolean,
  ): Observable<SafeUrl> {
    if (
      this.id === id &&
      this.workOrderNumber === workOrderNumber &&
      this.image &&
      !refresh
    ) {
      return of(this.image);
    }
    this.id = id;
    this.workOrderNumber = workOrderNumber;
    this.slowlyFrequency = slowlyFrequency;
    return refresh
      ? defer(() => interval(this.timeout)).pipe(
        take(1),
        repeat(),
        switchMap(() =>
          this.getSnapshot(id, workOrderNumber, serviceOrganization, refresh)
        )
      )
      : this.getSnapshot(id, workOrderNumber, serviceOrganization, refresh);
  }

  getSnapshot(
    id: string,
    workOrderNumber: string,
    serviceOrganization: string,
    refresh: boolean
  ) {
    return this.mediaService
      .snapshot(id, workOrderNumber, serviceOrganization)
      .pipe(
        switchMap((image) => {
          const ob = new Subject();
          if (image) {
            const reader = new FileReader();
            reader.addEventListener(
              'load',
              () => {
                ob.next(reader.result);
                this.time = this.time < 40 ? this.time + 1 : this.time;
                this.timeout =
                  this.slowlyFrequency && this.time === 40 && this.timeout < 15000
                  ? this.timeout + 500
                  : this.timeout >= 15000
                    ? 15000
                    : 3000;
              },
              false
            );
            reader.readAsDataURL(image);
          }
          return ob;
        }),
        tap((image: any) => (this.image = image)),
        catchError(async (err: HttpErrorResponse) => {
          const error = jsonSafeParse(await err?.error?.text());

          if (err.status === 400 || err.status === 500) {
            // stop refresh, show error and go back
            this.timeout = 9999999;
            this.modalService.errorModal({
              message: err.status === 500 ?
                this.translateService.instant('_general_error_message') : error?.message
            });
            history.back();
            return error;
          }

          this.timeout = 30000;
          return error;
        })
      );
  }
}
