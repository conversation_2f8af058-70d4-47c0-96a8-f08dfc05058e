import { HttpParams } from '@angular/common/http';
import { Pipe, PipeTransform } from '@angular/core';
import { SafeUrl } from '@angular/platform-browser';
import { Observable, of } from 'rxjs';

import { environment } from '../../../../../environments/environment';
import { ImageSizeEnum } from '../../enum/image-size.enum';
import { MediaService } from '../../service/media/media.service';

@Pipe({
  name: 'mediaSecure'
})
export class MediaSecurePipe implements PipeTransform {
  private id: string;
  private image: string;

  constructor(private mediaService: MediaService) {}

  transform(id: string, model: string, imageSize = ImageSizeEnum.mobilethumbnailsize): Observable<SafeUrl> {
    if (this.id === id && this.image) {
      return of(this.image);
    }
    this.id = id;

    return of(`${environment.imageApi}/image/equipment?` + new HttpParams({ fromObject: { productHierarchy: id, model, imageSize } }).toString());

    /*
    return this.mediaService.image(id,model, imageSize)
      .pipe(
        switchMap(image => {
          const ob = new Subject();
          if (image) {
            const reader = new FileReader();
            reader.addEventListener('load', () => {
              ob.next(reader.result);
            }, false);
            reader.readAsDataURL(image);
          }
          return ob;
        }),
        tap((image: any) => this.image = image),
      );*/
  }
}
