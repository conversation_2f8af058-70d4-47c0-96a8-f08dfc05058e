import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ImagePreviewComponent } from './component/image-preview/image-preview.component';
import { MediaSecurePipe } from './pipe/media-secure/media-secure.pipe';
import { DownloadFileDirective } from './directive/download-file/download-file.directive';
import { SnapshotComponent } from './component/snapshot/snapshot.component';
import { SnapshotSecurePipe } from './pipe/snapshot-secure/snapshot-secure.pipe';
import { NgxLoadingModule } from 'ngx-loading';

@NgModule({
  declarations: [
    ImagePreviewComponent,
    MediaSecurePipe,
    DownloadFileDirective,
    SnapshotComponent,
    SnapshotSecurePipe,
  ],
  exports: [
    ImagePreviewComponent,
    SnapshotComponent,
    DownloadFileDirective,
  ],
  imports: [
    CommonModule,
    NgxLoadingModule
  ],
})
export class MediaModule {
}
