import { Directive, HostListener, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { MediaService } from '../../service/media/media.service';

@Directive({
  selector: '[catDownloadPdf]',
})
export class DownloadPdfDirective {
  subscription: Subscription = new Subscription();
  @Input()
  id: string;

  @HostListener('click') onClick() {
    this.downloadFile(this.id);
  }

  constructor(
    private readonly mediaService: MediaService,
  ) {
  }

  downloadFile(id: string = null): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription = this.mediaService.offerPdf(id)
      .pipe(
        map((response: any) => {

          console.log(response);
          const dataType = response.type;
          const binaryData = [];
          binaryData.push(response);
          return new Blob(binaryData, { type: dataType });
        }),
      )
      .subscribe(
        (blob: Blob) => {
          const downloadLink = document.createElement('a');
          downloadLink.href = window.URL.createObjectURL(blob);
          console.log(id)
;
          if (id) {
            downloadLink.setAttribute('download', id);
          }
          document.body.appendChild(downloadLink);
          downloadLink.click();
        },
      );
  }
}
