import { Directive, ElementRef, EventEmitter, HostListener, Input, OnDestroy, Output } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { MediaService } from '../../service/media/media.service';
import { FrameMessageEnum } from '../../../../module/shared/enum/frame-message.enum';
import { MessageFrameService } from '../../../../module/shared/service/message-frame.service';
import { DownloadTypeEnum } from '../../enum/download-type.enum';
import { WorkOrderAttachmentsModel } from '../../../../module/customer/model/work-order.model';
import { Select, Store } from '@ngxs/store';
import { CommonState } from '../../../../module/shared/state/common/common.state';
import { ModalService } from '../../../../module/shared/service/modal.service';
import { TranslateService } from '@ngx-translate/core';
import {
  FinishDownloadAction,
  ShowPermissionErrorAction,
  StartDownloadAction,
} from '../../../../module/shared/state/common/common.actions';
import { Data } from '@angular/router';
import { appVersionController } from 'src/app/util/app-version-controller.util';

@Directive({
  selector: '[catDownloadFile]',
})
export class DownloadFileDirective implements OnDestroy {
  @Input()
  downloadType: any;

  @Input()
  downloadParams: any;

  @Input()
  haveDownload: boolean;

  @Output()
  downloadLoading: EventEmitter<[]> = new EventEmitter();

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  @Select(CommonState.startDownload)
  startDownload$: Observable<any>;

  @Select(CommonState.finishDownload)
  finishDownload$: Observable<any>;

  private subscriptions$: Subject<boolean> = new Subject();
  private errorModal: any;
  private subscribePermissionError = true;
  private version: boolean;

  constructor(
    private readonly mediaService: MediaService,
    private readonly el: ElementRef,
    private readonly messageFrameService: MessageFrameService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly store: Store,
  ) {
  }

  // Base64 API List
  base64UrlList = ['equipment/SosAnalyzePdf'];

  @HostListener('click')
  onClick() {
    if (this.haveDownload) {
      return;
    }
    this.store.dispatch(new ShowPermissionErrorAction(null));
    if (this.subscribePermissionError) {
      this.subscribePermissionError = false;
    }
    this.version = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    console.log('version', this.version);

    this.downloadFile();
  }

  downloadFile(): void {
    if (this.version) {
      const startDownload = this.startDownloadUrl();

      // tslint:disable-next-line: no-string-literal
      const isBase64 = startDownload?.url?.includes('equipment/SosAnalyzePdf') || startDownload?.url?.includes('/equipment/equipmentInspectionPdf');
      console.log('startDownload', startDownload);
      console.log('isBase64', isBase64);

      this.messageFrameService.sendMessage(FrameMessageEnum.downloadFile, {
        id: startDownload.id,
        url: startDownload.url,
        extension: startDownload?.extension.charAt(0) === '.'
          ? startDownload?.extension
          : '.' + startDownload?.extension,
        isBase64,
      });
      this.listenDownload();
    } else {
      switch (this.downloadType) {
        case DownloadTypeEnum.offer:
          return this.downloadOffer(
            this.downloadParams.quotationNumber,
            this.downloadParams.guid
          );
        case DownloadTypeEnum.public:
          return this.downloadPublicAttachment(this.downloadParams.attachment, this.downloadParams.serviceOrganization, this.downloadParams.workOrderNumber);
        case DownloadTypeEnum.sosAnalyzePdf:
          return this.downloadSosAnalyzePdf(this.downloadParams);
        case DownloadTypeEnum.inspectionHistory:
          return this.downloadInspectionHistoryPdf(this.downloadParams);
        case DownloadTypeEnum.catInspection:
          return this.downloadCatInspectionPdf(this.downloadParams);
        case DownloadTypeEnum.attachment:
        default:
          return this.downloadAttachment(this.downloadParams.attachment, this.downloadParams.serviceOrganization, this.downloadParams.workOrderNumber);
      }
    }
  }

  startDownloadUrl() {
    const startDownload = {
      url: '', id: '', extension: '.pdf'
    };
    switch (this.downloadType) {
      case DownloadTypeEnum.offer:
        startDownload.url = `/quotation/getPdf?quotationNumber=` + this.downloadParams.quotationNumber + '&guid=' + this.downloadParams.guid;
        startDownload.id = this.downloadParams.quotationNumber;
        return startDownload;
      case DownloadTypeEnum.sosAnalyzePdf:
        startDownload.url = `/equipment/SosAnalyzePdf?serialNumber=` + this.downloadParams.serialNumber + '&sampleNumber=' + this.downloadParams.analyzeDocumentNumber;
        startDownload.id = this.downloadParams.analyzeDocumentNumber;
        return startDownload;
      case DownloadTypeEnum.inspectionHistory:
        startDownload.url = `/inspectionForm/getPdf?formId=` + this.downloadParams.formId
          + '&portalUserInspectionFormId=' + this.downloadParams.portalUserInspectionFormId
          + '&language=' + this.downloadParams.language
          + (this.downloadParams?.isMasked ? '&isMasked=' + this.downloadParams?.isMasked : '');
        startDownload.id = this.downloadParams.portalUserInspectionFormId;
        return startDownload;
      case DownloadTypeEnum.public:
          startDownload.url = `/workorder/crc/attachment?attachmentId=` + this.downloadParams.attachment.id + '&serviceOrganization=' + this.downloadParams.serviceOrganization;
          startDownload.id = this.downloadParams.attachment.id;
          startDownload.extension = this.downloadParams.attachment?.extension;
          return startDownload;
      case DownloadTypeEnum.catInspection:
        startDownload.url = `/equipment/equipmentInspectionPdf?dcode=` + this.downloadParams.dCode + '&inspectionNumber='+ this.downloadParams.inspectionNumber + '&reportName=pdforderedbyform';
        startDownload.id = this.downloadParams.inspectionNumber;
        return startDownload;
      case DownloadTypeEnum.attachment:
      default:
        if (this.downloadParams.attachment.uploadTypeDescription === 'SERVICE_FORM') {
          startDownload.url = `/workorder/getAttachment?workOrderNumber=` + this.downloadParams.workOrderNumber + '&attachmentId=' + this.downloadParams.attachment.id;
          startDownload.id = this.downloadParams.attachment.id;
          startDownload.extension = this.downloadParams.attachment?.extension;
          return startDownload;
        } else {
          startDownload.url = `/equipment/getattachment?attachmentId=` + this.downloadParams.attachment.id + '&serviceOrganization=' + this.downloadParams.serviceOrganization;
          startDownload.id = this.downloadParams.attachment.id;
          startDownload.extension = this.downloadParams.attachment?.extension;
          return startDownload;
        }
    }
  }
  downloadPublicAttachment(attachment: WorkOrderAttachmentsModel, serviceOrganization, workOrderNumber: string) {
    console.log('att', attachment);
    this.loading({ data: { id: attachment.id }, downloading: true });
    const file = this.mediaService.getAttachmentPublic(attachment.id, serviceOrganization);
    return this.handleBlob(file, attachment.name, attachment.id);
  }

  downloadAttachment(attachment: WorkOrderAttachmentsModel, serviceOrganization, workOrderNumber: string) {
    console.log('att', attachment);
    this.loading({ data: { id: attachment.id }, downloading: true });
    if (attachment.uploadTypeDescription === 'SERVICE_FORM') {
      const file = this.mediaService.getWorkOrderAttachment(workOrderNumber, attachment.id);
      return this.handleBlob(file, attachment.name, attachment.id);
    } else {
      const file = this.mediaService.getAttachment(attachment.id, serviceOrganization);
      return this.handleBlob(file, attachment.name, attachment.id);
    }
  }


  downloadSosAnalyzePdf(params: any) {
    this.loading({ data: { id: params.analyzeDocumentNumber }, downloading: true });
    const analyzeFile = this.mediaService.getSosAnalyzePdf(params.serialNumber, params.analyzeDocumentNumber);
    return this.handleBase64Pdf(analyzeFile, params.analyzeDocumentNumber);
  }

  downloadInspectionHistoryPdf(params: any) {
    this.loading({
      data: { id: params.portalUserInspectionFormId },
      downloading: true,
    });
    const file = this.mediaService.inspectionHistoryPdf(
      params.formId,
      params.portalUserInspectionFormId,
      params.language,
      params?.isMasked
    );
    return this.handleBlob(file, params.portalUserInspectionFormId + '.pdf', params.portalUserInspectionFormId);
  }

  downloadOffer(quotationNumber, orderGuid) {
    this.loading({ data: { id: quotationNumber }, downloading: true });
    const file = this.mediaService.offerPdf(quotationNumber, orderGuid);
    return this.handleBlob(file, 'offer_' + quotationNumber, quotationNumber.id);
  }

  downloadCatInspectionPdf(params: any) {
    this.loading({
      data: { id: params.inspectionNumber },
      downloading: true,
    });
    const file = this.mediaService.catInspectionPdf(
      params.dCode,
      params.inspectionNumber
    );
    return this.handleBase64Pdf(file, params.inspectionNumber);
  }

  handleBlob(blob: Observable<any>, fileName, fileid) {
    this.loading({ data: { id: fileid }, downloading: true });
    blob
      .pipe(
        map((response: any) => {
          console.log('rest', response);
          const dataType = response?.type;
          const binaryData = [];
          binaryData.push(response);
          return new Blob(binaryData, { type: dataType });
        })
      )
      .subscribe(
        // tslint:disable-next-line: no-shadowed-variable
        (blob: Blob) => {
          const reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onload = () => {
            this.messageFrameService.sendMessage(FrameMessageEnum.openTeklif, {
              filename: fileName,
              contentType: blob.type,
              file: reader.result,
            });
            this.loading({ data: { id: fileid }, downloading: false });
          };
        },
      );
  }

  handleBase64Pdf(blob: Observable<any>, fileName) {
    this.loading({ data: { id: fileName }, downloading: true });
    blob.subscribe((res: Data) => {
        if (res.data) {
          this.messageFrameService.sendMessage(FrameMessageEnum.openTeklif, {
            filename: fileName + '.pdf',
            contentType: 'application/pdf',
            file: 'data:application/pdf;base64,' + res.data.base64Pdf,
          });
          this.loading({ data: { id: fileName }, downloading: false });
          // tslint:disable-next-line: no-unused-expression
          return (res.data.base64pdf, { contentType: 'application/pdf' });
        } else {
          this.modalService.errorModal({
            message: '_general_error_message',
            translate: true
          });
          return null;
        }
      },
      (err) => {
        console.log('Download PDF Error: ', err);
      });
  }

  private loading(value) {
    console.log('loading', value);

    this.downloadLoading.emit(value);
    if (value.downloading === false) {
      this.destroyAction();
    }
  }

  listenDownload() {
    this.startDownload$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.loading({ data, downloading: true });
        });
    this.finishDownload$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.loading({ data, downloading: false });
        });
  }

  ngOnDestroy() {
    this.destroyAction();
  }

  destroyAction() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.subscriptions$ = new Subject<boolean>(); // reused
    this.store.dispatch(new StartDownloadAction(null));
    this.store.dispatch(new FinishDownloadAction(null));
  }
}
