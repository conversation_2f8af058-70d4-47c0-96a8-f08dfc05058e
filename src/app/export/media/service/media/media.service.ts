import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CatInspectionPdfModel, SosAnalyzePdfModel } from 'src/app/module/customer/model/equipment.model';

import { environment } from '../../../../../environments/environment';
import { ImageSizeEnum } from '../../enum/image-size.enum';

@Injectable({
  providedIn: 'root'
})
export class MediaService {
  constructor(private readonly http: HttpClient) {}

  file(id: string = ''): Observable<any> {
    return this.http.get(`${environment.api}/file`, { responseType: 'blob' as 'json', params: { id } });
  }

  offerPdf(quotationNumber: string, guid?: string): Observable<any> {
    return this.http.post(`${environment.api}/quotation/getPdf`,
      { quotationNumber, guid },
      { responseType: 'blob' as 'json' }
    );
  }

  getAttachment(attachmentId: string, serviceOrganization): Observable<any> {
    return this.http.post(`${environment.api}/equipment/getattachment`, { attachmentId, serviceOrganization }, {
      responseType: 'blob'
    });
  }

  getAttachmentPublic(attachmentId: string, serviceOrganization): Observable<any> {
    return this.http.get(`${environment.api}/workorder/crc/attachment`, {
      responseType: 'blob',
      params: { attachmentId, serviceOrganization }
    });
  }

  getWorkOrderAttachment(workOrderNumber: string, attachmentId: string) {
    return this.http.get(`${environment.api}/workorder/getAttachment`, {
      responseType: 'blob',
      params: { workOrderNumber, attachmentId }
    });
  }

  getSosAnalyzePdf(serialNumber: string, sampleNumber: string): Observable<SosAnalyzePdfModel> {
    return this.http.post<SosAnalyzePdfModel>(`${environment.api}/equipment/SosAnalyzePdf`, {
      params: { serialNumber, sampleNumber }
    });
  }

  image(productHierarchy: string, model: string, imageSize = ImageSizeEnum.mobilethumbnailsize): Observable<any> {
    const time = new Date().getTime().toString();
    const httpOptions = {
      headers: new HttpHeaders({
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
        Expires: 'Sat, 01 Jan 2000 00:00:00 GMT'
      }),
      params: { productHierarchy, model, time, imageSize },
      responseType: 'blob' as any
    };
    return this.http.get<any>(`${environment.imageApi}/image/equipment`, httpOptions);
  }

  snapshot(cameraId: string, workOrderNumber: string, serviceOrganization: string): Observable<any> {
    const time = new Date().getTime().toString();
    const httpOptions = {
      headers: new HttpHeaders({
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
        Expires: 'Sat, 01 Jan 2000 00:00:00 GMT'
      }),
      params: { workOrderNumber, cameraId, time, serviceOrganization },
      responseType: 'blob' as any
    };
    return this.http.get<any>(`${environment.imageApi}/image/snapshot`, httpOptions);
  }

  inspectionHistoryPdf(formId: string, portalUserInspectionFormId: string, language: string, isMasked: string = 'false'): Observable<any> {
    return this.http.post(`${environment.api}/inspectionForm/getPdf`,
      { formId, portalUserInspectionFormId, language, isMasked },
      { responseType: 'blob' as 'json' }
    );
  }
  
  catInspectionPdf(dcode: string, inspectionNumber: string, reportName='pdforderedbyform'): Observable<CatInspectionPdfModel> {
    return this.http.post<CatInspectionPdfModel>(`${environment.api}/equipment/equipmentInspectionPdf`, { dcode, inspectionNumber, reportName });
  }
}
