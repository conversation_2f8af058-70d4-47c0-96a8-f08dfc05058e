import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../response/http.response';
import { UserEventLogModel } from '../model/user-event-log.model';

@Injectable({
  providedIn: 'root',
})
export class UserEventLogService {

  constructor(
    private readonly http: HttpClient,
  ) {
  }

  postClickEvent(section: string, subsection: string): Observable<string> {
    return this.postEvent({
      section,
      subsection,
      logData: {
        userClicked: true,
      },
    } as UserEventLogModel);
  }

  postEvent(body: UserEventLogModel): Observable<string> {
    return this.http.post<HttpResponse<string>>(`${environment.api}/log`, body)
      .pipe(
        map((res) => {
          if (res.code === 0) {
            return res.data;
          }
          return null;
        }),
      );
  }
}
