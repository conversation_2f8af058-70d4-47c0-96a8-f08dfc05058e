import { Directive, HostListener, Input, OnInit } from '@angular/core';
import { LogService } from '../../../module/customer/service/log.service';
import { LoginState } from '../../../module/authentication/state/login/login.state';
import { Store } from '@ngxs/store';
import { CustomerModel } from '../../../module/customer/model/customer.model';
import { CompanyModel } from '../../../module/company/model/company.model';
import { CommonState } from '../../../module/shared/state/common/common.state';

@Directive({
  selector: '[catUserClick]',
})
export class UserClickDirective implements OnInit {
  @Input()
  section: string;
  @Input()
  subsection: string;
  @Input()
  data: {};
  protected customer: CustomerModel;
  protected company: CompanyModel;
  protected trackerUserId: string;

  @HostListener('click') onClick() {
    this.buildParams();
    console.log('click logger', this.store.selectSnapshot(CommonState.trackerUser));
    this.logger.log(this.section, this.subsection, {
      userClicked: true,
      ...this.data,
      companyId: this.company?.id,
      customerId: this.customer?.id,
      ...(this.trackerUserId ? { trackerUserId: this.trackerUserId } : {})
    }).subscribe();
  }

  constructor(
    private readonly logger: LogService,
    private readonly store: Store,
  ) { }

  ngOnInit(): void {
    // this.customer = this.store.selectSnapshot(LoginState.customer);
    // this.company = this.store.selectSnapshot(LoginState.company);
  }

  protected buildParams() {
    this.customer = this.store.selectSnapshot(LoginState.customer);
    this.company = this.store.selectSnapshot(LoginState.company);
    this.trackerUserId = this.store.selectSnapshot(CommonState.trackerUser)?.uuid;
  }

}
