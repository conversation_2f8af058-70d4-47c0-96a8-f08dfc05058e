$body-bg: #FCFCFC;
$body-color: #2c2c2c;
$success: #5E9731;
$light-success: #73C700;
$warning: #FFA300;
$danger: #DA3A3C;
$info: #4A8EB0;
$secondary: #505050;
$light: #E4E8EB;
$text-muted: #9E9E9E;

$border-radius-sm: 8px;
$border-radius: 16px;

// region button
$btn-padding-y-sm: .57rem;
$btn-padding-x-sm: .75rem;
$btn-font-size-sm: .875rem;
$btn-padding-y: .75rem;
$btn-padding-x: 1rem;
$btn-font-size: 1rem;
$btn-padding-y-lg: 1rem;
$btn-padding-x-lg: 1.25rem;
$btn-font-size-lg: 1.25rem;
// #endregion

// region input
$input-bg: #EEF2F4;
$input-color: #8E8E8E;
$input-border-color: #D7E5EA;
$input-padding-y: .75rem;
$input-padding-x: 1.25rem;
$input-border-radius: .5rem;
$input-font-size: 16px;
// #endregion

// region progressbar
$progress-height: .625rem;
$progress-border-radius: 10px;
$progress-bg: rgba(255, 255, 255, 0.40);
$progress-bar-bg: white;
// #endregion

// region popover
$popover-arrow-width: 1.75rem;
$popover-arrow-height: .875rem;
// #endregion

// region table
$table-accent-bg: #f1f2f7;
$table-border-color: transparent;
$table-cell-padding: 1.3rem .75rem;
// #endregion

// region pagination
$pagination-bg: #f1f2f7;
$pagination-color: #8a8a8a;
$pagination-border-color: transparent;
$pagination-disabled-bg: #f9f9f9;
$pagination-disabled-color: #bfbaba;
$pagination-disabled-border-color: transparent;
$pagination-border-width: 0;
$pagination-hover-bg: $success;
$pagination-hover-color: white;
$pagination-hover-border-color: transparent;
$pagination-active-bg: $success;
$pagination-active-color: white;
$pagination-active-border-color: transparent;
// #endregion

// region heading
$h1-font-size: 1rem * 2.375;
$h2-font-size: 1rem * 2;
$h3-font-size: 1rem * 1.75;
$h4-font-size: 1rem * 1.25;
$h5-font-size: 1rem * 1.125;
$h6-font-size: 1rem !default;
// #endregion
