*:not(input):not(textarea) {
  -webkit-user-select: none; /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none; /* disable the IOS popup when long-press on a link */
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semi-bold {
  font-weight: 600;
}

.font-size-10px {
  font-size: 10px;
}

.font-size-11px {
  font-size: 11px;
}

.font-size-12px {
  font-size: 12px;
}

.font-size-13px {
  font-size: 13px;
}

.font-size-14px {
  font-size: 14px;
}

.font-size-16px {
  font-size: 16px;
}

.text-btn-light {
  color: #4A8EB0;
}

.btn-gradient {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, .1));
  background-repeat: repeat-x;
}

.equipment {
  &-image {
    width: 100px;
    height: 80px;
    position: relative;

    img {
      max-height: 100%;
      width: 97px;
      min-width: 97px;
    }

/*    &:before {
      content: ' ';
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      width: 80px;
      height: 80px;
      background-color: #EEF2F4;
      border-radius: 80px;
    }*/
  }

  .icon {
    &-clock, &-location {
      color: #4A8EB0;
      font-size: 14px;
    }
    &-sound {
      color: white;
      font-size: 16px !important;
    }
  }
}

.back-button-icon {
  font-size: 19px;
}

.cursor-pointer {
  cursor: pointer;
}

.min-width-200 {
  min-width: 200px;
}

.min-width-100 {
  min-width: 100px;
}

.spinner{
  animation: spinner 0.75s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.nav-back {
  font-size: 18px;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}
.success-message{
  font-size: 22px;
  font-weight: 700;
}
.icon-message-success {
  font-size: 60px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.font-weight-semi-bold {
  font-weight: 600;
}
.fw-bolder{
  font-weight: bolder;
}

.hidden {
  display: none !important;
}

@media only screen and (min-width: 600px) {
  .tablet-short-form-container{
    height: 95vh;
  }

  .tablet-form-container{
    height: 100vh;
  }
  
  .tablet-form{
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: .5rem;
    
    .textarea-form-element-container{
      flex: 1;
      
      .textarea-form-element{
        height: 100%;
      }
    }
  }
}