// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@import '~@angular/material/theming';
@import "variable/variable";
@import "variable/bootstrap-variable";
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
$custom-typography: mat-typography-config(
  $font-family: $font-family-base,
  $headline: mat-typography-level(32px, 48px, 700),
  $body-1: mat-typography-level(16px, 24px, 500)
);
// Override typography for all Angular Material, including mat-base-typography and all components.
@include mat-base-typography($custom-typography);
@include mat-core($custom-typography);


// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$cat724webview-primary: mat-palette($mat-indigo);
$cat724webview-accent: mat-palette($mat-pink, A200, A100, A400);

// The warn palette is optional (defaults to red).
$cat724webview-warn: mat-palette($mat-red);

// Create the theme object (a Sass map containing all of the palettes).
$cat724webview-theme: mat-light-theme($cat724webview-primary, $cat724webview-accent, $cat724webview-warn);

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include angular-material-theme($cat724webview-theme);
