<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="color-scheme" content="light only" />
  <title>Boom Webview</title>
  <base href="/">
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, viewport-fit=cover"
  />
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script type="application/javascript">
    window.onmessage = (m) => {
      // console.log('ONMESSSAGE', m);
      (window.postMessageQueue = window.postMessageQueue || []).push(m.data);
    };
    window.parent.postMessage(JSON.stringify({ function: 'moduleStarted', data: null }), '*');

    (function() {
      var api = '/api';
      // window.onerror = function(message, source, lineno, colno, error) {
      //   console.log('onError',message,source,lineno);
      //   var xhr = new XMLHttpRequest();
      //   xhr.open('POST', api + '/log');
      //   xhr.responseType = 'json';
      //   xhr.setRequestHeader("Content-Type", "application/json");
      //
      //   xhr.onreadystatechange = function() { // Call a function when the state changes.
      //     console.log('========================>',this.status);
      //     if (this.readyState === XMLHttpRequest.DONE && this.status === 200) {
      //       // Request finished. Do processing here.
      //     }
      //   }
      //   xhr.send(JSON.stringify({
      //     message,
      //     source,
      //     lineno,
      //     colno,
      //     error,
      //   }));
      // };

      function n(encoded) {
        if (typeof window.onmessage === 'function') {
          window.postMessage(encoded, '*');
        } else {
          (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
        }
      }

      function shiftParam(queryString, key) {
        var remaining = [];
        var value = null;
        var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
        for (var i = 0; i < pairs.length; i++) {
          var pair = pairs[i].split('=');
          if (decodeURIComponent(pair[0]) === key) {
            value = decodeURIComponent(pair[1]);
          } else {
            remaining.push(pairs[i]);
          }
        }
        return [value, remaining.join('&')];
      }

      function xott(ott, onDone, onError) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', api + '/user/xott?ott=' + ott);
        xhr.responseType = 'json';
        xhr.onload = function() {
          if (xhr.status !== 200) {
            return onError(xhr.response);
          }
          var responseObj = xhr.response;

          // console.log('xhr.response', responseObj);
          return onDone(responseObj);
        };
        xhr.send();
      }

      var s = window.location.search;
      if (s && s.indexOf('ott') !== -1) {
        var [ott, query] = shiftParam(s, 'ott');

        if (!ott) {
          return;
        }
        window.history.replaceState(null, '', window.location.pathname + (query.length ? '?' + query : '') + window.location.hash);

        n(JSON.stringify({
          type: 'loginStarted',
          data: new Date().getTime(),
        }));

        xott(ott, function(response) {
          n(JSON.stringify({
            type: 'loginResponse',
            data: {
              success      : true,
              loginResponse: response.data,
            },
          }));
        }, function(response) {
          n(JSON.stringify({
            type: 'loginResponse',
            data: {
              success      : false,
              loginResponse: response.data,
            },
          }));
        });
      }
    })();

    if (window.location.hostname === 'localhost') {
      function n(encoded) {
        if (typeof window.onmessage === 'function') {
          window.postMessage(encoded, '*');
        } else {
          (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
        }
      }

      n(JSON.stringify({
        type: 'mobileInfo',
        data: {
          version: '1.0.0',
          publicMenuHeaderCompany: '32f47d40-f093-4b99-b2d2-799d5b4ea0eb',
          // timezone     : 'test',
          firebaseToken: 'eAMhAw71TqKTM_ZrhR38Tl:APA91bEOBAjIk86mY3xbWubxgn6-6c4Ih_NLAwjXeVUSFJuqZS6H-gOiK56ZVnRkJp4i_1ylcBSdEnJvBKZBeDcibKlHq20AZ8tHhniveojJBvTsatAVLHH2HCUbmItzUASVnNY1HvWH',
        },
      }));
    }

  </script>
  <style>
    #front-loader {
      z-index: 10000;
      position: fixed;
      left: 50%;
      top: 50%;
      margin-left: -50px;
      margin-top: -50px;
      border: 16px solid #7f7e7e;
      border-radius: 50%;
      border-top: 16px solid #FFA300;
      border-bottom: 16px solid #FFA300;
      width: 100px;
      height: 100px;
      animation: spin 1s linear infinite;
      box-sizing: border-box;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

  </style>
  <script type="application/javascript">
    document.write('<div id="front-loader"></div>');
  </script>
</head>
<body class="mat-typography">
<cat-root></cat-root>
</body>
</html>
