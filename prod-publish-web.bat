SET deployUserName=%1
SET deployUserPassword=%2
SET machineCode=%3
SET folderName=%4
SET poolName=%5
SET buildNumber=%6
SET rebuild=%7

SET serverName=bgazweboomd01
SET configuration=build-production
if %machineCode%==App1 (
	SET serverName=bgazweboomp01
)
if %machineCode%==App2 (
	SET serverName=bgazweboomp02
)
if %machineCode%==App3 (
	SET serverName=bgazweboomp05
)
if %machineCode%==App4 (
	SET serverName=bgazweboomp06
)
if %machineCode%==App5 (
	SET serverName=bgazweboomp07
)

if %rebuild%==true (
  CALL npm install

  CALL npm run %configuration%

  CALL %ZIP_HOME%\7z a "%WORKSPACE%\dist\%buildNumber%.zip" "%WORKSPACE%\dist\cat724webview\*"
)

CALL net use \\%serverName%\ipc$ /user:%deployUserName% %deployUserPassword%

CALL net use U: "\\%serverName%\JenkinsPublish" /user:%deployUserName% %deployUserPassword% /persistent:no

CALL xcopy /Y /E "%WORKSPACE%\dist\%buildNumber%.zip" U:\BOOM\%folderName%\

CALL net use U: /delete | EXIT 0

CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% %ZIP_HOME%\7z a D:\AppBackups\BOOM\%folderName%\%buildNumber%.zip D:\Apps\BOOM\%folderName% | EXIT 0

CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% %ZIP_HOME%\7z x D:\JenkinsPublish\BOOM\%folderName%\%buildNumber%.zip -oD:\Apps\BOOM\%folderName% -r -y

CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% cmd /c del D:\JenkinsPublish\BOOM\%folderName%\%buildNumber%.zip | EXIT 0

SET BUILD_STATUS=%ERRORLEVEL%
IF [%BUILD_STATUS%] == [0] (
	EXIT 0
) ELSE (
	EXIT /B 1
)
