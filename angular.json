{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"cat724webview": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "cat", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/cat724webview", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets", "src/web.config"], "styles": ["src/styles.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/"]}}, "configurations": {"temp": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.temp.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/temp"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "development": {"index": {"input": "src/environments/index.development.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/development"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "stage": {"index": {"input": "src/environments/index.stage.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/stage"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "production": {"index": {"input": "src/environments/index.production.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/production"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "cat724webview:build"}, "configurations": {"temp": {"browserTarget": "cat724webview:build:temp"}, "development": {"browserTarget": "cat724webview:build:development"}, "stage": {"browserTarget": "cat724webview:build:stage"}, "production": {"browserTarget": "cat724webview:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "cat724webview:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss"]}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "cat724webview:serve"}, "configurations": {"temp": {"devServerTarget": "cat724webview:build:temp"}, "development": {"devServerTarget": "cat724webview:build:development"}, "stage": {"devServerTarget": "cat724webview:build:stage"}, "production": {"devServerTarget": "cat724webview:serve:production"}}}}}}, "defaultProject": "cat724webview", "cli": {"analytics": false}}