{"name": "cat724webview", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.js --port 2287 --host 0.0.0.0", "start:stage": "ng serve --proxy-config proxy.conf.js --port 2287 --host 0.0.0.0", "start:prod": "ng serve --proxy-config proxy.conf.js --port 2287 --host 0.0.0.0", "build": "ng build", "test": "ng test", "lint": "ng lint", "build-development": "ng build --aot true --buildOptimizer true --configuration=development --buildOptimizer true --deployUrl=https://borusancat360devm.azureedge.net/mobileview/", "build-stage": "ng build --aot true --buildOptimizer true --configuration=stage --buildOptimizer true --deployUrl=https://borusancat360qam.azureedge.net/mobileview/", "build-production": "ng build --aot true --buildOptimizer true --configuration=production --buildOptimizer true --deployUrl=https://borusancat360prodm.azureedge.net/mobileview/", "build-temp": "ng build --aot true --buildOptimizer true --configuration=temp --buildOptimizer true --deployUrl=https://borusancat360zm.azureedge.net/mobileview/", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~10.0.9", "@angular/cdk": "10.1.3", "@angular/common": "~10.0.9", "@angular/compiler": "~10.0.9", "@angular/core": "~10.0.9", "@angular/forms": "~10.0.9", "@angular/google-maps": "^11.2.0", "@angular/localize": "^10.0.14", "@angular/material": "10.1.3", "@angular/platform-browser": "~10.0.9", "@angular/platform-browser-dynamic": "~10.0.9", "@angular/router": "~10.0.9", "@ng-bootstrap/ng-bootstrap": "^9.1.0", "@ng-select/ng-select": "^5.0.1", "@ngx-translate/core": "^13.0.0", "@ngxs/devtools-plugin": "^3.6.2", "@ngxs/form-plugin": "^3.6.2", "@ngxs/logger-plugin": "^3.6.2", "@ngxs/router-plugin": "^3.6.2", "@ngxs/storage-plugin": "^3.6.2", "@ngxs/store": "^3.6.2", "@piumaz/pull-to-refresh": "^3.0.0", "bootstrap": "^4.5.2", "moment": "^2.27.0", "ng-connection-service": "^1.0.4", "ng2-pdf-viewer": "^6.3.2", "ngx-loading": "^8.0.0", "ngx-mask": "^12.0.0", "ngxs-reset-plugin": "^1.3.0", "recordrtc": "^5.6.1", "rxjs": "~6.6.2", "snq": "^1.0.3", "tslib": "^2.0.1", "uuid": "^8.3.2", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1000.5", "@angular/cli": "~10.0.5", "@angular/compiler-cli": "~10.0.9", "@angular/language-service": "~10.0.9", "@ngxs/schematics": "^0.0.1-alpha.5", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.5.12", "@types/jasminewd2": "~2.0.8", "@types/moment": "^2.13.0", "@types/node": "^14.0.27", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.2", "karma": "~5.1.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "protractor": "~7.0.0", "ts-node": "~8.10.2", "tslint": "~6.1.3", "typescript": "~3.9.7"}}