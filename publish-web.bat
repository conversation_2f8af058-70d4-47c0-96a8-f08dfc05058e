SET deployUserName=%1
SET deployUserPassword=%2
SET environmentName=%3
SET folderName=%4
SET poolName=%5
SET buildNumber=%6

SET serverName=bgazweboomd01
SET configuration=build-development
if %environmentName%==Development (
	SET serverName=bgazweboomd01
	SET configuration=build-development
)
if %environmentName%==Stage (
	SET serverName=bgazweboomd02
	SET configuration=build-stage
)

CALL npm install

CALL npm run %configuration%

SET BUILD_STATUS=%ERRORLEVEL%
IF NOT [%BUILD_STATUS%] == [0] (
	EXIT /B 1
)

CALL net use \\%serverName%\ipc$ /user:%deployUserName% %deployUserPassword%

CALL %ZIP_HOME%\7z a "%WORKSPACE%\dist\%buildNumber%.zip" "%WORKSPACE%\dist\cat724webview\*"

CALL net use U: "\\%serverName%\JenkinsPublish" /user:%deployUserName% %deployUserPassword% /persistent:no

CALL xcopy /Y /E "%WORKSPACE%\dist\%buildNumber%.zip" U:\BOOM\%environmentName%\%folderName%\

CALL net use U: /delete | EXIT 0

IF NOT %environmentName% == Development (
  CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% %ZIP_HOME%\7z a D:\AppBackups\BOOM\%environmentName%\%folderName%\%buildNumber%.zip D:\Apps\BOOM\%environmentName%\%folderName% | EXIT 0
)

CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% %ZIP_HOME%\7z x D:\JenkinsPublish\BOOM\%environmentName%\%folderName%\%buildNumber%.zip -oD:\Apps\BOOM\%environmentName%\%folderName% -r -y

CALL del %WORKSPACE%\dist\%buildNumber%.zip | EXIT 0

CALL "%PSTOOLS_HOME%\PsExec.exe" -h /accepteula \\%serverName% -u %deployUserName% -p %deployUserPassword% cmd /c del D:\JenkinsPublish\BOOM\%environmentName%\%folderName%\%buildNumber%.zip | EXIT 0

SET BUILD_STATUS=%ERRORLEVEL%
IF [%BUILD_STATUS%] == [0] (
	EXIT 0
) ELSE (
	EXIT /B 1
)
